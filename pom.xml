<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.2.11.RELEASE</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.raipeng</groupId>
    <artifactId>ai-call-engine</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>ai-call-engine</name>
    <description>ai-call-engine</description>

    <properties>
        <java.version>1.8</java.version>
        <spring-cloud.version>Hoxton.SR9</spring-cloud.version>
        <spring-cloud-alibaba.version>2.2.1.RELEASE</spring-cloud-alibaba.version>
        <httpcomponents.version>4.4</httpcomponents.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.antgroup.antchain.openapi</groupId>
            <artifactId>openapi-risknet</artifactId>
            <version>1.0.6</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.9</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
            <version>1.1.3</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.joda</groupId>
            <artifactId>joda-money</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>org.jadira.usertype</groupId>
            <artifactId>usertype.core</artifactId>
            <version>6.0.1.GA</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>objenesis</artifactId>
                    <groupId>org.objenesis</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>2.0.1</version>
        </dependency>

        <dependency>
            <groupId>ws.schild</groupId>
            <artifactId>jave-all-deps</artifactId>
            <version>2.5.1</version>
        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>

        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>2.9.2</version>
            <exclusions>
                <exclusion>
                    <artifactId>jsr305</artifactId>
                    <groupId>com.google.code.findbugs</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>2.9.2</version>
        </dependency>
        <dependency>
            <groupId>com.raipeng</groupId>
            <artifactId>ai-common</artifactId>
            <version>1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
            <version>3.1</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>2.9.5</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.59</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <version>0.7.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>2.2.0</version>
        </dependency>
        <dependency>
            <groupId>com.raiyi.data.encry</groupId>
            <artifactId>ry-data-encry-sdk</artifactId>
            <version>2.0.6</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
            <version>1.5.2.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <version>3.12.5</version>
        </dependency>

        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
            <version>2.9.3</version>
        </dependency>

        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>2.0.9</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>2.0.9</version>
            <scope>test</scope>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.zhongan</groupId>-->
        <!--            <artifactId>zhongan-sdk</artifactId>-->
        <!--            <version>1.0</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>za-gateway-common-share</artifactId>
            <version>1.0.5-201218-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
            <version>4.4.6</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-ecs</artifactId>
            <version>4.17.6</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/commons-codec/commons-codec -->
        <!-- BASE64处理 -->
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.11</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun.mq</groupId>
            <artifactId>mq-http-sdk</artifactId>
            <version>1.0.3</version>
            <!--<classifier>jar-with-dependencies</classifier>-->
        </dependency>

        <dependency>
            <groupId>com.zhongan.fcp</groupId>
            <artifactId>zhongan-fcp-gateway-common-share</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.zhongan.tech</groupId>
            <artifactId>anlink-openapi-sdk</artifactId>
            <version>1.3.4-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>org.dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>2.1.3</version>
        </dependency>



        <dependency>
            <groupId>org.freeswitch.esl.client</groupId>
            <artifactId>org.freeswitch.esl.client</artifactId>
            <version>0.9.2</version>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>22.0</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba.nls</groupId>
            <artifactId>nls-sdk-recognizer</artifactId>
            <version>2.2.1</version>
        </dependency>


        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>3.9.1</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>

        <dependency>
            <groupId>com.raipeng</groupId>
            <artifactId>ai-call-common</artifactId>
            <version>1.0.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>

    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <profiles>
        <profile>
            <!-- 本地开发环境 -->
            <id>dev1</id>
            <properties>
                <profiles.active>dev1</profiles.active>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <!-- 送呼一 -->
            <id>engine1</id>
            <properties>
                <profiles.active>engine1</profiles.active>
            </properties>
        </profile>
        <profile>
            <!-- 送呼二 -->
            <id>engine2</id>
            <properties>
                <profiles.active>engine2</profiles.active>
            </properties>
        </profile>
        <profile>
            <!-- 送呼三 -->
            <id>engine3</id>
            <properties>
                <profiles.active>engine3</profiles.active>
            </properties>
        </profile>
        <profile>
            <!-- 送呼四 -->
            <id>engine4</id>
            <properties>
                <profiles.active>engine4</profiles.active>
            </properties>
        </profile>

        <profile>
            <!-- 送呼5 -->
            <id>engine5</id>
            <properties>
                <profiles.active>engine5</profiles.active>
            </properties>
        </profile>

        <profile>
            <!-- 送呼6 -->
            <id>engine6</id>
            <properties>
                <profiles.active>engine6</profiles.active>
            </properties>
        </profile>

        <profile>
            <!-- 送呼7 -->
            <id>engine7</id>
            <properties>
                <profiles.active>engine7</profiles.active>
            </properties>
        </profile>

        <profile>
            <!-- 送呼8 -->
            <id>engine8</id>
            <properties>
                <profiles.active>engine8</profiles.active>
            </properties>
        </profile>

        <profile>
            <!-- 送呼9 -->
            <id>engine9</id>
            <properties>
                <profiles.active>engine9</profiles.active>
            </properties>
        </profile>

        <profile>
            <!-- 送呼9 -->
            <id>enginea</id>
            <properties>
                <profiles.active>enginea</profiles.active>
            </properties>
        </profile>

        <profile>
            <!-- 送呼9 -->
            <id>engineb</id>
            <properties>
                <profiles.active>engineb</profiles.active>
            </properties>
        </profile>

        <profile>
            <!-- 送呼9 -->
            <id>enginec</id>
            <properties>
                <profiles.active>enginec</profiles.active>
            </properties>
        </profile>

        <profile>
            <!-- 送呼9 -->
            <id>engined</id>
            <properties>
                <profiles.active>engined</profiles.active>
            </properties>
        </profile>

        <profile>
            <!-- 送呼9 -->
            <id>pt1prod</id>
            <properties>
                <profiles.active>pt1prod</profiles.active>
            </properties>
        </profile>

        <profile>
            <!-- 送呼9 -->
            <id>pt2prod</id>
            <properties>
                <profiles.active>pt2prod</profiles.active>
            </properties>
        </profile>

        <profile>
            <!-- 送呼9 -->
            <id>pt3prod</id>
            <properties>
                <profiles.active>pt3prod</profiles.active>
            </properties>
        </profile>

        <profile>
            <!-- 送呼压力测试1 -->
            <id>engine-pressure1</id>
            <properties>
                <profiles.active>engine-pressure1</profiles.active>
            </properties>
        </profile>
        <profile>
            <!-- 送呼压力测试1 -->
            <id>engine-pressure2</id>
            <properties>
                <profiles.active>engine-pressure2</profiles.active>
            </properties>
        </profile>

        <profile>
            <!-- test -->
            <id>test</id>
            <properties>
                <profiles.active>test</profiles.active>
            </properties>
        </profile>
        <profile>
            <!-- ptest1 -->
            <id>ptest1</id>
            <properties>
                <profiles.active>ptest1</profiles.active>
            </properties>
        </profile>
        <profile>
            <!-- ptest2 -->
            <id>ptest2</id>
            <properties>
                <profiles.active>ptest2</profiles.active>
            </properties>
        </profile>
        <profile>
            <!-- ptest3 -->
            <id>ptest3</id>
            <properties>
                <profiles.active>ptest3</profiles.active>
            </properties>
        </profile>
        <profile>
            <!-- ptest4 -->
            <id>ptest4</id>
            <properties>
                <profiles.active>ptest4</profiles.active>
            </properties>
        </profile>
    </profiles>

    <build>
        <finalName>ai-call-engine-${profiles.active}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <!-- 资源根目录排除各环境的配置，防止在生成目录中多余其它目录 -->
                <excludes>
                    <exclude>engine1/*</exclude>
                    <exclude>engine2/*</exclude>
                    <exclude>engine3/*</exclude>
                    <exclude>engine4/*</exclude>
                    <exclude>engine5/*</exclude>
                    <exclude>engine6/*</exclude>
                    <exclude>engine7/*</exclude>
                    <exclude>engine8/*</exclude>
                    <exclude>engine9/*</exclude>
                    <exclude>enginea/*</exclude>
                    <exclude>engineb/*</exclude>
                    <exclude>enginec/*</exclude>
                    <exclude>engined/*</exclude>
                    <exclude>pt1prod/*</exclude>
                    <exclude>pt2prod/*</exclude>
                    <exclude>pt3prod/*</exclude>
                    <exclude>engine-pressure1/*</exclude>
                    <exclude>engine-pressure2/*</exclude>
                    <exclude>ptest1/*</exclude>
                    <exclude>ptest2/*</exclude>
                    <exclude>ptest3/*</exclude>
                    <exclude>ptest4/*</exclude>
                    <exclude>dev/*</exclude>
                    <exclude>test/*</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources/${profiles.active}</directory>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>cn.dalgen.plugins</groupId>
                <artifactId>mybatis-maven-plugin</artifactId>
                <version>1.2.4</version>
                <configuration>
                    <!-- 第一步将ftl模板开放出来以便于自定义扩展 -->
                    <templateDirectory>dalgen/templates</templateDirectory>
                    <!-- 第二步先将值设为true，执行一次ftl文件才能真正被copy到templateDirectory，然后将值改为false -->
                    <copyTemplate>false</copyTemplate>
                    <outputDirectory>src</outputDirectory>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.0.0-M5</version>
                <configuration>
                    <!--表示执行任何子目录下所有命名以Test结尾的java类 -->
                    <includes>
                        <include>**/*Test.java</include>
                    </includes>
                    <!--表示不执行任何子目录下所有命名以Test开头的java类 -->
                    <excludes>
                        <exclude>**/ignore/*Test.java</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.6</version>
                <executions>
                    <execution>
                        <id>default-prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
