package com.raipeng.aidatapush;

import com.alibaba.fastjson.JSON;
import com.rabbitmq.client.Channel;
import com.raipeng.aidatacommon.model.dto.CallRecordResult;
import com.raipeng.aidatacommon.model.dto.CallRecordResultNoticeSpeechMaterial;

import com.raipeng.aidatapush.service.ChannelManagerService;
import com.raipeng.aidatapush.service.RedisSecondCacheService;
import com.raipeng.aidatapush.service.SupplyLineService;
import org.junit.jupiter.api.Test;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.raipeng.aidatapush.config.RabbitConstants.*;


@SpringBootTest
class AiDataPushApplicationTests {
    @Autowired
    private SupplyLineService supplyLineService;
    @Test
    void contextLoads() throws IOException {
        Map<String, String> supplyLineBelongMap = supplyLineService.getSupplyLineBelongMap();
        System.out.println(supplyLineBelongMap);

    }
}
