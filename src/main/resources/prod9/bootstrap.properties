spring.application.name=market-ai-call
spring.cloud.nacos.config.server-addr=**************:8848,**************:8849,**************:8850
spring.cloud.nacos.config.prefix=${spring.application.name}
spring.cloud.nacos.config.file-extension=properties
spring.cloud.nacos.discovery.server-addr=**************:8848,**************:8849,**************:8850

service.ip=localhost
service.ai.speech.ip=***************
service.ai.manager.ip=***************
ai.call.flag=162-ai-call-9
ai.call.dialog.id.flag=9
ai.call.ip=***************
ai.call.port=9001
data.center.id=1
machine.id=9