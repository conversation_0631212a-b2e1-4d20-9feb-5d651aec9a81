spring.application.name=market-ai-call-engine-pressure
spring.cloud.nacos.config.server-addr=***************:8848,***************:8849,***************:8850
spring.cloud.nacos.config.prefix=${spring.application.name}
spring.cloud.nacos.config.file-extension=properties
spring.cloud.nacos.discovery.server-addr=***************:8848,***************:8849,***************:8850

service.ip=localhost
service.ai.speech.ip=***************
service.ai.manager.ip=***************
ai.call.engine.ip=162-engine2
ai.call.engine.dialog.id.flag=2