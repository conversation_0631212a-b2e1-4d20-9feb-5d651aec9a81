FROM openjdk:8-jre-alpine3.9
VOLUME /tmp
ENV LANG="en_US.UTF-8"
EXPOSE 8204
ADD ai-data-push/target/ai-data-push-dev.jar  ai-data-push-dev.jar
ENTRYPOINT java -server -Xmx12288m -Xms12288m -Xmn9216m -XX:MetaspaceSize=134217728 -XX:MaxMetaspaceSize=268435456 -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintTenuringDistribution -XX:+PrintHeapAtGC -Xloggc:/static/file/gc.log -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=100M -jar ai-data-push-dev.jar --spring.profiles.active=dev -Duser.timezone=GMT+08
