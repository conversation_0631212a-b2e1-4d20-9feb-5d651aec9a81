pipeline{
	agent any
	stages {
		stage('Prepare') {
			steps{
				script {
				    echo '第一步:开始从Git拉取最新的代码'
					checkout([$class: 'GitSCM', branches: [[name: '*/master']], doGenerateSubmoduleConfigurations: false, extensions: [], submoduleCfg: [], userRemoteConfigs: [[credentialsId: 'fcd2dba3-9967-4565-97bd-1e6425583cbd', url: 'git@**************:root/insurance_system.git']]])

					artifact_id = "ai-data-push"
					profile = "prod"
					docker_img_name = "$artifact_id-$profile"
					build_tag = sh(returnStdout: true, script: 'git rev-parse --short HEAD').trim()
						if (env.BRANCH_NAME != 'master' && env.BRANCH_NAME != null) {
							build_tag = "${env.BRANCH_NAME}-$build_tag"
						}
					}
					echo "当前Tag是: $build_tag"
				}
			}
			stage('Test') {
				steps{
					echo '第二步：运行单元测试案例'
				}
			}
			stage('Build') {
				steps{
					echo "第三步：打成Jar包,当前环境:$profile"
					sh "cd common && mvn clean install -Dmaven.test.skip"
					sh "cd $artifact_id && mvn clean package  -Dmaven.test.skip=true -P$profile"
					echo "第四步：打成docker镜像"

					script {
						//需要删除的镜像
						image_id = sh(returnStdout: true, script: "docker images -q --filter reference=$docker_img_name:$build_tag")
						echo "镜像${image_id}需要删除"
					}
                    echo "开始打包"
					sh "docker build -t ${docker_img_name}:${build_tag} " +
					" --build-arg SPRING_PROFILE=${profile} " +
					" --build-arg JAR_FILE=${artifact_id}/target/${docker_img_name}.jar " +
					" -f ${artifact_id}/src/main/docker/Dockerfile.${profile} ."
					sh "docker images"
				}
			}
			stage('Push') {
				steps{
					echo '第五步:将docker镜像Push到私服'
					sh "docker tag $docker_img_name:$build_tag **************:5000/$docker_img_name:latest"
					sh "docker tag $docker_img_name:$build_tag **************:5000/$docker_img_name:$build_tag"
					sh "docker push **************:5000/$docker_img_name:latest"
					sh "docker push **************:5000/$docker_img_name:$build_tag"
				}
			}

			stage('Delete') {
				steps{
					echo '第六步:删除原镜像'
					script {
						if (image_id == ""){
							echo "没有需要删除的镜像"
						} else {
							echo "需要删除镜像:$image_id"
							docker_id_image = sh(returnStdout: true, script: "docker ps -a -q --filter ancestor=$image_id")
							if (docker_id_image != ""){
								echo "需要删除该镜像创建的Docker容器"
								sh(script:"docker rm $docker_id_image")
								echo "Docker容器:$docker_id_image已删除"
							}
							sh(script:"docker rmi $image_id")
							echo "镜像:${image_id}已删除"
						}
					}
				}
			}

			stage('Deploy') {
				steps{
					echo '第七步:将启动远程SSH部署的Jenkins任务'
				}
			}
		}
}
