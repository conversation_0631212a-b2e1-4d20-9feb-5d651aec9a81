package com.raipeng.aicallsub.entity.task;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HangUpSmsTriggerPojo {
    private String intentionType;
    private List<String> labelIds;
    private Long smsTemplateId;
    private Integer triggerOrder;
}
