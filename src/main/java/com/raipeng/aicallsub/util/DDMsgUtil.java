package com.raipeng.aicallsub.util;


import com.raipeng.aicallsub.enums.DingDingMsgType;
import com.raipeng.aicallsub.service.DingDingService;

import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Collections;

@Component
public class DDMsgUtil {
    private static DingDingService dingDingStaticService;

    @Resource
    private DingDingService dingDingService;

    @PostConstruct
    private void init() {
        dingDingStaticService = dingDingService;
    }

    public static void sedDDMsg(DingDingMsgType dingDingMsgType, String... contents) {
        dingDingStaticService.sendDingDingMessages(dingDingMsgType.getMessageType(), contents);
    }

    public static void sedDDMsg(String phone, DingDingMsgType dingDingMsgType, String... contents) {
        dingDingStaticService.sendDingDingMessages(dingDingMsgType.getMessageType(), Collections.singletonList(phone), contents);
    }
}
