package com.raipeng.aicallsub.controller;


import com.raipeng.aicallsub.entity.train.AIResponse;
import com.raipeng.aicallsub.entity.train.UserContentBody;
import com.raipeng.aicallsub.service.train.UserContentService;
import com.raipeng.aicallsub.util.GsonUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/userInteractive")
public class UserInteractiveController {
    @Autowired
    private UserContentService userContentService;

    @PostMapping("/start")
    public AIResponse start(@RequestBody UserContentBody userContent) {
        if (userContent.getSentence() == null && !userContent.getUserHangup() && !userContent.getIsContinue()) {
            return userContentService.startOnePhone(userContent);
        }
        return AIResponse.builder().success(false).build();
    }

    @PostMapping("/onCall")
    public AIResponse interactive(@RequestBody String userContent) {
        return userContentService.handleUserContent(GsonUtil.fromJson(userContent, UserContentBody.class));
    }

    @PostMapping("/interactive")
    public AIResponse interactive(@RequestBody UserContentBody userContent) {
        return userContentService.handleUserContent(userContent);
    }
}
