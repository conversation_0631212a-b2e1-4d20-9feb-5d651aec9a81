package com.raipeng.aicall.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.regex.Pattern;

/**
 * <AUTHOR> Shi
 * @Description 获取IP工具类
 * @Date 2019/9/2 16:06
 **/
@Slf4j
public class IpAddressUtil {
    private static final String IP_REGEX =
            "^((25[0-5]|(2[0-4][0-9]|1[0-9][0-9]?|[1-9]?[0-9]))\\.){3}(25[0-5]|(2[0-4][0-9]|1[0-9][0-9]?|[1-9]?[0-9]))$";
    private static final Pattern IP_PATTERN = Pattern.compile(IP_REGEX);

    /**
     * 获取Ip地址
     *
     * @param request
     * @return
     */
    public static String getIpAddress(HttpServletRequest request) {
        String Xip = request.getHeader("X-Real-IP");
        String XFor = request.getHeader("X-Forwarded-For");
        if (StringUtils.isNotEmpty(XFor) && !"unKnown".equalsIgnoreCase(XFor)) {
            //多次反向代理后会有多个ip值，第一个ip才是真实ip
            int index = XFor.indexOf(",");
            if (index != -1) {
                return XFor.substring(0, index);
            } else {
                return XFor;
            }
        }
        XFor = Xip;
        if (StringUtils.isNotEmpty(XFor) && !"unKnown".equalsIgnoreCase(XFor)) {
            return XFor;
        }
        if (StringUtils.isBlank(XFor) || "unknown".equalsIgnoreCase(XFor)) {
            XFor = request.getHeader("Proxy-Client-IP");
        }
        if (StringUtils.isBlank(XFor) || "unknown".equalsIgnoreCase(XFor)) {
            XFor = request.getHeader("WL-Proxy-Client-IP");
        }
        if (StringUtils.isBlank(XFor) || "unknown".equalsIgnoreCase(XFor)) {
            XFor = request.getHeader("HTTP_CLIENT_IP");
        }
        if (StringUtils.isBlank(XFor) || "unknown".equalsIgnoreCase(XFor)) {
            XFor = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (StringUtils.isBlank(XFor) || "unknown".equalsIgnoreCase(XFor)) {
            XFor = request.getRemoteAddr();
        }
        return XFor;
    }

    public static String getIpAddressTest(HttpServletRequest request) {
        log.info("host : " + request.getHeader("host"));
        log.info("X-Real-IP : " + request.getHeader("X-Real-IP"));
        log.info("x-forwarded-for : " + request.getHeader("x-forwarded-for"));
        log.info("X-Forwarded-For : " + request.getHeader("X-Forwarded-For"));
        log.info("Proxy-Client-IP : " + request.getHeader("Proxy-Client-IP"));
        log.info("WL-Proxy-Client-IP : " + request.getHeader("WL-Proxy-Client-IP"));
        log.info("HTTP_CLIENT_IP : " + request.getHeader("HTTP_CLIENT_IP"));
        log.info("HTTP_X_FORWARDED_FOR : " + request.getHeader("HTTP_X_FORWARDED_FOR"));
        log.info("HTTP_X_FORWARDED_FOR : " + request.getHeader("HTTP_X_FORWARDED_FOR"));

        return "";
    }

    public static boolean isIpInCSubnet(String ip, String subnet) {
        if (!isValidIp(ip) || !isValidSubnet(subnet)) {
            log.error("Invalid IP address or subnet : ip =" + ip + " subnet=" + subnet);
            throw new IllegalArgumentException("Invalid IP address or subnet");
        }

        String[] subnetParts = subnet.split("/");
        String subnetIp = subnetParts[0];
        int prefixLength = Integer.parseInt(subnetParts[1]);

        String[] ipParts = ip.split("\\.");
        String[] subnetPartsArray = subnetIp.split("\\.");

        int ipAsInt = (Integer.parseInt(ipParts[0]) << 24) +
                (Integer.parseInt(ipParts[1]) << 16) +
                (Integer.parseInt(ipParts[2]) << 8) +
                Integer.parseInt(ipParts[3]);

        int subnetAsInt = (Integer.parseInt(subnetPartsArray[0]) << 24) +
                (Integer.parseInt(subnetPartsArray[1]) << 16) +
                (Integer.parseInt(subnetPartsArray[2]) << 8) +
                Integer.parseInt(subnetPartsArray[3]);

        int mask = (-1 << (32 - prefixLength));

        return (ipAsInt & mask) == (subnetAsInt & mask);
    }

    public static void main(String[] args) {
        String ipAddress = "*************"; // 要判断的 IP 地址
        String subnet = "***********/23"; // 网段

        try {
            if (isIpInCSubnet(ipAddress, subnet)) {
                System.out.println(ipAddress + " 在 " + subnet + " 网段内");
            } else {
                System.out.println(ipAddress + " 不在 " + subnet + " 网段内");
            }
        } catch (IllegalArgumentException e) {
            System.out.println(e.getMessage());
        }
    }


    public static boolean isValidIp(String ip) {
        return StringUtils.isNotEmpty(ip) && IP_PATTERN.matcher(ip).matches();
    }

    public static boolean isValidSubnet(String subnet) {
        if (StringUtils.isEmpty(subnet)) {
            return false;
        }
        String[] parts = subnet.split("/");
        return parts.length == 2 && isValidIp(parts[0]) && isValidPrefixLength(parts[1]);
    }

    private static boolean isValidPrefixLength(String prefix) {
        try {
            int len = Integer.parseInt(prefix);
            return len >= 0 && len <= 32;
        } catch (NumberFormatException e) {
            return false;
        }
    }
}
