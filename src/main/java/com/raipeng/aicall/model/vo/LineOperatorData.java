package com.raipeng.aicall.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 线路运营商数据模型
 */
@Data
public class LineOperatorData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 运营商名称
     */
    private String operator;

    /**
     * 宽带商，走电信移动和联通
     */
    private String broadbandOperator;//宽带商

    /**
     * 线路号码
     */
    private String lineNumber;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 备注信息
     */
    private String remark;
}
