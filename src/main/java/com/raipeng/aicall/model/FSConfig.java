package com.raipeng.aicall.model;

import lombok.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR> Roki
 * @description: fs配置
 * @date ：2023/05/29 14:33
 */
@Entity
@Table(name = "t_fs_config")
@EqualsAndHashCode()
@Builder
@Data
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class FSConfig extends BasicEntity implements Serializable {

    @Column(name = "ip", columnDefinition = "varchar(50) default null COMMENT 'ip'")
    private String ip;

    @Column(name = "port", columnDefinition = "int(11) default null COMMENT '端口'")
    private Integer port;

    @Column(name = "pwd", columnDefinition = "varchar(50) default null COMMENT 'ps'")
    private String pwd;

    @Column(name = "enable", columnDefinition = "int(2) default 1 COMMENT '0:无效，1生效'")
    private Integer enable;

    @Column(name = "description", columnDefinition = "varchar(100) default null COMMENT '描述'")
    private String description;

    @Column(name = "ai_call_engine_ip", columnDefinition = "varchar(100) default null COMMENT '呼叫引擎ip'")
    private String aiCallEngineIp;

    @Transient
    private String bridgeKmlAddress1;

    @Transient
    private String bridgeKmlAddress2;

    @Transient
    private String currentCxcc;//当前FS宽带商

    @Transient
    private Integer isKml;

    @Transient
    private String kmlAddress;

    @Transient
    private String kmlAddressCmcc;

    @Transient
    private String kmlAddressCucc;

    @Transient
    private String engineIp;

    @Transient
    private Integer enginePort;

    @Transient
    private String callIp;

    @Transient
    private Integer callPort;

    @Transient
    private AtomicInteger tryConCount = new AtomicInteger(0);


}