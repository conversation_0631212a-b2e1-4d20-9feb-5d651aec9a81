package com.raipeng.aicall.feign;

import com.raipeng.aicall.model.vo.LineOperatorData;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 线路运营商Feign客户端
 */
@FeignClient(name="line-operator-feign", url="http://${service.ai.sip-meida.ip}:${service.ai.sip-meida.port}")
public interface LineOperatorFeign {

    /**
     * 获取所有线路运营商数据
     * @return 线路运营商数据列表
     */
    @RequestMapping(value = "/api/sip-ip/getAllLineOperatorData", method = RequestMethod.GET)
    List<LineOperatorData> getAllLineOperatorData();

    /**
     * 根据线路号码获取单个线路运营商数据
     * @param lineNumber 线路号码
     * @return 线路运营商数据
     */
    @RequestMapping(value = "/api/sip-ip/getLineOperatorByLineNumber", method = RequestMethod.GET)
    LineOperatorData getLineOperatorByLineNumber(@RequestParam String lineNumber);
}
