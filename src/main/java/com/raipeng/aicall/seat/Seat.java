package com.raipeng.aicall.seat;

import lombok.Data;

import javax.persistence.Column;
import java.time.LocalDateTime;

@Data
public class Seat {
    private String seatId;
    private String fsUser;//fs用户
    private String fsIp;//fs用户
    private String fsPort;//fs端口
    private Integer isUse;//是否使用 0 为被分配，1被分配
    private LocalDateTime expireTime;//过期时间
    private String seatTeamId;
    private Integer enable;//在线状态

}
