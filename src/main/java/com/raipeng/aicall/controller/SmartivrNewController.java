package com.raipeng.aicall.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.rabbitmq.client.Channel;
import com.raipeng.aicall.constant.*;
import com.raipeng.aicall.controller.qd.SmartivrResponse;
import com.raipeng.aicall.feign.CommonMonitorFeign;
import com.raipeng.aicall.mq.producter.CallRecordResultNoticeSpeechProducer;
import com.raipeng.aicall.mq.producter.material.CallRecordProducerMaterial;
import com.raipeng.aicall.mq.producter.material.CallRecordResultNoticeSpeechMaterial;
import com.raipeng.aicall.service.*;
import com.raipeng.aicall.utils.IpAddressUtil;
import com.raipeng.aicall.utils.MyExceptionUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@RestController
@RequestMapping("/smartivr")
public class SmartivrNewController {

    @Autowired
    private SmartivrNewService smartivrNewService;

    @Autowired
    private DirectCallService directCallService;

    @Autowired
    private ListenInOrTakeOverNewService listenInOrTakeOverNewService;

    @Autowired
    private CommonMonitorFeign commonMonitorFeign;

    @Autowired
    private SimpleRedisService simpleRedisService;

    static final String regex = "[0-9]{1,3}\\.";
    static Pattern pattern = Pattern.compile(regex);
    //1、监听
    //2、接管
    //3、监听和接管

    private TaskTypeEnum getTaskType(String callId){
        TaskTypeEnum typeEnum = null;
        if(callId.endsWith("listen_in")){
            typeEnum = TaskTypeEnum.LISTEN_IN;
        }else if(callId.endsWith("take_over")){
            typeEnum = TaskTypeEnum.TAKE_OVER;
        }else if(callId.endsWith("direct_call")){
            typeEnum = TaskTypeEnum.DIRECT_CALL;
        }else if(callId.endsWith("predict_call")){
            typeEnum = TaskTypeEnum.PREDICT_CALL;
        }else{
            typeEnum = TaskTypeEnum.AI;
        }
        return typeEnum;

    }

    /**
     * calleeid格式：bridge#uuid#fsIp
     * @param map
     * @return
     */
    @PostMapping("notice")
    public Map<String, Object> brNotice(@RequestBody Map<String, Object> map) {
        //跨网段桥接模式，从calleeid中透传callid，用于fs2
        if(map.containsKey("calleeid") && map.getOrDefault("calleeid","").toString().contains("bridge")){
            map.put("originCallId", map.get("callid"));
            map.put("callid", map.getOrDefault("calleeid","").toString().split("#")[1]);
            map.put("bridge_fs_ip",map.getOrDefault("calleeid","").toString().split("#")[2]);
            log.info("接收到桥接模式："+JSON.toJSONString(map));
        }
        String callId = String.valueOf(map.get("callid"));

        StringBuilder sb = new StringBuilder();
        if(callId.contains("fsasr_") || callId.contains("123530")){
            return SmartivrResponse.response("noop","noop",null,callId);
        }
        map.put("taskType",getTaskType(callId));
        //log.info("=====>notice" +JSON.toJSONString(map));
        Long startTime = System.currentTimeMillis();

        if(map.containsKey("message")&&map.get("message")!=null){
            Matcher matcher = pattern.matcher(String.valueOf(map.get("message")).toLowerCase());
            String message = matcher.replaceAll("");
            map.put("message",message);//将英文字母转成小写
        }

        if(map.get("taskType").equals(TaskTypeEnum.DIRECT_CALL)){
            Map returnMap = directCallService.dealNoticeMessage(map);
            return returnMap;
        }else if(map.get("taskType").equals(TaskTypeEnum.LISTEN_IN) || map.get("taskType").equals(TaskTypeEnum.TAKE_OVER)){
            Map returnMap = listenInOrTakeOverNewService.dealNoticeMessage(map,sb);
            Long endTime = System.currentTimeMillis();
            long subTime = endTime-startTime;
            if(subTime>100L) {//只打印50ms以上的通知日志
                log.info(callId+":各个环节耗时："+sb.toString());
            }
            return returnMap;
        }

        String notify1 = String.valueOf(map.get("notify"));
        //log.info(callId+"-->receive from smart："+JSON.toJSONString(map));
        if(callId.startsWith("L-")){
            String notify = String.valueOf(map.get("notify"));
            if(!notify.equals("enter") && !notify.equals("leave") && !notify.equals("asrprogress_notify")){
                map.put("notify",notify);
                map.put("message","好的,嗯，可以的，发吧，领取了，点击了，点开了，点了，领了，好了，行，填好了，填了，输入了");
                map.put("speakms","0");
                map.put("playstate","false");
            }
            if(notify.equals("asrprogress_notify")){
                map.put("notify",notify);
                map.put("message","我是");
                map.put("speakms","0");
                map.put("playstate","false");
            }
        }
        Map<String,Object> returnMap = null;

        returnMap = smartivrNewService.dealNoticeMessage(map);

        Long endTime = System.currentTimeMillis();
        long subTime = endTime-startTime;
        if(subTime>50L){//只打印50ms以上的通知日志
            //log.info(callId+"-->receive from smart："+JSON.toJSONString(map));
            log.info(callId+" notify1" +notify1+" notice耗时："+(endTime-startTime));
        }
        return returnMap;
    }

    @PostMapping("notice-two")
    public String cdr(HttpServletRequest request,String uuid, String cdr) {
        String ip = IpAddressUtil.getIpAddress(request);
        String ip2 = request.getRemoteAddr();
        log.info("===>cdr："+ip+":"+ip2+":"+uuid+": "+cdr);
        if(uuid.contains("-") || uuid.contains("third_user") || uuid.contains("fsasr_") || uuid.contains("123530")){
            return null;
        }
        if(uuid.startsWith("M-")){
            return null;
        }
        if(uuid.startsWith("engine_") || uuid.startsWith("C_"))
            simpleRedisService.setValueWithExpire(ApplicationConstants.HELPER_PREFIX+uuid,"hangup",60l);
        else
            LocalCache.helpCache.put(uuid,"hangup");
        if(uuid.contains("192.168")){
            //log.info("====进入192");
            Map<String,String> map = Maps.newConcurrentMap();
            JSONObject jsonObject = JSONObject.parseObject(cdr,JSONObject.class);
            JSONObject cc = (JSONObject)jsonObject.get("variables");
            String hangupCause= cc.getString("hangup_cause");
            int billSec = cc.getIntValue("billsec");

            String sipCallId = null;
            if(cc.containsKey("sip_call_id"))
                sipCallId = cc.getString("sip_call_id");

            int billmsec = cc.getIntValue("billmsec");
            int mduration = cc.getIntValue("mduration");
            int waitmsec = mduration - billmsec;

            String lastApp = "";
            if(cc.containsKey("last_app")){
                lastApp = cc.getString("last_app");
            }
            Integer whoHangup = 1;
            if(lastApp.contains("hangup")){
                whoHangup = 0;
            }

            Integer callStatus = 0;
            if(hangupCause.contains("NORMAL_CLEARING"))//正常，其他都是非正常
                callStatus = 7;

            if(hangupCause.contains("NORMAL_CLEARING") && billmsec < 100) {//小于100ms的都改成未呼通
                callStatus = 0;
            }
            Map<String,Object> dataMap = Maps.newHashMap();
            dataMap.put("callId",uuid);
            dataMap.put("cause",hangupCause);
            dataMap.put("billSec",billSec);
            dataMap.put("billmsec",billmsec);
            dataMap.put("waitmsec",waitmsec);
            dataMap.put("callStatus",callStatus);
            dataMap.put("whoHangup",whoHangup);
            dataMap.put("sipCallId",sipCallId);
            log.info("推送内容"+JSON.toJSONString(dataMap));
            commonMonitorFeign.fsEnableUpload(dataMap);
            return null;
        }
        smartivrNewService.cdr(uuid,cdr);
        return null;
    }

    @Data
    static class DATA{
        String data;
    }
}
