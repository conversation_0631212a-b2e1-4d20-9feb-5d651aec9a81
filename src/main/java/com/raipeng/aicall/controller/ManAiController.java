package com.raipeng.aicall.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.raipeng.aicall.constant.ApplicationConstants;
import com.raipeng.aicall.constant.TaskTypeEnum;
import com.raipeng.aicall.controller.qd.ManAiSmartivrResponse;
import com.raipeng.aicall.controller.response.Response;
import com.raipeng.aicall.scriptsystem.ScriptStatus;
import com.raipeng.aicall.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/man-ai")
public class ManAiController {

    @Autowired
    private CallThirdControlService callThirdControlService;

    @Autowired
    DirectCallService manAiService;

    @PostMapping("notice")
    public Map<String, Object> notice(@RequestBody Map<String, Object> map) {
        log.info("用户Asr通知："+JSON.toJSONString(map));

        /* 1 这段是调试，demo的时候可以放开，注释调下面 2
        if(map.containsKey("notify")){
            String notify = String.valueOf(map.get("notify"));//接管通知
            if(notify.equals("bridge_notify")){
                log.info("接管通知");
            }
        }
        String callId = String.valueOf(map.get("callId"));
        Map<String,Object> re = ManAiSmartivrResponse.response("playback_result","",callId);
        return re;
         */
        // 2
        Map returnMap = manAiService.dealNoticeMessage(map);
        return returnMap;
    }

    @PostMapping("notice-two")
    public String noticeTwo(String uuid,String cdr) {
        return "success";
    }

    @Autowired
    private DialogRecordService dialogRecordService;

    private TaskTypeEnum getTaskType(String callId){
        TaskTypeEnum typeEnum = null;
        if(callId.endsWith("listen_in")){
            typeEnum = TaskTypeEnum.LISTEN_IN;
        }else if(callId.endsWith("take_over")){
            typeEnum = TaskTypeEnum.TAKE_OVER;
        }else if(callId.endsWith("direct_call")){
            typeEnum = TaskTypeEnum.DIRECT_CALL;
        }else if(callId.endsWith("predict_call")){
            typeEnum = TaskTypeEnum.PREDICT_CALL;
        }else{
            typeEnum = TaskTypeEnum.AI;
        }
        return typeEnum;

    }

    @Autowired
    private SimpleRedisService simpleRedisService;

    @Autowired
    private CommonStatusService commonStatusService;

    private ScriptStatus getCurrentStatus(String callId,String from,String content){
        Long time11 = System.currentTimeMillis();
        /*
        String jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callId);
        ScriptStatus oldStatus = JSONObject.parseObject(jsonString,ScriptStatus.class);
        if(oldStatus == null){
            jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callId);
            oldStatus = JSONObject.parseObject(jsonString,ScriptStatus.class);
            if(oldStatus == null) {
                jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS + callId);
                oldStatus = JSONObject.parseObject(jsonString, ScriptStatus.class);
            }
        }

         */

        ScriptStatus oldStatus = commonStatusService.getCurrentStatus(callId);


        Long time22 = System.currentTimeMillis();
        if(oldStatus == null) {
            oldStatus = new ScriptStatus();
            oldStatus.setScriptId("88ed99a1-bca3-4206-be7a-a110ce95e56e");
            oldStatus.setVersion(0);
            oldStatus.setCallId(callId);
            log.error("====>callId："+callId+"没有获取到当前用户状态");
        }
        return oldStatus;
    }

    @PostMapping("seat-notice")
    public String seatNotice(@RequestBody Map<String, Object> map) {
        //log.info("=====>坐席" +JSON.toJSONString(map));
        String callId = String.valueOf(map.getOrDefault("check_uuid","")).substring(0,String.valueOf(map.getOrDefault("check_uuid","")).indexOf("_third_user"));
        String content = String.valueOf(map.getOrDefault("asrtext",""));
        String audioFileUrl = String.valueOf(map.getOrDefault("recordfile",""));
        Integer recordms = Integer.valueOf(String.valueOf(map.getOrDefault("recordms","")));
        map.put("taskType",getTaskType(callId));
        //接通以后，给客服播放等待音
        ScriptStatus currentStatus = getCurrentStatus(callId,"seat-notice","");
        if(map.get("taskType").equals(TaskTypeEnum.DIRECT_CALL)){
            dialogRecordService.toMQ(callId,"http://"+currentStatus.getCallStatus().getFsIp()+audioFileUrl,3,content,0,recordms, null, null,null, null, null,null,currentStatus.getTaskName(), System.currentTimeMillis(),currentStatus.getPhoneDataCache().getSpeechCallId());
        }else if (map.get("taskType").equals(TaskTypeEnum.TAKE_OVER )|| map.get("taskType").equals(TaskTypeEnum.LISTEN_IN)){
            dialogRecordService.toMQ(callId,"http://"+currentStatus.getCallStatus().getFsIp()+audioFileUrl,3,content,0,recordms, null, null,null, null, null,null,currentStatus.getTaskName(), System.currentTimeMillis(),currentStatus.getPhoneDataCache().getSpeechCallId());
        }
        //log.info("坐席Asr通知："+JSON.toJSONString(map));
        return "success";
    }

    @PostMapping("seat-notice-two/{fealCallId}")
    public String seatNotice(@RequestBody Map<String, Object> map,@PathVariable String fealCallId) {
        //log.info("=====>坐席" +JSON.toJSONString(map));
        String callId = String.valueOf(map.getOrDefault("check_uuid","")).substring(0,String.valueOf(map.getOrDefault("check_uuid","")).indexOf("_third_user"));
        callId = fealCallId;
        String content = String.valueOf(map.getOrDefault("asrtext",""));
        String audioFileUrl = String.valueOf(map.getOrDefault("recordfile",""));
        Integer recordms = Integer.valueOf(String.valueOf(map.getOrDefault("recordms","")));
        map.put("taskType",getTaskType(callId));
        //接通以后，给客服播放等待音
        ScriptStatus currentStatus = getCurrentStatus(callId,"seat-notice","");
        if(map.get("taskType").equals(TaskTypeEnum.DIRECT_CALL)){
            dialogRecordService.toMQ(callId,"http://"+currentStatus.getCallStatus().getFsIp()+audioFileUrl,3,content,0,recordms, null, null,null, null, null,null,currentStatus.getTaskName(), System.currentTimeMillis(),currentStatus.getPhoneDataCache().getSpeechCallId());
        }else if (map.get("taskType").equals(TaskTypeEnum.TAKE_OVER )|| map.get("taskType").equals(TaskTypeEnum.LISTEN_IN)){
            dialogRecordService.toMQ(callId,"http://"+currentStatus.getCallStatus().getFsIp()+audioFileUrl,3,content,0,recordms, null, null,null, null, null,null,currentStatus.getTaskName(), System.currentTimeMillis(),currentStatus.getPhoneDataCache().getSpeechCallId());
        }
        //log.info("坐席Asr通知："+JSON.toJSONString(map));
        return "success";
    }


    @PostMapping("take-over")
    public Response takeOver(@RequestBody Map<String, String> map) {
        Response response = new Response();
        String callId = map.getOrDefault("callId","");
        String fsIp = map.getOrDefault("fsIp","");
        String seat = map.getOrDefault("seat","");
        String seatIp = map.getOrDefault("seatIp","");
        String seatPort = map.getOrDefault("seatPort","");
        callThirdControlService.takeOver(callId,fsIp,seat,seatIp,seatPort);
        response.setResponseSuccess();
        return response;
    }

    @PostMapping("listen-in")
    public Response listenIn(@RequestBody Map<String, String> map) {
        Response response = new Response();
        String callId = map.getOrDefault("callId","");
        String fsIp = map.getOrDefault("fsIp","");
        String seat = map.getOrDefault("seat","");
        String seatIp = map.getOrDefault("seatIp","");
        String seatPort = map.getOrDefault("seatPort","");
        callThirdControlService.listenIn(callId,fsIp,seat,seatIp,seatPort);
        response.setResponseSuccess();
        return response;
    }

    @PostMapping("call")
    public Response call(@RequestBody Map<String, String> map) {
        Response response = new Response();
        String user = map.getOrDefault("user","");
        String fsIp = map.getOrDefault("fsIp","");
        String callId = callThirdControlService.call(fsIp,user);
        response.setResponseSuccess();
        response.setData(callId);
        return response;
    }


    @PostMapping("direct-call")
    public Response directCall(@RequestBody Map<String, String> map) {
        Response response = new Response();
        String callId = callThirdControlService.directCall(map);
        response.setResponseSuccess();
        response.setData(callId);
        return response;
    }

    @PostMapping("direct-call-hangup")
    public Response directCallHangup(@RequestBody Map<String, String> map) {
        Response response = new Response();
        log.info("===>坐席主动挂机"+JSON.toJSONString(map));
        String callId = callThirdControlService.directCallHangup(map);
        response.setResponseSuccess();
        response.setData(callId);
        return response;
    }
}
