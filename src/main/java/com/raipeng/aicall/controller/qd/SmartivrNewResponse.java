package com.raipeng.aicall.controller.qd;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.raipeng.aicall.constant.*;
import com.raipeng.aicall.scriptsystem.*;
import com.raipeng.aicall.service.DialogRecordService;
import com.raipeng.aicall.service.DingDingService;
import com.raipeng.common.entity.script.AiScript;
import com.raipeng.common.entity.script.abstractcorpus.AbstractBaseCorpus;
import com.raipeng.common.entity.script.acoustics.AiAcousticParameters;
import com.raipeng.common.entity.script.multicontent.ActiveUnitContent;
import com.raipeng.common.entity.script.multicontent.MultiContent;
import com.raipeng.common.entity.script.multicontent.UnitContent;
import com.raipeng.common.entity.script.presentence.PreInterruptCorpus;
import com.raipeng.common.enums.InterruptType;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.raipeng.common.enums.InterruptType.SUPPORT_SEMANTIC_INTERRUPT_NO_REPLY;
import static com.raipeng.common.enums.InterruptType.SUPPORT_SEMANTIC_INTERRUPT_WITH_REPLY;
import static com.raipeng.common.enums.ScriptCorpusTypeEnum.FUNC_PRIOR_QA;

@Slf4j
@Component
public class SmartivrNewResponse {

    @Autowired
    private DialogRecordService dialogRecordService;

    public Map<String,Object> response(NotifyEnum notify, ActionTypeEnum type, ScriptStatus scriptStatus, String callId,Map<String,Object> msgNotify){

        if(type.getValue().equals("exception")){
            EndResponse response = new EndResponse();
            response.getParams().setPrompt("异常.wav");
            response.setFlowdata("");
            DingDingService.dingDingSendMsgException("获取话术出现异常："+callId+"当前话术"+scriptStatus.getScriptId()+":"+scriptStatus.getScriptName()+":"+scriptStatus.getScriptLongId());
            return getMap(notify,type,response,callId);
        }

        if(type.getValue().equals("noop")){//不做任何动作
            NoopResponse response = new NoopResponse();
            return getMap(notify,ActionTypeEnum.NOOP,response,callId);
        }

        if(type.getValue().equals("wait")){
            Wait wait = new Wait();
            Wait.Params params = wait.getParams();
            params.setTimeout(scriptStatus.getCurrentCorpus().getMaxWaitingTime());//默认2秒
            return getMap(notify,ActionTypeEnum.WAIT,wait,callId);
        }

        if(type.getValue().equals("noop_hangup")){//不做任何动作
            NoopHangupResponse response = new NoopHangupResponse();
            //log.info("====>进入noop_hangup");
            return getMap(notify,ActionTypeEnum.NOOP,response,callId);
        }

        String filename = null;
        String robotWavFilePath = null;

        if(scriptStatus.getIsEnd()){//检测需要挂机，播放一段录音，然后
            LocalCache.pauseCache.invalidate(callId);
            if(scriptStatus.getCurrentMultiContent().getPreInterruptCorpusId() > 0l){//播放打断垫句
                AsrMessageResponse response = new AsrMessageResponse();
                robotWavFilePath = currentRobot(scriptStatus, robotWavFilePath,scriptStatus.getCurrentMultiContent().getPreInterruptCorpusId());
                //scriptStatus.getCurrentMultiContent().setPreContinueCorpusId(0l);
                response.getParams().setPrompt(robotWavFilePath);
                response.setFlowdata("");
                scriptStatus.setVideoStatus(1);//打断垫句中
                dialogRecordService.toMqNewForInterruptAndContinue(callId,0,scriptStatus,scriptStatus.getCurrentMultiContent().getPreInterruptCorpusId(),msgNotify);
                scriptStatus.getCurrentMultiContent().setPreInterruptCorpusId(0l);
                LocalCache.statusCache.put(callId,scriptStatus);
                return getMap(notify,type,response,callId);
            }else if(scriptStatus.getCurrentCorpus().getCorpusType() == FUNC_PRIOR_QA && scriptStatus.getCurrentMultiContent().getBlankVideo() > 0){//需要播放空音频
                scriptStatus.getCurrentMultiContent().setBlankVideo(0);//播放空音频，将空音频id置为0
                scriptStatus.getCurrentMultiContent().setBlankVideoing(true);//空音频播放中
                scriptStatus.getCurrentMultiContent().setBlankVideoCount(
                        scriptStatus.getCurrentMultiContent().getBlankVideoCount()+1
                );
                BlankVideoResponse response = new BlankVideoResponse();
                response.getParams().setPrompt(BlankVideoEnum.getEnumByName(scriptStatus.getAiAcousticParameters().getVocalStopWaitTime()).getValue());
                scriptStatus.setVideoStatus(0);//空音频播放中
                scriptStatus.getCurrentMultiContent().setIfTriggerNeedEnd(true);
                LocalCache.statusCache.put(callId,scriptStatus);
                return getMap(notify,type,response,callId);
            }else{
                EndResponse response = new EndResponse();
                robotWavFilePath = currentContent(scriptStatus, callId,robotWavFilePath);
                response.getParams().setPrompt(robotWavFilePath);
                scriptStatus.getCurrentMultiContent().setBlankVideo(0);//播放空音频，将空音频id置为0
                scriptStatus.getCurrentMultiContent().setBlankVideoing(false);//
                scriptStatus.setVideoStatus(4);
                scriptStatus.setIsCurrentCorpusPlayed(true);
                LocalCache.statusCache.put(callId, scriptStatus);
                response.setFlowdata("");
                return getMap(notify,type,response,callId);
            }

        }

        //异常挂机语料
        /*
        if(!Files.exists(Paths.get(ApplicationConstants.FS_BG_MUSIC_DIR+"/"+robotWavFilePath))){
            log.error("callId："+callId+"，"+notify+"====>server not such bg music file :"+robotWavFilePath);
            EndResponse response = new EndResponse();
            response.getParams().setPrompt("异常.wav");
            response.setFlowdata("");
            return getMap(notify,type,response,callId);
        }
        */

        if(type.getValue().equals("enter")){//播放开场白
            EnterResponse response = new EnterResponse();
            robotWavFilePath = currentContent(scriptStatus, callId,robotWavFilePath);
            response.getAfter_params().setPrompt(robotWavFilePath);
            List<ActiveUnitContent> noVideoList = scriptStatus.getCurrentMultiContent().getActiveUnitContents().stream().filter(p->!p.isPlayed()).collect(Collectors.toList());
            if(noVideoList.size() < 1)//最后一个设置等待时长
                response.getAfter_params().setWait(scriptStatus.getCurrentCorpus().getMaxWaitingTime());
            response.setFlowdata("");
            scriptStatus.setVideoStatus(4);//设置当前播放语料
            scriptStatus.setIsCurrentCorpusPlayed(true);
            LocalCache.statusCache.put(callId, scriptStatus);
            LocalCache.pauseCache.invalidate(callId);
            //设置对应的声学参数：
            AiAcousticParameters aiAcousticParameters = scriptStatus.getAiAcousticParameters();
            VoiceParam voiceParam = transferVoiceParam(aiAcousticParameters);
            BeanUtils.copyProperties(voiceParam,response.getParams());
            return getMap(notify,type,response,callId);
        }else if (type.getValue().equals("resume")||type.getValue().equals("pause")){
            //这里需要判断下是否有打断垫句，如果设置了打断垫句，直接播放打断句，否则直接播放进行暂停
            ResumeResponse response = new ResumeResponse();
            if(type.getValue().equals("pause"))
                scriptStatus.setVideoStatus(5);//5真实pause中

            if(type.getValue().equals("resume"))
                LocalCache.pauseCache.invalidate(callId);
            LocalCache.statusCache.put(callId,scriptStatus);

            response.getParams().setCommand(type.getValue());
            //log.info("恢复播放返回时的空音频播放次数"+scriptStatus.getCurrentMultiContent().getBlankVideoCount());
            return getMap(notify,type,response,callId);

        }else{//非结束
           //log.info("播放空音频之前设置的索引"+scriptStatus.getCurrentMultiContent().getUnitContentIndex());
            LocalCache.pauseCache.invalidate(callId);
            if(scriptStatus.getCurrentMultiContent().getPreInterruptCorpusId() > 0l){//播放打断垫句
                AsrMessageResponse response = new AsrMessageResponse();
                robotWavFilePath = currentRobot(scriptStatus, robotWavFilePath,scriptStatus.getCurrentMultiContent().getPreInterruptCorpusId());
                //scriptStatus.getCurrentMultiContent().setPreContinueCorpusId(0l);
                response.getParams().setPrompt(robotWavFilePath);
                response.setFlowdata("");
                scriptStatus.setVideoStatus(1);//打断垫句中
                dialogRecordService.toMqNewForInterruptAndContinue(callId,0,scriptStatus,scriptStatus.getCurrentMultiContent().getPreInterruptCorpusId(),msgNotify);
                scriptStatus.getCurrentMultiContent().setPreInterruptCorpusId(0l);
                LocalCache.statusCache.put(callId,scriptStatus);
                return getMap(notify,type,response,callId);
            }else if(scriptStatus.getCurrentMultiContent().getBlankVideo() > 0){//需要播放空音频
                scriptStatus.getCurrentMultiContent().setBlankVideo(0);//播放空音频，将空音频id置为0
                scriptStatus.getCurrentMultiContent().setBlankVideoing(true);//空音频播放中
                scriptStatus.getCurrentMultiContent().setBlankVideoCount(
                        scriptStatus.getCurrentMultiContent().getBlankVideoCount()+1
                );
                BlankVideoResponse response = new BlankVideoResponse();
                //log.info("设置的参数："+JSON.toJSONString(scriptStatus.getAiAcousticParameters()));
                response.getParams().setPrompt(BlankVideoEnum.getEnumByName(scriptStatus.getAiAcousticParameters().getVocalStopWaitTime()).getValue());
                //log.info("当前状态播音状态："+scriptStatus.getVideoStatus().intValue());
                if(scriptStatus.getVideoStatus().intValue() == 4 ||
                        (scriptStatus.getVideoStatus().intValue() == 1 && scriptStatus.getCurrentMultiContent().isInPreInterruptMidstCopus())
                        ||
                        scriptStatus.getVideoStatus().intValue() == 5 ){//指针回拨
                    //scriptStatus.getCurrentMultiContent().setUnitContentIndex(scriptStatus.getCurrentMultiContent().getUnitContentIndex()-1);
                    scriptStatus.getCurrentMultiContent().setIsNeedIndexReturn(true);
                }
                scriptStatus.setVideoStatus(0);//空音频播放中
                //如果是打断空音频，需要将索引回拨
                scriptStatus.getCurrentMultiContent().setInPreInterruptMidstCopus(true);
                LocalCache.statusCache.put(callId,scriptStatus);
                //log.info("播放空音频之后设置的索引"+scriptStatus.getCurrentMultiContent().getUnitContentIndex());
                return getMap(notify,type,response,callId);
            }else if(scriptStatus.getCurrentMultiContent().getPreContinueCorpusId() != null
                    &&  scriptStatus.getCurrentMultiContent().getPreContinueCorpusId() > 0){//播放续播垫句
                AsrMessageResponse response = new AsrMessageResponse();
                robotWavFilePath = currentRobot(scriptStatus, robotWavFilePath,scriptStatus.getCurrentMultiContent().getPreContinueCorpusId());
                response.getParams().setPrompt(robotWavFilePath);
                response.setFlowdata("");
                scriptStatus.setVideoStatus(2);//续播垫句中
                dialogRecordService.toMqNewForInterruptAndContinue(callId,0,scriptStatus,scriptStatus.getCurrentMultiContent().getPreContinueCorpusId(),msgNotify);
                scriptStatus.getCurrentMultiContent().setPreContinueCorpusId(0l);
                LocalCache.statusCache.put(callId,scriptStatus);
                return getMap(notify,type,response,callId);

            }else if(scriptStatus.getCurrentMultiContent().isReturn() && scriptStatus.getCurrentMultiContent().getPreContinueCorpusIdForReturn() != null
                    &&  scriptStatus.getCurrentMultiContent().getPreContinueCorpusIdForReturn() > 0){//播放返回续播垫句
                AsrMessageResponse response = new AsrMessageResponse();
                robotWavFilePath = currentRobot(scriptStatus, robotWavFilePath,scriptStatus.getCurrentMultiContent().getPreContinueCorpusIdForReturn());
                response.getParams().setPrompt(robotWavFilePath);
                response.setFlowdata("");
                scriptStatus.setVideoStatus(3);//返回续播垫句中
                dialogRecordService.toMqNewForInterruptAndContinue(callId,0,scriptStatus,scriptStatus.getCurrentMultiContent().getPreContinueCorpusIdForReturn(),msgNotify);
                scriptStatus.getCurrentMultiContent().setPreContinueCorpusIdForReturn(0l);//返回续播垫举设置为0
                LocalCache.statusCache.put(callId,scriptStatus);
                return getMap(notify,type,response,callId);
            }else{
                AsrMessageResponse response = new AsrMessageResponse();
                //log.info("当前语料块大小"+ JSON.toJSONString(scriptStatus.getCurrentMultiContent().getActiveUnitContents()));
                //log.info("当前语料块索引"+scriptStatus.getCurrentMultiContent().getUnitContentIndex());
                if(scriptStatus.getCurrentMultiContent().getIsNeedIndexReturn()){//如果需要指针回拨
                    scriptStatus.getCurrentMultiContent().setUnitContentIndex(scriptStatus.getCurrentMultiContent().getUnitContentIndex()-1);
                    scriptStatus.getCurrentMultiContent().setIsNeedIndexReturn(false);
                }
                int currentIndex = scriptStatus.getCurrentMultiContent().getUnitContentIndex();
                /*
                if(VideoStatusEnum.getNotifyByValue(scriptStatus.getVideoStatus()) == VideoStatusEnum.STATUS_4 ||
                        VideoStatusEnum.getNotifyByValue(scriptStatus.getVideoStatus()) == VideoStatusEnum.STATUS_1||
                        VideoStatusEnum.getNotifyByValue(scriptStatus.getVideoStatus()) == VideoStatusEnum.STATUS_5
                ){
                    currentIndex = scriptStatus.getCurrentMultiContent().getUnitContentIndex() - 1;
                }

                 */
                ActiveUnitContent activeUnitContent = scriptStatus.getCurrentMultiContent().getActiveUnitContents().get(
                        currentIndex);

                //log.info("获取当前上下文之前"+scriptStatus.getCurrentMultiContent().getUnitContentIndex());
                robotWavFilePath = currentContent(scriptStatus, callId,robotWavFilePath);
                //log.info("获取当前上下文之后"+scriptStatus.getCurrentMultiContent().getUnitContentIndex());
                response.getParams().setPrompt(robotWavFilePath);
                List<ActiveUnitContent> noVideoList = scriptStatus.getCurrentMultiContent().getActiveUnitContents().stream().filter(p->!p.isPlayed()).collect(Collectors.toList());
                //log.info("当前语料的音频个数"+noVideoList.size());
                if(noVideoList.size() < 1) {//最后一个设置等待时长
                    //log.info("当前域名名称"+scriptStatus.getCurrentCorpus().getName()+"当前语料的等待时长"+scriptStatus.getCurrentCorpus().getMaxWaitingTime());
                    response.getParams().setWait(scriptStatus.getCurrentCorpus().getMaxWaitingTime());
                    scriptStatus.getCurrentMultiContent().setFinishedPlaying(true);
                }
                response.setFlowdata("");
                scriptStatus.setVideoStatus(4);//
                activeUnitContent.setPlayedCount(activeUnitContent.getPlayedCount() + 1);
                scriptStatus.setIsCurrentCorpusPlayed(true);
                LocalCache.statusCache.put(callId,scriptStatus);
                InterruptType interruptType = activeUnitContent.getUnitContent().getInterruptType();//
                if(interruptType.equals(InterruptType.SUPPORT_SOUND_INTERRUPT_NO_REPLY)||interruptType.equals(InterruptType.SUPPORT_SOUND_INTERRUPT_WITH_REPLY)){
                    //response.getParams().setAuto_intercept_number(3L);//默认最多打断次数
                    response.getParams().setAllow_interrupt(200);
                }else if(interruptType.equals(SUPPORT_SEMANTIC_INTERRUPT_NO_REPLY)||interruptType.equals(SUPPORT_SEMANTIC_INTERRUPT_WITH_REPLY)){
                    //response.getParams().setConsole_intercept_number(3L);
                    response.getParams().setAllow_interrupt(-1);
                }else{
                    //response.getParams().setAuto_intercept_number(0L);//默认最多打断次数
                    //response.getParams().setConsole_intercept_number(3L);
                    response.getParams().setAllow_interrupt(-1);
                }
                //log.info("返回的时的索引"+scriptStatus.getCurrentMultiContent().getUnitContentIndex());
                return getMap(notify,type,response,callId);
            }

        }
    }

    private static String currentRobot(ScriptStatus scriptStatus, String robotWavFilePath,Long id) {
        String filename;
        AiScript script = TriggerUtils.getAiScript(scriptStatus);
        AbstractBaseCorpus interruptCorpus = script.getCorpusById(id);
        UnitContent unitContent = script.getUnitContentById(script.getMultiContentById(interruptCorpus.getMultiContentIds().get(0)).getUnitContentIds().get(0));
        String audioPath = unitContent.getAudioPath();
        if(audioPath!=null){
            filename = audioPath.split("/")[audioPath.split("/").length-1];
            robotWavFilePath = scriptStatus.getScriptId()+"/"+filename;
        }
        return robotWavFilePath;
    }

    public static AbstractBaseCorpus currentRobotUnitContent(ScriptStatus scriptStatus,Long id) {
        AiScript script = TriggerUtils.getAiScript(scriptStatus);
        AbstractBaseCorpus interruptCorpus = script.getCorpusById(id);
        return interruptCorpus;
    }

    private String currentContent(ScriptStatus scriptStatus, String callId,String robotWavFilePath) {
        String filename;
        ActiveUnitContent activeUnitContent = scriptStatus.getCurrentMultiContent().getActiveUnitContents().get(
                scriptStatus.getCurrentMultiContent().getUnitContentIndex()
        );
        scriptStatus.getCurrentMultiContent().setBlankVideo(0);
        scriptStatus.getCurrentMultiContent().setBlankVideoing(false);
        scriptStatus.getCurrentMultiContent().setBlankVideoCount(0);
        activeUnitContent.setPlayed(true);
        activeUnitContent.setPlayFinished(false);
        scriptStatus.getCurrentMultiContent().setUnitContentIndex(scriptStatus.getCurrentMultiContent().getUnitContentIndex()+1);//将指针指向下一个未播放音频

        //使用时设置可允许打断时间
        InterruptType interruptType = activeUnitContent.getUnitContent().getInterruptType();
        if(interruptType !=null && (interruptType.equals(SUPPORT_SEMANTIC_INTERRUPT_NO_REPLY)
                ||interruptType.equals(SUPPORT_SEMANTIC_INTERRUPT_WITH_REPLY))) {
            scriptStatus.setCurrentInterruptContent(InterruptContent.builder()
                    .allowedInterruptTime(activeUnitContent.getUnitContent().getAllowedInterruptTime())
                    .interruptEndTime(activeUnitContent.getUnitContent().getAllowedInterruptTime()+System.currentTimeMillis()/1000)
                    .build());
        }else{
            scriptStatus.setCurrentInterruptContent(null);
        }

        if(activeUnitContent.getUnitContent().getAudioPath() != null){
            filename = activeUnitContent.getUnitContent().getAudioPath().split("/")[activeUnitContent.getUnitContent().getAudioPath().split("/").length-1];
            robotWavFilePath = scriptStatus.getScriptId()+"/"+filename;

            scriptStatus.getCallStatus().getDialogContentsCache().add(DialogContent.builder()
                    .content(activeUnitContent.getUnitContent().getContent())
                    .fromNumber("0").createTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).build());
        }

        LocalCache.statusCache.put(callId, scriptStatus);
        return robotWavFilePath;
    }

    private static VoiceParam transferVoiceParam(AiAcousticParameters aiAcousticParameters){
        VoiceParam voiceParam = new VoiceParam();
        voiceParam.setThreshold(aiAcousticParameters.getLoudness());
        voiceParam.setCheck_frame_time_width(aiAcousticParameters.getSampleTime());
        voiceParam.setStart_talking_ratio(aiAcousticParameters.getStartTalkingSensitivity());
        voiceParam.setStop_talking_ratio(aiAcousticParameters.getStopTalkingSensitivity());
        voiceParam.setMin_pause_ms(aiAcousticParameters.getPauseTime());
        voiceParam.setMax_pause_ms(aiAcousticParameters.getSentencePauseTime());
        voiceParam.setMax_speak_ms(aiAcousticParameters.getMaxSentenceTime());
        voiceParam.setMin_speak_ms(90);
        return voiceParam;
    }


    /**
     * 应答回复
     */
    @Data
    public static class EnterResponse implements Serializable{
        private String action = "start_asr";
        private String flowdata;
        private Params params = new Params();
        private String after_action = "playback";
        private Boolean after_ignore_error = true;
        private After_params after_params = new After_params();


        //默认参数，这里暂时以对方提供的为准
        @Data
        public static class Params implements Serializable {
            private Integer min_speak_ms = 90;
            private Integer max_speak_ms = 10000;
            private Integer min_pause_ms = 300;
            private Integer max_pause_ms = 600;
            private Integer threshold = 200;
            private Integer pause_play_ms = 0;//暂停播放时间
            //private Integer start_talking_ratio = 30;
            //private Integer stop_talking_ratio = 70;
            //private Integer check_frame_time_width = 300;
            //private Long console_intercept_number = 3L;//TODO 测试控制打断
        }

        @Data
        public static class After_params implements Serializable{
            private String prompt;
            private Long wait;
            private Integer retry = 0;//重播次数
        }
    }


    /**
     * 汇总回复
     */
    @Data
    public static class AsrMessageResponse implements Serializable {
        private String action = "playback";
        private Boolean suspend_asr = false;
        private String flowdata;
        private Params params = new Params();

        @Data
        public static class Params implements Serializable{
            private String prompt = "";
            private Long wait = 0l;
            private Integer retry = 0;
            private Integer allow_interrupt = -1;
        }


    }


    /**
     * 结束
     */
    @Data
    public static class EndResponse implements Serializable {
        private String action = "playback";
        private String flowdata;
        private Boolean  suspend_asr = false;
        private Params params = new Params();

        @Data
        public static class Params implements Serializable{
            private String prompt = "";
        }

        //这里必须传字段，可以为空
        private String after_action = "hangup";
        private Boolean after_ignore_error = true;
        private After_params after_params = new After_params();
        @Data
        public static class After_params implements Serializable{
            private Integer cause = 0;
            private String usermsg ="";
        }

    }

    @Data
    public static class NoopResponse implements Serializable {
        private String action = "noop";//不做任何操作
        private Params params = new Params();

        @Data
        public static class Params implements Serializable{
            private String usermsg = "" ;
        }
    }

    @Data
    public static class NoopHangupResponse implements Serializable {
        private String action = "noop";//不做任何操作
        private Params params = new Params();

        @Data
        public static class Params implements Serializable{
            private String usermsg = "" ;
        }

        //这里必须传字段，可以为空
        private String after_action = "hangup";
        private Boolean after_ignore_error = true;
        private EndResponse.After_params after_params = new EndResponse.After_params();
        @Data
        public static class After_params implements Serializable{
            private Integer cause = 0;
            private String usermsg ="";
        }

    }

    @Data
    public static class ResumeResponse implements Serializable {
        private String action = "console_playback";//不做任何操作
        private String flowdata;
        private Params params = new Params();

        @Data
        public static class Params implements Serializable{
            private String command = "resume" ;
        }
    }

    @Data
    public static class Wait implements Serializable {
        private String action = "wait";//不做任何操作
        private String flowdata;
        private Params params = new Params();

        @Data
        public static class Params implements Serializable{
            private Long timeout = 1000l ;
        }
    }


    private static Map<String,Object> getMap(NotifyEnum notify,ActionTypeEnum type,Object object,String callId){
        String returnString = JSON.toJSONString(object);
        //log.info(callId+"-"+notify.getValue()+"->web-->qd===>:"+ returnString+","+type);
        Map<String,Object> map = JSONObject.parseObject(returnString,Map.class);
        return map;
    }

    @Data
    public static class BlankVideoResponse implements Serializable{
        private String action = "playback";
        private Boolean suspend_asr = false;
        private String flowdata;
        private Params params = new Params();

        @Data
        public static class Params implements Serializable{
            private String prompt = "";
            private Long wait = 0l;
            private Integer retry = 0;
            private Integer allow_interrupt = 200;//空音频允许发声打断
        }
    }

}
