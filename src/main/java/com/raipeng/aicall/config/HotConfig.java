package com.raipeng.aicall.config;


import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Component
@RefreshScope
public class HotConfig {
    @Value("${is.train.phone.need.event:false}")
    private boolean startPreMinute;


    @Value("${global.operator.null.mapping:ctcc}")
    private String globalOperatorNullMapping;

    public String getGlobalOperatorNullMapping() {
        return globalOperatorNullMapping;
    }


    public boolean isTrainPhoneNeedEvent() {
        return startPreMinute;
    }
}
