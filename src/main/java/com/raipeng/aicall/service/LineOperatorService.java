package com.raipeng.aicall.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.raipeng.aicall.feign.LineOperatorFeign;
import com.raipeng.aicall.model.vo.LineOperatorData;
import com.raipeng.common.util.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * 线路运营商服务
 */
@Slf4j
@Service
public class LineOperatorService {

    /**
     * 线路号码到运营商的缓存映射
     */
    private final Map<String, LineOperatorData> lineOperatorCache = Maps.newConcurrentMap();

    @Autowired
    private LineOperatorFeign lineOperatorFeign;

    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    /**
     * 初始化定时任务
     */
    @PostConstruct
    public void init() {
        // 启动时立即执行一次
        pullAllLineOperatorData();
        // 每10分钟执行一次全量拉取
        scheduler.scheduleAtFixedRate(this::pullAllLineOperatorData, 1, 1, TimeUnit.MINUTES);
        log.info("线路运营商服务初始化完成，定时任务已启动");
    }

    /**
     * 定时拉取所有线路运营商数据
     */
    public void pullAllLineOperatorData() {
        try {
            // 只在营销时间段内更新缓存
            if (!(LocalDateTime.now().getHour() >= 8 && LocalDateTime.now().getHour() <= 22)) {
                log.info("===>非营销时间段，跳过线路运营商数据拉取");
                return;
            }

            List<LineOperatorData> operatorDataList = lineOperatorFeign.getAllLineOperatorData();
            if (CollectionUtil.isNotEmpty(operatorDataList)) {
                operatorDataList.forEach(new Consumer<LineOperatorData>() {
                    @Override
                    public void accept(LineOperatorData operatorData) {
                        // 更新线路号码缓存
                        if (StringUtils.hasText(operatorData.getLineNumber())) {
                            lineOperatorCache.compute(operatorData.getLineNumber(), (k, v) -> operatorData);
                        }
                    }
                });
                log.info("===================》拉取线路运营商数据完成，共{}条，{}", operatorDataList.size(),JSON.toJSONString(operatorDataList));
            } else {
                log.warn("拉取到的线路运营商数据为空");
            }
        } catch (Exception e) {
            log.error("拉取线路运营商数据失败", e);
            // 可以集成钉钉通知
            // DingDingService.dingDingSendMsgException("拉取线路运营商数据失败" + e.getMessage());
        }
    }


    /**
     * 根据线路号码获取运营商信息
     * @param lineNumber 线路号码
     * @return 运营商名称
     */
    public String getOperatorByLineNumber(String lineNumber) {
        if (!StringUtils.hasText(lineNumber)) {
            return null;
        }

        LineOperatorData operatorData = lineOperatorCache.computeIfAbsent(lineNumber, new Function<String, LineOperatorData>() {
            @Override
            public LineOperatorData apply(String line) {
                return getSingleLineOperatorByLineNumber(line);
            }
        });
        log.info("获取的operatorData"+ JSON.toJSONString(operatorData));
        return operatorData != null ? operatorData.getBroadbandOperator() : null;
    }

    /**
     * 从远程服务获取单个线路号码的运营商数据
     * @param lineNumber 线路号码
     * @return 线路运营商数据
     */
    private LineOperatorData getSingleLineOperatorByLineNumber(String lineNumber) {
        LineOperatorData operatorData = null;
        try {
            operatorData = lineOperatorFeign.getLineOperatorByLineNumber(lineNumber);
        } catch (Exception e) {
            // 重试机制
            try {
                operatorData = lineOperatorFeign.getLineOperatorByLineNumber(lineNumber);
            } catch (Exception e1) {
                try {
                    operatorData = lineOperatorFeign.getLineOperatorByLineNumber(lineNumber);
                } catch (Exception e2) {
                    log.error("获取线路{}的运营商数据失败，请排查！", lineNumber, e2);
                    // DingDingService.dingDingSendMsgException("获取线路运营商数据失败，请排查！" + e2.getMessage());
                }
            }
        }
        return operatorData;
    }

    /**
     * 获取缓存统计信息
     * @return 缓存统计信息
     */
    public String getCacheStats() {
        return String.format("IP缓存数量: %d, 线路缓存数量: %d",
                lineOperatorCache.size());
    }
}
