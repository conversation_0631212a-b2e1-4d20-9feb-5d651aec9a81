package com.raipeng.aicall.service;

import com.raipeng.aicall.config.HotConfig;
import com.raipeng.aicall.constant.KmlAddressEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
/**
 * KML选择服务
 * 根据线路运营商信息和配置规则选择合适的KML地址
 */
@Slf4j
@Service
@RefreshScope
public class KmlSelectionService {

    @Autowired
    private HotConfig hotConfig;

    @Autowired
    private LineOperatorService lineOperatorService;

    /**
     * 根据线路信息选择KML字段名
     * @param lineNumber 线路号码
     * @return KML字段名（如 "kml_address", "kml_address_cmcc", "kml_address_cucc"）
     */
    public String selectKmlAddress(String lineNumber) {
        try {
            // 检查配置版本是否有变化
            // 获取线路的运营商信息
            String broadbandOperator = getLineOperator(lineNumber);
            // ******* 不存在，查看是否有全局配置
            String globalKml = getGlobalOperatorKml(broadbandOperator);
            if (StringUtils.hasText(globalKml)) {
                // *******.1 有，直接返回具体地址（保持兼容性）
                log.info("线路{}运营商{}使用全局配置KML: {}", lineNumber, broadbandOperator, globalKml);
                return globalKml;
            } else {
                // *******.2 没有，返回默认运营商字段名
                String fieldName = getGlobalOperatorKml(hotConfig.getGlobalOperatorNullMapping());
                log.info("线路{}运营商{}使用默认运营商KML字段: {}", lineNumber, broadbandOperator, fieldName);
                return fieldName;
            }

        } catch (Exception e) {
            e.printStackTrace();
            log.error("选择KML字段异常，线路: {}, 使用默认KML字段", lineNumber, e);
            return "kmlAddress";
        }
    }

    /**
     * 获取线路的运营商信息
     */
    private String getLineOperator(String lineNumber) {
        try {
            // 优先从新的LineOperatorService获取运营商信息
            String operator = lineOperatorService.getOperatorByLineNumber(lineNumber);
            if (StringUtils.hasText(operator)) {
                return operator;
            }

        } catch (Exception e) {
            log.error("获取线路{}运营商信息失败", lineNumber, e);
        }
        return null;
    }

    /**
     * 获取全局运营商KML配置
     */
    private String getGlobalOperatorKml(String broadbandOperator) {
        KmlAddressEnum kmlAddressEnum = KmlAddressEnum.getKmlAddressEnum(broadbandOperator);
        if(kmlAddressEnum != null) {
            return kmlAddressEnum.getValue();
        }else{
            return null;
        }
    }



    /**
     * 从fsConfig获取KML地址，如果指定字段不存在则基于kml_address生成
     * @param fsConfig FS配置对象
     * @param fieldName 字段名（如 "kmlAddress","kmlAddressCmcc", "kmlAddressCucc"）
     * @return KML地址
     */
    public String getKmlAddressFromFsConfig(Object fsConfig, String fieldName) {
        if (fsConfig == null || !StringUtils.hasText(fieldName)) {
            return null;
        }

        try {

            // 使用反射获取字段值
            Class<?> fsConfigClass = fsConfig.getClass();

            // 首先尝试获取指定字段的值
            String kmlAddress = getFieldValue(fsConfig, fsConfigClass, fieldName);
            if (StringUtils.hasText(kmlAddress)) {
                log.debug("从fsConfig获取{}字段值: {}", fieldName, kmlAddress);
                return kmlAddress;
            }
            // 如果都获取不到，返回null
            log.warn("无法从fsConfig获取{}字段值", fieldName);
            return null;

        } catch (Exception e) {
            log.error("从fsConfig获取KML地址异常，字段: {}", fieldName, e);
            return null;
        }
    }

    /**
     * 使用反射获取字段值
     */
    private String getFieldValue(Object obj, Class<?> clazz, String fieldName) {
        try {
            // 尝试通过getter方法获取

            String getterName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
            try {
                Method getter = clazz.getMethod(getterName);
                Object value = getter.invoke(obj);
                return value != null ? value.toString() : null;
            } catch (NoSuchMethodException e) {
                // 如果没有getter方法，尝试直接访问字段
                Field field = clazz.getDeclaredField(fieldName);
                field.setAccessible(true);
                Object value = field.get(obj);
                return value != null ? value.toString() : null;
            }
        } catch (Exception e) {
            log.debug("获取字段{}值失败: {}", fieldName, e.getMessage());
            return null;
        }
    }

}
