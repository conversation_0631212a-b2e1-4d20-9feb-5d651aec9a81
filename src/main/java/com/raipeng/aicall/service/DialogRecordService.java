package com.raipeng.aicall.service;
import com.alibaba.fastjson.JSON;
import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.Channel;
import com.raipeng.aicall.constant.MqTypeEnum;
import com.raipeng.aicall.controller.qd.SmartivrNewResponse;
import com.raipeng.aicall.model.DialogRecord;
import com.raipeng.aicall.mq.DialogRecordPushQueue;
import com.raipeng.aicall.mq.producter.DialoglRecordProducer;
import com.raipeng.aicall.mq.producter.TrainPhoneDialogProducer;
import com.raipeng.aicall.repository.DialogRecordRepository;
import com.raipeng.aicall.scriptsystem.ScriptStatus;
import com.raipeng.aicall.scriptsystem.TriggerUtils;
import com.raipeng.aicall.utils.SnowFlakeUtil;
import com.raipeng.common.entity.script.AiScript;
import com.raipeng.common.entity.script.abstractcorpus.AbstractBaseCorpus;
import com.raipeng.common.entity.script.multicontent.ActiveUnitContent;
import com.raipeng.common.entity.script.multicontent.UnitContent;
import com.raipeng.common.enums.InterruptType;
import com.raipeng.common.model.dto.DialogRecordDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.persistence.Column;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.raipeng.aicall.constant.RabbitMqConstants.*;

@Slf4j
@Service
@RefreshScope
public class DialogRecordService {
    public static final Pattern pattern = Pattern.compile("\\d+(?=\\.wav)");

    @Autowired
    private DialoglRecordProducer dialoglRecordProducer;

    @Autowired
    private DialogRecordRepository dialogRecordRepository;

    @Value("${train.phone.dialog.notice.url:http://**************:8201/market/AiSpeech/trainAccount/syncSentences}")
    private String trainPhoneDialogNoticeUrl;

    @Autowired
    @Qualifier("mainRabbitTemplate")
    private RabbitTemplate mainRabbitTemplate;

    @Autowired
    DialogRecordPushQueue dialogRecordPushQueue;

    @Autowired
    private CommonStatusService commonStatusService;


    @Data
    public static class TrainPhoneDialogRecord{
        private LocalDate date;
        private LocalDateTime dialogTime;
        private String callId;
        private String audioFileUrl;
        private String content;
        private String unitContent;
        private String dialogCorpusTempUuid;
        private Integer type;
        private Integer recordms;
        private Integer speakms;
        private String hitSemantic;
        private String hitPhrase;
        private String hitIntention;
        private String hitBranch;
        private String corpusName;
        private Long corpusId;
        private InterruptType interruptType;
        private String speechCallId;
    }

    public void dialogPush() {
        //log.info("每10s归还一次线路");
        dialogRecordPushQueue.subMit();
    }



    public void toMqNew(String callId , Integer type , ScriptStatus nextStatus
            , ActiveUnitContent activeUnitContent, Map<String,Object> msgNotify, String sayContent){
        String corpusName =  nextStatus.getCurrentCorpus().getName();
        //log.info("当前入数据库语料名称"+corpusName+": "+type);
        Long corpusId =  nextStatus.getCurrentCorpus().getId();
        InterruptType interruptType = null;
        if(activeUnitContent != null)
            interruptType = activeUnitContent.getUnitContent().getInterruptType();
        String hitIntention =  nextStatus.getCurrentIntentionType();
        String speechCallId = nextStatus.getPhoneDataCache().getSpeechCallId();
        String taskName = nextStatus.getPhoneDataCache().getTaskName();
        String hitPhrase = null;
        String hitSemantic = null;
        String hitBranch = null;
        Long timeStamp =  System.currentTimeMillis();
        DialogRecord dialog = new DialogRecord();
        Integer recordms = 0;
        Integer speakms = 0;
        String audioFileUrl = null;
        String content = sayContent;
        if(type == 0){
            audioFileUrl = commonStatusService.genAudioFileUrl(nextStatus,activeUnitContent.getUnitContent(),msgNotify);
            StringBuilder tempAllContent = new StringBuilder();
            for (ActiveUnitContent unitContent : nextStatus.getCurrentMultiContent().getActiveUnitContents()) {
                tempAllContent.append(unitContent.getUnitContent().getContent()).append("\n");
            }
            content = tempAllContent.toString();
            dialog.setDialogCorpusTempUuid(nextStatus.getDialogCorpusTempUUID());
            dialog.setUnitContent(activeUnitContent.getUnitContent().getContent());
            dialog.setHitBranch(nextStatus.getHitBranch());
            dialog.setHitPhrase(nextStatus.getHitExtraPhrase());
        }else if(type == 1){
            String recordfile = String.valueOf(msgNotify.get("recordfile"));
            if(msgNotify.containsKey("bridge_fs_ip")){
                audioFileUrl = "http://"+msgNotify.getOrDefault("bridge_fs_ip","").toString().replace(":5060","")+recordfile;
            }else{
                audioFileUrl = "http://"+nextStatus.getCallStatus().getFsIp()+recordfile;
            }
            recordms = Integer.valueOf(String.valueOf(msgNotify.get("recordms")));
        }else if(type == 2){
            if(activeUnitContent != null){
                Set<Long> hitSemanticIds = commonStatusService.getHitSemanticId(nextStatus, sayContent);
                hitSemantic = hitSemanticIds.stream().map(String::valueOf).collect(Collectors.joining(","));
            }
            String recordfile = String.valueOf(msgNotify.get("recordfile"));
            if(msgNotify.containsKey("bridge_fs_ip")){
                audioFileUrl = "http://"+msgNotify.getOrDefault("bridge_fs_ip","").toString().replace(":5060","")+recordfile;
            }else{
                audioFileUrl = "http://"+nextStatus.getCallStatus().getFsIp()+recordfile;
            }
            speakms = Integer.valueOf(String.valueOf(msgNotify.get("speakms")));
        }

        dialog.setDialogTime(LocalDateTime.now());
        dialog.setDate(LocalDate.now());
        dialog.setCallId(callId);
        dialog.setAudioFileUrl(audioFileUrl);
        dialog.setType(type);
        dialog.setContent(content);
        dialog.setSpeakms(speakms);
        dialog.setRecordms(recordms);
        dialog.setId(SnowFlakeUtil.getSnowFlakeIdTwo());
        dialog.setHitSemantic(hitSemantic);
        dialog.setHitIntention(hitIntention);
        dialog.setCorpusName(corpusName);
        dialog.setCorpusId(corpusId);
        dialog.setInterruptType(interruptType);
        dialog.setTimeStamp(timeStamp);
        //log.info("对话记录入MQ"+ JSON.toJSONString(dialog));
        try{
            //dialoglRecordProducer.send(dialog);
            //toDialogMq(dialog,speechCallId);
            dialog.setPlatform(speechCallId.split("_")[0]);
            dialogRecordPushQueue.addData(dialog);
            trainPhoneNotice(taskName, dialog);
        }catch (Exception e){
            e.printStackTrace();
            log.error("对话记录推送MQ失败：推送记录"+ JSON.toJSONString(dialog));
        }
    }


    public void toMqNewForInterruptAndContinue(String callId , Integer type , ScriptStatus nextStatus
            , Long id,Map<String,Object> msgNotify){
        //log.info("对话记录入MQ"+ JSON.toJSONString(dialog));
        DialogRecord dialog = new DialogRecord();
        try{
            String corpusName = null;
            Long corpusId = null;
            InterruptType interruptType =  InterruptType.CAN_NOT_BE_INTERRUPTED;
            String hitIntention =   nextStatus.getCurrentIntentionType();
            String speechCallId = nextStatus.getPhoneDataCache().getSpeechCallId();
            String taskName = nextStatus.getPhoneDataCache().getTaskName();
            String hitPhrase = null;
            String hitSemantic = null;
            String hitBranch = null;
            Long timeStamp =  System.currentTimeMillis();
            Integer recordms = 0;
            Integer speakms = 0;

            AiScript script = TriggerUtils.getAiScript(nextStatus);
            AbstractBaseCorpus interruptCorpus = script.getCorpusById(id);
            UnitContent unitContent = script.getUnitContentById(script.getMultiContentById(interruptCorpus.getMultiContentIds().get(0)).getUnitContentIds().get(0));
            corpusName = interruptCorpus.getName();
            corpusId = interruptCorpus.getId();

            String content = unitContent.getContent();
            String audioFileUrl = commonStatusService.genAudioFileUrl(nextStatus,unitContent,msgNotify);
            dialog.setDialogCorpusTempUuid(nextStatus.getDialogCorpusTempUUID());
            dialog.setUnitContent(content);
            dialog.setDialogTime(LocalDateTime.now());
            dialog.setDate(LocalDate.now());
            dialog.setCallId(callId);
            dialog.setAudioFileUrl(audioFileUrl);
            dialog.setType(type);
            dialog.setContent(content);
            dialog.setSpeakms(speakms);
            dialog.setRecordms(recordms);
            dialog.setId(SnowFlakeUtil.getSnowFlakeIdTwo());
            dialog.setHitPhrase(hitPhrase);
            dialog.setHitSemantic(hitSemantic);
            dialog.setHitBranch(hitBranch);
            dialog.setHitIntention(hitIntention);
            dialog.setCorpusName(corpusName);
            dialog.setCorpusId(corpusId);
            dialog.setInterruptType(interruptType);
            dialog.setTimeStamp(timeStamp);
            //dialoglRecordProducer.send(dialog);
            //toDialogMq(dialog,speechCallId);
            dialog.setPlatform(speechCallId.split("_")[0]);
            dialogRecordPushQueue.addData(dialog);
            trainPhoneNotice(taskName, dialog);
        }catch (Exception e){
            e.printStackTrace();
            log.error("对话记录推送MQ失败：推送记录"+ JSON.toJSONString(dialog));
        }
    }


    public void toMQ(String callId, String audioFileUrl, Integer type, String content, Integer speakms, Integer recordms,
                     String corpusName, InterruptType interruptType, String hitIntention,
                     String hitPhrase, String hitSemantic, String hitBranch,String taskName, Long timeStamp,String speechCallId){

        DialogRecord dialog = new DialogRecord();
        dialog.setDialogTime(LocalDateTime.now());
        dialog.setDate(LocalDate.now());
        dialog.setCallId(callId);
        dialog.setAudioFileUrl(audioFileUrl);
        dialog.setType(type);
        dialog.setContent(content);
        dialog.setSpeakms(speakms);
        dialog.setRecordms(recordms);
        dialog.setId(SnowFlakeUtil.getSnowFlakeIdTwo());
        dialog.setHitPhrase(hitPhrase);
        dialog.setHitSemantic(hitSemantic);
        dialog.setHitBranch(hitBranch);
        dialog.setHitIntention(hitIntention);
        dialog.setCorpusName(corpusName);
        dialog.setInterruptType(interruptType);
        dialog.setTimeStamp(timeStamp);
        //log.info("对话记录入MQ"+ JSON.toJSONString(dialog));
        try{
            //dialoglRecordProducer.send(dialog);
            //toDialogMq(dialog,speechCallId);
            dialog.setPlatform(speechCallId.split("_")[0]);
            dialogRecordPushQueue.addData(dialog);
            trainPhoneNotice(taskName, dialog);
        }catch (Exception e){
            e.printStackTrace();
            log.error("对话记录推送MQ失败：推送记录"+ JSON.toJSONString(dialog));
        }
    }

    private void toDialogMq(DialogRecord data,String speechCallId){
        try {
            //这里是获取队列的一条消息
            //参数二：表示此时即使消费者已经收到消息了，RabbitMQ 也不会立马将消息移除，而是等待消费者显式的回复确认信号后，才会将消息删除
            String pushString = JSON.toJSONString(data);
            if(data.getCallId().contains("take_over")||data.getCallId().contains("listen_in")||data.getCallId().contains("direct_call")){
                List<Channel> channels = ChannelManagerService.getRabbitMqChannelBySpeechRecordId(speechCallId, MqTypeEnum.MAIN);
                for (Channel channel : channels) {
                    channel.basicPublish(DIALOG_RECORD_SEAT_EXCHANGE,DIALOG_RECORD_SEAT_ROUTING, new AMQP.BasicProperties().builder().deliveryMode(2).build(),pushString.getBytes(StandardCharsets.UTF_8));
                }
            }else{
                List<Channel> channels = ChannelManagerService.getRabbitMqChannelBySpeechRecordId(speechCallId, MqTypeEnum.MAIN);
                for (Channel channel : channels) {
                    channel.basicPublish(DIALOG_RECORD_EXCHANGE,DIALOG_RECORD_ROUTING, new AMQP.BasicProperties().builder().deliveryMode(2).build(),pushString.getBytes(StandardCharsets.UTF_8));
                }
            }

        } catch (IOException e) {
            ChannelManagerService.updateRabbitMqChannelBySpeechRecordId(speechCallId,MqTypeEnum.MAIN);
            e.printStackTrace();
            DingDingService.dingDingSendMsgException("回写sql报错","sfServerId",e.getMessage());
        }
    }

    @Autowired
    private TrainPhoneDialogProducer trainPhoneDialogProducer;
    private void trainPhoneNotice(String taskName, DialogRecord dialog) {

        if(taskName.contains("话术训练")){
            //TODO 推送对话给
            TrainPhoneDialogRecord trainPhoneDialogRecord = new TrainPhoneDialogRecord();
            BeanUtils.copyProperties(dialog,trainPhoneDialogRecord);
            String[] fields = taskName.split("##");
            if(fields.length>1){
                trainPhoneDialogRecord.setSpeechCallId(fields[1]);
            }else{
                DingDingService.dingDingSendMsgException("话术训练-没有传speech_call_id");
            }
            String paramJson = JSON.toJSONString(trainPhoneDialogRecord);
            //log.info("===>话术训练，通知"+paramJson);
            /*
            HttpResult httpResult = HttpClientUtils.doPostJson(trainPhoneDialogNoticeUrl,paramJson,2000);
            if(httpResult == null || httpResult.getCode() != 200){
                httpResult = HttpClientUtils.doPostJson(trainPhoneDialogNoticeUrl,paramJson,2000);
                if(httpResult == null || httpResult.getCode() != 200){
                    httpResult = HttpClientUtils.doPostJson(trainPhoneDialogNoticeUrl,paramJson,2000);
                }
            }
             */
            trainPhoneDialogProducer.send(paramJson);

        }
    }

    public List<DialogRecord> getDialogInfo(LocalDate date,String callId){
        List<DialogRecord> robotAFullSenList = dialogRecordRepository.findByDateAndCallIdAndTypeIn(date,callId, Arrays.asList(1,2));
        robotAFullSenList.sort((a,b)->a.getId().compareTo(b.getId()));//排序
        return robotAFullSenList;
    }

    public List<DialogRecordDTO> getDialogRecordsByCallId(String callId) {
        List<DialogRecord> dialogRecords = dialogRecordRepository.findDialogRecordsByCallIdOrderByTimeStamp(callId);
        List<DialogRecordDTO> dialogRecordDTOList = new ArrayList<>();
        for (DialogRecord dialogRecord : dialogRecords) {
            DialogRecordDTO dialogRecordDTO = new DialogRecordDTO();
            BeanUtils.copyProperties(dialogRecord, dialogRecordDTO);
            dialogRecordDTOList.add(dialogRecordDTO);
        }
        return dialogRecordDTOList;
    }

    public  List<String> getDialogueIdsByContent(String requestContent, String startTime, String endTime){
        return  dialogRecordRepository.getDialogueIdsByContent(requestContent, startTime,endTime);
    }

}

