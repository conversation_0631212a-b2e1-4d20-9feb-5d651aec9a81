package com.raipeng.aicall.service.seat;

import com.raipeng.aicall.seat.Seat;
import com.raipeng.aicall.service.SeatManager;
import lombok.Data;
import org.jetbrains.annotations.NotNull;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

@Data
public class SeatObtainTask implements Delayed {

    private String taskId;
    private String callId;
    private String phone;
    private final long delayTime;
    private final long expire;
    private Boolean isComplete = false;//false，true
    private boolean isEnd = false;
    private String callTeamHandleType;//take_over or listen_in or direct_call
    private String takeOverOrListenIn;
    private LocalDateTime expireTime;
    private String plainPhone;
    private String speechCallId;
    private String fsIp;
    private SeatManager seatManager;
    private String originCallId;
    private String bridgeFsIp;

    public SeatObtainTask(long delayTime,String callId,String taskId,String phone,String plainPhone,String speechCallId,String fsIp,SeatManager seatManager,String originCallId,String bridgeFsIp) {
        //this.delayTime = LocalDateTime.now().plusSeconds(15).get
        this.delayTime = LocalDateTime.now().plusSeconds(delayTime/1000).toInstant(ZoneOffset.of("+8")).toEpochMilli();
        this.expire = delayTime;
        this.callId = callId;
        this.taskId = taskId;
        this.phone = phone;
        this.plainPhone = plainPhone;
        this.speechCallId = speechCallId;
        this.fsIp = fsIp;
        this.seatManager = seatManager;
        this.originCallId = originCallId;
        this.bridgeFsIp = bridgeFsIp;
    }

    public SeatObtainTask(long delayTime,String callId,String taskId,String phone,String plainPhone,String speechCallId,SeatManager seatManager,String originCallId,String bridgeFsIp) {
        //this.delayTime = System.currentTimeMillis() + delayTime;
        this.delayTime = LocalDateTime.now().plusSeconds(delayTime/1000).toInstant(ZoneOffset.of("+8")).toEpochMilli();
        this.expire = delayTime;
        this.callId = callId;
        this.taskId = taskId;
        this.phone = phone;
        this.plainPhone = plainPhone;
        this.speechCallId = speechCallId;
        this.seatManager = seatManager;
        this.originCallId = originCallId;
        this.bridgeFsIp = bridgeFsIp;
    }

    @Override
    public long getDelay(@NotNull TimeUnit unit) {
        return unit.convert(delayTime - System.currentTimeMillis(), TimeUnit.MILLISECONDS);
    }

    @Override
    public int compareTo(@NotNull Delayed o) {
        if (this == o) {
            return 0;
        }
        if (o instanceof SeatObtainTask) {
            SeatObtainTask other = (SeatObtainTask) o;
            return Long.compare(this.getDelay(TimeUnit.MILLISECONDS), other.getDelay(TimeUnit.MILLISECONDS));
        }
        return 0;
    }

}
