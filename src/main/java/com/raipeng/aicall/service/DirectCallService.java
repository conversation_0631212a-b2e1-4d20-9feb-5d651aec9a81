package com.raipeng.aicall.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.Channel;
import com.raipeng.aicall.constant.ApplicationConstants;
import com.raipeng.aicall.constant.LocalCache;
import com.raipeng.aicall.constant.ManAiConstants;
import com.raipeng.aicall.controller.qd.ManAiSmartivrReactResponse;
import com.raipeng.aicall.controller.qd.SmartivrResponse;
import com.raipeng.aicall.mq.producter.material.CallPhoneProducerMaterial;
import com.raipeng.aicall.scriptsystem.*;
import com.raipeng.aicall.service.seat.SeatObtainTaskOld;
import com.raipeng.common.entity.callsetting.SeatTaskData;
import com.raipeng.aicall.service.seat.SeatObtainTask;
import com.raipeng.common.entity.linemanager.CallLineUnit;
import com.raipeng.common.entity.script.abstractcorpus.AbstractOrdinaryCorpus;
import com.raipeng.common.entity.script.intention.IntentionTag;
import com.raipeng.common.entity.script.masterprocess.MasterOrdinaryCorpus;
import com.raipeng.common.enums.InterruptType;
import com.raipeng.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

import static com.raipeng.aicall.constant.RabbitMqConstants.CALL_PHONE_SEND_ERROR_SINGLE_EXCHANGE;
import static com.raipeng.aicall.constant.RabbitMqConstants.CALL_PHONE_SEND_ERROR_SINGLE_ROUTING;

@Service
@Slf4j
@RefreshScope
public class DirectCallService {

    @Autowired
    private SimpleRedisService simpleRedisService;

    @Autowired
    private CallRecordService callRecordService;

    @Autowired
    private DialogRecordService dialogRecordService;

    @Autowired
    private CallService callService;

    @Value("${send.call.error.retry.duration:200}")
    private Integer sendCallErrorRetryDuration;

    @Value("${send.call.error.retry.times:3}")
    private Integer sendCallErrorRetryTimes;

    @Value("${send.call.error.switch:0}")
    private String sendCallErrorSwitch;

    @Value("${direct.call.ring:人工直呼拨号等待音.wav}")
    private String directCallRing;

    @Value("${audio.suffix:wav}")
    private String audioSuffix;

    @Autowired
    private CallThirdControlService callThirdControlService;

    @Autowired
    private SeatManager seatManager;

    @Autowired
    private CommonStatusService commonStatusService;

    //获取当前状态
    private ScriptStatus getCurrentStatus(String callId,String from,String content){
        Long time11 = System.currentTimeMillis();
        /*
        String jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callId);
        ScriptStatus oldStatus = JSONObject.parseObject(jsonString,ScriptStatus.class);
        if(oldStatus == null){
            jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callId);
            oldStatus = JSONObject.parseObject(jsonString,ScriptStatus.class);
            if(oldStatus == null) {
                jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS + callId);
                oldStatus = JSONObject.parseObject(jsonString, ScriptStatus.class);
            }
        }
         */
        ScriptStatus oldStatus = commonStatusService.getCurrentStatus(callId);
        Long time22 = System.currentTimeMillis();
        if(oldStatus == null) {
            oldStatus = new ScriptStatus();
            oldStatus.setScriptId("88ed99a1-bca3-4206-be7a-a110ce95e56e");
            oldStatus.setVersion(0);
            oldStatus.setCallId(callId);
            log.error("====>callId："+callId+"没有获取到当前用户状态");
        }
        return oldStatus;
    }


    private void setCallStatus(ScriptStatus nextStatus,String callId,String userContent){
        CallStatus callStatus = nextStatus.getCallStatus();
        Long duration = System.currentTimeMillis()/1000 - callStatus.getCallStartTime();
        callStatus.setCallDuration(duration.intValue());
        if(StringUtils.isNotEmpty(userContent))//说话内容为空，不记录用户说话次数
            callStatus.getSayCount().incrementAndGet();
        if(callStatus.getUserFullAnswerContent()==null){
            callStatus.setUserFullAnswerContent(new StringBuilder(""));
        }
        callStatus.getUserFullAnswerContent().append(userContent).append("##");
        //simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+callId, JSONObject.toJSONString(nextStatus, SerializerFeature.WriteClassName),ApplicationConstants.CALL_USER_STATUS_EXPIRE);
        commonStatusService.setLocalStatusCache(callId,nextStatus);
    }


    public Map<String,Object> dealNoticeMessage(Map<String,Object> map){
        String notify = String.valueOf(map.get("notify"));
        String callId = String.valueOf(map.get("callid"));
        ScriptStatus nextStatus = null;
        if(notify.equals("enter")){
            try{
                //接通以后，给客服播放等待音
                //nextStatus = getCurrentStatus(callId,"enter","");
                nextStatus = commonStatusService.getCurrentStatus(callId);
                MasterOrdinaryCorpus corpus = new MasterOrdinaryCorpus();
                nextStatus.setScriptId("direct-call");
                nextStatus.setIsEnd(false);
                nextStatus.setCurrentCorpus(corpus);
                nextStatus.getCurrentCorpus().setAudioPath(directCallRing);
                //设置呼叫初始值
                nextStatus.getCallStatus().setCallStartTime(System.currentTimeMillis()/1000);//呼叫开始时间
                CallStatus callStatus = nextStatus.getCallStatus();
                Long duration = System.currentTimeMillis()/1000 - callStatus.getCallStartTime();
                callStatus.setCallDuration(duration.intValue());
                callStatus.setCallStatus(CallStatusEnum.CALLING.ordinal());
                String talkTimeStart = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                nextStatus.getCallStatus().setContactTime(talkTimeStart);
                nextStatus.getCallStatus().setTalkTimeStart(talkTimeStart);
                nextStatus.getCallStatus().setCallStatus(CallStatusEnum.CALL_SUCCESS.ordinal());//呼叫完成
                //simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+callId, JSONObject.toJSONString(nextStatus, SerializerFeature.WriteClassName),ApplicationConstants.CALL_USER_STATUS_EXPIRE);
                commonStatusService.setLocalStatusCache(callId,nextStatus);
                CallPhoneProducerMaterial data = nextStatus.getCallStatus().getCallPhoneProducerMaterial();
                CallLineUnit callLineUnit = nextStatus.getCallStatus().getCallLineUnit();
                SeatTaskData seatTaskData = nextStatus.getCallStatus().getSeatTaskData();
                //callThirdControlService.directCallTakeOver(callId,seatTaskData.getFsIp(),data.getData().get(0).getPlainPhone(),callLineUnit);
                callThirdControlService.takeOver(callId,seatTaskData.getFsIp(),seatTaskData.getFsUser(),seatTaskData.getFsIp(),seatTaskData.getFsPort());
                SeatObtainTask task = LocalCache.seatObtainTaskCache.getIfPresent(callId);
                if(task != null){
                    seatManager.addSeatObtainTask2(callId,nextStatus.getCallStatus().getCallPhoneProducerMaterial().getTaskId(),nextStatus.getPhoneDataCache().getPhone(),nextStatus.getPhoneDataCache().getPlainPhone(),1000l,LocalDateTime.now().plusMinutes(100l),false,nextStatus.getPhoneDataCache().getCallTeamHandleType(),null,nextStatus.getPhoneDataCache().getSpeechCallId(),map.getOrDefault("originCallId","").toString(),map.getOrDefault("bridge_fs_ip","").toString());
                }


            }catch (Exception e){
                return ManAiSmartivrReactResponse.response("enter","exception",null,callId);
            }

            return ManAiSmartivrReactResponse.response("enter","enter",nextStatus,callId);

        }else if(notify.equals("bridge_notify")) {//接管通知
            //nextStatus = getCurrentStatus(callId,"enter","");
            nextStatus = commonStatusService.getCurrentStatus(callId);
            //设置呼叫初始值
            SeatObtainTask task = LocalCache.seatObtainTaskCache.getIfPresent(callId);
            if(task != null){
                task.setTakeOverOrListenIn("take_over");//已经接管
            }
            Map<String,Object> returnMap = ManAiSmartivrReactResponse.response(notify,"noop",null,callId);
            return returnMap;

        }else if(notify.equals("asrprogress_notify")){//TODO progress里面只能做暂停和恢复播放操作，播放动作只在message中进行
            String userAnswerContent = String.valueOf(map.get("message")).trim();
            String recordfile = String.valueOf(map.get("recordfile"));
            Integer recordms = Integer.valueOf(String.valueOf(map.get("recordms")));
            //ScriptStatus currentStatus = getCurrentStatus(callId,"progress",userAnswerContent);
            ScriptStatus currentStatus = commonStatusService.getCurrentStatus(callId);
            //dialogRecordService.toMQ(callId,"http://"+currentStatus.getCallStatus().getFsIp()+recordfile,1,userAnswerContent,0,recordms, null, null, null, null, null, null,"人工直呼", System.currentTimeMillis(),currentStatus.getPhoneDataCache().getSpeechCallId());
            dialogRecordService.toMqNew(callId,1,currentStatus,null, map,userAnswerContent);
            Map<String,Object> returnMap = ManAiSmartivrReactResponse.response(notify,"noop",null,callId);
            return returnMap;
        }else if(notify.equals("asrmessage_notify")){
            String userAnswerContent = String.valueOf(map.get("message")).trim();
            Integer speakms = Integer.valueOf(String.valueOf(map.get("speakms")));
            String playstate = String.valueOf(map.getOrDefault("playstate",""));
            //ScriptStatus currentStatus = getCurrentStatus(callId,"message",userAnswerContent);
            ScriptStatus currentStatus = commonStatusService.getCurrentStatus(callId);
            setCallStatus(currentStatus,callId,userAnswerContent);
            //dialogRecordService.toMQ(callId,"",2,userAnswerContent,speakms,0, null, null, null , null, null, null,currentStatus.getPhoneDataCache().getTaskName(), System.currentTimeMillis(),currentStatus.getPhoneDataCache().getSpeechCallId());//切片文件通过;号分析得出
            dialogRecordService.toMqNew(callId,2,currentStatus,null, map,userAnswerContent);
            Map<String,Object> returnMap = ManAiSmartivrReactResponse.response(notify,"noop",null,callId);
            return returnMap;

        }else if(notify.equals("playback_result")){
            //ScriptStatus currentStatus = getCurrentStatus(callId,"playback end","");
            ScriptStatus currentStatus = commonStatusService.getCurrentStatus(callId);
            MasterOrdinaryCorpus corpus = new MasterOrdinaryCorpus();
            currentStatus.setScriptId("direct-call");
            currentStatus.setIsEnd(false);
            currentStatus.setCurrentCorpus(corpus);
            currentStatus.getCurrentCorpus().setAudioPath(directCallRing);
            currentStatus.getCurrentCorpus().setInterruptType(InterruptType.SUPPORT_SEMANTIC_INTERRUPT_NO_REPLY);
            SeatObtainTask task = LocalCache.seatObtainTaskCache.getIfPresent(callId);
            if(task != null && task.getTakeOverOrListenIn() != null && task.getTakeOverOrListenIn().equals("take_over")){
                Map<String,Object> returnMap = ManAiSmartivrReactResponse.response(notify,"noop",null,callId);
                return returnMap;
            }else{
                return ManAiSmartivrReactResponse.response(notify,"other",currentStatus,callId);
            }


        }else if(notify.equals("leave")){
            //ScriptStatus currentStatus = getCurrentStatus(callId,"leave","");
            ScriptStatus currentStatus = commonStatusService.getCurrentStatus(callId);
            currentStatus.getCallStatus().setCallEndTime(System.currentTimeMillis()/1000);
            String wholeRecordFile = "http://"+currentStatus.getCallStatus().getFsIp()+ApplicationConstants.FS_RECORD_FILE_DIR+ApplicationConstants.FS_RECORD_FILE_DIR_WHOLE+"/"+ LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"))+"/"+callId+"."+audioSuffix;
            CallPhoneProducerMaterial.PhoneDataCache phoneDataCache = currentStatus.getPhoneDataCache();
            currentStatus.getCallStatus().setCallStatus(CallStatusEnum.CALL_SUCCESS.ordinal());//呼叫完成
            Long duration = 0L;

            if(currentStatus.getCallStatus().getCallStartTime()==null || currentStatus.getCallStatus().getCallStartTime()==0){
                LocalDateTime time = LocalDateTime.parse(currentStatus.getCallStatus().getCallOutTime(),DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                Long timestamp = time.toInstant(ZoneOffset.ofHours(8)).toEpochMilli()/1000;
                duration = currentStatus.getCallStatus().getCallEndTime() - timestamp;
            }else{
                duration = currentStatus.getCallStatus().getCallEndTime() - currentStatus.getCallStatus().getCallStartTime();
            }

            currentStatus.getCallStatus().setCallDuration(duration.intValue());
            currentStatus.getCallStatus().setIntentionClass(currentStatus.getFinalIntentionType());

            currentStatus.getCallStatus().setTalkTimeEnd(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            currentStatus.getCallStatus().setIntentionLabelIds(currentStatus.getFinalIntentionLabelIdsString());
            currentStatus.getCallStatus().setIntentionLabels(currentStatus.getFinalIntentionLabelsString());
            currentStatus.getCallStatus().setHitAnswerIds(currentStatus.getHitAnswerIds());
            callRecordService.toMQ(phoneDataCache.getTaskId(),phoneDataCache.getTaskName(),phoneDataCache.getPhone(),phoneDataCache.getLineId(),phoneDataCache.getScriptId(),callId,phoneDataCache.getSpeechCallId(),"",wholeRecordFile,phoneDataCache.getLineCode(),"","",currentStatus.getVersion(),currentStatus.getCallStatus(),currentStatus.getScriptLongId(), JSONObject.toJSONString(currentStatus.getMatchedCorpusIds()), currentStatus.getHitSemanticIdsString(),phoneDataCache.getPlainPhone(), currentStatus.getFinalExtraInfo());

            //如果结束，则移除该本地缓存
            /*
            if(ManAiConstants.seatObtainGlobleTaskMap.containsKey(callId)){
                ManAiConstants.seatObtainGlobleTaskMap.remove(callId);
            }
             */

            if(LocalCache.seatObtainTaskCache.getIfPresent(callId) != null){
                //LocalCache.seatObtainTaskCache.invalidate(callId);
            }

            return ManAiSmartivrReactResponse.response(notify,"noop",null,callId);
        }else{
            return SmartivrResponse.response(notify,"noop",null,callId);
        }

    }


    public void cdr(String uuid,String cdr){

        JSONObject jsonObject = JSONObject.parseObject(cdr,JSONObject.class);
        JSONObject cc = (JSONObject)jsonObject.get("variables");
        String hangupCause= cc.getString("hangup_cause");
        String hangupCauseQ850= cc.getString("hangup_cause_q850");
        int answerSec = cc.getIntValue("answersec");
        int billSec = cc.getIntValue("billsec");
        String speechCallId = cc.getString("speech_call_id");

        String sipCallId = null;
        if(cc.containsKey("sip_call_id"))
            sipCallId = cc.getString("sip_call_id");

        int billmsec = cc.getIntValue("billmsec");
        int mduration = cc.getIntValue("mduration");
        int waitmsec = mduration - billmsec;

        String lastApp = "";
        if(cc.containsKey("last_app")){
            lastApp = cc.getString("last_app");
        }
        Integer whoHangup = 1;
        if(lastApp.contains("hangup")){
            whoHangup = 0;
        }

        toDB(uuid,hangupCause,cdr,billSec,hangupCauseQ850,waitmsec,whoHangup,billmsec,sipCallId);
    }

    public void toDB(String uuid,String hangupCause,String cdr,int billSec,String hangupCauseQ850,int waitmsec,int whoHangup,int billmsec,String sipCallId){
        String callChannelUniqueId = uuid;
        /*
        String cacheString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callChannelUniqueId);
        ScriptStatus userStatus = JSONObject.parseObject(cacheString,ScriptStatus.class);
        if(userStatus == null){
            try{Thread.sleep(20l);}catch (Exception e){}
            cacheString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callChannelUniqueId);
            userStatus = JSONObject.parseObject(cacheString,ScriptStatus.class);
            if(userStatus == null){
                try{Thread.sleep(20l);}catch (Exception e){}
                cacheString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callChannelUniqueId);
                userStatus = JSONObject.parseObject(cacheString,ScriptStatus.class);
            }
        }
         */
        ScriptStatus userStatus = commonStatusService.getCurrentStatus(callChannelUniqueId);


        if(userStatus == null){
            DingDingService.dingDingSendMsgException("Redis缓存中没有获取到用户的状态信息，call_id: " + uuid,"缓存字符串：");
            return ;
        }

        CallPhoneProducerMaterial.PhoneDataCache userInfo = userStatus.getPhoneDataCache();
        userStatus.getCallStatus().setCallDuration(billSec);
        userStatus.getCallStatus().setCallMDuration(billmsec);//通话时长毫秒
        userStatus.getCallStatus().setWaitMsec(waitmsec);//等待时间
        userStatus.getCallStatus().setIsNewRecord("0");//默认记录为非新建
        userStatus.getCallStatus().setWhoHangup(whoHangup);
        if(userInfo.getSendCallErrorRetryTimes() < sendCallErrorRetryTimes && waitmsec <= sendCallErrorRetryDuration && sendCallErrorSwitch.equals("1")){
            userStatus.getCallStatus().setIsNewRecord("1");//记录为新建
            if(StringUtils.isNotEmpty(userInfo.getTaskName()) && !userInfo.getTaskName().contains("话术训练"))
                callService.backQueueSendFailure(ChannelManagerService.channelList1.get(0),userInfo);

        }

        if(userInfo.getSendCallErrorRetryTimes() >= sendCallErrorRetryTimes && waitmsec <= sendCallErrorRetryDuration && sendCallErrorSwitch.equals("1")){
            hangupCause = "呼叫失败";
        }

        callService.getCallRecordService().toMQCdr(userInfo.getTaskId(),userInfo.getTaskName(),userInfo.getPhone(),
                userInfo.getLineId(),userStatus.getScriptId(),callChannelUniqueId,userInfo.getSpeechCallId(),
                hangupCause,"",userInfo.getLineCode(),hangupCauseQ850,hangupCause,userStatus.getVersion(),
                userStatus.getCallStatus(), userStatus.getScriptLongId(), cdr, userStatus.getHitSemanticIdsString(),userInfo.getPlainPhone(),sipCallId);

    }



}
