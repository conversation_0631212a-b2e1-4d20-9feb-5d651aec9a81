package com.raipeng.aicall.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.raipeng.aicall.constant.*;
import com.raipeng.aicall.controller.qd.SmartivrResponse;
import com.raipeng.aicall.controller.qd.SmartivrTestResponse;
import com.raipeng.aicall.mq.producter.material.CallPhoneProducerMaterial;
import com.raipeng.aicall.scriptsystem.*;
import com.raipeng.aicall.utils.ObjectCloneUtils;
import com.raipeng.common.entity.script.AiScript;
import com.raipeng.common.entity.script.intention.IntentionLevel;
import com.raipeng.common.entity.script.intention.IntentionTag;
import com.raipeng.common.enums.InterruptType;
import com.raipeng.common.util.CollectionUtil;
import com.raipeng.common.util.StringUtils;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.raipeng.common.enums.InterruptType.*;

/**
 * brightivr 服务
 */
@Slf4j
@Service
@RefreshScope
public class SmartivrService {

    @Autowired
    private RobotWavFileService robotWavFileService;

    @Autowired
    private SimpleRedisService simpleRedisService;

    @Autowired
    private ScriptService scriptService;

    @Autowired
    private CallRecordService callRecordService;

    @Autowired
    private DialogRecordService dialogRecordService;

    @Autowired
    private CallService callService;

    @Value("${send.call.error.retry.duration:200}")
    private Integer sendCallErrorRetryDuration;

    @Value("${send.call.error.retry.times:3}")
    private Integer sendCallErrorRetryTimes;

    @Value("${send.call.error.switch:0}")
    private String sendCallErrorSwitch;

    @Value("${is.pressure.test:0}")
    private String isPressureTest;

    @Value("${audio.suffix:wav}")
    private String audioSuffix;

    @Autowired
    private CurrentServerIpService currentServerIpService;

    @Autowired
    private CommonStatusService commonStatusService;


    //获取当前状态
    private ScriptStatus getCurrentStatus(String callId,String from,String content){
        Long time11 = System.currentTimeMillis();
        /*
        String jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callId);
        ScriptStatus oldStatus = JSONObject.parseObject(jsonString,ScriptStatus.class);
        if(oldStatus == null){
            jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callId);
            oldStatus = JSONObject.parseObject(jsonString,ScriptStatus.class);
            if(oldStatus == null) {
                jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS + callId);
                oldStatus = JSONObject.parseObject(jsonString, ScriptStatus.class);
            }
        }

         */
        ScriptStatus oldStatus = commonStatusService.getCurrentStatus(callId);
        Long time22 = System.currentTimeMillis();
        //log.info(callId+"asrprogress_notify获取redis当前用户状态耗时："+(time22-time11));
        if(oldStatus == null) {
            oldStatus = new ScriptStatus();
            oldStatus.setScriptId("88ed99a1-bca3-4206-be7a-a110ce95e56e");
            oldStatus.setVersion(0);
            oldStatus.setCallId(callId);
            log.error("====>callId："+callId+"没有获取到当前用户状态");
        }
        return oldStatus;
    }

    private ScriptStatus getNextStatus(String callId,String userContent,Boolean isSilence,String type){
        if(userContent == null){
            userContent = "";
        }
        /*
        String jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callId);
        ScriptStatus oldStatus = JSONObject.parseObject(jsonString,ScriptStatus.class);
        if(oldStatus == null){
            jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callId);
            oldStatus = JSONObject.parseObject(jsonString,ScriptStatus.class);
            if(oldStatus == null) {
                jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS + callId);
                oldStatus = JSONObject.parseObject(jsonString, ScriptStatus.class);
            }
        }
        */
        ScriptStatus oldStatus = commonStatusService.getCurrentStatus(callId);
        if(oldStatus == null) {
            oldStatus = new ScriptStatus();
            oldStatus.setScriptId("88ed99a1-bca3-4206-be7a-a110ce95e56e");
            oldStatus.setVersion(0);
        }
        oldStatus.setUserContent(userContent);
        oldStatus.setIsSilence(isSilence);
        //log.info("===>获取话术传参："+JSON.toJSONString(oldStatus));
        ScriptStatus nextStatus = scriptService.getNextStatus(oldStatus);
        if(nextStatus.getIsEnd()){//
            nextStatus.getCallStatus().setWhoHangup(0);
        }
        if(type.equals("enter")){
            if(nextStatus.getCallStatus()==null){
                nextStatus.setCallStatus(new CallStatus());
                nextStatus.getCallStatus().setCallOutTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                nextStatus.getCallStatus().setCallId(callId);
            }
            nextStatus.getCallStatus().setCallStartTime(System.currentTimeMillis()/1000);//呼叫开始时间
            setCallStatus(nextStatus,CallStatusEnum.CALLING,callId,null);
            String talkTimeStart = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            nextStatus.getCallStatus().setContactTime(talkTimeStart);
            nextStatus.getCallStatus().setTalkTimeStart(talkTimeStart);
            nextStatus.getCallStatus().setCallStatus(CallStatusEnum.CALL_SUCCESS.ordinal());//呼叫完成
        }

        //TODO 全局不打断
        //nextStatus.getCurrentCorpus().setInterruptType(CAN_NOT_BE_INTERRUPTED);
        //nextStatus.getMatchedCorpusList().add(nextStatus.getCurrentCorpus());

        //InterruptType interruptType = nextStatus.getCurrentCorpus()==null?null:nextStatus.getCurrentCorpus().getInterruptType();

        InterruptType interruptType = null;
        if(nextStatus.getCurrentCorpus() != null && nextStatus.getCurrentMultiContent() != null
                && nextStatus.getCurrentMultiContent().getActiveUnitContents() != null
                && nextStatus.getCurrentMultiContent().getActiveUnitContents().size() > 0) {
            interruptType = nextStatus.getCurrentMultiContent().getActiveUnitContents().get(0).getUnitContent().getInterruptType();
        }

        if(interruptType != null && (interruptType.equals(SUPPORT_SEMANTIC_INTERRUPT_NO_REPLY)
                ||interruptType.equals(SUPPORT_SEMANTIC_INTERRUPT_WITH_REPLY))) {
            nextStatus.setCurrentInterruptContent(InterruptContent.builder()
                    .allowedInterruptTime(nextStatus.getCurrentMultiContent().getActiveUnitContents().get(0).getUnitContent().getAllowedInterruptTime())
                    .interruptEndTime(nextStatus.getCurrentMultiContent().getActiveUnitContents().get(0).getUnitContent().getAllowedInterruptTime()+System.currentTimeMillis()/1000)
                    .build());
        }else{
            nextStatus.setCurrentInterruptContent(null);
        }

        return nextStatus;
    }


    private ScriptStatus getNextStatus1(String callId,String userContent,Boolean isSilence,String type){
        if(userContent == null){
            userContent = "";
        }
        /*
        String jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callId);
        ScriptStatus oldStatus = JSONObject.parseObject(jsonString,ScriptStatus.class);
        if(oldStatus == null){
            jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callId);
            oldStatus = JSONObject.parseObject(jsonString,ScriptStatus.class);
            if(oldStatus == null) {
                jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS + callId);
                oldStatus = JSONObject.parseObject(jsonString, ScriptStatus.class);
            }
        }
         */

        ScriptStatus oldStatus = commonStatusService.getCurrentStatus(callId);

        if(oldStatus == null) {
            oldStatus = new ScriptStatus();
        }
        oldStatus.setUserContent(userContent);
        oldStatus.setIsSilence(isSilence);
        ScriptStatus nextStatus = scriptService.getNextStatus(oldStatus);
        if(nextStatus.getIsEnd()){//
            nextStatus.getCallStatus().setWhoHangup(0);
        }

        //log.info(callId+"打断回复====>请求文本："+userContent+"/返回文件名："+nextStatus.getCurrentCorpus().getAudioPath()+"/名称："+nextStatus.getCurrentCorpus().getName()
        //        +"/是否结束："+nextStatus.getIsEnd()+"/打断类型："+nextStatus.getCurrentCorpus().getInterruptType()+"/是否命中："+nextStatus.getHitPhrase());


        //TODO 全局不打断
       // nextStatus.getMatchedCorpusList().add(nextStatus.getCurrentCorpus());
        return nextStatus;
    }

    private ScriptStatus getNextStatusInterrupter(String callId,String userContent,Boolean isSilence,String type){
        if(userContent == null){
            userContent = "";
        }
        //Long time11 = System.currentTimeMillis();
        /*
        String jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callId);
        ScriptStatus oldStatus = JSONObject.parseObject(jsonString,ScriptStatus.class);
        if(oldStatus == null){
            jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callId);
            oldStatus = JSONObject.parseObject(jsonString,ScriptStatus.class);
            if(oldStatus == null) {
                jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS + callId);
                oldStatus = JSONObject.parseObject(jsonString, ScriptStatus.class);
            }
        }
         */

        ScriptStatus oldStatus = commonStatusService.getCurrentStatus(callId);
        setHitSemanticId(oldStatus, userContent);
        oldStatus.setUserContent(userContent);
        oldStatus.setInterruptOperation(true);
        ScriptStatus nextStatus = scriptService.getNextStatus(oldStatus);
        //Long time22 = System.currentTimeMillis();
        //log.info(callId+"asrprogress_notify打断获取最高优先耗时："+(time22-time11));
        //log.info("-->progress semanticInterrupt callId："+callId+"，userContent："+userContent+"，audioPath："+nextStatus.getCurrentCorpus()==null?null:nextStatus.getCurrentCorpus().getAudioPath()+",name："+nextStatus.getCurrentCorpus()==null?null:nextStatus.getCurrentCorpus().getName()
        //        +"，isEnd："+nextStatus.getIsEnd()+"，InterruptType："+nextStatus.getCurrentCorpus()==null?null:nextStatus.getCurrentCorpus().getInterruptType()+"，isHitPhrase："+nextStatus.getHitPhrase());

        return nextStatus;
    }

    private void setCallStatus(ScriptStatus nextStatus,String callId,String userContent){
        CallStatus callStatus = nextStatus.getCallStatus();
        Long duration = System.currentTimeMillis()/1000 - callStatus.getCallStartTime();
        callStatus.setCallDuration(duration.intValue());
        if(StringUtils.isNotEmpty(userContent))//说话内容为空，不记录用户说话次数
            callStatus.getSayCount().incrementAndGet();
        if(callStatus.getUserFullAnswerContent()==null){
            callStatus.setUserFullAnswerContent(new StringBuilder(""));
        }
        callStatus.getUserFullAnswerContent().append(userContent).append("##");
        //simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+callId, JSONObject.toJSONString(nextStatus, SerializerFeature.WriteClassName),ApplicationConstants.CALL_USER_STATUS_EXPIRE);
        nextStatus.getCallStatus().getDialogContentsCache().add(DialogContent.builder()
                .content(userContent).fromNumber("1").createTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).build());
        commonStatusService.setLocalStatusCache(callId,nextStatus);
    }

    private void setCallStatus(ScriptStatus nextStatus,CallStatusEnum statusEnum,String callId,String userContent){
        CallStatus callStatus = nextStatus.getCallStatus();
        Long duration = System.currentTimeMillis()/1000 - callStatus.getCallStartTime();
        callStatus.setCallDuration(duration.intValue());
        if(userContent!=null){
            if(StringUtils.isNotEmpty(userContent))
                callStatus.getSayCount().incrementAndGet();
            if(callStatus.getUserFullAnswerContent()==null){
                callStatus.setUserFullAnswerContent(new StringBuilder(""));
            }
            callStatus.getUserFullAnswerContent().append(userContent).append("##");
        }
        callStatus.setCallStatus(statusEnum.ordinal());

    }

    public Map<String,Object> dealNoticeMessage(Map<String,Object> map){
        String notify = String.valueOf(map.get("notify"));
        String callId = String.valueOf(map.get("callid"));
        ScriptStatus nextStatus = null;
        Long start = System.currentTimeMillis();
        //log.info("===>receive ivr message：callId："+map.toString());
        //Long time11 = null;
        //Long time22 = null;
        if(notify.equals("enter")){
            //StopWatch stopWatch = new StopWatch();
            try{
                if(callId.startsWith("engine_") || callId.startsWith("C_"))
                    simpleRedisService.setValueWithExpire(ApplicationConstants.HELPER_PREFIX+callId,"hangup",60l);
                else
                    LocalCache.helpCache.put(callId,"success");
                //time11 = System.currentTimeMillis();
                //nextStatus = getNextStatus(callId,"",false,"enter");
                //stopWatch.start("获取current耗时");
                nextStatus = commonStatusService.getNextStatusOld(callId,"",false, NotifyEnum.ENTER);
                //stopWatch.stop();
                //time22 = System.currentTimeMillis();
                //log.info(callId+"enter匹配凡哥话术耗时："+(time22-time11));
            }catch (Exception e){
                e.printStackTrace();
                if(isPressureTest.equals("1")){
                    return SmartivrTestResponse.response("enter","exception",null,callId);
                }
                return SmartivrResponse.response("enter","exception",null,callId);
            }
            //time11 = System.currentTimeMillis();
            //String audioFileUrl = "http://"+nextStatus.getCallStatus().getFsIp()+ApplicationConstants.FS_BG_MUSIC_DIR+"/"+nextStatus.getScriptId()+"/"+nextStatus.getCurrentCorpus().getAudioPath().split("/")[nextStatus.getCurrentCorpus().getAudioPath().split("/").length-1];
            //String content = nextStatus.getCurrentCorpus() != null ? nextStatus.getCurrentCorpus().getContent() : "";
            //dialogRecordService.toMQ(callId,audioFileUrl,0,content,0,0, nextStatus.getCurrentCorpus().getName(), nextStatus.getCurrentCorpus().getInterruptType(), nextStatus.getCurrentIntentionType(), null, null,null,nextStatus.getPhoneDataCache().getTaskName(), System.currentTimeMillis(),nextStatus.getPhoneDataCache().getSpeechCallId());
            //stopWatch.start("dialogRecord to mq");
            dialogRecordService.toMqNew(callId,0,nextStatus,nextStatus.getCurrentMultiContent().getActiveUnitContents().get(0), map,null);
            //stopWatch.stop();
            //time22 = System.currentTimeMillis();
            //log.info(callId+"enter插入dialog耗时："+(time22-time11));
            Long end = System.currentTimeMillis();
            //log.info("===>enter耗时：callId："+callId+",time："+(end-start));
            if(isPressureTest.equals("1")){
                return SmartivrTestResponse.response("enter","enter",nextStatus,callId);
            }
            //time11 = System.currentTimeMillis();
            //stopWatch.start("获取next耗时");
            nextStatus.getCallStatus().getDialogContentsCache().add(DialogContent.builder()
                    .content(nextStatus.getCurrentMultiContent()
                            .getActiveUnitContents().get(0)
                            .getUnitContent().getContent()).fromNumber("0").createTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).build());
            nextStatus = scriptService.callbackNextStatus(nextStatus);
            //simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+callId, JSONObject.toJSONString(nextStatus, SerializerFeature.WriteClassName),ApplicationConstants.CALL_USER_STATUS_EXPIRE);
            //stopWatch.stop();
            //stopWatch.start("存localCache耗时");
            commonStatusService.setLocalStatusCache(callId,nextStatus);
            Map<String,Object> resultMap = SmartivrResponse.response("enter","enter",nextStatus,callId);
            //stopWatch.stop();
            //if(stopWatch.getTotalTimeMillis() > 50l){
            //    log.info("===>enter耗时：callId："+callId+": "+stopWatch.prettyPrint());
            //}
            //time22 = System.currentTimeMillis();
            //log.info(callId+"enter组装中间件内容耗时："+(time22-time11));
            return resultMap;

        }/*else if (!notify.contains("enter")) {
            return SmartivrResponse.response("test","enter",nextStatus,callId);
        }*/else if(notify.equals("asrprogress_notify")){//TODO progress里面只能做暂停和恢复播放操作，播放动作只在message中进行
            String userAnswerContent = String.valueOf(map.get("message")).trim();
            String recordfile = String.valueOf(map.get("recordfile"));
            Integer recordms = Integer.valueOf(String.valueOf(map.get("recordms")));
            //time11 = System.currentTimeMillis();
            ScriptStatus currentStatus = getCurrentStatus(callId,"progress",userAnswerContent);
            //time22 = System.currentTimeMillis();
            //log.info(callId+"asrprogress_notify获取当前用户状态耗时："+(time22-time11));
            //time11 = System.currentTimeMillis();
            //dialogRecordService.toMQ(callId,"http://"+currentStatus.getCallStatus().getFsIp()+recordfile,1,userAnswerContent,0,recordms, null, null, null, null, null, null,currentStatus.getPhoneDataCache().getTaskName(), System.currentTimeMillis(),currentStatus.getPhoneDataCache().getSpeechCallId());
            dialogRecordService.toMqNew(callId,1,currentStatus,currentStatus.getCurrentMultiContent().getActiveUnitContents().get(0), map,userAnswerContent);
            //time22 = System.currentTimeMillis();
            //log.info(callId+"asrprogress_notify插入dialog耗时："+(time22-time11));
            if(currentStatus.getIsEnd()){
                Long end = System.currentTimeMillis();
                //log.info("===>message1耗时：callId："+callId+",time："+(end-start));
                //time11 = System.currentTimeMillis();
                Map<String,Object> resultMap = messageNotifyResponse(notify,"noop_hangup",null,callId);
                //time22 = System.currentTimeMillis();
                //log.info(callId+"asrmessage_notify当前语料是结束语料，不做任何处理耗时："+(time22-time11));
                return resultMap;
            }

            //InterruptType interruptType = currentStatus.getCurrentCorpus()==null?null:currentStatus.getCurrentCorpus().getInterruptType();
            InterruptType interruptType = currentStatus.getCurrentCorpus()==null ? null : currentStatus.getCurrentMultiContent().getActiveUnitContents().get(0).getUnitContent().getInterruptType();
            if(interruptType!=null&&(interruptType.equals(SUPPORT_SEMANTIC_INTERRUPT_NO_REPLY)
                    ||interruptType.equals(SUPPORT_SEMANTIC_INTERRUPT_WITH_REPLY))&&userAnswerContent.length()>0) {
                ScriptStatus tempStatus = null;
                try{
                    //time11 = System.currentTimeMillis();
                    tempStatus = getNextStatusInterrupter(callId,userAnswerContent,false,"");
                    if(currentStatus.getCurrentInterruptContent() != null){
                        if(currentStatus.getCurrentInterruptContent().getInterruptEndTime() > System.currentTimeMillis()/1000){
                            //不可打断
                            return SmartivrResponse.response(notify,"noop",null,callId);
                        }
                    }
                    //time22 = System.currentTimeMillis();
                    //log.info(callId+"asrprogress_notify语义打断匹配凡哥话术："+(time22-time11));
                }catch (Exception e){
                    e.printStackTrace();
                    if(isPressureTest.equals("1")){
                        return SmartivrTestResponse.response("enter","exception",null,callId);
                    }
                    return SmartivrResponse.response("enter","exception",null,callId);
                }

                if(tempStatus.getHitPhrase()){//命中
                    //判斷完整一句话来了没，如果来了，丢弃该次命中，如果没有，直接执行。
                    //String wholeSentence = simpleRedisService.getValue(ApplicationConstants.WHOLE_SENTENCE+callId);
                    //if(StringUtils.isEmpty(wholeSentence)){
                    //log.info("命中最高优先开始打断"+callId);
                    tempStatus.setIsEnd(false);
                    Long end = System.currentTimeMillis();
                    //log.info("===>progress耗时：callId："+callId+",time："+(end-start));
                    //time11 = System.currentTimeMillis();
                    //simpleRedisService.setValueWithExpire(ApplicationConstants.PAUSE_PREFIX+callId,"1",15l);//最多也就5s
                    commonStatusService.setLocalPauseCache(callId);
                    //time22 = System.currentTimeMillis();
                    //log.info(callId+"asrprogress_notify命中设置redis缓存耗时："+(time22-time11));
                    if(isPressureTest.equals("1")){
                        return SmartivrTestResponse.response(notify, "pause", tempStatus,callId);
                    }
                    return SmartivrResponse.response(notify, "pause", tempStatus,callId);
                    //}
                }else{
                    //log.info("progress semanticInterrupt ：===> callId："+callId+" not hit，content"+userAnswerContent);
                }
            }
            Long end = System.currentTimeMillis();
            //log.info("===>progress耗时：callId："+callId+",time："+(end-start));
            if(isPressureTest.equals("1")){
                return SmartivrTestResponse.response(notify,"noop",null,callId);
            }

            //time11 = System.currentTimeMillis();
            Map<String,Object> returnMap = SmartivrResponse.response(notify,"noop",null,callId);
            //time22 = System.currentTimeMillis();
            //log.info(callId+"asrprogress_notifyr组装中间件内容耗时："+(time22-time11));
            return returnMap;
        }else if(notify.equals("asrmessage_notify")){
            String userAnswerContent = String.valueOf(map.get("message")).trim();
            //time11 = System.currentTimeMillis();
            //simpleRedisService.setValueWithExpire(ApplicationConstants.WHOLE_SENTENCE+callId,userAnswerContent,ApplicationConstants.WHOLE_SENTENCE_EXPIRE);
            //time22 = System.currentTimeMillis();
            //log.info(callId+"asrmessage_notify设置完整一句话到redis耗时："+(time22-time11));
            Integer speakms = Integer.valueOf(String.valueOf(map.get("speakms")));
            String playstate = String.valueOf(map.getOrDefault("playstate",""));
            //time11 = System.currentTimeMillis();
            ScriptStatus currentStatus = getCurrentStatus(callId,"message",userAnswerContent);
            Set<Long> hitSemanticIds = setHitSemanticId(currentStatus, userAnswerContent);
            setCallStatus(currentStatus,callId,userAnswerContent);
            //dialogRecordService.toMQ(callId,"",2,userAnswerContent,speakms,0, null, null, null , null, hitSemanticIds.stream().map(String::valueOf).collect(Collectors.joining(",")), null,currentStatus.getPhoneDataCache().getTaskName(), System.currentTimeMillis(),currentStatus.getPhoneDataCache().getSpeechCallId());//切片文件通过;号分析得出
            dialogRecordService.toMqNew(callId,2,currentStatus,currentStatus.getCurrentMultiContent().getActiveUnitContents().get(0), map,userAnswerContent);
            //InterruptType interruptType = currentStatus.getCurrentCorpus()==null?null:currentStatus.getCurrentCorpus().getInterruptType();
            //log.info("当前语料"+JSON.toJSONString(currentStatus));
            InterruptType interruptType = currentStatus.getCurrentCorpus() == null ? null : currentStatus.getCurrentMultiContent().getActiveUnitContents().get(0).getUnitContent().getInterruptType();
            if(StringUtils.isEmpty(userAnswerContent) && !currentStatus.getIsEnd()){
                if (playstate.equals("false")) {//放音结束
                    //wait，产生一个wait事件，并且
                    if(LocalCache.waitCache.getIfPresent(callId) == null){
                        LocalCache.waitCache.put(callId,1);
                        return SmartivrResponse.response(notify, "wait", currentStatus, callId);//直接等2s
                    }
                    /*
                    if(!simpleRedisService.hasKey(String.format(ApplicationConstants.WAIT,callId))) {
                        simpleRedisService.setValueWithExpire(String.format(ApplicationConstants.WAIT, callId), "1", ApplicationConstants.WAIT_EXPIRE);
                        return SmartivrResponse.response(notify, "wait", currentStatus, callId);//直接等2s
                    }
                     */
                }
                return SmartivrResponse.response(notify,"noop",null,callId);
            }
            /*
            if(simpleRedisService.hasKey(String.format(ApplicationConstants.WAIT,callId))) {//到这里，一定是有识别不为空的一句话
                simpleRedisService.delete(String.format(ApplicationConstants.WAIT,callId));
            }
            */

            if(LocalCache.waitCache.getIfPresent(callId) != null){//到这里，一定是有识别不为空的一句话
                LocalCache.waitCache.invalidate(callId);
            }

            if(currentStatus.getIsEnd()){
                Long end = System.currentTimeMillis();
                //log.info("===>message1耗时：callId："+callId+",time："+(end-start));
                //time11 = System.currentTimeMillis();
                Map<String,Object> resultMap = messageNotifyResponse(notify,"noop_hangup",null,callId);
                //time22 = System.currentTimeMillis();
                //log.info(callId+"asrmessage_notify当前语料是结束语料，不做任何处理耗时："+(time22-time11));
                return resultMap;
            }

            if(interruptType!=null&&(interruptType.equals(SUPPORT_SOUND_INTERRUPT_NO_REPLY)||interruptType.equals(SUPPORT_SEMANTIC_INTERRUPT_NO_REPLY))){//发声不做回复，恢复播音-这里不管原来是什么情况，都恢复一下
                if(playstate.equals("false")){
                    try{
                        //time11 = System.currentTimeMillis();
                        nextStatus = getNextStatus(callId, userAnswerContent,false,"");
                        //time22 = System.currentTimeMillis();
                        //log.info(callId+"asrmessage_notify语义不可打断匹配凡哥话术耗时："+(time22-time11));
                    }catch (Exception e){
                        e.printStackTrace();
                        if(isPressureTest.equals("1")){
                            return SmartivrTestResponse.response("enter","exception",null,callId);
                        }
                        return SmartivrResponse.response("enter","exception",null,callId);
                    }
                    //simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_DIALOGUE_CONTENT+callId,"",ApplicationConstants.CALL_DIALOGUE_CONTENT_EXPIRE);
                    //String audioFileUrl = "http://"+nextStatus.getCallStatus().getFsIp()+ApplicationConstants.FS_BG_MUSIC_DIR+"/"+nextStatus.getScriptId()+"/"+nextStatus.getCurrentCorpus().getAudioPath().split("/")[nextStatus.getCurrentCorpus().getAudioPath().split("/").length-1];
                    //String content = nextStatus.getCurrentCorpus().getContent();
                    //time11 = System.currentTimeMillis();
                    //dialogRecordService.toMQ(callId,audioFileUrl,0,content,0,0,  nextStatus.getCurrentCorpus().getName(), nextStatus.getCurrentCorpus().getInterruptType(), nextStatus.getCurrentIntentionType(), nextStatus.getHitExtraPhrase(), null, nextStatus.getHitBranch(),nextStatus.getPhoneDataCache().getTaskName(), System.currentTimeMillis(),nextStatus.getPhoneDataCache().getSpeechCallId());
                    dialogRecordService.toMqNew(callId,0,nextStatus,nextStatus.getCurrentMultiContent().getActiveUnitContents().get(0), map,null);
                    //time22 = System.currentTimeMillis();
                    //log.info(callId+"asrmessage_notify语义不可打断插入dialog耗时："+(time22-time11));
                    Long end = System.currentTimeMillis();
                    //time11 = System.currentTimeMillis();
                    //log.info("===>message2耗时：callId："+callId+",time："+(end-start));
                    nextStatus.getCallStatus().getDialogContentsCache().add(DialogContent.builder()
                            .content(nextStatus.getCurrentMultiContent()
                                    .getActiveUnitContents().get(0)
                                    .getUnitContent().getContent()).fromNumber("0").createTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).build());
                    nextStatus = scriptService.callbackNextStatus(nextStatus);
                    //simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+callId, JSONObject.toJSONString(nextStatus, SerializerFeature.WriteClassName),ApplicationConstants.CALL_USER_STATUS_EXPIRE);
                    commonStatusService.setLocalStatusCache(callId,nextStatus);
                    Map<String,Object> resultMap = messageNotifyResponse(notify,"other",nextStatus,callId);
                    //time22 = System.currentTimeMillis();
                    //log.info(callId+"asrmessage_notify语义不可打断不做任何动作耗时："+(time22-time11));
                    return resultMap;

                }

                if(interruptType.equals(SUPPORT_SEMANTIC_INTERRUPT_NO_REPLY)){
                    //log.info("当前可打断时间："+(currentStatus.getCurrentInterruptContent() == null ? null :currentStatus.getCurrentInterruptContent().getInterruptEndTime()) + ":" + System.currentTimeMillis()/1000);
                    if(currentStatus.getCurrentInterruptContent() != null){
                        if(currentStatus.getCurrentInterruptContent().getInterruptEndTime() > System.currentTimeMillis()/1000){
                            ScriptStatus tempStatus = getCurrentTempStatus(callId, currentStatus, userAnswerContent);
                            ScriptStatus tempStatus1 = scriptService.getNextStatus(tempStatus);
                            //log.info("不可打断时间内，匹配最高优先,是否命中结束语料"+tempStatus1.getIsEnd());
                            //log.info("===>message match highest priority callId："+callId+"，content："+userAnswerContent+"，isEnd"+tempStatus1.getIsEnd()+"，audioPath："+tempStatus1.getCurrentCorpus()==null?null:tempStatus1.getCurrentCorpus().getAudioPath());
                            //log.info("===>message match highest priority callId："+callId);
                            if(tempStatus1.getIsEnd()){
                                interrupt(tempStatus1, callId,map);
                                tempStatus1 = scriptService.callbackNextStatus(tempStatus1);
                                if(isPressureTest.equals("1")){
                                    return SmartivrTestResponse.response("message","end",tempStatus1,callId);
                                }
                                return SmartivrResponse.response("message","end",tempStatus1,callId);
                            }
                            //log.info("走到noop1");
                            //不可打断
                            return SmartivrResponse.response(notify,"noop",null,callId);
                        }
                    }
                }

                if(isPressureTest.equals("1")){
                    return SmartivrTestResponse.response(notify, "resume", currentStatus,callId);
                }
                return SmartivrResponse.response(notify, "resume", currentStatus,callId);
            }else if(interruptType!=null&&(interruptType.equals(SUPPORT_SOUND_INTERRUPT_WITH_REPLY)||interruptType.equals(SUPPORT_SEMANTIC_INTERRUPT_WITH_REPLY))){//发声回复，恢复播音
                if(playstate.equals("false")){
                    try{
                        //time11 = System.currentTimeMillis();
                        nextStatus = getNextStatus(callId, userAnswerContent,false,"");
                        //time22 = System.currentTimeMillis();
                        //log.info(callId+"asrmessage_notify语义可打断匹配凡哥耗时："+(time22-time11));
                    }catch (Exception e){
                        e.printStackTrace();
                        if(isPressureTest.equals("1")){
                            return SmartivrTestResponse.response("enter","exception",null,callId);
                        }
                        return SmartivrResponse.response("enter","exception",null,callId);
                    }
                    //simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_DIALOGUE_CONTENT+callId,"",ApplicationConstants.CALL_DIALOGUE_CONTENT_EXPIRE);
                    //String audioFileUrl = "http://"+nextStatus.getCallStatus().getFsIp()+ApplicationConstants.FS_BG_MUSIC_DIR+"/"+nextStatus.getScriptId()+"/"+nextStatus.getCurrentCorpus().getAudioPath().split("/")[nextStatus.getCurrentCorpus().getAudioPath().split("/").length-1];
                    //String content = nextStatus.getCurrentCorpus().getContent();
                    //dialogRecordService.toMQ(callId,audioFileUrl,0,content,0,0, nextStatus.getCurrentCorpus().getName(), nextStatus.getCurrentCorpus().getInterruptType(), nextStatus.getCurrentIntentionType(), nextStatus.getHitExtraPhrase(), null, nextStatus.getHitBranch(),nextStatus.getPhoneDataCache().getTaskName(), System.currentTimeMillis(),nextStatus.getPhoneDataCache().getSpeechCallId());
                    dialogRecordService.toMqNew(callId,0,nextStatus,nextStatus.getCurrentMultiContent().getActiveUnitContents().get(0), map,null);
                    Long end = System.currentTimeMillis();
                    //log.info("===>message3耗时：callId："+callId+",time："+(end-start));
                    nextStatus.getCallStatus().getDialogContentsCache().add(DialogContent.builder()
                            .content(nextStatus.getCurrentMultiContent()
                                    .getActiveUnitContents().get(0)
                                    .getUnitContent().getContent()).fromNumber("0").createTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).build());

                    nextStatus = scriptService.callbackNextStatus(nextStatus);
                    //simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+callId, JSONObject.toJSONString(nextStatus, SerializerFeature.WriteClassName),ApplicationConstants.CALL_USER_STATUS_EXPIRE);
                    commonStatusService.setLocalStatusCache(callId,nextStatus);
                    return messageNotifyResponse(notify,"other",nextStatus,callId);
                }

                if(interruptType.equals(SUPPORT_SEMANTIC_INTERRUPT_WITH_REPLY)){
                    //log.info("当前可打断时间："+(currentStatus.getCurrentInterruptContent() == null ? null :currentStatus.getCurrentInterruptContent().getInterruptEndTime()) + ":" + System.currentTimeMillis()/1000);
                    if(currentStatus.getCurrentInterruptContent() != null){
                        if(currentStatus.getCurrentInterruptContent().getInterruptEndTime() > System.currentTimeMillis()/1000){
                            ScriptStatus tempStatus = getCurrentTempStatus(callId, currentStatus, userAnswerContent);
                            ScriptStatus tempStatus1 = scriptService.getNextStatus(tempStatus);
                            //log.info("===>message match highest priority callId："+callId+"，content："+userAnswerContent+"，isEnd"+tempStatus1.getIsEnd()+"，audioPath："+tempStatus1.getCurrentCorpus()==null?null:tempStatus1.getCurrentCorpus().getAudioPath());
                            //log.info("===>message match highest priority callId："+callId);
                            //log.info("不可打断时间内，匹配最高优先,是否命中结束语料"+tempStatus1.getIsEnd());
                            if(tempStatus1.getIsEnd()){
                                interrupt(tempStatus1, callId,map);
                                tempStatus1 = scriptService.callbackNextStatus(tempStatus1);
                                if(isPressureTest.equals("1")){
                                    return SmartivrTestResponse.response("message","end",tempStatus1,callId);
                                }
                                return SmartivrResponse.response("message","end",tempStatus1,callId);
                            }
                            //不可打断
                            //log.info("走到noop1");
                            return SmartivrResponse.response(notify,"noop",null,callId);
                        }
                    }
                }


                try{
                    nextStatus = reply(callId, userAnswerContent,map);//TODO 没命中的时候，丢弃
                }catch (Exception e){
                    e.printStackTrace();
                    if(isPressureTest.equals("1")){
                        return SmartivrTestResponse.response("enter","exception",null,callId);
                    }
                    return SmartivrResponse.response("enter","exception",null,callId);
                }

                if(nextStatus.getHitPhrase()){
                    InterruptType interruptTypeReplyHit = null;
                    if(nextStatus.getCurrentCorpus() != null && nextStatus.getCurrentMultiContent() != null
                            && nextStatus.getCurrentMultiContent().getActiveUnitContents() != null
                            && nextStatus.getCurrentMultiContent().getActiveUnitContents().size() > 0) {
                        interruptTypeReplyHit = nextStatus.getCurrentMultiContent().getActiveUnitContents().get(0).getUnitContent().getInterruptType();
                    }
                    if(interruptTypeReplyHit !=null && (interruptTypeReplyHit.equals(SUPPORT_SEMANTIC_INTERRUPT_NO_REPLY)
                            ||interruptTypeReplyHit.equals(SUPPORT_SEMANTIC_INTERRUPT_WITH_REPLY))) {
                        nextStatus.setCurrentInterruptContent(InterruptContent.builder()
                                .allowedInterruptTime(nextStatus.getCurrentMultiContent().getActiveUnitContents().get(0).getUnitContent().getAllowedInterruptTime())
                                .interruptEndTime(nextStatus.getCurrentMultiContent().getActiveUnitContents().get(0).getUnitContent().getAllowedInterruptTime()+System.currentTimeMillis()/1000)
                                .build());
                    }else{
                        nextStatus.setCurrentInterruptContent(null);
                    }
                    Long end = System.currentTimeMillis();
                    //log.info("===>message4耗时：callId："+callId+",time："+(end-start));
                    if(isPressureTest.equals("1")){
                        return messageNotifyResponse("other", "other", nextStatus,callId);
                    }
                    nextStatus.getCallStatus().getDialogContentsCache().add(DialogContent.builder()
                            .content(nextStatus.getCurrentMultiContent()
                                    .getActiveUnitContents().get(0)
                                    .getUnitContent().getContent()).fromNumber("0").createTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).build());
                    nextStatus = scriptService.callbackNextStatus(nextStatus);
                    //simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+callId, JSONObject.toJSONString(nextStatus, SerializerFeature.WriteClassName),ApplicationConstants.CALL_USER_STATUS_EXPIRE);
                    commonStatusService.setLocalStatusCache(callId,nextStatus);
                    return messageNotifyResponse("other", "other", nextStatus,callId);
                }else{
                    Long end = System.currentTimeMillis();
                    //log.info("===>message5耗时：callId："+callId+",time："+(end-start));
                    String playpause = String.valueOf(map.getOrDefault("playpause",""));
                    if(playpause.equals("true")){
                        if(interruptType.equals(SUPPORT_SOUND_INTERRUPT_WITH_REPLY)){//发声打断，直接恢复
                            return SmartivrResponse.response(notify, "resume", currentStatus,callId);
                        }
                    }
                    //String pause = simpleRedisService.getValue(ApplicationConstants.PAUSE_PREFIX+callId);
                    if(LocalCache.pauseCache.getIfPresent(callId) != null){//语义打断，有暂停时恢复播放
                        //simpleRedisService.delete(ApplicationConstants.PAUSE_PREFIX+callId);//暂停
                        LocalCache.pauseCache.invalidate(callId);
                        if(isPressureTest.equals("1")){
                            return SmartivrTestResponse.response(notify, "resume", currentStatus,callId);
                        }
                        return SmartivrResponse.response(notify, "resume", currentStatus,callId);
                    }else{
                        return messageNotifyResponse(notify,"noop",null,callId);
                    }

                }
            }else if(interruptType!=null&&interruptType.equals(CAN_NOT_BE_INTERRUPTED)){
                /*
                if(!simpleRedisService.hasKey(ApplicationConstants.CALL_DIALOGUE_CONTENT+callId)){
                    simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_DIALOGUE_CONTENT+callId,userAnswerContent,ApplicationConstants.CALL_DIALOGUE_CONTENT_EXPIRE);
                }else{
                    String text = simpleRedisService.getValue(ApplicationConstants.CALL_DIALOGUE_CONTENT+callId);
                    text = text+userAnswerContent;
                    simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_DIALOGUE_CONTENT+callId,text,ApplicationConstants.CALL_DIALOGUE_CONTENT_EXPIRE);
                }*/
                //time11 = System.currentTimeMillis();
                ScriptStatus tempStatus = getCurrentTempStatus(callId, currentStatus, userAnswerContent);
                ScriptStatus tempStatus1 = scriptService.getNextStatus(tempStatus);
                //log.info("===>message match highest priority callId："+callId+"，content："+userAnswerContent+"，isEnd"+tempStatus1.getIsEnd()+"，audioPath："+tempStatus1.getCurrentCorpus()==null?null:tempStatus1.getCurrentCorpus().getAudioPath());
                //log.info("===>message match highest priority callId："+callId);
                if(tempStatus1.getIsEnd()){
                    //log.info("最高优先不可打断命中最高优先"+callId+":"+JSON.toJSONString(tempStatus1));

                    interrupt(tempStatus1, callId,map);
                    tempStatus1 = scriptService.callbackNextStatus(tempStatus1);
                    if(isPressureTest.equals("1")){
                        return SmartivrTestResponse.response("message","end",tempStatus1,callId);
                    }
                    return SmartivrResponse.response("message","end",tempStatus1,callId);
                }
                if(playstate.equals("false")){
                    //String allText = simpleRedisService.getValue(ApplicationConstants.CALL_DIALOGUE_CONTENT+callId);
                    try{
                        //time11 = System.currentTimeMillis();
                        nextStatus = getNextStatus(callId, userAnswerContent,false,"");
                        //time22 = System.currentTimeMillis();
                        //log.info(callId+"asrmessage_notify设置redis耗时5555："+(time22-time11));
                    }catch (Exception e){
                        e.printStackTrace();
                        if(isPressureTest.equals("1")){
                            return SmartivrTestResponse.response("enter","exception",null,callId);
                        }
                        return SmartivrResponse.response("enter","exception",null,callId);
                    }
                    //simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_DIALOGUE_CONTENT+callId,"",ApplicationConstants.CALL_DIALOGUE_CONTENT_EXPIRE);
                    //String audioFileUrl = "http://"+nextStatus.getCallStatus().getFsIp()+ApplicationConstants.FS_BG_MUSIC_DIR+"/"+nextStatus.getScriptId()+"/"+nextStatus.getCurrentCorpus().getAudioPath().split("/")[nextStatus.getCurrentCorpus().getAudioPath().split("/").length-1];
                    //String content = nextStatus.getCurrentCorpus().getContent();
                    //dialogRecordService.toMQ(callId,audioFileUrl,0,content,0,0, nextStatus.getCurrentCorpus().getName(), nextStatus.getCurrentCorpus().getInterruptType(), nextStatus.getCurrentIntentionType(), nextStatus.getHitExtraPhrase(), null, nextStatus.getHitBranch(),nextStatus.getPhoneDataCache().getTaskName(), System.currentTimeMillis(),nextStatus.getPhoneDataCache().getSpeechCallId());
                    dialogRecordService.toMqNew(callId,0,nextStatus,nextStatus.getCurrentMultiContent().getActiveUnitContents().get(0), map,null);
                    Long end = System.currentTimeMillis();
                    //log.info("===>message7耗时：callId："+callId+",time："+(end-start));
                    nextStatus.getCallStatus().getDialogContentsCache().add(DialogContent.builder()
                            .content(nextStatus.getCurrentMultiContent()
                                    .getActiveUnitContents().get(0)
                                    .getUnitContent().getContent()).fromNumber("0").createTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).build());
                    nextStatus = scriptService.callbackNextStatus(nextStatus);
                    //simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+callId, JSONObject.toJSONString(nextStatus, SerializerFeature.WriteClassName),ApplicationConstants.CALL_USER_STATUS_EXPIRE);
                    commonStatusService.setLocalStatusCache(callId,nextStatus);
                    return messageNotifyResponse(notify,"other",nextStatus,callId);
                }
                //Long end = System.currentTimeMillis();
                //log.info("===>message8耗时：callId："+callId+",time："+(end-start));
                return messageNotifyResponse(notify,"noop",null,callId);
            }else{

                //Long end = System.currentTimeMillis();
                //log.info("===>message9耗时：callId："+callId+",time："+(end-start));
                return messageNotifyResponse(notify,"noop",null,callId);
            }

        }else if (notify.equals("wait_result")) {//等待结果
            /*
            if(!simpleRedisService.hasKey(String.format(ApplicationConstants.WAIT,callId))){
                return messageNotifyResponse(notify,"noop",null,callId);
            }
            simpleRedisService.delete(String.format(ApplicationConstants.WAIT,callId));
            */
            if(LocalCache.waitCache.getIfPresent(callId) == null){
                return messageNotifyResponse(notify,"noop",null,callId);
            }
            LocalCache.waitCache.invalidate(callId);
            ScriptStatus currentStatus = getCurrentStatus(callId,"playback end","");
            if(map.containsKey("asrstate")){
                String asrstate = String.valueOf(map.get("asrstate"));
                if(asrstate.equals("true")){
                    //log.info("===>用户已经说话：callId："+callId+"");
                    return messageNotifyResponse(notify,"noop",null,callId);
                }
            }
            if(currentStatus.getIsEnd()){
                return messageNotifyResponse(notify,"noop_hangup",null,callId);
            }
            try{
                nextStatus = getNextStatus(callId, "",true,"");
            }catch (Exception e){
                e.printStackTrace();
                if(isPressureTest.equals("1")){
                    return SmartivrTestResponse.response("enter","exception",null,callId);
                }
                return SmartivrResponse.response("enter","exception",null,callId);
            }

            //String audioFileUrl = "http://"+nextStatus.getCallStatus().getFsIp()+ApplicationConstants.FS_BG_MUSIC_DIR+"/"+nextStatus.getScriptId()+"/"+nextStatus.getCurrentCorpus().getAudioPath().split("/")[nextStatus.getCurrentCorpus().getAudioPath().split("/").length-1];
            //String content = nextStatus.getCurrentCorpus().getContent();
            //dialogRecordService.toMQ(callId,audioFileUrl,0,content,0,0, nextStatus.getCurrentCorpus().getName(), nextStatus.getCurrentCorpus().getInterruptType(), nextStatus.getCurrentIntentionType(), nextStatus.getHitExtraPhrase(), null, nextStatus.getHitBranch(),nextStatus.getPhoneDataCache().getTaskName(), System.currentTimeMillis(),nextStatus.getPhoneDataCache().getSpeechCallId());
            dialogRecordService.toMqNew(callId,0,nextStatus,nextStatus.getCurrentMultiContent().getActiveUnitContents().get(0), map,null);
            nextStatus.getCallStatus().getDialogContentsCache().add(DialogContent.builder()
                    .content(nextStatus.getCurrentMultiContent()
                            .getActiveUnitContents().get(0)
                            .getUnitContent().getContent()).fromNumber("0").createTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).build());
            nextStatus = scriptService.callbackNextStatus(nextStatus);
            //simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+callId, JSONObject.toJSONString(nextStatus, SerializerFeature.WriteClassName),ApplicationConstants.CALL_USER_STATUS_EXPIRE);
            commonStatusService.setLocalStatusCache(callId,nextStatus);
            return SmartivrResponse.response(notify,"other",nextStatus,callId);
        }else if(notify.equals("playback_result")){
            //time11 = System.currentTimeMillis();
            ScriptStatus currentStatus = getCurrentStatus(callId,"playback end","");
            //time22 = System.currentTimeMillis();
            //log.info(callId+"playback_result 获取当前用户状态耗时："+(time22-time11));
            String userAnswerContent = String.valueOf(map.get("message")).trim();
            if(userAnswerContent.toLowerCase().contains("playback error")){
                //log.info("===>用户挂断，播放失败场景 耗时：callId："+callId+"");
                return messageNotifyResponse(notify,"noop",null,callId);
            }

            if(map.containsKey("asrstate")){
                String asrstate = String.valueOf(map.get("asrstate"));
                if(asrstate.equals("true")){
                    //log.info("===>用户已经说话：callId："+callId+"");
                    return messageNotifyResponse(notify,"noop",null,callId);
                }
            }


            if(currentStatus.getIsEnd()){
                Long end = System.currentTimeMillis();
                //log.info("===>playback end1 耗时：callId："+callId+",time："+(end-start));
                return messageNotifyResponse(notify,"noop_hangup",null,callId);
            }
            //String text = simpleRedisService.getValue(ApplicationConstants.CALL_DIALOGUE_CONTENT+callId);
            try{
                //time11 = System.currentTimeMillis();
                nextStatus = getNextStatus(callId, "",true,"");
                //time22 = System.currentTimeMillis();
                //log.info(callId+"playback_result 匹配凡哥话术耗时："+(time22-time11));
            }catch (Exception e){
                e.printStackTrace();
                if(isPressureTest.equals("1")){
                    return SmartivrTestResponse.response("enter","exception",null,callId);
                }
                return SmartivrResponse.response("enter","exception",null,callId);
            }

            //simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_DIALOGUE_CONTENT+callId,"",ApplicationConstants.CALL_DIALOGUE_CONTENT_EXPIRE);
            //String audioFileUrl = "http://"+nextStatus.getCallStatus().getFsIp()+ApplicationConstants.FS_BG_MUSIC_DIR+"/"+nextStatus.getScriptId()+"/"+nextStatus.getCurrentCorpus().getAudioPath().split("/")[nextStatus.getCurrentCorpus().getAudioPath().split("/").length-1];
            //String content = nextStatus.getCurrentCorpus().getContent();
            //dialogRecordService.toMQ(callId,audioFileUrl,0,content,0,0, nextStatus.getCurrentCorpus().getName(), nextStatus.getCurrentCorpus().getInterruptType(), nextStatus.getCurrentIntentionType(), nextStatus.getHitExtraPhrase(), null, nextStatus.getHitBranch(),nextStatus.getPhoneDataCache().getTaskName(), System.currentTimeMillis(),nextStatus.getPhoneDataCache().getSpeechCallId());
            dialogRecordService.toMqNew(callId,0,nextStatus,nextStatus.getCurrentMultiContent().getActiveUnitContents().get(0), map,null);
            //Long end = System.currentTimeMillis();
            //log.info("===>playback end2 耗时：callId："+callId+",time："+(end-start));

            if(isPressureTest.equals("1")){
                return SmartivrTestResponse.response(notify,"other",nextStatus,callId);
            }
            nextStatus.getCallStatus().getDialogContentsCache().add(DialogContent.builder()
                    .content(nextStatus.getCurrentMultiContent()
                            .getActiveUnitContents().get(0)
                            .getUnitContent().getContent()).fromNumber("0").createTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).build());
            nextStatus = scriptService.callbackNextStatus(nextStatus);
            //simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+callId, JSONObject.toJSONString(nextStatus, SerializerFeature.WriteClassName),ApplicationConstants.CALL_USER_STATUS_EXPIRE);
            commonStatusService.setLocalStatusCache(callId,nextStatus);
            return SmartivrResponse.response(notify,"other",nextStatus,callId);

        }else if(notify.equals("leave")){
            //time11 = System.currentTimeMillis();
            ScriptStatus currentStatus = getCurrentStatus(callId,"leave","");
            //time22 = System.currentTimeMillis();
            //log.info(callId+"leave 获取当前用户状态耗时 ："+(time22-time11));
            currentStatus.getCallStatus().setCallEndTime(System.currentTimeMillis()/1000);
            String wholeRecordFile = null;
            if(map.containsKey("bridge_fs_ip")){
                wholeRecordFile = "http://"+map.getOrDefault("bridge_fs_ip","").toString().replace(":5060","")+ApplicationConstants.FS_RECORD_FILE_DIR+ApplicationConstants.FS_RECORD_FILE_DIR_WHOLE+"/"+ LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"))+"/"+callId+"."+audioSuffix;
            }else{
                wholeRecordFile = "http://"+currentStatus.getCallStatus().getFsIp()+ApplicationConstants.FS_RECORD_FILE_DIR+ApplicationConstants.FS_RECORD_FILE_DIR_WHOLE+"/"+ LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"))+"/"+callId+"."+audioSuffix;
            }
            CallPhoneProducerMaterial.PhoneDataCache phoneDataCache = currentStatus.getPhoneDataCache();
            currentStatus.getCallStatus().setCallStatus(CallStatusEnum.CALL_SUCCESS.ordinal());//呼叫完成
            Long duration = 0L;
            if(currentStatus.getCallStatus().getCallStartTime()==null || currentStatus.getCallStatus().getCallStartTime()==0){
                LocalDateTime time = LocalDateTime.parse(currentStatus.getCallStatus().getCallOutTime(),DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                Long timestamp = time.toInstant(ZoneOffset.ofHours(8)).toEpochMilli()/1000;
                duration = currentStatus.getCallStatus().getCallEndTime() - timestamp;
            }else{
                duration = currentStatus.getCallStatus().getCallEndTime() - currentStatus.getCallStatus().getCallStartTime();
            }

            currentStatus.getCallStatus().setCallDuration(duration.intValue());

            currentStatus.getCallStatus().setTalkTimeEnd(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            currentStatus.getCallStatus().setIntentionClass(currentStatus.getFinalIntentionType());
            currentStatus.getCallStatus().setIntentionLabelIds(currentStatus.getFinalIntentionLabelIdsString());
            currentStatus.getCallStatus().setIntentionLabels(currentStatus.getFinalIntentionLabelsString());
            /*
            if(currentStatus.getIsEnd()){
                currentStatus.getCallStatus().setWhoHangup(0);
            }else{
                currentStatus.getCallStatus().setWhoHangup(1);
            }
            */
            currentStatus.getCallStatus().setHitAnswerIds(currentStatus.getHitAnswerIds());
            //log.info("===>message user statistics callId："+callId+"，"+currentStatus.getCallStatus().toString());
            //TODO 请求高级标签，获取对应的等级和标签，该过程可能在对话中多次触发
            callRecordService.toMQ(phoneDataCache.getTaskId(),phoneDataCache.getTaskName(),phoneDataCache.getPhone(),phoneDataCache.getLineId(),phoneDataCache.getScriptId(),callId,phoneDataCache.getSpeechCallId(),"",wholeRecordFile,phoneDataCache.getLineCode(),"","",currentStatus.getVersion(),currentStatus.getCallStatus(),currentStatus.getScriptLongId(), JSONObject.toJSONString(currentStatus.getMatchedCorpusIds()), currentStatus.getHitSemanticIdsString(),phoneDataCache.getPlainPhone(), currentStatus.getFinalExtraInfo());
            Long end = System.currentTimeMillis();
            //time22 = System.currentTimeMillis();
            //log.info(callId+"leave 处理耗时 ："+(time22-time11));
            //log.info("===>leave 耗时：callId："+callId+",time："+(end-start));
            if(isPressureTest.equals("1")){
                return SmartivrTestResponse.response(notify,"noop",null,callId);
            }
            return SmartivrResponse.response(notify,"noop",null,callId);
        }else{
            if(isPressureTest.equals("1")){
                return SmartivrTestResponse.response(notify,"noop",null,callId);
            }
            return SmartivrResponse.response(notify,"noop",null,callId);
        }
    }

    private Set<Long> setHitSemanticId(ScriptStatus status, String userContent) {
        if (StringUtils.isNotEmpty(userContent) && !userContent.endsWith(";")) {
            userContent = userContent + ";";
        }
        Map<String, Set<Long>> hitSemanticIdMap = status.getHitSemanticIdMap();
        if (!hitSemanticIdMap.containsKey(userContent)) {
            Set<Long> hitSemanticIds = TriggerUtils.getHitSemanticIds(userContent, status.getSecondIndustryId());
            hitSemanticIdMap.put(userContent, hitSemanticIds);
            return hitSemanticIds;
        } else {
            return hitSemanticIdMap.get(userContent);
        }
    }

    private void interrupt(ScriptStatus tempStatus1, String callId,Map<String,Object> msgNotify) {

        InterruptType interruptTypeTemp = null;
        if(tempStatus1.getCurrentCorpus() != null && tempStatus1.getCurrentMultiContent() != null
                && tempStatus1.getCurrentMultiContent().getActiveUnitContents() != null
                && tempStatus1.getCurrentMultiContent().getActiveUnitContents().size() > 0) {
            interruptTypeTemp = tempStatus1.getCurrentMultiContent().getActiveUnitContents().get(0).getUnitContent().getInterruptType();
        }

        if(interruptTypeTemp !=null && (interruptTypeTemp.equals(SUPPORT_SEMANTIC_INTERRUPT_NO_REPLY)
                ||interruptTypeTemp.equals(SUPPORT_SEMANTIC_INTERRUPT_WITH_REPLY))) {
            tempStatus1.setCurrentInterruptContent(InterruptContent.builder()
                    .allowedInterruptTime(tempStatus1.getCurrentMultiContent().getActiveUnitContents().get(0).getUnitContent().getAllowedInterruptTime())
                    .interruptEndTime(tempStatus1.getCurrentMultiContent().getActiveUnitContents().get(0).getUnitContent().getAllowedInterruptTime()+System.currentTimeMillis()/1000)
                    .build());
        }else{
            tempStatus1.setCurrentInterruptContent(null);
        }

        tempStatus1.getCallStatus().getDialogContentsCache().add(DialogContent.builder()
                .content(tempStatus1.getCurrentMultiContent()
                        .getActiveUnitContents()
                        .get(0).getUnitContent()
                        .getContent()).fromNumber("0").createTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).build());

        //tempStatus1.getMatchedCorpusList().add(tempStatus1.getCurrentCorpus());
        //simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+ callId, JSONObject.toJSONString(tempStatus1, SerializerFeature.WriteClassName),ApplicationConstants.CALL_USER_STATUS_EXPIRE);
        commonStatusService.setLocalStatusCache(callId,tempStatus1);
        //time22 = System.currentTimeMillis();
        //log.info(callId+"asrmessage_notify设置redis耗时3333："+(time22-time11));
        //记录辱骂的机器人录音
        //String audioFileUrl = "http://"+ tempStatus1.getCallStatus().getFsIp()+ApplicationConstants.FS_BG_MUSIC_DIR+"/"+ tempStatus1.getScriptId()+"/"+ tempStatus1.getCurrentCorpus().getAudioPath().split("/")[tempStatus1.getCurrentCorpus().getAudioPath().split("/").length-1];
        //String content = tempStatus1.getCurrentCorpus().getContent();
        //time11 = System.currentTimeMillis();
        //dialogRecordService.toMQ(callId,audioFileUrl,0,content,0,0, tempStatus1.getCurrentCorpus().getName(), tempStatus1.getCurrentCorpus().getInterruptType(), tempStatus1.getCurrentIntentionType(), tempStatus1.getHitExtraPhrase(), null, tempStatus1.getHitBranch(), tempStatus1.getPhoneDataCache().getTaskName(), System.currentTimeMillis(),tempStatus1.getPhoneDataCache().getSpeechCallId());
        dialogRecordService.toMqNew(callId,0,tempStatus1,tempStatus1.getCurrentMultiContent().getActiveUnitContents().get(0), msgNotify,null);
        //time22 = System.currentTimeMillis();
        //log.info(callId+"asrmessage_notify设置redis耗时4444："+(time22-time11));
        //Long end = System.currentTimeMillis();
        //log.info("===>message6耗时：callId："+callId+",time："+(end-start));
    }

    private ScriptStatus getCurrentTempStatus(String callId, ScriptStatus currentStatus, String userAnswerContent) {
        /*
        String jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+ callId);
        ScriptStatus tempStatus = JSONObject.parseObject(jsonString,ScriptStatus.class);
        if(tempStatus == null){
            jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+ callId);
            tempStatus = JSONObject.parseObject(jsonString,ScriptStatus.class);
            if(tempStatus == null) {
                jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS + callId);
                tempStatus = JSONObject.parseObject(jsonString, ScriptStatus.class);
            }
        }
        */
        ScriptStatus tempStatus = commonStatusService.getCurrentStatus(callId);
        //time22 = System.currentTimeMillis();
        //log.info(callId+"asrmessage_notify语义不可打断获取redis缓存耗时："+(time22-time11));

        if(tempStatus == null) {
            tempStatus = new ScriptStatus();
            tempStatus.setScriptId(currentStatus.getScriptId());
            tempStatus.setVersion(currentStatus.getVersion());
            tempStatus.setCallId(callId);
        }
        tempStatus.setUserContent(userAnswerContent.replace(".","").replace("1","").replace("2","").replace("3",""));
        tempStatus.setMatchHighestPriority(true);
        return tempStatus;
    }

    private Map<String,Object> messageNotifyResponse(String notify,String type,ScriptStatus scriptStatus,String callId){
        //simpleRedisService.delete(ApplicationConstants.WHOLE_SENTENCE+callId);
        if(isPressureTest.equals("1")){
            return SmartivrTestResponse.response(notify,type,scriptStatus,callId);//不做任何动作
        }
        return SmartivrResponse.response(notify,type,scriptStatus,callId);//不做任何动作
    }


    private ScriptStatus reply(String callId,String userAnswerContent,Map<String,Object> msgNotify){
        ScriptStatus nextStatus = getNextStatus1(callId,userAnswerContent,false,"");
        if(nextStatus.getHitPhrase()){
            //String audioFileUrl = "http://"+nextStatus.getCallStatus().getFsIp()+ApplicationConstants.FS_BG_MUSIC_DIR+"/"+nextStatus.getScriptId()+"/"+nextStatus.getCurrentCorpus().getAudioPath().split("/")[nextStatus.getCurrentCorpus().getAudioPath().split("/").length-1];
            //String content = nextStatus.getCurrentCorpus().getContent();
            //dialogRecordService.toMQ(callId,audioFileUrl,0,content,0,0, nextStatus.getCurrentCorpus().getName(), nextStatus.getCurrentCorpus().getInterruptType(), nextStatus.getCurrentIntentionType(), nextStatus.getHitExtraPhrase(), null, nextStatus.getHitBranch(),nextStatus.getPhoneDataCache().getTaskName(), System.currentTimeMillis(),nextStatus.getPhoneDataCache().getSpeechCallId());
            dialogRecordService.toMqNew(callId,0,nextStatus,nextStatus.getCurrentMultiContent().getActiveUnitContents().get(0),msgNotify ,null);
        }
        return nextStatus;
    }

    private RobotWavFileService.RobotDialogueData getCurrentDialog(String callid){
        RobotWavFileService.RobotDialogueData oldDialog = JSONObject.parseObject(simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callid),RobotWavFileService.RobotDialogueData.class);
        if(oldDialog == null) {
            oldDialog = new RobotWavFileService.RobotDialogueData();
            oldDialog.setScriptId("masterTree0");
        }
        RobotWavFileService.RobotDialogueData oldDialogClone = (RobotWavFileService.RobotDialogueData) ObjectCloneUtils.clone(oldDialog);
        return oldDialogClone;
    }

    private RobotWavFileService.RobotDialogueData getNewDialog(String callid,String userAnswerContent){
        if(userAnswerContent == null){
            userAnswerContent = "";
        }
        RobotWavFileService.RobotDialogueData oldDialog = JSONObject.parseObject(simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callid),RobotWavFileService.RobotDialogueData.class);
        if(oldDialog == null) {
            oldDialog = new RobotWavFileService.RobotDialogueData();
            oldDialog.setScriptId("masterTree0");
        }
        oldDialog.setUserAnswerContent(userAnswerContent);
        RobotWavFileService.RobotDialogueData oldDialogClone = (RobotWavFileService.RobotDialogueData) ObjectCloneUtils.clone(oldDialog);
        RobotWavFileService.RobotDialogueData newDialog = robotWavFileService.getRobotNextWavFileNew(oldDialogClone);
        simpleRedisService.setValue(ApplicationConstants.CALL_USER_STATUS+callid, JSON.toJSONString(newDialog));
        return newDialog;
    }



    public void cdr(String uuid,String cdr){

        //log.info("===>cdr："+cdr);
        JSONObject jsonObject = JSONObject.parseObject(cdr,JSONObject.class);
        JSONObject cc = (JSONObject)jsonObject.get("variables");
        String hangupCause= cc.getString("hangup_cause");
        String hangupCauseQ850= cc.getString("hangup_cause_q850");
        int answerSec = cc.getIntValue("answersec");
        int billSec = cc.getIntValue("billsec");
        String speechCallId = cc.getString("speech_call_id");

        String sipCallId = null;
        if(cc.containsKey("sip_call_id"))
            sipCallId = cc.getString("sip_call_id");

        int billmsec = cc.getIntValue("billmsec");
        int mduration = cc.getIntValue("mduration");
        int waitmsec = mduration - billmsec;

        String lastApp = "";
        if(cc.containsKey("last_app")){
            lastApp = cc.getString("last_app");
        }
        String sip_hangup_disposition = "";
        if(cc.containsKey("sip_hangup_disposition")){
            sip_hangup_disposition = cc.getString("sip_hangup_disposition");
        }
        Integer whoHangup = 1;
        /*
        if(lastApp.contains("hangup")){
            whoHangup = 0;
        }
        //log.info("返回的cdr-json"+cdr);
        if((uuid.contains("take_over") || uuid.contains("listen_in") || uuid.contains("direct_call")) && whoHangup == 1 && cc.containsKey("bridge_hangup_cause")){
            whoHangup = 2;
        }
         */
        if(sip_hangup_disposition.startsWith("send")){
            whoHangup = 0;
            if(LocalCache.seatObtainTaskCache.getIfPresent(uuid) != null){
                String takeO = LocalCache.seatObtainTaskCache.getIfPresent(uuid).getTakeOverOrListenIn();
                if(takeO!=null && takeO.equals("take_over")){//如果是接管，则直接记录通话记录
                    whoHangup = 2;
                }
            }
        }

        toDB(uuid,hangupCause,cdr,billSec,hangupCauseQ850,waitmsec,whoHangup,billmsec,sipCallId);
    }

    public void toDB(String uuid,String hangupCause,String cdr,int billSec,String hangupCauseQ850,int waitmsec,int whoHangup,int billmsec,String sipCallId){
        String callChannelUniqueId = uuid;
        /*
        String cacheString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callChannelUniqueId);
        ScriptStatus userStatus = JSONObject.parseObject(cacheString,ScriptStatus.class);
        if(userStatus == null){
            try{Thread.sleep(20l);}catch (Exception e){}
            cacheString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callChannelUniqueId);
            userStatus = JSONObject.parseObject(cacheString,ScriptStatus.class);
            if(userStatus == null){
                try{Thread.sleep(20l);}catch (Exception e){}
                cacheString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callChannelUniqueId);
                userStatus = JSONObject.parseObject(cacheString,ScriptStatus.class);
            }
        }
        */
        ScriptStatus userStatus = commonStatusService.getCurrentStatus(callChannelUniqueId);
        simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+callChannelUniqueId, JSONObject.toJSONString(userStatus, SerializerFeature.WriteClassName),ApplicationConstants.CALL_USER_STATUS_EXPIRE);

        if(uuid.endsWith("listen_in")||uuid.endsWith("take_over")||uuid.endsWith("direct_call"))
            if(LocalCache.seatObtainTaskCache.getIfPresent(callChannelUniqueId) != null){
                LocalCache.seatObtainTaskCache.getIfPresent(callChannelUniqueId).setEnd(true);
                if(LocalCache.seatObtainTaskExpire.getIfPresent(callChannelUniqueId) != null){
                    LocalCache.seatObtainTaskExpire.put(callChannelUniqueId,1);
                }
            }

        //log.info("===>缓存字符串："+cacheString);

        if(userStatus == null){
            //DingDingService.dingDingSendMsgException("Redis缓存中没有获取到用户的状态信息，call_id: " + uuid,"缓存字符串：");
            log.error("Exception 当前callId 在redis缓冲中没找到" + uuid+
                    "当前ai-call: "+currentServerIpService.getAiCallFlag()+
                    "当前ip: "+currentServerIpService.getAiCallIp()+
                    "当前port: "+currentServerIpService.getAiCallPort()
            );
            DingDingService.dingDingSendMsgException(
                    "Exception 当前callId 在redis缓冲中没找到" + uuid,
                    "当前ai-call: "+currentServerIpService.getAiCallFlag(),
                    "当前ip: "+currentServerIpService.getAiCallIp(),
                    "当前port: "+currentServerIpService.getAiCallPort());
            return ;
        }

        CallPhoneProducerMaterial.PhoneDataCache userInfo = userStatus.getPhoneDataCache();
        //callService.getCallRecordService().toMQ(phoneDataCache.getTaskId(),phoneDataCache.getTaskName(),phoneDataCache.getPhone(),phoneDataCache.getLineId(),phoneDataCache.getTaskId(),-4,callChannelUniqueId,phoneDataCache.getSpeechCallId(),0L,"当前外呼用户有问题："+returnMsg,null,phoneDataCache.getLineCode());
        userStatus.getCallStatus().setCallDuration(billSec);
        userStatus.getCallStatus().setCallMDuration(billmsec);//通话时长毫秒
        userStatus.getCallStatus().setWaitMsec(waitmsec);//等待时间
        userStatus.getCallStatus().setIsNewRecord("0");//默认记录为非新建
        userStatus.getCallStatus().setWhoHangup(whoHangup);
        Integer callStatus = 0;
        if(hangupCause.contains("NORMAL_CLEARING"))//正常，其他都是非正常
            callStatus = 7;

        if(callStatus==0 && userStatus.getCallStatus().getCallMDuration()>0)
            callStatus =  7;

        if(hangupCause.contains("NORMAL_CLEARING") && billmsec < 100) {//小于100ms的都改成未呼通
            callStatus = 0;
            userStatus.getCallStatus().setCallStatus(0);
        }


        if(callStatus != 7 && userInfo.getSendCallErrorRetryTimes() < sendCallErrorRetryTimes && waitmsec <= sendCallErrorRetryDuration && sendCallErrorSwitch.equals("1")
        && !callChannelUniqueId.endsWith(TaskTypeEnum.LANDING_PAGE.getName())
        ){
            //
            //log.info("===>等待时长少于200ms，推送到送呼失败队列"+ JSON.toJSONString(userInfo));
            userStatus.getCallStatus().setIsNewRecord("1");//记录为新建
            if(StringUtils.isNotEmpty(userInfo.getTaskName()) && !userInfo.getTaskName().contains("话术训练") && !userInfo.getTaskName().contains("人工直呼"))
                //callService.backQueueSendFailure(ChannelManagerService.channelList1.get(0),userInfo);
                //log.info("=====>当前平台："+currentServerIpService.getCurrentPlatform());
                callService.backQueueSendFailure(ChannelManagerService.getRabbitMqChannelByPlatform(currentServerIpService.getCurrentPlatform(), MqTypeEnum.SECOND),userInfo);

        }

        /*
        if(userInfo.getPhone().contains("###fast-recall")){
            userStatus.getCallStatus().setIsNewRecord("1");//记录为新建
            userInfo.setPhone(userInfo.getPhone().replace("###fast-recall",""));
        }
        */

        if(userInfo.getSendCallErrorRetryTimes() >= sendCallErrorRetryTimes && waitmsec <= sendCallErrorRetryDuration && sendCallErrorSwitch.equals("1")){
            hangupCause = "呼叫失败";
        }

        callService.getCallRecordService().toMQCdr(userInfo.getTaskId(),userInfo.getTaskName(),userInfo.getPhone(),
                userInfo.getLineId(),userStatus.getScriptId(),callChannelUniqueId,userInfo.getSpeechCallId(),
                hangupCause,"",userInfo.getLineCode(),hangupCauseQ850,hangupCause,userStatus.getVersion(),
                userStatus.getCallStatus(), userStatus.getScriptLongId(), cdr, userStatus.getHitSemanticIdsString(),userInfo.getPlainPhone(),sipCallId);

    }

}

