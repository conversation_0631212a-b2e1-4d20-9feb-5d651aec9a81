package com.raipeng.aicall.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Maps;
import com.raipeng.aicall.constant.ApplicationConstants;
import com.raipeng.aicall.feign.AiWorkbenchFeign;
import com.raipeng.aicall.feign.DirectCallCallStatusFeign;
import com.raipeng.aicall.feign.LineObtainFeign;
import com.raipeng.aicall.model.FSConfig;
import com.raipeng.aicall.mq.producter.material.CallRecordProducerMaterial;
import com.raipeng.common.entity.callsetting.AvailableSeatQueryParam;
import com.raipeng.common.entity.callsetting.SeatTaskData;
import com.raipeng.aicall.mq.producter.material.CallPhoneProducerMaterial;
import com.raipeng.aicall.repository.FSConfigRepository;
import com.raipeng.aicall.scriptsystem.CallStatus;
import com.raipeng.aicall.scriptsystem.ScriptStatus;
import com.raipeng.aicall.service.els.FsClientData;
import com.raipeng.aicall.service.els.FsEslClient;
import com.raipeng.aicall.utils.SnowFlakeUtil;
import com.raipeng.common.entity.linemanager.CallLineUnit;

import com.raipeng.common.util.StringUtils;
import feign.Request;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.compress.utils.Lists;
import org.freeswitch.esl.client.transport.message.EslMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
@RefreshScope
public class CallThirdControlService {


    @Autowired
    private FSConfigRepository fsConfigRepository;

    @Value("${seat.notice.url:http://**************:8099/man-ai/seat-notice}")
    private String seatAsrUrl;

    @Value("${ai.call.ip}")
    private String aiCallIp;

    @Value("${ai.call.port}")
    private String aiCallPort;

    @Value("${direct.call.kml.ip:***************}")
    private String directCallKmlIp;

    @Value("${direct.call.kml.cmcc.ip:***************}")
    private String directCallKmlCmccIp;

    @Value("${direct.call.kml.cucc.ip:***************}")
    private String directCallKmlCuccIp;

    @Value("${direct.call.current.cxcc:ctcc}")
    private String directCallCurrentCxcc;

    @Value("${direct.call.bridge.kml.address1:cmcc#***************:5060#**************:5060}")
    private String directCallBridgeKmlAddress1;

    @Value("${direct.call.bridge.kml.address2:cucc#***************:5060#**************:5060}")
    private String directCallBridgeKmlAddress2;

    @Autowired
    private KmlSelectionService kmlSelectionService;


    @Autowired
    private AiWorkbenchFeign aiWorkbenchFeign;


    @Autowired
    private SimpleRedisService simpleRedisService;

    @Autowired
    private CurrentServerIpService currentServerIpService;

    @Autowired
    private LineObtainFeign lineObtainFeign;

    public static Map<String,FsClientData> fsClients = Maps.newHashMap();

    @Autowired
    private CallRecordService callRecordService;

    @Autowired
    private DirectCallCallStatusFeign directCallCallStatusFeign;

    @Value("${fetch.rtpip.broadband.operator.url:http://*************:87/api/rtp-ip/broadband-operator}")
    private String fetchRtpipBroadbandOperatorUrl;

    @Value("${bridge.self.define.sip.header:true}")
    private Boolean bridgeSelfDefineSipHeader;

    @Value("${bridge.self.define.sip.port:50035}")
    private String bridgeSelfDefineSipPort;

    /**
     * 接管
     */
   public void takeOver(String callId,String fsIp,String seat,String seatIp,String seatPort){
       Long id = simpleRedisService.increase("seat_call_id_random",1l);
       String callId1 = callId+"_third_user"+"_"+id;
       String args = "{origination_uuid="+callId1+",ignore_early_media=true,origination_caller_id_number="+seat+"}sofia/external/"+seat+"@"+seatIp+":"+seatPort+
               " 'm:`:custom_eavesdrop:"+callId+"`start_asr:^^|150|10000|600|800|0|0|'/home/<USER>/"+ LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"))+
               "'|0|0.200000|'/etc/smartivr.json'|3|'"+"http://"+aiCallIp+":"+aiCallPort+"/man-ai/seat-notice"+"'`eval:${uuid_custom_event("+callId+" stopasr)}`eval:${uuid_break("+callId+")}`eval:${uuid_bridge(${uuid} "+
               callId+")}' inline";

       try{
           takeOrListenOrCall(callId, fsIp, args);
       }catch (Exception e){
           try{
               takeOrListenOrCall(callId, fsIp, args);
           }catch (Exception e1){
               try{
                   takeOrListenOrCall(callId, fsIp, args);
               }catch (Exception e2){
                   DingDingService.dingDingSendMsgException("接管人工失败，请排查"
                           ,"aiCallIp"+currentServerIpService.getAiCallIp()
                           ,"aiCallPort"+currentServerIpService.getAiCallPort()
                           ,"aiCallFlag"+currentServerIpService.getAiCallFlag()
                           ,"涉及fs"+fsIp);
                   e.printStackTrace();
               }
           }
       }
   }

    public void directCallTakeOver(String callId,String fsIp,String phone,CallLineUnit callLineUnit){
        String callId1 = callId+"_third_user";//
        String args = "{origination_uuid="+callId1+",ignore_early_media=true,origination_caller_id_number="+callLineUnit.getMasterCallNumber()+
                ",origination_caller_id_name="+callLineUnit.getMasterCallNumber()+",originate_timeout=5,call_timeout=5}sofia/external/"+
                callLineUnit.getPrefix()+phone+"@"+callLineUnit.getRegisterIp()+":"+callLineUnit.getRegisterPort()+
                " 'm:`:custom_eavesdrop:"+callId+"`start_asr:^^|150|10000|600|800|0|0|'/home/<USER>/"+ LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"))+
                "'|0|0.200000|'/etc/smartivr.json'|3|'"+seatAsrUrl+"'`eval:${uuid_custom_event("+callId+" stopasr)}`eval:${uuid_break("+callId+")}`eval:${uuid_bridge(${uuid} "+
                callId+")}' inline";
        /*
        String args = "{origination_uuid="+callId1+",call_timeout=35,ignore_early_media=true,origination_caller_id_number="+phone+"}sofia/external/"+phone+"@"+"**************"+":"+"5060"+
                " 'm:`:custom_eavesdrop:"+callId+"`start_asr:^^|150|10000|600|800|0|0|'/home/<USER>/"+ LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"))+
                "'|0|0.200000|'/etc/smartivr.json'|3|'"+seatAsrUrl+"'`eval:${uuid_custom_event("+callId+" stopasr)}`eval:${uuid_break("+callId+")}`eval:${uuid_bridge(${uuid} "+
                callId+")}' inline";


         */


        try{
            takeOrListenOrCall(callId, fsIp, args);
        }catch (Exception e){
            try{
                takeOrListenOrCall(callId, fsIp, args);
            }catch (Exception e1){
                try{
                    takeOrListenOrCall(callId, fsIp, args);
                }catch (Exception e2){
                    DingDingService.dingDingSendMsgException("接管人工失败，请排查"
                            ,"aiCallIp"+currentServerIpService.getAiCallIp()
                            ,"aiCallPort"+currentServerIpService.getAiCallPort()
                            ,"aiCallFlag"+currentServerIpService.getAiCallFlag()
                            ,"涉及fs"+fsIp);
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 监听
     */
    public void listenIn(String callId,String fsIp,String seat,String seatIp,String seatPort){
        Long id = simpleRedisService.increase("seat_call_id_random",1l);
        String seatCallId = callId+"_third_user"+"_"+id;
        String args = "{origination_uuid="+seatCallId+",ignore_early_media=true,origination_caller_id_number="+seat+"}sofia/external/"+seat+"@"+seatIp+":"+seatPort+
                " 'm:`:eavesdrop:"+callId+"`start_asr:^^|150|10000|600|800|0|0|'/home/<USER>/"+ LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"))+
                "'|0|0.200000|'/etc/smartivr.json'|3|'"+"http://"+aiCallIp+":"+aiCallPort+"/man-ai/seat-notice"+"'`eval:${uuid_custom_event("+callId+" stopasr)}`eval:${uuid_break("+callId+")}`eval:${uuid_bridge(${uuid} "+
                callId+")}' inline";

        /*
        String args = "{ignore_early_media=true,origination_caller_id_number="+seat+"}sofia/external/"+seat+"@"+seatIp+":"+seatPort+
                " 'm:`:eavesdrop:"+callId+"`start_asr:^^|150|10000|600|800|0|0|'/home/<USER>/"+ LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"))+
                "'|0|0.200000|'/etc/smartivr.json'|3|'"+"http://"+aiCallIp+":"+aiCallPort+"/man-ai/seat-notice"+"'`eval:${uuid_custom_event("+callId+" stopasr)}`eval:${uuid_break("+callId+")}`eval:${uuid_bridge(${uuid} "+
                callId+")}' inline";

         */

        try{
            log.info("转接命令："+args);
            takeOrListenOrCall(callId, fsIp, args);
        }catch (Exception e){
            try{
                takeOrListenOrCall(callId, fsIp, args);
            }catch (Exception e1){
                try{
                    takeOrListenOrCall(callId, fsIp, args);
                }catch (Exception e2){
                    DingDingService.dingDingSendMsgException("接管人工失败，请排查"
                            ,"aiCallIp"+currentServerIpService.getAiCallIp()
                            ,"aiCallPort"+currentServerIpService.getAiCallPort()
                            ,"aiCallFlag"+currentServerIpService.getAiCallFlag()
                            ,"涉及fs"+fsIp);
                    e.printStackTrace();
                }
            }
        }
    }

    public void listenInWithFealCallId(String callId,String fsIp,String seat,String seatIp,String seatPort,String fealCallId){
        Long id = simpleRedisService.increase("seat_call_id_random",1l);
        String seatCallId = callId+"_third_user"+"_"+id;
        String args = "{origination_uuid="+seatCallId+",ignore_early_media=true,origination_caller_id_number="+seat+"}sofia/external/"+seat+"@"+seatIp+":"+seatPort+
                " 'm:`:eavesdrop:"+callId+"`start_asr:^^|150|10000|600|800|0|0|'/home/<USER>/"+ LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"))+
                "'|0|0.200000|'/etc/smartivr.json'|3|'"+"http://"+aiCallIp+":"+aiCallPort+"/man-ai/seat-notice-two/"+fealCallId+"'`eval:${uuid_custom_event("+callId+" stopasr)}`eval:${uuid_break("+callId+")}`eval:${uuid_bridge(${uuid} "+
                callId+")}' inline";

        /*
        String args = "{ignore_early_media=true,origination_caller_id_number="+seat+"}sofia/external/"+seat+"@"+seatIp+":"+seatPort+
                " 'm:`:eavesdrop:"+callId+"`start_asr:^^|150|10000|600|800|0|0|'/home/<USER>/"+ LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"))+
                "'|0|0.200000|'/etc/smartivr.json'|3|'"+"http://"+aiCallIp+":"+aiCallPort+"/man-ai/seat-notice"+"'`eval:${uuid_custom_event("+callId+" stopasr)}`eval:${uuid_break("+callId+")}`eval:${uuid_bridge(${uuid} "+
                callId+")}' inline";

         */

        try{
            log.info("转接命令："+args);
            takeOrListenOrCall(callId, fsIp, args);
        }catch (Exception e){
            try{
                takeOrListenOrCall(callId, fsIp, args);
            }catch (Exception e1){
                try{
                    takeOrListenOrCall(callId, fsIp, args);
                }catch (Exception e2){
                    DingDingService.dingDingSendMsgException("接管人工失败，请排查"
                            ,"aiCallIp"+currentServerIpService.getAiCallIp()
                            ,"aiCallPort"+currentServerIpService.getAiCallPort()
                            ,"aiCallFlag"+currentServerIpService.getAiCallFlag()
                            ,"涉及fs"+fsIp);
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 呼叫
     */
    public String call(String fsIp,String user){
        String callId = getCallIdBySnow("direct_call");
        String args = "{origination_uuid="+callId+"}user/"+user+" 50005 XML default";
        try{
            takeOrListenOrCall(callId, fsIp, args);
        }catch (Exception e){
            try{
                takeOrListenOrCall(callId, fsIp, args);
            }catch (Exception e1){
                try{
                    takeOrListenOrCall(callId, fsIp, args);
                }catch (Exception e2){
                    DingDingService.dingDingSendMsgException("接管人工失败，请排查"
                            ,"aiCallIp"+currentServerIpService.getAiCallIp()
                            ,"aiCallPort"+currentServerIpService.getAiCallPort()
                            ,"aiCallFlag"+currentServerIpService.getAiCallFlag()
                            ,"涉及fs"+fsIp);
                    e.printStackTrace();
                }
            }
        }

        return callId;
    }


    public String directCall(Map<String, String> map){
        log.info("人工直呼请求参数："+JSON.toJSONString(map));
        String phone = map.getOrDefault("phone","");//密文
        String plainPhone = map.getOrDefault("plainPhone","");//明文
        String seatId =  map.getOrDefault("seatId","");//坐席
        String seatTeamId =  map.getOrDefault("seatTeamId","");//坐席组

        String callId = getCallIdBySnow("direct_call");
        String speechCallId = map.getOrDefault("speechCallId","");//call_record 里面的record_id字段
        String groupId = map.getOrDefault("groupId","");//call_record 里面的record_id字段
        String dialingHistoryString = map.get("dialingHistory");
        String callingHistoryString = map.get("callingHistory");

        AvailableSeatQueryParam param = new AvailableSeatQueryParam();
        param.setCallId(callId);
        param.setTaskId(null);
        param.setPhone(phone);
        param.setAiCallIp(currentServerIpService.getAiCallIp());
        param.setAiCallPort(currentServerIpService.getAiCallPort());
        param.setPlainPhone(plainPhone);
        param.setSpeechCallId(speechCallId);
        param.setCallTeamHandleType("direct_call");
        param.setSeatId(seatId);
        param.setSeatTeamId(seatTeamId);
        Request.Options options = new Request.Options(5L, TimeUnit.SECONDS, 5L, TimeUnit.SECONDS, true);
        SeatTaskData seatTaskData = aiWorkbenchFeign.getOneAvailableCallSeat(param, options);
        log.info("====>获取坐席信息返回接口"+ JSON.toJSONString(seatTaskData));
        String merchantLineId = null;
        String merchantLineCode = null;
        if(seatTaskData != null){
            merchantLineId = seatTaskData.getMerchantLineId();
            merchantLineCode = seatTaskData.getMerchantLineCode();
        }else{
            log.info("====>error_当前坐席忙，没有获取到坐席信息"+ JSON.toJSONString(seatTaskData));
            return "error_当前坐席忙，没有获取到坐席信息";
        }


        String province = map.getOrDefault("province","");//省
        String provinceCode = map.getOrDefault("provinceCode","");//省
        String city = map.getOrDefault("city","");//市
        String cityCode = map.getOrDefault("cityCode","");//市
        String operator = map.getOrDefault("operator","");//运营商
        String lineId = map.getOrDefault("lineId","");//线路id
        String lineCode = map.getOrDefault("lineCode","");//
        String startTime = map.getOrDefault("startTime","");//
        String endTime = map.getOrDefault("endTime","");//

        //根据坐席组去找线路

        String fsIp = seatTaskData.getFsIp();
        CallPhoneProducerMaterial data = new CallPhoneProducerMaterial();
        List<CallPhoneProducerMaterial.PhoneData> phoneEleDataList = Lists.newArrayList();
        data.setData(phoneEleDataList);
        data.setDate(LocalDate.now());
        data.setLineId(lineId);
        data.setLineCode(lineCode);
        data.setScriptId(null);
        data.setMerchantLineId(merchantLineId);
        data.setMerchantLineCode(merchantLineCode);
        data.setStartTime(startTime);
        data.setEndTime(endTime);
        data.setTaskName("人工直呼");
        data.setScriptVersion(0);
        data.setScriptLongId(0l);
        data.setTaskId("0");
        data.setLineGroupCode(null);
        data.setGroupId(groupId);
        data.setCallTeamHandleType("direct_call");

        CallPhoneProducerMaterial.PhoneData phoneData = new CallPhoneProducerMaterial.PhoneData();
        phoneEleDataList.add(phoneData);
        phoneData.setOperator(operator);
        phoneData.setProvince(province);
        phoneData.setCity(city);
        phoneData.setCityCode(cityCode);
        phoneData.setSpeechCallId(speechCallId);
        phoneData.setPhone(phone);
        phoneData.setPlainPhone(plainPhone);
        phoneData.setCallingHistory(getHistoryMap(callingHistoryString));
        phoneData.setDialingHistory(getHistoryMap(dialingHistoryString));
        phoneData.setSendCallErrorRetryTimes(0);
        phoneData.setHistoryMatchSupplyNumbers(null);
        phoneData.setFirstCallOrReCall("firstCall");
        phoneData.setProvinceCode(provinceCode);
        CallPhoneProducerMaterial.PhoneDataCache phoneDataCache = CallPhoneProducerMaterial.PhoneDataCache.getPhoneDataCache(data,phoneData);
        //TODO 先获取线路资源
        //lineObtainFeign.obtainBundleLines(data);

        //先获取线路资源
        List<CallLineUnit> lineUnits = null;
        //TODO  先注释,需要释放
        try{
            lineUnits = lineObtainFeign.obtainBundleLines(data);
        }catch (Exception e){
            try{
                lineUnits = lineObtainFeign.obtainBundleLines(data);
            }catch (Exception e1){
                try{
                    lineUnits = lineObtainFeign.obtainBundleLines(data);
                }catch (Exception e2){
                    e2.printStackTrace();
                    //DingDingService.dingDingSendMsgException("线路获取异常",aiCallEngineIp.replace("162-",""),e2.getMessage());
                }
            }
        }
        log.info("获取到的线路"+JSON.toJSONString(lineUnits));

        Map<String,CallLineUnit> lineUnitMap = Maps.newHashMap();
        if(lineUnits!=null){
            for (CallLineUnit lineUnit : lineUnits) {
                lineUnitMap.put(lineUnit.getPhone(),lineUnit);
            }
        }
        CallLineUnit callLineUnit = lineUnitMap.get(phone);
        // TODO  先注释,需要释放

        if(!callLineUnit.getStatus().equals(CallLineUnit.CallLineStatus.SUCCESS)){
            //记录
            directCallRouteFailure(callId,speechCallId,null,lineCode,data.getTaskName(),phone,lineId,plainPhone);
            log.info("===》人工直呼坐席路由失败挂机通知"+seatTaskData.getFsUser()+":"+callId+":"+"1");
            Map<String, String> directParam = Maps.newHashMap();
            directParam.put("fsUser",seatTaskData.getFsUser());
            directParam.put("callId",callId);
            directParam.put("callStatus","1");
            directParam.put("cause","路由失败");
            directParam.put("billSec",String.valueOf(0));
            directCallCallStatusFeign.callStatus(directParam);
            log.info("没有获取到线路资源"+JSON.toJSONString(callLineUnit));
            return "error_没有获取到线路资源";
        }

        phoneDataCache.setLineCode(callLineUnit.getSupplyLineName());
        phoneDataCache.setLineId(callLineUnit.getSupplyLineNumber());
        genScriptStatus(callId,fsIp,province,city,callLineUnit.getSupplyLineNumber(),callLineUnit.getSupplyLineName(),merchantLineId,merchantLineCode,seatTaskData,phoneDataCache,callLineUnit,data);


        // 从fsConfig获取实际的KML地址

        FSConfig fsConfig = new FSConfig();
        fsConfig.setKmlAddress(directCallKmlIp);
        fsConfig.setKmlAddressCmcc(directCallKmlCmccIp);
        fsConfig.setKmlAddressCucc(directCallKmlCuccIp);
        fsConfig.setCurrentCxcc(directCallCurrentCxcc);
        fsConfig.setBridgeKmlAddress1(directCallBridgeKmlAddress1);
        fsConfig.setBridgeKmlAddress2(directCallBridgeKmlAddress2);

        String selectedKmlAddress = fsConfig.getKmlAddress();
        String customSipHeaders = "";
        String diaPlan = "50005";
        if(bridgeSelfDefineSipHeader){
            // 根据线路选择KML字段名
            String kmlFieldName = kmlSelectionService.selectKmlAddress(
                    callLineUnit.getSupplyLineNumber()
            );
            // 从fsConfig获取实际的KML地址
            selectedKmlAddress = kmlSelectionService.getKmlAddressFromFsConfig(
                    fsConfig, kmlFieldName
            );
            log.info("选择的KML地址是：" + selectedKmlAddress);
            customSipHeaders = buildCustomSipHeaders(fsConfig,selectedKmlAddress);
            diaPlan = bridgeSelfDefineSipPort;
        }

        //String args = "{origination_uuid="+callId+",origination_caller_id_number="+callLineUnit.getMasterCallNumber()+",origination_caller_id_name="+callLineUnit.getMasterCallNumber()+",originate_timeout=120,ignore_early_media=true,sip_sticky_contact=true}"+"sofia/external/"+callLineUnit.getPrefix()+plainPhone+"@"+callLineUnit.getRegisterIp()+":"+callLineUnit.getRegisterPort()+" 50005 XML default";
        String args = "{origination_uuid="+callId+",origination_caller_id_number="+callLineUnit.getMasterCallNumber()+",origination_caller_id_name="+callLineUnit.getMasterCallNumber()+customSipHeaders+",originate_timeout=120,ignore_early_media=true,sip_sticky_contact=true}"+"sofia/external/"+callLineUnit.getRegisterIp()+".."+callLineUnit.getRegisterPort()+"..."+callLineUnit.getPrefix()+plainPhone+"@"+selectedKmlAddress+" "+diaPlan+" XML default";
        //log.info("人工直呼请求FS参数："+args);
        //String args = "{origination_uuid="+callId+"}user/"+phone+" 50005 XML default";
        //String args = "{origination_uuid="+callId+",call_timeout=60}sofia/external/"+seatTaskData.getFsUser()+"@"+seatTaskData.getFsIp()+":"+seatTaskData.getFsPort()+" 50005 XML default";
        try{
            takeOrListenOrCall(callId, fsIp, args);
        }catch (Exception e){
            try{
                takeOrListenOrCall(callId, fsIp, args);
            }catch (Exception e1){
                try{
                    takeOrListenOrCall(callId, fsIp, args);
                }catch (Exception e2){
                    DingDingService.dingDingSendMsgException("人工直呼，请排查");
                    e.printStackTrace();
                }
            }
        }
        return callId;
    }

    /**
     * 构建自定义SIP头参数
     *
     * @param fsConfig FreeSWITCH配置对象
     * @return 自定义SIP头参数字符串，如果没有配置则返回空字符串
     */
    private String buildCustomSipHeaders(FSConfig fsConfig,String kmlAddress) {
        if (fsConfig == null) {
            return "";
        }

        StringBuilder customHeaders = new StringBuilder();

        // 添加桥接KML地址1参数
        if (StringUtils.isNotEmpty(fsConfig.getBridgeKmlAddress1()) &&
                StringUtils.isNotEmpty(fsConfig.getBridgeKmlAddress1().trim())) {
            customHeaders.append(",").append(fsConfig.getBridgeKmlAddress1().trim().substring(0,fsConfig.getBridgeKmlAddress1().trim().indexOf("#"))).append("=").append(kmlAddress+"#"+fsConfig.getBridgeKmlAddress1().trim().replace("cmcc#","").replace("ctcc#","").replace("cucc#",""));
        }

        // 添加桥接KML地址2参数
        if (StringUtils.isNotEmpty(fsConfig.getBridgeKmlAddress2()) &&
                StringUtils.isNotEmpty(fsConfig.getBridgeKmlAddress2().trim())) {
            customHeaders.append(",").append(fsConfig.getBridgeKmlAddress2().trim().substring(0,fsConfig.getBridgeKmlAddress2().trim().indexOf("#"))).append("=").append(kmlAddress+"#"+fsConfig.getBridgeKmlAddress2().trim().replace("cmcc#","").replace("ctcc#","").replace("cucc#",""));
        }

        // 添加桥接FS IP参数
        if (StringUtils.isNotEmpty(fsConfig.getCurrentCxcc()) &&
                StringUtils.isNotEmpty(fsConfig.getCurrentCxcc().trim())) {
            customHeaders.append(",current_cxcc=").append(fsConfig.getCurrentCxcc().trim());
        }

        // 添加获取RTP IP宽带运营商URL参数
        if (StringUtils.isNotEmpty(fetchRtpipBroadbandOperatorUrl) &&
                StringUtils.isNotEmpty(fetchRtpipBroadbandOperatorUrl.trim())) {
            customHeaders.append(",fetch-rtpip-broadband-operator-url=").append(fetchRtpipBroadbandOperatorUrl.trim());
        }

        return customHeaders.toString();
    }


    public String directCallHangup(Map<String, String> map){
        String callId = map.getOrDefault("callId","");//call_record 里面的record_id字段
        String fsUser = map.getOrDefault("fsUser","");
        String fsIp = map.getOrDefault("fsIp","");
        Map<Long,Long> centerIdsMap = Maps.newHashMap();
        centerIdsMap.put(4l,4l);
        centerIdsMap.put(5l,5l);
        centerIdsMap.put(6l,6l);
        if(centerIdsMap.containsKey(currentServerIpService.getDataCenterId())){
            CallEngineService.fsMulClient.get(fsIp).getFsClient().sendSyncApiCommand("uuid_kill", callId);
        }else{
            EslMessage eslMessage = fsClients.computeIfAbsent(fsIp, k->{
                FSConfig fsConfig = fsConfigRepository.findFirstByIp(fsIp);
                //log.info("从数据库中查询到的fs配置信息："+JSON.toJSONString(fsConfig));
                FsEslClient fsClient = new FsEslClient(1,2);
                try {
                    fsClient.connect(fsConfig.getIp(), fsConfig.getPort(), fsConfig.getPwd(), 2);
                }catch (Exception e) {
                    e.printStackTrace();
                    DingDingService.dingDingSendMsgException("fs没有连接上","ip："+fsConfig.getIp()+",+端口："+fsConfig.getPort());
                }
                FsClientData fsClientData = new FsClientData(fsConfig,fsClient,0,0);
                CallEngineService.fsMulClient.put(fsConfig.getIp(),fsClientData);
                return fsClientData;
            }).getFsClient().sendSyncApiCommand("uuid_kill", callId);

            log.info(eslMessage.toString());
        }
        return callId;
    }


    private String genScriptStatus(String callId,String fsIp,String province,String city
            ,String lineId,String lineCode,String merchantLineId,String merchantLineCode,
                                   SeatTaskData seatTaskData,CallPhoneProducerMaterial.PhoneDataCache phoneDataCache,
                                   CallLineUnit callLineUnit,CallPhoneProducerMaterial data){
        //log.info("===>jobApiId："+jobApiId);
        ScriptStatus status = new ScriptStatus();
        status.setTrainPhone(false);
        status.setScriptId(null);
        status.setVersion(0);
        status.setScriptLongId(null);
        status.setUserContent("");
        status.setCallId(callId);
        phoneDataCache.setCallingHistory(null);
        phoneDataCache.setDialingHistory(null);
        status.setPhoneDataCache(phoneDataCache);
        if(status.getCallStatus()==null){
            status.setCallStatus(new CallStatus());
            status.getCallStatus().setCallOutTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            status.getCallStatus().setCallId(callId);
        }
        status.getCallStatus().setFsIp(fsIp);
        status.getCallStatus().setAiCallIp(currentServerIpService.getIp());
        status.getCallStatus().setProvince(province);
        status.getCallStatus().setCity(city);
        status.getCallStatus().setLineId(lineId);
        status.getCallStatus().setLineCode(lineCode);
        status.getCallStatus().setMerchantLineId(merchantLineId);
        status.getCallStatus().setMerchantLineCode(merchantLineCode);
        status.getCallStatus().setCallLineUnit(null);//
        status.getCallStatus().setSeatTaskData(seatTaskData);
        status.getCallStatus().setCallLineUnit(callLineUnit);
        status.getCallStatus().setCallPhoneProducerMaterial(data);
        String jsonString = JSONObject.toJSONString(status, SerializerFeature.WriteClassName);

        /**
         *
         */
        try{
            simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+callId, jsonString,ApplicationConstants.CALL_USER_STATUS_EXPIRE);
        }catch (Exception e){
            try{
                simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+callId, jsonString,ApplicationConstants.CALL_USER_STATUS_EXPIRE);
            }catch (Exception e1){
            }
        }
        if(!simpleRedisService.hasKey(ApplicationConstants.CALL_USER_STATUS+callId)){
            try{
                simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+callId, jsonString,ApplicationConstants.CALL_USER_STATUS_EXPIRE);
            }catch (Exception e2){
                e2.printStackTrace();
            }
            if(!simpleRedisService.hasKey(ApplicationConstants.CALL_USER_STATUS+callId)){
                log.error("===>callId: "+callId+"呼叫的时候，保存用户状态出错");
                DingDingService.dingDingSendMsgException("===>callId: "+callId,"往FS推送数据时候，保存用户状态没有成功");
            }
        }
        return null;
    }

    private String getCallIdBySnow(String flag){
        return SnowFlakeUtil.getSnowFlakeIdTwo()+"_"+flag;
    }

    private void takeOrListenOrCall(String callId, String fsIp, String args) {
        Map<Long,Long> centerIdsMap = Maps.newHashMap();
        centerIdsMap.put(4l,4l);
        centerIdsMap.put(5l,5l);
        centerIdsMap.put(6l,6l);
        if(centerIdsMap.containsKey(currentServerIpService.getDataCenterId())){
            CallEngineService.fsMulClient.get(fsIp).getFsClient().sendAsyncApiCommand("originate", args);
        }else{
            log.info("当前使用的fs："+fsIp);
            fsClients.computeIfAbsent(fsIp, k->{
                FSConfig fsConfig = fsConfigRepository.findFirstByIp(fsIp);
                log.info("从数据库中查询到的fs配置信息："+JSON.toJSONString(fsConfig));
                FsEslClient fsClient = new FsEslClient(1,2);
                try {
                    fsClient.connect(fsConfig.getIp(), fsConfig.getPort(), fsConfig.getPwd(), 2);
                }catch (Exception e) {
                    e.printStackTrace();
                    DingDingService.dingDingSendMsgException("fs没有连接上","ip："+fsConfig.getIp()+",+端口："+fsConfig.getPort());
                }
                FsClientData fsClientData = new FsClientData(fsConfig,fsClient,0,0);
                CallEngineService.fsMulClient.put(fsConfig.getIp(),fsClientData);
                return fsClientData;
            }).getFsClient().sendAsyncApiCommand("originate", args);
        }

    }

    private Map<String, List<LocalDateTime>> getHistoryMap(String historyString) {
        if (historyString == null) {
            return null;
        } else {
            Map<String, List<LocalDateTime>> result = new HashMap<>();
            Map<String, List<LocalDateTime>> listMap = JSONObject.parseObject(historyString, new TypeReference<Map<String, List<LocalDateTime>>>() {
            });
            listMap.forEach((k, v) -> {
                result.put(k, new ArrayList<>(v));
            });
            return result;
        }
    }



    public void directCallRouteFailure(String callId,String speechCallId,String taskId,String lineCode,String taskName,String phone,String lineId,String plainPhone){
        CallRecordProducerMaterial data = new CallRecordProducerMaterial();
        //归还线路
        data.setDate(LocalDate.now());
        data.setCallId(callId);
        data.setSpeechCallId(speechCallId);
        data.setTaskId(taskId);
        data.setLineCode(lineCode);
        data.setTaskName(taskName);
        data.setPhone(phone);
        data.setLineId(lineId);
        data.setCause("路由失败");
        data.setCallDurationSec(0);
        data.setCycleCount(0);
        data.setSayCount(0);
        data.setCallDuration(0);
        data.setCallStatus(6);
        data.setWhoHangup(0);
        data.setUserFullAnswerContent("");
        data.setIntentionClass(null);
        data.setId(SnowFlakeUtil.getSnowFlakeIdTwo());
        data.setWholeAudioFileUrl("");
        data.setScriptLongId(0l);
        data.setIntentionLabelIds("");
        data.setIntentionLabels("");
        data.setCallOutTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        data.setCdr(null);
        data.setIsCdr(0);
        data.setIsNewRecord("0");
        data.setPlainPhone(plainPhone);
        //通知数据speech
        data.setCallStatus(1);

        List<CallRecordProducerMaterial> dataList = Lists.newArrayList();
        data.setCdr(null);//
        dataList.add(data);

        if(callId.endsWith("take_over") ||callId.endsWith("listen_in") || callId.endsWith("direct_call")){
            callRecordService.noticeSpeechDirectCall(dataList);
        }

    }

}
