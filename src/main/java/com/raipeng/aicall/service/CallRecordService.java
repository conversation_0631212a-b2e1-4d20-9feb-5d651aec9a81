package com.raipeng.aicall.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.Channel;
import com.raipeng.aicall.constant.ApplicationConstants;
import com.raipeng.aicall.constant.MqTypeEnum;
import com.raipeng.aicall.constant.RabbitMqConstants;
import com.raipeng.aicall.constant.TaskTypeEnum;
import com.raipeng.aicall.feign.*;
import com.raipeng.aicall.model.CallRecord;
import com.raipeng.aicall.model.CdrRecord;
import com.raipeng.aicall.mq.CdrQueue;
import com.raipeng.aicall.mq.LineReturnQueue;
import com.raipeng.aicall.mq.producter.material.CallRecordProducerMaterial;
import com.raipeng.aicall.mq.producter.material.CallRecordResultNoticeSpeechMaterial;
import com.raipeng.aicall.repository.CallRecordRepository;
import com.raipeng.aicall.scriptsystem.CallStatus;
import com.raipeng.aicall.scriptsystem.ScriptStatus;
import com.raipeng.aicall.utils.MyExceptionUtils;
import com.raipeng.aicall.utils.SnowFlakeUtil;
import com.raipeng.common.entity.linemanager.CallLineUnit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeoutException;

import static com.raipeng.aicall.constant.RabbitMqConstants.*;

@Slf4j
@Service
@RefreshScope
public class CallRecordService {

    @Autowired
    private CallRecordRepository callRecordRepository;

    @Autowired
    private CurrentServerIpService currentServerIpService;

    @Autowired
    @Qualifier("secondRabbitTemplate")
    private RabbitTemplate secondRabbitTemplate;

    @Autowired
    @Qualifier("mainRabbitTemplate")
    private RabbitTemplate mainRabbitTemplate;

    @Value("${cdr.is.insert.database:0}")
    private Integer cdrIsInsertDatabase;

    @Value("${not.cdr.is.insert.database:0}")
    private Integer notCdrIsInsertDatabase;

    @Autowired
    private LineReturnQueue lineReturnQueue;

    @Autowired
    private LineReturnFeign lineReturnFeign;

    @Autowired
    private DirectCallCallStatusFeign directCallCallStatusFeign;

    @Autowired
    private UnCallRecordBatchToUnCallQueueService unCallRecordBatchToUnCallQueueService;

    @Autowired
    private CdrQueue cdrQueue;

    @Autowired
    private CommonStatusService commonStatusService;

    @Value("${audio.suffix:wav}")
    private String audioSuffix;

    @Autowired
    private FeignClientManager feignClientManager;

    public void save(CallRecordProducerMaterial data){
        CallRecord callRecord = new CallRecord();
        BeanUtils.copyProperties(data,callRecord);
        try{
            callRecordRepository.save(callRecord);
        }catch (Exception e){
            e.printStackTrace();
        }

    }

    public void toMQ(String taskId, String taskName, String phone, String lineId, String scriptId,
                     String callId, String speechCallId, String message, String wholeRecordFile,
                     String lineCode, String errorCode, String cause, Integer scriptVersion, CallStatus callStatus,
                     Long scriptLongId, String corpusIds, String hitSemanticIds,String plainPhone, String extraInfo){
        CallRecordProducerMaterial data = new CallRecordProducerMaterial();
        data.setDate(LocalDate.now());
        data.setCallId(callId);
        data.setSpeechCallId(speechCallId);
        data.setTaskId(taskId);
        data.setLineCode(lineCode);
        data.setTaskName(taskName);
        data.setPhone(phone);
        data.setLineId(lineId);
        data.setScriptId(scriptId);
        data.setScriptVersion(scriptVersion);
        data.setErrorCode(errorCode);
        data.setCause(cause);
        data.setCallDurationSec(0);
        data.setCallDurationSec(callStatus.getCallDuration().intValue());
        data.setCycleCount(callStatus.getCycleCount().get());
        data.setSayCount(callStatus.getSayCount().get());
        data.setCallDuration(callStatus.getCallDuration());
        data.setCallStatus(callStatus.getCallStatus());
        data.setWhoHangup(callStatus.getWhoHangup());
        data.setUserFullAnswerContent(callStatus.getUserFullAnswerContent()!=null?callStatus.getUserFullAnswerContent().toString():"");
        data.setIntentionClass(callStatus.getIntentionClass());
        data.setId(SnowFlakeUtil.getSnowFlakeIdTwo());
        data.setWholeAudioFileUrl(wholeRecordFile);
        data.setScriptLongId(scriptLongId);
        data.setIntentionLabelIds(callStatus.getIntentionLabelIds());
        data.setIntentionLabels(callStatus.getIntentionLabels());
        data.setCallOutTime(callStatus.getCallOutTime());
        data.setContactTime(callStatus.getContactTime());
        data.setTalkTimeEnd(callStatus.getTalkTimeEnd());
        data.setTalkTimeStart(callStatus.getTalkTimeStart());
        data.setHitAnswerIds(callStatus.getHitAnswerIds());
        data.setProvince(callStatus.getProvince());
        data.setCity(callStatus.getCity());
        data.setFsIp(callStatus.getFsIp());
        data.setAiCallIp(callStatus.getAiCallIp());
        data.setIsCdr(0);
        data.setLineId(callStatus.getLineId());
        data.setLineCode(callStatus.getLineCode());
        data.setMerchantLineId(callStatus.getMerchantLineId());
        data.setMerchantLineCode(callStatus.getMerchantLineCode());
        data.setIsNewRecord("0");
        data.setCorpusIds(corpusIds);
        data.setHitSemanticIds(hitSemanticIds);
        data.setLineGatewayNumbers(getLineGatewayNumbers(callStatus));
        data.setPlainPhone(plainPhone);
        data.setExtraInfo(extraInfo);
        data.setDialogContentsCache(callStatus.getDialogContentsCache());
        //通知数据给speech-把通知speech迁移到前面
        List<CallRecordProducerMaterial> dataList = Lists.newArrayList();
        dataList.add(data);
        if(callId.endsWith("take_over") ||callId.endsWith("listen_in")){
            noticeSpeechTakeOverOrListenIn(dataList);
        }else if(callId.endsWith("direct_call")){
            noticeSpeechDirectCall(dataList);
        }else if(callId.endsWith(TaskTypeEnum.LANDING_PAGE.getName())){
            //TODO 这里注释，只传cdr记录
            //noticeSpeechLandingPage(dataList);
        }else {
            noticeSpeech(dataList);
        }
        if(notCdrIsInsertDatabase==1){
            toNotCdrMq(data);
        }

        //callRecordProducer.send(data);

    }

    private void toNotCdrMq(CallRecordProducerMaterial data){
        try {
            //这里是获取队列的一条消息
            //参数二：表示此时即使消费者已经收到消息了，RabbitMQ 也不会立马将消息移除，而是等待消费者显式的回复确认信号后，才会将消息删除
            String pushString = JSON.toJSONString(data);
            ChannelManagerService.channelList4.get(0).basicPublish(CALL_RECORD_EXCHANGE,CALL_RECORD_ROUTING, new AMQP.BasicProperties().builder().deliveryMode(2).build(),pushString.getBytes(StandardCharsets.UTF_8));

        } catch (IOException e) {
            try {
                ChannelManagerService.channelList4.get(0).close();
            } catch (IOException e1) {
                throw new RuntimeException(e1);
            } catch (TimeoutException e2) {
                throw new RuntimeException(e2);
            }
            Channel channel = mainRabbitTemplate.getConnectionFactory().createConnection().createChannel(false);
            ChannelManagerService.channelList4.clear();
            ChannelManagerService.channelList4.add(channel);

            e.printStackTrace();
            DingDingService.dingDingSendMsgException("回写sql报错","sfServerId",e.getMessage());
        }
    }


    public void toMQCdr(String taskId, String taskName, String phone, String lineId, String scriptId,
                     String callId, String speechCallId, String message, String wholeRecordFile,
                     String lineCode, String errorCode, String cause, Integer scriptVersion, CallStatus callStatus,
                     Long scriptLongId, String cdr, String hitSemanticIds,String plainPhone,String sipCallId){
        CallRecordProducerMaterial data = new CallRecordProducerMaterial();
        //归还线路
        log.info("call_id:"+callId+",cause:"+cause);
        if(!cause.contains("路由失败")&&!cause.contains("获取线路异常")&&!taskName.contains("话术训练")&&!cause.contains("没有话术")
        && !callId.endsWith(TaskTypeEnum.LANDING_PAGE.getName())
        ){
            //log.info("进入了归还逻辑");
            lineReturnQueue.addLine(callStatus.getCallLineUnit());
        }

        data.setDate(LocalDate.now());
        data.setCallId(callId);
        data.setSpeechCallId(speechCallId);
        data.setTaskId(taskId);
        data.setLineCode(lineCode);
        data.setTaskName(taskName);
        data.setPhone(phone);
        data.setLineId(lineId);
        data.setScriptId(scriptId);
        data.setScriptVersion(scriptVersion);
        data.setErrorCode(errorCode);
        data.setCause(cause);
        data.setCallDurationSec(callStatus.getCallDuration().intValue());
        data.setCycleCount(callStatus.getCycleCount().get());
        data.setSayCount(callStatus.getSayCount().get());
        data.setCallDuration(callStatus.getCallMDuration());
        data.setCallStatus(callStatus.getCallStatus());
        data.setWhoHangup(callStatus.getWhoHangup());
        data.setUserFullAnswerContent(callStatus.getUserFullAnswerContent()!=null?callStatus.getUserFullAnswerContent().toString():"");
        data.setIntentionClass(callStatus.getIntentionClass());
        data.setId(SnowFlakeUtil.getSnowFlakeIdTwo());
        data.setWholeAudioFileUrl(wholeRecordFile);
        data.setScriptLongId(scriptLongId);
        data.setIntentionLabelIds(callStatus.getIntentionLabelIds());
        data.setIntentionLabels(callStatus.getIntentionLabels());
        data.setCallOutTime(callStatus.getCallOutTime());
        data.setContactTime(callStatus.getContactTime());
        data.setTalkTimeEnd(callStatus.getTalkTimeEnd());
        data.setTalkTimeStart(callStatus.getTalkTimeStart());
        data.setHitAnswerIds(callStatus.getHitAnswerIds());
        data.setCdr(cdr);
        data.setProvince(callStatus.getProvince());
        data.setCity(callStatus.getCity());
        data.setFsIp(callStatus.getFsIp());
        data.setAiCallIp(callStatus.getAiCallIp());
        data.setIsCdr(1);
        data.setLineId(callStatus.getLineId());
        data.setLineCode(callStatus.getLineCode());
        data.setMerchantLineId(callStatus.getMerchantLineId());
        data.setMerchantLineCode(callStatus.getMerchantLineCode());
        data.setIsNewRecord(callStatus.getIsNewRecord());
        data.setLineGatewayNumbers(getLineGatewayNumbers(callStatus));
        data.setHitSemanticIds(hitSemanticIds);
        data.setPlainPhone(plainPhone);
        data.setSipCallId(sipCallId);
        data.setDialogContentsCache(callStatus.getDialogContentsCache());
        //通知数据speech
        if(cause.contains("NORMAL_CLEARING") && data.getCallDuration().intValue() >= 100){//正常，其他都是非正常
            data.setCallStatus(7);
        }else if(cause.contains("路由失败")){
            data.setCallStatus(1);
            data.setCallOutTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }else if(cause.contains("获取线路异常")){
            data.setCallStatus(2);
            data.setCallOutTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }else if(cause.contains("没有话术")){
            data.setCallStatus(4);
            data.setCallOutTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }else if(cause.contains("呼叫失败")){
            data.setCallStatus(6);
            data.setCallOutTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }else{
            data.setCallStatus(0);
            if(callStatus.getCallMDuration()>0){
                data.setCallStatus(7);
            }
        }
        if(data.getCallStatus()==0||data.getCallStatus()==7){
            data.setWaitmsec(callStatus.getWaitMsec());
        }else{
            data.setWaitmsec(-10);//区分开
        }

        List<CallRecordProducerMaterial> dataList = Lists.newArrayList();
        //String cdrString = data.getCdr();
        collectCdrValueField(cdr);
        data.setCdr(null);//推送给speech时将cdr置为null
        dataList.add(data);

        if(callId.endsWith("take_over") ||callId.endsWith("listen_in")){
            noticeSpeechTakeOverOrListenIn(dataList);
        }else if(callId.endsWith("direct_call")){
            log.info("===》人工直呼坐席主动挂机通知"+callStatus.getSeatTaskData().getFsUser()+":"+callId+":"+String.valueOf(data.getCallStatus()));
            Map<String, String> param = Maps.newHashMap();
            param.put("fsUser",callStatus.getSeatTaskData().getFsUser());
            param.put("callId",callId);
            param.put("callStatus",String.valueOf(data.getCallStatus()));
            param.put("cause",cause);
            param.put("billSec",String.valueOf(callStatus.getCallDuration().intValue()));
            directCallCallStatusFeign.callStatus(param);
            noticeSpeechDirectCall(dataList);
        }else if(callId.endsWith(TaskTypeEnum.LANDING_PAGE.getName())){
            ScriptStatus currentStatus = commonStatusService.getCurrentStatus(callId);
            String aiLandingCallIp = currentStatus.getPhoneDataCache().getExtParams().getOrDefault("aiLandingCallIp","").toString();
            String aiLandingCallPort = currentStatus.getPhoneDataCache().getExtParams().getOrDefault("aiLandingCallPort","").toString();
            AiLandingCallFeign aiLandingCallFeign = feignClientManager.aiLandingCallFeignMap.computeIfAbsent(aiLandingCallIp+":"+aiLandingCallPort, k->{
                 return FeignClientUtils.build(k, AiLandingCallFeign.class);
            });
            int tryCount = 3;
            while(tryCount > 0){
                try{
                    aiLandingCallFeign.releaseFsUser(data.getFsIp(),data.getPlainPhone(),callId);
                    break;
                }catch (Exception e){
                    --tryCount;
                }
            }
            dataList.get(0).setTalkTimeEnd(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            currentStatus.getCallStatus().setCallEndTime(System.currentTimeMillis()/1000);
            wholeRecordFile = "http://"+currentStatus.getCallStatus().getFsIp()+ ApplicationConstants.FS_RECORD_FILE_DIR+ApplicationConstants.FS_RECORD_FILE_DIR_WHOLE+"/"+ LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"))+"/"+callId+"."+audioSuffix;
            dataList.get(0).setWholeAudioFileUrl(wholeRecordFile);
            dataList.get(0).setIntentionClass(currentStatus.getFinalIntentionType());
            dataList.get(0).setIntentionLabelIds(currentStatus.getFinalIntentionLabelIdsString());
            dataList.get(0).setIntentionLabels(currentStatus.getFinalIntentionLabelsString());
            dataList.get(0).setHitAnswerIds(currentStatus.getHitAnswerIds());
            noticeSpeechLandingPage(dataList);
        }else {
            noticeSpeech(dataList);
        }

        //data.setCdr(cdrString);
        //log.info("===>no city to cdr"+JSON.toJSONString(data));

        if(!cause.contains("路由失败")&&!cause.contains("获取线路异常")&&!taskName.contains("话术训练")&&!cause.contains("没有话术")){
            if(cdrIsInsertDatabase==1){
                toCdrMq(data);
            }
        }
        //callRecordCdrProducer.send(data);

    }

    private void collectCdrValueField(String cdr){
        JSONObject jsonObject = JSONObject.parseObject(cdr,JSONObject.class);
        JSONObject variables = (JSONObject)jsonObject.get("variables");
        String callId = variables.getString("uuid");
        String speechCallId = variables.getString("speech_call_id");
        String masterCallNumber = variables.getString("origination_caller_id_number");//主叫
        String fsIp = variables.getString("local_media_ip");//主叫
        String sipReqUri = variables.getString("sip_req_uri");//请求uri,里面可以直接解析出kml
        String kmlUrl = "";
        String sipIp = "";
        String rtpIp = "";
        if(StringUtils.isNotEmpty(sipReqUri) && sipReqUri.contains("@") && sipReqUri.contains("...")){
            kmlUrl = sipReqUri.substring(sipReqUri.lastIndexOf("@")+1);
            sipIp = sipReqUri.substring(0,sipReqUri.lastIndexOf("..."));
        }
        rtpIp = variables.getString("remote_media_ip");//被叫
        String customerId = variables.containsKey("customerId")?variables.getString("customerId"):"";//被叫
        String hangupCause= variables.getString("hangup_cause");
        int billmsec = variables.getIntValue("billmsec");
        int mduration = variables.getIntValue("mduration");//总时长
        int waitmsec = mduration - billmsec;
        String sipCallId = null;
        if(variables.containsKey("sip_call_id"))
            sipCallId = variables.getString("sip_call_id");

        //媒体包
        Long rtpAudioInPacketCount = variables.getLongValue("rtp_audio_in_packet_count");
        Long rtpAudioInMediaPacketCount = variables.getLongValue("rtp_audio_in_media_packet_count");
        Long rtpAudioOutPacketCount = variables.getLongValue("rtp_audio_out_packet_count");
        Long rtpAudioOutMediaPacketCount = variables.getLongValue("rtp_audio_out_media_packet_count");
        double rtpAudioInQualityPercentage = variables.getDoubleValue("rtp_audio_in_quality_percentage");
        double rtpAudioInMos = variables.getDoubleValue("rtp_audio_in_mos");

        //时间
        Long createdTime = variables.getLongValue("start_uepoch");
        Long progressTime = variables.getLongValue("progress_uepoch");
        Long processMediaTime = variables.getLongValue("progress_media_uepoch");
        Long answerTime = variables.getLongValue("answer_uepoch");
        Long hangupTime = variables.getLongValue("end_uepoch");
        Long ringStartTime = variables.getLongValue("progress_uepoch") + variables.getLongValue("progress_media_uepoch");
        Long ringEndTime = variables.getLongValue("answer_uepoch") != 0l ? variables.getLongValue("answer_uepoch"):variables.getLongValue("end_uepoch");
        CdrRecord cdrRecord = CdrRecord.builder()
                .id(SnowFlakeUtil.getSnowFlakeIdTwo())
                .date(LocalDate.now())
                .fsIp(fsIp)
                .callId(callId)
                .billmsec(billmsec)
                .createdTime(createdTime)
                .customerId(customerId)
                .kmlUrl(kmlUrl)
                .cause(hangupCause)
                .hangupTime(hangupTime)
                .answerTime(answerTime)
                .masterCallNumber(masterCallNumber)
                .mduration(mduration)
                .speechCallId(speechCallId)
                .progressTime(progressTime)
                .processMediaTime(processMediaTime)
                .ringStartTime(ringStartTime)
                .ringEndTime(ringEndTime)
                .rtpAudioInQualityPercentage(rtpAudioInQualityPercentage)
                .rtpAudioInMos(rtpAudioInMos)
                .rtpAudioInPacketCount(rtpAudioInPacketCount)
                .rtpAudioInMediaPacketCount(rtpAudioInMediaPacketCount)
                .rtpAudioOutPacketCount(rtpAudioOutPacketCount)
                .rtpAudioOutMediaPacketCount(rtpAudioOutMediaPacketCount)
                .rtpIp(rtpIp)
                .sipIp(sipIp)
                .sipCallId(sipCallId)
                .waitmsec(waitmsec)
                .build();

        if(ringStartTime == 0l) {
            cdrRecord.setRingDuration(0l);
            cdrRecord.setRingEndTime(0l);
        }else
            cdrRecord.setRingDuration(cdrRecord.getRingEndTime()-cdrRecord.getRingStartTime());

        if(speechCallId != null)
            cdrRecord.setPlatform(speechCallId.split("_")[0]);
        else
            cdrRecord.setPlatform("PT1");
        cdrQueue.add(cdrRecord);

    }



    private void lineReturn(CallLineUnit callLineUnit){
        URI uri = null;
        try{
            uri = new URI("http://"+callLineUnit.getUnitAddress());
        }catch (Exception e){
            e.printStackTrace();
        }
        if(uri!=null){
            try{
                lineReturnFeign.returnOneLine(uri,callLineUnit);
            }catch (Exception e){
                try{
                    lineReturnFeign.returnOneLine(uri,callLineUnit);
                }catch (Exception e1){
                    try{
                        lineReturnFeign.returnOneLine(uri,callLineUnit);
                    }catch (Exception e2){
                        e2.printStackTrace();
                        DingDingService.dingDingSendMsgException("归还线路线路异常","aiCallIp"+currentServerIpService.getIp(),e2.getMessage());
                    }
                }
            }

        }
    }




    public void toMQCdr111(String taskId, String taskName, String phone, String lineId, String scriptId,
                        String callId, String speechCallId, String message, String wholeRecordFile,
                        String lineCode, String errorCode, String cause, Integer scriptVersion, CallStatus callStatus, Long scriptLongId,String cdr){
        CallRecordProducerMaterial data = new CallRecordProducerMaterial();
        //归还线路
        if(!cause.contains("路由失败")&&!cause.contains("获取线路异常")&&!taskName.contains("话术训练")&&!cause.contains("没有话术")&&!cause.contains("没有找到商户线路")&&!cause.contains("呼叫失败")){
            lineReturn(callStatus.getCallLineUnit());
        }

        data.setDate(LocalDate.now());
        data.setCallId(callId);
        data.setSpeechCallId(speechCallId);
        data.setTaskId(taskId);
        data.setLineCode(lineCode);
        data.setTaskName(taskName);
        data.setPhone(phone);
        data.setLineId(lineId);
        data.setScriptId(scriptId);
        data.setScriptVersion(scriptVersion);
        data.setErrorCode(errorCode);
        data.setCause(cause);
        data.setCallDurationSec(callStatus.getCallDuration().intValue());
        data.setCycleCount(callStatus.getCycleCount().get());
        data.setSayCount(callStatus.getSayCount().get());
        data.setCallDuration(callStatus.getCallDuration());
        data.setCallStatus(callStatus.getCallStatus());
        data.setWhoHangup(callStatus.getWhoHangup());
        data.setUserFullAnswerContent(callStatus.getUserFullAnswerContent()!=null?callStatus.getUserFullAnswerContent().toString():"");
        data.setIntentionClass(callStatus.getIntentionClass());
        data.setId(SnowFlakeUtil.getSnowFlakeIdTwo());
        data.setWholeAudioFileUrl(wholeRecordFile);
        data.setScriptLongId(scriptLongId);
        data.setIntentionLabelIds(callStatus.getIntentionLabelIds());
        data.setIntentionLabels(callStatus.getIntentionLabels());
        data.setCallOutTime(callStatus.getCallOutTime());
        data.setContactTime(callStatus.getContactTime());
        data.setTalkTimeEnd(callStatus.getTalkTimeEnd());
        data.setTalkTimeStart(callStatus.getTalkTimeStart());
        data.setHitAnswerIds(callStatus.getHitAnswerIds());
        data.setCdr(cdr);
        data.setProvince(callStatus.getProvince());
        data.setCity(callStatus.getCity());
        data.setFsIp(callStatus.getFsIp());
        data.setAiCallIp(callStatus.getAiCallIp());
        data.setIsCdr(1);
        data.setLineId(callStatus.getLineId());
        data.setLineCode(callStatus.getLineCode());
        data.setMerchantLineId(callStatus.getMerchantLineId());
        data.setMerchantLineCode(callStatus.getMerchantLineCode());
        data.setIsNewRecord(callStatus.getIsNewRecord());
        data.setPlainPhone(callStatus.getPhoneDataCache().getPlainPhone());
        //通知数据speech
        if(cause.contains("NORMAL_CLEARING")){//正常，其他都是非正常
            data.setCallStatus(7);
        }else if(cause.contains("路由失败")){
            data.setCallStatus(1);
            data.setCallOutTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }else if(cause.contains("获取线路异常")){
            data.setCallStatus(2);
            data.setCallOutTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }else if(cause.contains("没有话术")){
            data.setCallStatus(4);
            data.setCallOutTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }else if(cause.contains("没有找到商户线路")){
            data.setCallStatus(5);
            data.setCallOutTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }else if(cause.contains("呼叫失败")){
            data.setCallStatus(6);
            data.setCallOutTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }else{
            data.setCallStatus(0);
        }
        if(data.getCallStatus()==0||data.getCallStatus()==7){
            data.setWaitmsec(callStatus.getWaitMsec());
        }else{
            data.setWaitmsec(-10);//区分开
        }
        //callRecordCdrProducer.send(data);
        List<CallRecordProducerMaterial> dataList = Lists.newArrayList();
        data.setCdr(null);//推送给speech时将cdr置为null
        dataList.add(data);

        if(StringUtils.isNotEmpty(callStatus.getPhoneDataCache().getCallTeamHandleType()) && (callStatus.getPhoneDataCache().getCallTeamHandleType().endsWith("take_over") ||callStatus.getPhoneDataCache().getCallTeamHandleType().endsWith("listen_in"))){
            noticeSpeechTakeOverOrListenIn(dataList);
        }else if(StringUtils.isNotEmpty(callId) && callId.endsWith("direct_call")){
            noticeSpeechDirectCall(dataList);
        }else {
            noticeSpeech(dataList);
        }

    }



    private void toCdrMq(CallRecordProducerMaterial data){
        try {
            //这里是获取队列的一条消息
            //参数二：表示此时即使消费者已经收到消息了，RabbitMQ 也不会立马将消息移除，而是等待消费者显式的回复确认信号后，才会将消息删除
            String pushString = JSON.toJSONString(data);
            ChannelManagerService.channelList3.get(0).basicPublish(CALL_RECORD_CDR_EXCHANGE,CALL_RECORD_CDR_ROUTING, new AMQP.BasicProperties().builder().deliveryMode(2).build(),pushString.getBytes(StandardCharsets.UTF_8));

        } catch (IOException e) {
            try {
                ChannelManagerService.channelList3.get(0).close();
            } catch (IOException e1) {
                throw new RuntimeException(e1);
            } catch (TimeoutException e2) {
                throw new RuntimeException(e2);
            }
            Channel channel = mainRabbitTemplate.getConnectionFactory().createConnection().createChannel(false);
            ChannelManagerService.channelList3.clear();
            ChannelManagerService.channelList3.add(channel);

            e.printStackTrace();
            DingDingService.dingDingSendMsgException("回写sql报错","sfServerId",e.getMessage());
        }
    }


    public void noticeSpeech(List<CallRecordProducerMaterial> dataList){
        if(dataList!=null&&dataList.size()>0){
            CallRecordResultNoticeSpeechMaterial noticeSpeechMaterial = new CallRecordResultNoticeSpeechMaterial();
            noticeSpeechMaterial.setData(dataList);
            try{
                if(dataList.get(0).getCallStatus()==7){
                    //log.info("===>通知结果数据给speech"+JSON.toJSONString(dataList.get(0)));
                    List<Channel> channels = ChannelManagerService.getRabbitMqChannelBySpeechRecordId(dataList.get(0).getSpeechCallId(), MqTypeEnum.SECOND);
                    for (Channel channel : channels) {
                        //log.info("==>循环推送mq"+channel.toString()+":"+JSON.toJSONString(noticeSpeechMaterial));
                        channel.basicPublish(RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_EXCHANGE, RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_ROUTING, null,JSON.toJSONString(noticeSpeechMaterial).getBytes(StandardCharsets.UTF_8));
                    }

                }else{
                    List<Channel> channels = ChannelManagerService.getRabbitMqChannelBySpeechRecordId(dataList.get(0).getSpeechCallId(), MqTypeEnum.SECOND);
                    for (Channel channel : channels) {
                        //log.info("==>循环推送mq"+channel.toString()+":"+JSON.toJSONString(noticeSpeechMaterial));
                        //channel.basicPublish(RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_EXCHANGE, RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_ROUTING, null, JSON.toJSONString(noticeSpeechMaterial).getBytes(StandardCharsets.UTF_8));
                        unCallRecordBatchToUnCallQueueService.add(noticeSpeechMaterial);
                    }
                }
            }catch (Exception e){
                try{
                    if(dataList.get(0).getCallStatus()==7){
                        //log.info("===>通知结果数据给speech"+JSON.toJSONString(dataList.get(0)));
                        List<Channel> channels = ChannelManagerService.getRabbitMqChannelBySpeechRecordId(dataList.get(0).getSpeechCallId(), MqTypeEnum.SECOND);
                        for (Channel channel : channels) {
                            channel.basicPublish(RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_EXCHANGE, RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_ROUTING, null, JSON.toJSONString(noticeSpeechMaterial).getBytes(StandardCharsets.UTF_8));
                        }
                    }else{
                        List<Channel> channels = ChannelManagerService.getRabbitMqChannelBySpeechRecordId(dataList.get(0).getSpeechCallId(), MqTypeEnum.SECOND);
                        for (Channel channel : channels) {
                            //channel.basicPublish(RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_EXCHANGE, RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_ROUTING, null, JSON.toJSONString(noticeSpeechMaterial).getBytes(StandardCharsets.UTF_8));
                            unCallRecordBatchToUnCallQueueService.add(noticeSpeechMaterial);
                        }
                    }

                }catch (Exception e1){
                    e1.printStackTrace();
                    String exceptionString = MyExceptionUtils.getExceptionToString(e1);
                    DingDingService.dingDingSendMsgException("通知结果给speech出错：","fsServerId"+currentServerIpService.getIp(), JSON.toJSONString(exceptionString));
                    try {
                        ChannelManagerService.updateRabbitMqChannelBySpeechRecordId(dataList.get(0).getSpeechCallId(), MqTypeEnum.SECOND);
                    } catch (Exception ex) {
                        throw new RuntimeException(ex);
                    }finally {
                        //ConsumerStartup.setChannel(secondRabbitTemplate.getConnectionFactory().createConnection().createChannel(false));
                        try {
                            if(dataList.get(0).getCallStatus()==7){
                                List<Channel> channels = ChannelManagerService.getRabbitMqChannelBySpeechRecordId(dataList.get(0).getSpeechCallId(), MqTypeEnum.SECOND);
                                for (Channel channel : channels) {
                                    channel.basicPublish(RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_EXCHANGE, RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_ROUTING, null, JSON.toJSONString(noticeSpeechMaterial).getBytes(StandardCharsets.UTF_8));
                                }
                            }else{
                                List<Channel> channels = ChannelManagerService.getRabbitMqChannelBySpeechRecordId(dataList.get(0).getSpeechCallId(), MqTypeEnum.SECOND);
                                for (Channel channel : channels) {
                                    //channel.basicPublish(RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_EXCHANGE, RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_ROUTING, null, JSON.toJSONString(noticeSpeechMaterial).getBytes(StandardCharsets.UTF_8));
                                    unCallRecordBatchToUnCallQueueService.add(noticeSpeechMaterial);
                                }
                            }
                        } catch (IOException ex) {
                            throw new RuntimeException(ex);
                        }
                    }
                }
            }
        }
    }

    public void noticeSpeechLandingPage(List<CallRecordProducerMaterial> dataList){
        if(dataList!=null&&dataList.size()>0){
            CallRecordResultNoticeSpeechMaterial noticeSpeechMaterial = new CallRecordResultNoticeSpeechMaterial();
            noticeSpeechMaterial.setData(dataList);
            try{
                List<Channel> channels = ChannelManagerService.getRabbitMqChannelBySpeechRecordId(dataList.get(0).getSpeechCallId(), MqTypeEnum.SECOND);
                for (Channel channel : channels) {
                    log.info("==>land_page循环推送mq"+channel.toString()+":"+JSON.toJSONString(noticeSpeechMaterial));
                    channel.basicPublish(RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_LANDING_PAGE_EXCHANGE, RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_LANDING_PAGE_ROUTING, null,JSON.toJSONString(noticeSpeechMaterial).getBytes(StandardCharsets.UTF_8));
                }
            }catch (Exception e){
                try{
                    //log.info("===>通知结果数据给speech"+JSON.toJSONString(dataList.get(0)));
                    List<Channel> channels = ChannelManagerService.getRabbitMqChannelBySpeechRecordId(dataList.get(0).getSpeechCallId(), MqTypeEnum.SECOND);
                    for (Channel channel : channels) {
                        channel.basicPublish(RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_LANDING_PAGE_EXCHANGE, RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_LANDING_PAGE_ROUTING, null,JSON.toJSONString(noticeSpeechMaterial).getBytes(StandardCharsets.UTF_8));
                    }

                }catch (Exception e1){
                    e1.printStackTrace();
                    String exceptionString = MyExceptionUtils.getExceptionToString(e1);
                    DingDingService.dingDingSendMsgException("通知结果给speech出错：","fsServerId"+currentServerIpService.getIp(), JSON.toJSONString(exceptionString));
                    try {
                        ChannelManagerService.updateRabbitMqChannelBySpeechRecordId(dataList.get(0).getSpeechCallId(), MqTypeEnum.SECOND);
                    } catch (Exception ex) {
                        throw new RuntimeException(ex);
                    }finally {
                        try {
                             List<Channel> channels = ChannelManagerService.getRabbitMqChannelBySpeechRecordId(dataList.get(0).getSpeechCallId(), MqTypeEnum.SECOND);
                             for (Channel channel : channels) {
                                 channel.basicPublish(RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_LANDING_PAGE_EXCHANGE, RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_LANDING_PAGE_ROUTING, null,JSON.toJSONString(noticeSpeechMaterial).getBytes(StandardCharsets.UTF_8));
                             }
                        } catch (IOException ex) {
                            throw new RuntimeException(ex);
                        }
                    }
                }
            }
        }
    }

    public void noticeSpeechDirectCall(List<CallRecordProducerMaterial> dataList){
        if(dataList!=null&&dataList.size()>0){
            CallRecordResultNoticeSpeechMaterial noticeSpeechMaterial = new CallRecordResultNoticeSpeechMaterial();
            noticeSpeechMaterial.setData(dataList);
            try{
                if(dataList.get(0).getCallStatus()==7){
                    //log.info("===>通知结果数据给speech"+JSON.toJSONString(dataList.get(0)));
                    List<Channel> channels = ChannelManagerService.getRabbitMqChannelBySpeechRecordId(dataList.get(0).getSpeechCallId(), MqTypeEnum.SECOND);
                    for (Channel channel : channels) {
                        channel.basicPublish(RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_DIRECT_CALL_EXCHANGE, RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_DIRECT_CALL_ROUTING, null, JSON.toJSONString(noticeSpeechMaterial).getBytes(StandardCharsets.UTF_8));
                    }
                }else{
                    List<Channel> channels = ChannelManagerService.getRabbitMqChannelBySpeechRecordId(dataList.get(0).getSpeechCallId(), MqTypeEnum.SECOND);
                    for (Channel channel : channels) {
                        channel.basicPublish(RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_DIRECT_CALL_EXCHANGE, RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_DIRECT_CALL_ROUTING, null, JSON.toJSONString(noticeSpeechMaterial).getBytes(StandardCharsets.UTF_8));
                    }
                    //unCallRecordBatchToUnCallQueueService.add1(noticeSpeechMaterial);
                }
            }catch (Exception e){
                try{
                    if(dataList.get(0).getCallStatus()==7){
                        //log.info("===>通知结果数据给speech"+JSON.toJSONString(dataList.get(0)));
                        List<Channel> channels = ChannelManagerService.getRabbitMqChannelBySpeechRecordId(dataList.get(0).getSpeechCallId(), MqTypeEnum.SECOND);
                        for (Channel channel : channels) {
                            channel.basicPublish(RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_DIRECT_CALL_EXCHANGE, RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_DIRECT_CALL_ROUTING, null, JSON.toJSONString(noticeSpeechMaterial).getBytes(StandardCharsets.UTF_8));
                        }
                    }else{
                        List<Channel> channels = ChannelManagerService.getRabbitMqChannelBySpeechRecordId(dataList.get(0).getSpeechCallId(), MqTypeEnum.SECOND);
                        for (Channel channel : channels) {
                            channel.basicPublish(RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_DIRECT_CALL_EXCHANGE, RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_DIRECT_CALL_ROUTING, null, JSON.toJSONString(noticeSpeechMaterial).getBytes(StandardCharsets.UTF_8));
                        }
                        //unCallRecordBatchToUnCallQueueService.add1(noticeSpeechMaterial);
                    }

                }catch (Exception e1){
                    e1.printStackTrace();
                    String exceptionString = MyExceptionUtils.getExceptionToString(e1);
                    DingDingService.dingDingSendMsgException("通知结果给speech出错：","fsServerId"+currentServerIpService.getIp(), JSON.toJSONString(exceptionString));
                    try {
                        //ConsumerStartup.getChannel().close();
                        ChannelManagerService.updateRabbitMqChannelBySpeechRecordId(dataList.get(0).getSpeechCallId(), MqTypeEnum.SECOND);
                    } catch (Exception ex) {
                        throw new RuntimeException(ex);
                    }finally {
                        //ConsumerStartup.setChannel(secondRabbitTemplate.getConnectionFactory().createConnection().createChannel(false));
                        try {
                            if(dataList.get(0).getCallStatus()==7){
                                List<Channel> channels = ChannelManagerService.getRabbitMqChannelBySpeechRecordId(dataList.get(0).getSpeechCallId(), MqTypeEnum.SECOND);
                                for (Channel channel : channels) {
                                    channel.basicPublish(RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_DIRECT_CALL_EXCHANGE, RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_DIRECT_CALL_ROUTING, null, JSON.toJSONString(noticeSpeechMaterial).getBytes(StandardCharsets.UTF_8));
                                }
                            }else{
                                List<Channel> channels = ChannelManagerService.getRabbitMqChannelBySpeechRecordId(dataList.get(0).getSpeechCallId(), MqTypeEnum.SECOND);
                                for (Channel channel : channels) {
                                    channel.basicPublish(RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_DIRECT_CALL_EXCHANGE, RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_DIRECT_CALL_ROUTING, null, JSON.toJSONString(noticeSpeechMaterial).getBytes(StandardCharsets.UTF_8));
                                }
                                //unCallRecordBatchToUnCallQueueService.add1(noticeSpeechMaterial);
                            }
                        } catch (IOException ex) {
                            throw new RuntimeException(ex);
                        }
                    }
                }
            }
        }
    }

    public void noticeSpeechTakeOverOrListenIn(List<CallRecordProducerMaterial> dataList){
        if(dataList!=null&&dataList.size()>0){
            CallRecordResultNoticeSpeechMaterial noticeSpeechMaterial = new CallRecordResultNoticeSpeechMaterial();
            noticeSpeechMaterial.setData(dataList);
            try{
                log.info("通知speech"+JSON.toJSONString(noticeSpeechMaterial));
                if(dataList.get(0).getCallStatus()==7){
                    //ConsumerStartup.getChannel().basicPublish(RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_TAKE_OVER_LISTEN_IN_EXCHANGE, RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_TAKE_OVER_LISTEN_IN_ROUTING, null,JSON.toJSONString(noticeSpeechMaterial).getBytes(StandardCharsets.UTF_8));
                    List<Channel> channels = ChannelManagerService.getRabbitMqChannelBySpeechRecordId(dataList.get(0).getSpeechCallId(), MqTypeEnum.SECOND);
                    for (Channel channel : channels) {
                        channel.basicPublish(RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_TAKE_OVER_LISTEN_IN_EXCHANGE, RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_TAKE_OVER_LISTEN_IN_ROUTING, null, JSON.toJSONString(noticeSpeechMaterial).getBytes(StandardCharsets.UTF_8));
                    }
                }else{
                    //ConsumerStartup.getChannel().basicPublish(RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_TAKE_OVER_LISTEN_IN_EXCHANGE, RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_TAKE_OVER_LISTEN_IN_ROUTING, null,JSON.toJSONString(noticeSpeechMaterial).getBytes(StandardCharsets.UTF_8));
                    //unCallRecordBatchToUnCallQueueService.add2(noticeSpeechMaterial);
                    List<Channel> channels = ChannelManagerService.getRabbitMqChannelBySpeechRecordId(dataList.get(0).getSpeechCallId(), MqTypeEnum.SECOND);
                    for (Channel channel : channels) {
                        channel.basicPublish(RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_TAKE_OVER_LISTEN_IN_EXCHANGE, RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_TAKE_OVER_LISTEN_IN_ROUTING, null, JSON.toJSONString(noticeSpeechMaterial).getBytes(StandardCharsets.UTF_8));
                    }
                }
            }catch (Exception e){
                try{
                    if(dataList.get(0).getCallStatus()==7){
                        //log.info("===>通知结果数据给speech"+JSON.toJSONString(dataList.get(0)));
                        List<Channel> channels = ChannelManagerService.getRabbitMqChannelBySpeechRecordId(dataList.get(0).getSpeechCallId(), MqTypeEnum.SECOND);
                        for (Channel channel : channels) {
                            channel.basicPublish(RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_TAKE_OVER_LISTEN_IN_EXCHANGE, RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_TAKE_OVER_LISTEN_IN_ROUTING, null, JSON.toJSONString(noticeSpeechMaterial).getBytes(StandardCharsets.UTF_8));
                        }
                    }else{
                        List<Channel> channels = ChannelManagerService.getRabbitMqChannelBySpeechRecordId(dataList.get(0).getSpeechCallId(), MqTypeEnum.SECOND);
                        for (Channel channel : channels) {
                            channel.basicPublish(RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_TAKE_OVER_LISTEN_IN_EXCHANGE, RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_TAKE_OVER_LISTEN_IN_ROUTING, null, JSON.toJSONString(noticeSpeechMaterial).getBytes(StandardCharsets.UTF_8));
                        }
                    }

                }catch (Exception e1){
                    e1.printStackTrace();
                    String exceptionString = MyExceptionUtils.getExceptionToString(e1);
                    DingDingService.dingDingSendMsgException("通知结果给speech出错：","fsServerId"+currentServerIpService.getIp(), JSON.toJSONString(exceptionString));
                    try {
                        ChannelManagerService.updateRabbitMqChannelBySpeechRecordId(dataList.get(0).getSpeechCallId(), MqTypeEnum.SECOND);
                    } catch (Exception ex) {
                        throw new RuntimeException(ex);
                    }finally {
                        //ConsumerStartup.setChannel(secondRabbitTemplate.getConnectionFactory().createConnection().createChannel(false));
                        try {
                            if(dataList.get(0).getCallStatus()==7){
                                List<Channel> channels = ChannelManagerService.getRabbitMqChannelBySpeechRecordId(dataList.get(0).getSpeechCallId(), MqTypeEnum.SECOND);
                                for (Channel channel : channels) {
                                    channel.basicPublish(RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_TAKE_OVER_LISTEN_IN_EXCHANGE, RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_TAKE_OVER_LISTEN_IN_ROUTING, null, JSON.toJSONString(noticeSpeechMaterial).getBytes(StandardCharsets.UTF_8));
                                }
                            }else{
                                List<Channel> channels = ChannelManagerService.getRabbitMqChannelBySpeechRecordId(dataList.get(0).getSpeechCallId(), MqTypeEnum.SECOND);
                                for (Channel channel : channels) {
                                    channel.basicPublish(RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_TAKE_OVER_LISTEN_IN_EXCHANGE, RabbitMqConstants.CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_TAKE_OVER_LISTEN_IN_ROUTING, null, JSON.toJSONString(noticeSpeechMaterial).getBytes(StandardCharsets.UTF_8));
                                }
                                //unCallRecordBatchToUnCallQueueService.add2(noticeSpeechMaterial);
                            }
                        } catch (IOException ex) {
                            throw new RuntimeException(ex);
                        }
                    }
                }
            }
        }
    }


    private String getLineGatewayNumbers(CallStatus callStatus) {
        String lineGatewayNumbers = null;
        if (callStatus.getCallLineUnit() != null && callStatus.getCallLineUnit().getLineGatewayNumbers() != null) {
            lineGatewayNumbers = JSONObject.toJSONString(callStatus.getCallLineUnit().getLineGatewayNumbers());
        }
        return lineGatewayNumbers;
    }
}

