package com.raipeng.aicall.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.raipeng.aicall.constant.ApplicationConstants;
import com.raipeng.aicall.constant.LocalCache;
import com.raipeng.aicall.constant.ManAiConstants;
import com.raipeng.aicall.controller.qd.ManAiSmartivrReactResponse;
import com.raipeng.aicall.controller.qd.SmartivrResponse;
import com.raipeng.aicall.controller.qd.SmartivrTestResponse;
import com.raipeng.aicall.feign.SeatFeign;
import com.raipeng.aicall.mq.producter.material.CallPhoneProducerMaterial;
import com.raipeng.aicall.scriptsystem.*;
import com.raipeng.aicall.service.seat.SeatObtainTask;
import com.raipeng.aicall.service.seat.SeatObtainTaskOld;
import com.raipeng.aicall.utils.ObjectCloneUtils;
import com.raipeng.common.entity.script.AiScript;
import com.raipeng.common.entity.script.intention.IntentionLevel;
import com.raipeng.common.entity.script.intention.IntentionTag;
import com.raipeng.common.enums.InterruptType;
import com.raipeng.common.util.CollectionUtil;
import com.raipeng.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.raipeng.common.enums.InterruptType.*;

/**
 * 监管和监听 服务
 */
@Slf4j
@Service
@RefreshScope
public class ListenInOrTakeOverService {

    @Autowired
    private RobotWavFileService robotWavFileService;

    @Autowired
    private SimpleRedisService simpleRedisService;

    @Autowired
    private ScriptService scriptService;

    @Autowired
    private CallRecordService callRecordService;

    @Autowired
    private DialogRecordService dialogRecordService;

    @Autowired
    private CallService callService;

    @Value("${send.call.error.retry.duration:200}")
    private Integer sendCallErrorRetryDuration;

    @Value("${send.call.error.retry.times:3}")
    private Integer sendCallErrorRetryTimes;

    @Value("${send.call.error.switch:0}")
    private String sendCallErrorSwitch;

    @Value("${is.pressure.test:0}")
    private String isPressureTest;

    @Value("${audio.suffix:wav}")
    private String audioSuffix;

    @Autowired
    private SeatFeign seatFeign;

    @Autowired
    private SeatManager seatManager;

    @Autowired
    private CommonStatusService commonStatusService;


    //获取当前状态
    private ScriptStatus getCurrentStatus(String callId,String from,String content){
        /*
        String jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callId);
        ScriptStatus oldStatus = JSONObject.parseObject(jsonString,ScriptStatus.class);
        if(oldStatus == null){
            jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callId);
            oldStatus = JSONObject.parseObject(jsonString,ScriptStatus.class);
            if(oldStatus == null) {
                jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS + callId);
                oldStatus = JSONObject.parseObject(jsonString, ScriptStatus.class);
            }
        }
         */

        ScriptStatus oldStatus = commonStatusService.getCurrentStatus(callId);

        if(oldStatus == null) {
            oldStatus = new ScriptStatus();
            oldStatus.setScriptId("88ed99a1-bca3-4206-be7a-a110ce95e56e");
            oldStatus.setVersion(0);
            oldStatus.setCallId(callId);
            log.error("====>callId："+callId+"没有获取到当前用户状态");
        }
        return oldStatus;
    }

    private ScriptStatus getNextStatus(String callId,String userContent,Boolean isSilence,String type){
        if(userContent == null){
            userContent = "";
        }

        /*
        String jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callId);
        ScriptStatus oldStatus = JSONObject.parseObject(jsonString,ScriptStatus.class);
        if(oldStatus == null){
            jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callId);
            oldStatus = JSONObject.parseObject(jsonString,ScriptStatus.class);
            if(oldStatus == null) {
                jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS + callId);
                oldStatus = JSONObject.parseObject(jsonString, ScriptStatus.class);
            }
        }
         */
        ScriptStatus oldStatus = commonStatusService.getCurrentStatus(callId);

        if(oldStatus == null) {
            oldStatus = new ScriptStatus();
            oldStatus.setScriptId("88ed99a1-bca3-4206-be7a-a110ce95e56e");
            oldStatus.setVersion(0);
        }
        oldStatus.setUserContent(userContent);
        oldStatus.setIsSilence(isSilence);
        //log.info("===>获取话术传参："+JSON.toJSONString(oldStatus));
        ScriptStatus nextStatus = scriptService.getNextStatus(oldStatus);
        if(nextStatus.getIsEnd()){//
            nextStatus.getCallStatus().setWhoHangup(0);
        }
        if(type.equals("enter")){
            if(nextStatus.getCallStatus()==null){
                nextStatus.setCallStatus(new CallStatus());
                nextStatus.getCallStatus().setCallOutTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                nextStatus.getCallStatus().setCallId(callId);
            }
            nextStatus.getCallStatus().setCallStartTime(System.currentTimeMillis()/1000);//呼叫开始时间
            setCallStatus(nextStatus,CallStatusEnum.CALLING,callId,null);
            String talkTimeStart = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            nextStatus.getCallStatus().setContactTime(talkTimeStart);
            nextStatus.getCallStatus().setTalkTimeStart(talkTimeStart);
            nextStatus.getCallStatus().setCallStatus(CallStatusEnum.CALL_SUCCESS.ordinal());//呼叫完成
        }


        //TODO 全局不打断
        //nextStatus.getCurrentCorpus().setInterruptType(CAN_NOT_BE_INTERRUPTED);
//        nextStatus.getMatchedCorpusList().add(nextStatus.getCurrentCorpus());

        InterruptType interruptType = null;
        if(nextStatus.getCurrentCorpus() != null && nextStatus.getCurrentMultiContent() != null
                && nextStatus.getCurrentMultiContent().getActiveUnitContents() != null
                && nextStatus.getCurrentMultiContent().getActiveUnitContents().size() > 0) {
            interruptType = nextStatus.getCurrentMultiContent().getActiveUnitContents().get(0).getUnitContent().getInterruptType();
        }

        if(interruptType !=null && (interruptType.equals(SUPPORT_SEMANTIC_INTERRUPT_NO_REPLY)
                ||interruptType.equals(SUPPORT_SEMANTIC_INTERRUPT_WITH_REPLY))) {
            nextStatus.setCurrentInterruptContent(InterruptContent.builder()
                    .allowedInterruptTime(nextStatus.getCurrentMultiContent().getActiveUnitContents().get(0).getUnitContent().getAllowedInterruptTime())
                    .interruptEndTime(nextStatus.getCurrentMultiContent().getActiveUnitContents().get(0).getUnitContent().getAllowedInterruptTime()+System.currentTimeMillis()/1000)
                    .build());
        }else{
            nextStatus.setCurrentInterruptContent(null);
        }

        return nextStatus;
    }


    private ScriptStatus getNextStatus1(String callId,String userContent,Boolean isSilence,String type){
        if(userContent == null){
            userContent = "";
        }
        /*
        String jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callId);
        ScriptStatus oldStatus = JSONObject.parseObject(jsonString,ScriptStatus.class);
        if(oldStatus == null){
            jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callId);
            oldStatus = JSONObject.parseObject(jsonString,ScriptStatus.class);
            if(oldStatus == null) {
                jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS + callId);
                oldStatus = JSONObject.parseObject(jsonString, ScriptStatus.class);
            }
        }

         */
        ScriptStatus oldStatus = commonStatusService.getCurrentStatus(callId);
        if(oldStatus == null) {
            oldStatus = new ScriptStatus();
        }
        oldStatus.setUserContent(userContent);
        oldStatus.setIsSilence(isSilence);
        ScriptStatus nextStatus = scriptService.getNextStatus(oldStatus);
        if(nextStatus.getIsEnd()){//
            nextStatus.getCallStatus().setWhoHangup(0);
        }

        return nextStatus;
    }

    private ScriptStatus getNextStatusInterrupter(String callId,String userContent,Boolean isSilence,String type){
        if(userContent == null){
            userContent = "";
        }
        //Long time11 = System.currentTimeMillis();
        /*
        String jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callId);
        ScriptStatus oldStatus = JSONObject.parseObject(jsonString,ScriptStatus.class);
        if(oldStatus == null){
            jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callId);
            oldStatus = JSONObject.parseObject(jsonString,ScriptStatus.class);
            if(oldStatus == null) {
                jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS + callId);
                oldStatus = JSONObject.parseObject(jsonString, ScriptStatus.class);
            }
        }
         */

        ScriptStatus oldStatus = commonStatusService.getCurrentStatus(callId);

        setHitSemanticId(oldStatus, userContent);
        oldStatus.setUserContent(userContent);
        oldStatus.setInterruptOperation(true);
        ScriptStatus nextStatus = scriptService.getNextStatus(oldStatus);
        return nextStatus;
    }

    private void setCallStatus(ScriptStatus nextStatus,String callId,String userContent){
        CallStatus callStatus = nextStatus.getCallStatus();
        Long duration = System.currentTimeMillis()/1000 - callStatus.getCallStartTime();
        callStatus.setCallDuration(duration.intValue());
        if(StringUtils.isNotEmpty(userContent))//说话内容为空，不记录用户说话次数
            callStatus.getSayCount().incrementAndGet();
        if(callStatus.getUserFullAnswerContent()==null){
            callStatus.setUserFullAnswerContent(new StringBuilder(""));
        }
        callStatus.getUserFullAnswerContent().append(userContent).append("##");
        //simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+callId, JSONObject.toJSONString(nextStatus, SerializerFeature.WriteClassName),ApplicationConstants.CALL_USER_STATUS_EXPIRE);
        commonStatusService.setLocalStatusCache(callId,nextStatus);
    }

    private void setCallStatus(ScriptStatus nextStatus,CallStatusEnum statusEnum,String callId,String userContent){
        CallStatus callStatus = nextStatus.getCallStatus();
        Long duration = System.currentTimeMillis()/1000 - callStatus.getCallStartTime();
        callStatus.setCallDuration(duration.intValue());
        if(StringUtils.isNotEmpty(userContent))//说话内容为空，不记录用户说话次数
            callStatus.getSayCount().incrementAndGet();
        if(userContent!=null){
            if(callStatus.getUserFullAnswerContent()==null){
                callStatus.setUserFullAnswerContent(new StringBuilder(""));
            }
            callStatus.getUserFullAnswerContent().append(userContent).append("##");
        }
        callStatus.setCallStatus(statusEnum.ordinal());

    }



    public Map<String,Object> dealNoticeMessage(Map<String,Object> map,StringBuilder sb){
        String notify = String.valueOf(map.get("notify"));
        String callId = String.valueOf(map.get("callid"));
        ScriptStatus nextStatus = null;

        if(notify.equals("enter")){
            try{
                //StopWatch startWatch1 = new StopWatch();
                //startWatch1.start();
                nextStatus = getNextStatus(callId,"",false,"enter");
                //startWatch1.stop();
                //sb.append("1："+startWatch1.getTotalTimeMillis()+"ms\n");

                nextStatus.getCallStatus().setCallStartTime(System.currentTimeMillis()/1000);//呼叫开始时间
                CallStatus callStatus = nextStatus.getCallStatus();
                Long duration = System.currentTimeMillis()/1000 - callStatus.getCallStartTime();
                callStatus.setCallDuration(duration.intValue());
                callStatus.setCallStatus(CallStatusEnum.CALLING.ordinal());
                String talkTimeStart = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                nextStatus.getCallStatus().setContactTime(talkTimeStart);
                nextStatus.getCallStatus().setTalkTimeStart(talkTimeStart);
                nextStatus.getCallStatus().setCallStatus(CallStatusEnum.CALL_SUCCESS.ordinal());//呼叫完成
                //simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+callId, JSONObject.toJSONString(nextStatus, SerializerFeature.WriteClassName),ApplicationConstants.CALL_USER_STATUS_EXPIRE);
                commonStatusService.setLocalStatusCache(callId,nextStatus);
                String fsIp = nextStatus.getCallStatus().getFsIp();
                //StopWatch startWatch2= new StopWatch();
                //startWatch2.start();
                /*
                if(nextStatus.getCurrentCorpus().isListenInOrTakeOver()){
                    if(!ManAiConstants.seatObtainGlobleTaskMap.containsKey(callId)){
                        seatManagerOld.addSeatObtainTask(callId,nextStatus.getPhoneDataCache().getTaskId(),nextStatus.getPhoneDataCache().getPhone(),nextStatus.getPhoneDataCache().getPlainPhone(),1000l,LocalDateTime.now().plusMinutes(100l),false,nextStatus.getPhoneDataCache().getCallTeamHandleType(),null,nextStatus.getPhoneDataCache().getSpeechCallId(),fsIp);
                    }
                }
                */
                //log.info("是否是转人工标识1："+callId+nextStatus.getCurrentCorpus().getName()+" : "+nextStatus.getCurrentCorpus().isListenInOrTakeOver());
                if(nextStatus.getCurrentCorpus().isListenInOrTakeOver()){
                    //if(!ManAiConstants.seatObtainGlobleTaskMap.containsKey(callId)){
                    if(LocalCache.seatObtainTaskCache.getIfPresent(callId) == null){
                        //log.info("走到转人工1");
                        seatManager.addSeatObtainTask(callId,nextStatus.getPhoneDataCache().getTaskId(),nextStatus.getPhoneDataCache().getPhone(),nextStatus.getPhoneDataCache().getPlainPhone(),1000l, LocalDateTime.now().plusMinutes(100l),false,nextStatus.getPhoneDataCache().getCallTeamHandleType(),null,nextStatus.getPhoneDataCache().getSpeechCallId(),fsIp,map.getOrDefault("originCallId","").toString(),map.getOrDefault("bridge_fs_ip","").toString());
                    }
                }


                //startWatch2.stop();
                //sb.append("2："+startWatch2.getTotalTimeMillis()+"ms\n");

            }catch (Exception e){
                e.printStackTrace();
                if(isPressureTest.equals("1")){
                    return SmartivrTestResponse.response("enter","exception",null,callId);
                }
                return SmartivrResponse.response("enter","exception",null,callId);
            }
            //String audioFileUrl = "http://"+nextStatus.getCallStatus().getFsIp()+ApplicationConstants.FS_BG_MUSIC_DIR+"/"+nextStatus.getScriptId()+"/"+nextStatus.getCurrentCorpus().getAudioPath().split("/")[nextStatus.getCurrentCorpus().getAudioPath().split("/").length-1];
            //String content = nextStatus.getCurrentCorpus() != null ? nextStatus.getCurrentCorpus().getContent() : "";
            //dialogRecordService.toMQ(callId,audioFileUrl,0,content,0,0, nextStatus.getCurrentCorpus().getName(), nextStatus.getCurrentCorpus().getInterruptType(), nextStatus.getCurrentIntentionType(), null, null,null,nextStatus.getPhoneDataCache().getTaskName(), System.currentTimeMillis(),nextStatus.getPhoneDataCache().getSpeechCallId());
            dialogRecordService.toMqNew(callId, 0, nextStatus, nextStatus.getCurrentMultiContent().getActiveUnitContents().get(0), map, null);
            if(isPressureTest.equals("1")){
                return SmartivrTestResponse.response("enter","enter",nextStatus,callId);
            }
            StopWatch startWatch3= new StopWatch();
            startWatch3.start();
            //log.info("调用callback1");
            nextStatus = scriptService.callbackNextStatus(nextStatus);
            startWatch3.stop();;
            sb.append("3："+startWatch3.getTotalTimeMillis()+"ms\n");
            //simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+callId, JSONObject.toJSONString(nextStatus, SerializerFeature.WriteClassName),ApplicationConstants.CALL_USER_STATUS_EXPIRE);
            commonStatusService.setLocalStatusCache(callId,nextStatus);
            Map<String,Object> resultMap = SmartivrResponse.response("enter","enter",nextStatus,callId);
            return resultMap;

        }else if(notify.equals("bridge_notify")) {//接管通知
            //nextStatus = getCurrentStatus(callId,"enter","");
            //设置呼叫初始值

            StopWatch startWatch4= new StopWatch();
            startWatch4.start();
            SeatObtainTask task = LocalCache.seatObtainTaskCache.getIfPresent(callId);
            if(task != null){
                log.info("桥接成功"+callId);
                task.setIsComplete(true);
                task.setTakeOverOrListenIn("take_over");//已经接管
            }
            startWatch4.stop();
            sb.append("4："+startWatch4.getTotalTimeMillis()+"ms\n");

            Map<String,Object> returnMap = ManAiSmartivrReactResponse.response(notify,"noop",null,callId);
            return returnMap;

        }else if(notify.equals("asrprogress_notify")){//TODO progress里面只能做暂停和恢复播放操作，播放动作只在message中进行
            String userAnswerContent = String.valueOf(map.get("message")).trim();
            String recordfile = String.valueOf(map.get("recordfile"));
            Integer recordms = Integer.valueOf(String.valueOf(map.get("recordms")));
            ScriptStatus currentStatus = getCurrentStatus(callId,"progress",userAnswerContent);
            //dialogRecordService.toMQ(callId,"http://"+currentStatus.getCallStatus().getFsIp()+recordfile,1,userAnswerContent,0,recordms, null, null, null, null, null, null,currentStatus.getPhoneDataCache().getTaskName(), System.currentTimeMillis(),currentStatus.getPhoneDataCache().getSpeechCallId());
            dialogRecordService.toMqNew(callId, 1, currentStatus, currentStatus.getCurrentMultiContent().getActiveUnitContents().get(0), map, userAnswerContent);
            if(currentStatus.getIsEnd()){
                Long end = System.currentTimeMillis();
                //log.info("===>message1耗时：callId："+callId+",time："+(end-start));
                //time11 = System.currentTimeMillis();
                Map<String,Object> resultMap = messageNotifyResponse(notify,"noop_hangup",null,callId);
                //time22 = System.currentTimeMillis();
                //log.info(callId+"asrmessage_notify当前语料是结束语料，不做任何处理耗时："+(time22-time11));
                return resultMap;
            }
            StopWatch startWatch5= new StopWatch();
            startWatch5.start();
            if(LocalCache.seatObtainTaskCache.getIfPresent(callId) != null){
                String takeO = LocalCache.seatObtainTaskCache.getIfPresent(callId).getTakeOverOrListenIn();
                if(takeO!=null && takeO.equals("take_over")){//如果是接管，则直接记录通话记录
                    return SmartivrResponse.response(notify,"noop",null,callId);//如果当前属于
                }
            }

            /*
            if(ManAiConstants.seatObtainGlobleTaskMap.containsKey(callId)){
                String takeO = ManAiConstants.seatObtainGlobleTaskMap.get(callId).getTakeOverOrListenIn();
                if(takeO!=null && takeO.equals("take_over")){//如果是接管，则直接记录通话记录
                    return SmartivrResponse.response(notify,"noop",null,callId);//如果当前属于
                }
            }
             */

            startWatch5.stop();
            sb.append("5："+startWatch5.getTotalTimeMillis()+"ms\n");
            InterruptType interruptType = currentStatus.getCurrentCorpus()==null?null:currentStatus.getCurrentMultiContent().getActiveUnitContents().get(0).getUnitContent().getInterruptType();
            if(interruptType!=null&&(interruptType.equals(SUPPORT_SEMANTIC_INTERRUPT_NO_REPLY)
                    ||interruptType.equals(SUPPORT_SEMANTIC_INTERRUPT_WITH_REPLY))&&userAnswerContent.length()>0) {
                ScriptStatus tempStatus = null;
                try{
                    tempStatus = getNextStatusInterrupter(callId,userAnswerContent,false,"");
                    if(currentStatus.getCurrentInterruptContent() != null){
                        if(currentStatus.getCurrentInterruptContent().getInterruptEndTime() > System.currentTimeMillis()/1000){
                            //不可打断
                            return SmartivrResponse.response(notify,"noop",null,callId);
                        }
                    }

                }catch (Exception e){
                    e.printStackTrace();
                    if(isPressureTest.equals("1")){
                        return SmartivrTestResponse.response("enter","exception",null,callId);
                    }
                    return SmartivrResponse.response("enter","exception",null,callId);
                }

                if(tempStatus.getHitPhrase()){//命中
                    tempStatus.setIsEnd(false);
                    //simpleRedisService.setValueWithExpire(ApplicationConstants.PAUSE_PREFIX+callId,"1",15l);//最多也就5s
                    commonStatusService.setLocalPauseCache(callId);
                    if(isPressureTest.equals("1")){
                        return SmartivrTestResponse.response(notify, "pause", tempStatus,callId);
                    }
                    return SmartivrResponse.response(notify, "pause", tempStatus,callId);

                }
            }
            if(isPressureTest.equals("1")){
                return SmartivrTestResponse.response(notify,"noop",null,callId);
            }
            Map<String,Object> returnMap = SmartivrResponse.response(notify,"noop",null,callId);
            return returnMap;
        }else if(notify.equals("asrmessage_notify")){
            String userAnswerContent = String.valueOf(map.get("message")).trim();
            //time11 = System.currentTimeMillis();
            //simpleRedisService.setValueWithExpire(ApplicationConstants.WHOLE_SENTENCE+callId,userAnswerContent,ApplicationConstants.WHOLE_SENTENCE_EXPIRE);
            //time22 = System.currentTimeMillis();
            //log.info(callId+"asrmessage_notify设置完整一句话到redis耗时："+(time22-time11));
            Integer speakms = Integer.valueOf(String.valueOf(map.get("speakms")));
            String playstate = String.valueOf(map.getOrDefault("playstate",""));
            //time11 = System.currentTimeMillis();
            ScriptStatus currentStatus = getCurrentStatus(callId,"message",userAnswerContent);
            StopWatch startWatch6 = new StopWatch();
            startWatch6.start();
            Set<Long> hitSemanticIds = setHitSemanticId(currentStatus, userAnswerContent);
            startWatch6.stop();
            sb.append("6："+startWatch6.getTotalTimeMillis()+"ms\n");
            setCallStatus(currentStatus,callId,userAnswerContent);
            //dialogRecordService.toMQ(callId,"",2,userAnswerContent,speakms,0, null, null, null , null, hitSemanticIds.stream().map(String::valueOf).collect(Collectors.joining(",")), null,currentStatus.getPhoneDataCache().getTaskName(), System.currentTimeMillis(),currentStatus.getPhoneDataCache().getSpeechCallId());//切片文件通过;号分析得出
            dialogRecordService.toMqNew(callId, 2, currentStatus, currentStatus.getCurrentMultiContent().getActiveUnitContents().get(0), map, userAnswerContent);
            InterruptType interruptType = currentStatus.getCurrentCorpus()==null?null:currentStatus.getCurrentMultiContent().getActiveUnitContents().get(0).getUnitContent().getInterruptType();
            if(StringUtils.isEmpty(userAnswerContent) && !currentStatus.getIsEnd()){
                if (playstate.equals("false")) {//放音结束
                    //wait，产生一个wait事件，并且
                    /*
                    if(!simpleRedisService.hasKey(String.format(ApplicationConstants.WAIT,callId))) {
                        simpleRedisService.setValueWithExpire(String.format(ApplicationConstants.WAIT, callId), "1", ApplicationConstants.WAIT_EXPIRE);
                        return SmartivrResponse.response(notify, "wait", currentStatus, callId);//直接等2s
                    }
                     */

                    if(LocalCache.waitCache.getIfPresent(callId) == null){
                        LocalCache.waitCache.put(callId,1);
                        return SmartivrResponse.response(notify, "wait", currentStatus, callId);//直接等2s
                    }

                }
                return SmartivrResponse.response(notify,"noop",null,callId);
            }
            /*
            if(simpleRedisService.hasKey(String.format(ApplicationConstants.WAIT,callId))) {//到这里，一定是有识别不为空的一句话
                simpleRedisService.delete(String.format(ApplicationConstants.WAIT,callId));
            }
             */
            if(LocalCache.waitCache.getIfPresent(callId) != null){
                LocalCache.waitCache.invalidate(callId);
            }


            StopWatch startWatch7 = new StopWatch();
            startWatch7.start();
            /*
            if(ManAiConstants.seatObtainGlobleTaskMap.containsKey(callId)){
                String takeO = ManAiConstants.seatObtainGlobleTaskMap.get(callId).getTakeOverOrListenIn();
                if(takeO!=null && takeO.equals("take_over")){//如果是接管，则直接记录通话记录
                    return SmartivrResponse.response(notify,"noop",null,callId);//如果当前属于
                }
            }
             */
            if(LocalCache.seatObtainTaskCache.getIfPresent(callId) != null){
                String takeO = LocalCache.seatObtainTaskCache.getIfPresent(callId).getTakeOverOrListenIn();
                if(takeO!=null && takeO.equals("take_over")){//如果是接管，则直接记录通话记录
                    return SmartivrResponse.response(notify,"noop",null,callId);//如果当前属于
                }
            }

            startWatch7.stop();
            sb.append("7："+startWatch7.getTotalTimeMillis()+"ms\n");

            if(currentStatus.getIsEnd()){
                Long end = System.currentTimeMillis();
                //log.info("===>message1耗时：callId："+callId+",time："+(end-start));
                //time11 = System.currentTimeMillis();
                Map<String,Object> resultMap = messageNotifyResponse(notify,"noop_hangup",null,callId);
                //time22 = System.currentTimeMillis();
                //log.info(callId+"asrmessage_notify当前语料是结束语料，不做任何处理耗时："+(time22-time11));
                return resultMap;
            }
            if(interruptType!=null&&(interruptType.equals(SUPPORT_SOUND_INTERRUPT_NO_REPLY)||interruptType.equals(SUPPORT_SEMANTIC_INTERRUPT_NO_REPLY))){//发声不做回复，恢复播音-这里不管原来是什么情况，都恢复一下
                if(playstate.equals("false")){
                    try{
                        nextStatus = getNextStatus(callId, userAnswerContent,false,"");
                        String fsIp = nextStatus.getCallStatus().getFsIp();
                        /*
                        if(nextStatus.getCurrentCorpus().isListenInOrTakeOver()){
                            if(!ManAiConstants.seatObtainGlobleTaskMap.containsKey(callId)){
                                seatManagerOld.addSeatObtainTask(callId,nextStatus.getPhoneDataCache().getTaskId(),nextStatus.getPhoneDataCache().getPhone(),nextStatus.getPhoneDataCache().getPlainPhone(),1000l,LocalDateTime.now().plusMinutes(100l),false,nextStatus.getPhoneDataCache().getCallTeamHandleType(),null,nextStatus.getPhoneDataCache().getSpeechCallId(),fsIp);
                            }
                        }
                         */
                        //log.info("是否是转人工标识2："+callId+nextStatus.getCurrentCorpus().getName()+" : "+nextStatus.getCurrentCorpus().isListenInOrTakeOver());
                        if(nextStatus.getCurrentCorpus().isListenInOrTakeOver()){
                            //if(!ManAiConstants.seatObtainGlobleTaskMap.containsKey(callId)){
                            if(LocalCache.seatObtainTaskCache.getIfPresent(callId) == null){
                                //log.info("走到转人工2");
                                seatManager.addSeatObtainTask(callId,nextStatus.getPhoneDataCache().getTaskId(),nextStatus.getPhoneDataCache().getPhone(),nextStatus.getPhoneDataCache().getPlainPhone(),1000l, LocalDateTime.now().plusMinutes(100l),false,nextStatus.getPhoneDataCache().getCallTeamHandleType(),null,nextStatus.getPhoneDataCache().getSpeechCallId(),fsIp,map.getOrDefault("originCallId","").toString(),map.getOrDefault("bridge_fs_ip","").toString());
                            }
                        }

                    }catch (Exception e){
                        e.printStackTrace();
                        if(isPressureTest.equals("1")){
                            return SmartivrTestResponse.response("enter","exception",null,callId);
                        }
                        return SmartivrResponse.response("enter","exception",null,callId);
                    }
                    //String audioFileUrl = "http://"+nextStatus.getCallStatus().getFsIp()+ApplicationConstants.FS_BG_MUSIC_DIR+"/"+nextStatus.getScriptId()+"/"+nextStatus.getCurrentCorpus().getAudioPath().split("/")[nextStatus.getCurrentCorpus().getAudioPath().split("/").length-1];
                    //String content = nextStatus.getCurrentCorpus().getContent();
                    //dialogRecordService.toMQ(callId,audioFileUrl,0,content,0,0,  nextStatus.getCurrentCorpus().getName(), nextStatus.getCurrentCorpus().getInterruptType(), nextStatus.getCurrentIntentionType(), nextStatus.getHitExtraPhrase(), null, nextStatus.getHitBranch(),nextStatus.getPhoneDataCache().getTaskName(), System.currentTimeMillis(),nextStatus.getPhoneDataCache().getSpeechCallId());
                    dialogRecordService.toMqNew(callId, 0, nextStatus, nextStatus.getCurrentMultiContent().getActiveUnitContents().get(0), map, null);
                    StopWatch startWatch8 = new StopWatch();
                    startWatch8.start();
                    //log.info("调用callback2");
                    nextStatus = scriptService.callbackNextStatus(nextStatus);
                    startWatch8.stop();
                    sb.append("8："+startWatch8.getTotalTimeMillis()+"ms\n");

                    //simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+callId, JSONObject.toJSONString(nextStatus, SerializerFeature.WriteClassName),ApplicationConstants.CALL_USER_STATUS_EXPIRE);
                    commonStatusService.setLocalStatusCache(callId,nextStatus);
                    Map<String,Object> resultMap = messageNotifyResponse(notify,"other",nextStatus,callId);
                    return resultMap;

                }

                if(interruptType.equals(SUPPORT_SEMANTIC_INTERRUPT_NO_REPLY)){
                    if(currentStatus.getCurrentInterruptContent() != null){
                        if(currentStatus.getCurrentInterruptContent().getInterruptEndTime() > System.currentTimeMillis()/1000){
                            ScriptStatus tempStatus = getCurrentTempStatus(callId, currentStatus, userAnswerContent);
                            ScriptStatus tempStatus1 = scriptService.getNextStatus(tempStatus);
                            if(tempStatus1.getIsEnd()){
                                interapter(tempStatus1, callId,map);
                                //log.info("调用callback3");
                                tempStatus1 = scriptService.callbackNextStatus(tempStatus1);
                                if(isPressureTest.equals("1")){
                                    return SmartivrTestResponse.response("message","end",tempStatus1,callId);
                                }
                                return SmartivrResponse.response("message","end",tempStatus1,callId);
                            }
                            //不可打断
                            return SmartivrResponse.response(notify,"noop",null,callId);
                        }
                    }
                }

                if(isPressureTest.equals("1")){
                    return SmartivrTestResponse.response(notify, "resume", currentStatus,callId);
                }
                return SmartivrResponse.response(notify, "resume", currentStatus,callId);
            }else if(interruptType!=null&&(interruptType.equals(SUPPORT_SOUND_INTERRUPT_WITH_REPLY)||interruptType.equals(SUPPORT_SEMANTIC_INTERRUPT_WITH_REPLY))){//发声回复，恢复播音
                if(playstate.equals("false")){
                    try{
                        StopWatch startWatch9 = new StopWatch();
                        startWatch9.start();
                        nextStatus = getNextStatus(callId, userAnswerContent,false,"");
                        startWatch9.stop();
                        sb.append("9："+startWatch9.getTotalTimeMillis()+"ms\n");
                        String fsIp = nextStatus.getCallStatus().getFsIp();
                        /*
                        if(nextStatus.getCurrentCorpus().isListenInOrTakeOver()){
                            if(!ManAiConstants.seatObtainGlobleTaskMap.containsKey(callId)){
                                seatManagerOld.addSeatObtainTask(callId,nextStatus.getPhoneDataCache().getTaskId(),nextStatus.getPhoneDataCache().getPhone(),nextStatus.getPhoneDataCache().getPlainPhone(),1000l,LocalDateTime.now().plusMinutes(100l),false,nextStatus.getPhoneDataCache().getCallTeamHandleType(),null,nextStatus.getPhoneDataCache().getSpeechCallId(),fsIp);
                            }
                        }
                         */
                        //log.info("是否是转人工标识3："+callId+nextStatus.getCurrentCorpus().getName()+" : "+nextStatus.getCurrentCorpus().isListenInOrTakeOver());
                        if(nextStatus.getCurrentCorpus().isListenInOrTakeOver()){
                            //if(!ManAiConstants.seatObtainGlobleTaskMap.containsKey(callId)){
                            if(LocalCache.seatObtainTaskCache.getIfPresent(callId) == null){
                                //log.info("走到转人工3");
                                seatManager.addSeatObtainTask(callId,nextStatus.getPhoneDataCache().getTaskId(),nextStatus.getPhoneDataCache().getPhone(),nextStatus.getPhoneDataCache().getPlainPhone(),1000l, LocalDateTime.now().plusMinutes(100l),false,nextStatus.getPhoneDataCache().getCallTeamHandleType(),null,nextStatus.getPhoneDataCache().getSpeechCallId(),fsIp,map.getOrDefault("originCallId","").toString(),map.getOrDefault("bridge_fs_ip","").toString());
                            }
                        }
                    }catch (Exception e){
                        e.printStackTrace();
                        if(isPressureTest.equals("1")){
                            return SmartivrTestResponse.response("enter","exception",null,callId);
                        }
                        return SmartivrResponse.response("enter","exception",null,callId);
                    }
                    //String audioFileUrl = "http://"+nextStatus.getCallStatus().getFsIp()+ApplicationConstants.FS_BG_MUSIC_DIR+"/"+nextStatus.getScriptId()+"/"+nextStatus.getCurrentCorpus().getAudioPath().split("/")[nextStatus.getCurrentCorpus().getAudioPath().split("/").length-1];
                    //String content = nextStatus.getCurrentCorpus().getContent();
                    //dialogRecordService.toMQ(callId,audioFileUrl,0,content,0,0, nextStatus.getCurrentCorpus().getName(), nextStatus.getCurrentCorpus().getInterruptType(), nextStatus.getCurrentIntentionType(), nextStatus.getHitExtraPhrase(), null, nextStatus.getHitBranch(),nextStatus.getPhoneDataCache().getTaskName(), System.currentTimeMillis(),nextStatus.getPhoneDataCache().getSpeechCallId());
                    dialogRecordService.toMqNew(callId, 0, nextStatus, nextStatus.getCurrentMultiContent().getActiveUnitContents().get(0), map, null);
                    StopWatch startWatch10 = new StopWatch();
                    startWatch10.start();
                    //log.info("调用callback4");
                    nextStatus = scriptService.callbackNextStatus(nextStatus);
                    startWatch10.stop();
                    sb.append("10："+startWatch10.getTotalTimeMillis()+"ms\n");
                    //simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+callId, JSONObject.toJSONString(nextStatus, SerializerFeature.WriteClassName),ApplicationConstants.CALL_USER_STATUS_EXPIRE);
                    commonStatusService.setLocalStatusCache(callId,nextStatus);
                    return messageNotifyResponse(notify,"other",nextStatus,callId);
                }

                if(interruptType.equals(SUPPORT_SEMANTIC_INTERRUPT_WITH_REPLY)){
                    if(currentStatus.getCurrentInterruptContent() != null){
                        if(currentStatus.getCurrentInterruptContent().getInterruptEndTime() > System.currentTimeMillis()/1000){
                            ScriptStatus tempStatus = getCurrentTempStatus(callId, currentStatus, userAnswerContent);
                            ScriptStatus tempStatus1 = scriptService.getNextStatus(tempStatus);
                            if(tempStatus1.getIsEnd()){
                                interapter(tempStatus1, callId,map);
                                //log.info("调用callback5");
                                tempStatus1 = scriptService.callbackNextStatus(tempStatus1);
                                if(isPressureTest.equals("1")){
                                    return SmartivrTestResponse.response("message","end",tempStatus1,callId);
                                }
                                return SmartivrResponse.response("message","end",tempStatus1,callId);
                            }
                            //不可打断
                            return SmartivrResponse.response(notify,"noop",null,callId);
                        }
                    }
                }

                try{
                    StopWatch startWatch11 = new StopWatch();
                    startWatch11.start();
                    nextStatus = reply(callId, userAnswerContent,map);//TODO 没命中的时候，丢弃
                    startWatch11.stop();
                    sb.append("11："+startWatch11.getTotalTimeMillis()+"ms\n");
                }catch (Exception e){
                    e.printStackTrace();
                    if(isPressureTest.equals("1")){
                        return SmartivrTestResponse.response("enter","exception",null,callId);
                    }
                    return SmartivrResponse.response("enter","exception",null,callId);
                }
                //log.info("===>"+callId+":"+JSON.toJSONString(nextStatus));
                if(nextStatus.getHitPhrase()){
                    String fsIp = nextStatus.getCallStatus().getFsIp();
                    /*
                    if(nextStatus.getCurrentCorpus().isListenInOrTakeOver()){
                        if(!ManAiConstants.seatObtainGlobleTaskMap.containsKey(callId)){
                            seatManagerOld.addSeatObtainTask(callId,nextStatus.getPhoneDataCache().getTaskId(),nextStatus.getPhoneDataCache().getPhone(),nextStatus.getPhoneDataCache().getPlainPhone(),1000l,LocalDateTime.now().plusMinutes(100l),false,nextStatus.getPhoneDataCache().getCallTeamHandleType(),null,nextStatus.getPhoneDataCache().getSpeechCallId(),fsIp);
                        }
                    }
                     */
                    //log.info("是否是转人工标识4："+callId+nextStatus.getCurrentCorpus().getName()+" : "+nextStatus.getCurrentCorpus().isListenInOrTakeOver());
                    if(nextStatus.getCurrentCorpus().isListenInOrTakeOver()){
                        //if(!ManAiConstants.seatObtainGlobleTaskMap.containsKey(callId)){
                        if(LocalCache.seatObtainTaskCache.getIfPresent(callId) == null){
                           //log.info("走到转人工4");
                            seatManager.addSeatObtainTask(callId,nextStatus.getPhoneDataCache().getTaskId(),nextStatus.getPhoneDataCache().getPhone(),nextStatus.getPhoneDataCache().getPlainPhone(),1000l, LocalDateTime.now().plusMinutes(100l),false,nextStatus.getPhoneDataCache().getCallTeamHandleType(),null,nextStatus.getPhoneDataCache().getSpeechCallId(),fsIp,map.getOrDefault("originCallId","").toString(),map.getOrDefault("bridge_fs_ip","").toString());
                        }
                    }


                    InterruptType interruptTypeReplyHit = null;
                    if(nextStatus.getCurrentCorpus() != null && nextStatus.getCurrentMultiContent() != null
                            && nextStatus.getCurrentMultiContent().getActiveUnitContents() != null
                            && nextStatus.getCurrentMultiContent().getActiveUnitContents().size() > 0) {
                        interruptTypeReplyHit = nextStatus.getCurrentMultiContent().getActiveUnitContents().get(0).getUnitContent().getInterruptType();
                    }

                    if(interruptTypeReplyHit !=null && (interruptTypeReplyHit.equals(SUPPORT_SEMANTIC_INTERRUPT_NO_REPLY)
                            ||interruptTypeReplyHit.equals(SUPPORT_SEMANTIC_INTERRUPT_WITH_REPLY))) {
                        nextStatus.setCurrentInterruptContent(InterruptContent.builder()
                                .allowedInterruptTime(nextStatus.getCurrentMultiContent().getActiveUnitContents().get(0).getUnitContent().getAllowedInterruptTime())
                                .interruptEndTime(nextStatus.getCurrentMultiContent().getActiveUnitContents().get(0).getUnitContent().getAllowedInterruptTime()+System.currentTimeMillis()/1000)
                                .build());
                    }else{
                        nextStatus.setCurrentInterruptContent(null);
                    }
                    if(isPressureTest.equals("1")){
                        return messageNotifyResponse("other", "other", nextStatus,callId);
                    }
                    StopWatch startWatch12 = new StopWatch();
                    startWatch12.start();
                    //log.info("调用callback6");
                    nextStatus = scriptService.callbackNextStatus(nextStatus);
                    startWatch12.stop();
                    sb.append("12："+startWatch12.getTotalTimeMillis()+"ms\n");
                    //simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+callId, JSONObject.toJSONString(nextStatus, SerializerFeature.WriteClassName),ApplicationConstants.CALL_USER_STATUS_EXPIRE);
                    commonStatusService.setLocalStatusCache(callId,nextStatus);
                    return messageNotifyResponse("other", "other", nextStatus,callId);
                }else{
                    String playpause = String.valueOf(map.getOrDefault("playpause",""));
                    if(playpause.equals("true")){
                        if(interruptType.equals(SUPPORT_SOUND_INTERRUPT_WITH_REPLY)){//发声打断，直接恢复
                            return SmartivrResponse.response(notify, "resume", currentStatus,callId);
                        }
                    }
                    //String pause = simpleRedisService.getValue(ApplicationConstants.PAUSE_PREFIX+callId);
                    if(LocalCache.pauseCache.getIfPresent(callId) != null){//语义打断，有暂停时恢复播放
                        //simpleRedisService.delete(ApplicationConstants.PAUSE_PREFIX+callId);//暂停
                        LocalCache.pauseCache.invalidate(callId);
                        if(isPressureTest.equals("1")){
                            return SmartivrTestResponse.response(notify, "resume", currentStatus,callId);
                        }
                        return SmartivrResponse.response(notify, "resume", currentStatus,callId);
                    }else{
                        return messageNotifyResponse(notify,"noop",null,callId);
                    }

                }
            }else if(interruptType!=null&&interruptType.equals(CAN_NOT_BE_INTERRUPTED)){
                StopWatch startWatch13 = new StopWatch();
                startWatch13.start();
                ScriptStatus tempStatus = getCurrentTempStatus(callId, currentStatus, userAnswerContent);
                ScriptStatus tempStatus1 = scriptService.getNextStatus(tempStatus);
                /* 不确定有没有走到这个语料，所以这里不需要判断转不转人工
                String fsIp = tempStatus1.getCallStatus().getFsIp();
                if(tempStatus1.getCurrentCorpus().isListenInOrTakeOver()){
                    if(!ManAiConstants.seatObtainGlobleTaskMap.containsKey(callId)){
                        seatManager.addSeatObtainTask(callId,tempStatus1.getPhoneDataCache().getTaskId(),tempStatus1.getPhoneDataCache().getPhone(),tempStatus1.getPhoneDataCache().getPlainPhone(),1000l,LocalDateTime.now().plusMinutes(100l),false,tempStatus1.getPhoneDataCache().getCallTeamHandleType(),null,tempStatus1.getPhoneDataCache().getSpeechCallId(),fsIp);
                    }
                }
                 */
                startWatch13.stop();
                sb.append("13："+startWatch13.getTotalTimeMillis()+"ms\n");

                if(tempStatus1.getIsEnd()){
                    interapter(tempStatus1, callId,map);
                    //log.info("调用callback7");
                    tempStatus1 = scriptService.callbackNextStatus(tempStatus1);
                    if(isPressureTest.equals("1")){
                        return SmartivrTestResponse.response("message","end",tempStatus1,callId);
                    }
                    return SmartivrResponse.response("message","end",tempStatus1,callId);
                }
                if(playstate.equals("false")){
                    try{
                        StopWatch startWatch14 = new StopWatch();
                        startWatch14.start();
                        nextStatus = getNextStatus(callId, userAnswerContent,false,"");
                        String fsIp = nextStatus.getCallStatus().getFsIp();
                        /*
                        if(nextStatus.getCurrentCorpus().isListenInOrTakeOver()){
                            if(!ManAiConstants.seatObtainGlobleTaskMap.containsKey(callId)){
                                log.info("触发转人工"+callId);
                                seatManagerOld.addSeatObtainTask(callId,nextStatus.getPhoneDataCache().getTaskId(),nextStatus.getPhoneDataCache().getPhone(),nextStatus.getPhoneDataCache().getPlainPhone(),1000l,LocalDateTime.now().plusMinutes(100l),false,nextStatus.getPhoneDataCache().getCallTeamHandleType(),null,nextStatus.getPhoneDataCache().getSpeechCallId(),fsIp);
                            }
                        }
                         */
                        //log.info("是否是转人工标识5："+callId+nextStatus.getCurrentCorpus().getName()+" : "+nextStatus.getCurrentCorpus().isListenInOrTakeOver());
                        if(nextStatus.getCurrentCorpus().isListenInOrTakeOver()){
                            //if(!ManAiConstants.seatObtainGlobleTaskMap.containsKey(callId)){
                            if(LocalCache.seatObtainTaskCache.getIfPresent(callId) == null){
                                //log.info("走到转人工5");
                                seatManager.addSeatObtainTask(callId,nextStatus.getPhoneDataCache().getTaskId(),nextStatus.getPhoneDataCache().getPhone(),nextStatus.getPhoneDataCache().getPlainPhone(),1000l, LocalDateTime.now().plusMinutes(100l),false,nextStatus.getPhoneDataCache().getCallTeamHandleType(),null,nextStatus.getPhoneDataCache().getSpeechCallId(),fsIp,map.getOrDefault("originCallId","").toString(),map.getOrDefault("bridge_fs_ip","").toString());
                            }
                        }

                        startWatch14.stop();
                        sb.append("14："+startWatch14.getTotalTimeMillis()+"ms\n");
                    }catch (Exception e){
                        e.printStackTrace();
                        if(isPressureTest.equals("1")){
                            return SmartivrTestResponse.response("enter","exception",null,callId);
                        }
                        return SmartivrResponse.response("enter","exception",null,callId);
                    }
                    //String audioFileUrl = "http://"+nextStatus.getCallStatus().getFsIp()+ApplicationConstants.FS_BG_MUSIC_DIR+"/"+nextStatus.getScriptId()+"/"+nextStatus.getCurrentCorpus().getAudioPath().split("/")[nextStatus.getCurrentCorpus().getAudioPath().split("/").length-1];
                    //String content = nextStatus.getCurrentCorpus().getContent();
                    //dialogRecordService.toMQ(callId,audioFileUrl,0,content,0,0, nextStatus.getCurrentCorpus().getName(), nextStatus.getCurrentCorpus().getInterruptType(), nextStatus.getCurrentIntentionType(), nextStatus.getHitExtraPhrase(), null, nextStatus.getHitBranch(),nextStatus.getPhoneDataCache().getTaskName(), System.currentTimeMillis(),nextStatus.getPhoneDataCache().getSpeechCallId());
                    dialogRecordService.toMqNew(callId, 0, nextStatus, nextStatus.getCurrentMultiContent().getActiveUnitContents().get(0), map, null);
                    StopWatch startWatch15 = new StopWatch();
                    startWatch15.start();
                    //log.info("调用callback8");
                    nextStatus = scriptService.callbackNextStatus(nextStatus);
                    startWatch15.stop();
                    sb.append("15："+startWatch15.getTotalTimeMillis()+"ms\n");
                    //simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+callId, JSONObject.toJSONString(nextStatus, SerializerFeature.WriteClassName),ApplicationConstants.CALL_USER_STATUS_EXPIRE);
                    commonStatusService.setLocalStatusCache(callId,nextStatus);
                    return messageNotifyResponse(notify,"other",nextStatus,callId);
                }

                return messageNotifyResponse(notify,"noop",null,callId);
            }else{

                return messageNotifyResponse(notify,"noop",null,callId);
            }

        }else if (notify.equals("wait_result")) {//等待结果
            /*
            if(!simpleRedisService.hasKey(String.format(ApplicationConstants.WAIT,callId))){
                return messageNotifyResponse(notify,"noop",null,callId);
            }
            simpleRedisService.delete(String.format(ApplicationConstants.WAIT,callId));
             */
            if(LocalCache.waitCache.getIfPresent(callId) != null){
                return messageNotifyResponse(notify,"noop",null,callId);
            }
            LocalCache.waitCache.invalidate(callId);


            ScriptStatus currentStatus = getCurrentStatus(callId,"playback end","");
            if(map.containsKey("asrstate")){
                String asrstate = String.valueOf(map.get("asrstate"));
                if(asrstate.equals("true")){
                    //log.info("===>用户已经说话：callId："+callId+"");
                    return messageNotifyResponse(notify,"noop",null,callId);
                }
            }
            if(currentStatus.getIsEnd()){
                return messageNotifyResponse(notify,"noop_hangup",null,callId);
            }
            try{
                nextStatus = getNextStatus(callId, "",true,"");
                String fsIp = nextStatus.getCallStatus().getFsIp();
                StopWatch startWatch16 = new StopWatch();
                startWatch16.start();
                /*
                if(nextStatus.getCurrentCorpus().isListenInOrTakeOver()){
                    if(!ManAiConstants.seatObtainGlobleTaskMap.containsKey(callId)){
                        seatManagerOld.addSeatObtainTask(callId,nextStatus.getPhoneDataCache().getTaskId(),nextStatus.getPhoneDataCache().getPhone(),nextStatus.getPhoneDataCache().getPlainPhone(),1000l,LocalDateTime.now().plusMinutes(100l),false,nextStatus.getPhoneDataCache().getCallTeamHandleType(),null,nextStatus.getPhoneDataCache().getSpeechCallId(),fsIp);
                    }
                }
                 */
                //log.info("是否是转人工标识6："+callId+nextStatus.getCurrentCorpus().getName()+" : "+nextStatus.getCurrentCorpus().isListenInOrTakeOver());
                if(nextStatus.getCurrentCorpus().isListenInOrTakeOver()){
                    //if(!ManAiConstants.seatObtainGlobleTaskMap.containsKey(callId)){
                    if(LocalCache.seatObtainTaskCache.getIfPresent(callId) == null){
                        //log.info("走到转人工6");
                        seatManager.addSeatObtainTask(callId,nextStatus.getPhoneDataCache().getTaskId(),nextStatus.getPhoneDataCache().getPhone(),nextStatus.getPhoneDataCache().getPlainPhone(),1000l, LocalDateTime.now().plusMinutes(100l),false,nextStatus.getPhoneDataCache().getCallTeamHandleType(),null,nextStatus.getPhoneDataCache().getSpeechCallId(),fsIp,map.getOrDefault("originCallId","").toString(),map.getOrDefault("bridge_fs_ip","").toString());
                    }
                }

                startWatch16.stop();
                sb.append("16："+startWatch16.getTotalTimeMillis()+"ms\n");
            }catch (Exception e){
                e.printStackTrace();
                if(isPressureTest.equals("1")){
                    return SmartivrTestResponse.response("enter","exception",null,callId);
                }
                return SmartivrResponse.response("enter","exception",null,callId);
            }

            //String audioFileUrl = "http://"+nextStatus.getCallStatus().getFsIp()+ApplicationConstants.FS_BG_MUSIC_DIR+"/"+nextStatus.getScriptId()+"/"+nextStatus.getCurrentCorpus().getAudioPath().split("/")[nextStatus.getCurrentCorpus().getAudioPath().split("/").length-1];
            //String content = nextStatus.getCurrentCorpus().getContent();
            //dialogRecordService.toMQ(callId,audioFileUrl,0,content,0,0, nextStatus.getCurrentCorpus().getName(), nextStatus.getCurrentCorpus().getInterruptType(), nextStatus.getCurrentIntentionType(), nextStatus.getHitExtraPhrase(), null, nextStatus.getHitBranch(),nextStatus.getPhoneDataCache().getTaskName(), System.currentTimeMillis(),nextStatus.getPhoneDataCache().getSpeechCallId());
            dialogRecordService.toMqNew(callId, 0, nextStatus, nextStatus.getCurrentMultiContent().getActiveUnitContents().get(0), map, null);
            StopWatch startWatch17 = new StopWatch();
            startWatch17.start();
            //log.info("调用callback9");
            nextStatus = scriptService.callbackNextStatus(nextStatus);
            startWatch17.stop();
            sb.append("17："+startWatch17.getTotalTimeMillis()+"ms\n");
            //simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+callId, JSONObject.toJSONString(nextStatus, SerializerFeature.WriteClassName),ApplicationConstants.CALL_USER_STATUS_EXPIRE);
            commonStatusService.setLocalStatusCache(callId,nextStatus);
            return SmartivrResponse.response(notify,"other",nextStatus,callId);
        }else if(notify.equals("playback_result")){
            ScriptStatus currentStatus = getCurrentStatus(callId,"playback end","");
            String userAnswerContent = String.valueOf(map.get("message")).trim();
            StopWatch startWatch18 = new StopWatch();
            startWatch18.start();

            /*
            if(ManAiConstants.seatObtainGlobleTaskMap.containsKey(callId)){
                String takeO = ManAiConstants.seatObtainGlobleTaskMap.get(callId).getTakeOverOrListenIn();
                if(takeO!=null && takeO.equals("take_over")){//如果是接管，则直接记录通话记录
                    return SmartivrResponse.response(notify,"noop",null,callId);//如果当前属于
                }
            }
             */
            if(LocalCache.seatObtainTaskCache.getIfPresent(callId) != null){
                String takeO = LocalCache.seatObtainTaskCache.getIfPresent(callId).getTakeOverOrListenIn();
                if(takeO!=null && takeO.equals("take_over")){//如果是接管，则直接记录通话记录
                    return SmartivrResponse.response(notify,"noop",null,callId);//如果当前属于
                }
            }

            startWatch18.stop();
            sb.append("18："+startWatch18.getTotalTimeMillis()+"ms\n");

            if(userAnswerContent.toLowerCase().contains("playback error")){
                return messageNotifyResponse(notify,"noop",null,callId);
            }

            if(map.containsKey("asrstate")){
                String asrstate = String.valueOf(map.get("asrstate"));
                if(asrstate.equals("true")){
                    return messageNotifyResponse(notify,"noop",null,callId);
                }
            }


            if(currentStatus.getIsEnd()){
                Long end = System.currentTimeMillis();
                //log.info("===>playback end1 耗时：callId："+callId+",time："+(end-start));
                return messageNotifyResponse(notify,"noop_hangup",null,callId);
            }
            try{
                StopWatch startWatch19 = new StopWatch();
                startWatch19.start();
                nextStatus = getNextStatus(callId, "",true,"");
                String fsIp = nextStatus.getCallStatus().getFsIp();
                /*
                if(nextStatus.getCurrentCorpus().isListenInOrTakeOver()){
                    if(!ManAiConstants.seatObtainGlobleTaskMap.containsKey(callId)){
                        seatManagerOld.addSeatObtainTask(callId,nextStatus.getPhoneDataCache().getTaskId(),nextStatus.getPhoneDataCache().getPhone(),nextStatus.getPhoneDataCache().getPlainPhone(),1000l,LocalDateTime.now().plusMinutes(100l),false,nextStatus.getPhoneDataCache().getCallTeamHandleType(),null,nextStatus.getPhoneDataCache().getSpeechCallId(),fsIp);
                    }
                }
                 */
                //log.info("是否是转人工标识7："+callId+nextStatus.getCurrentCorpus().getName()+" : "+nextStatus.getCurrentCorpus().isListenInOrTakeOver());
                if(nextStatus.getCurrentCorpus().isListenInOrTakeOver()){
                    //if(!ManAiConstants.seatObtainGlobleTaskMap.containsKey(callId)){
                    if(LocalCache.seatObtainTaskCache.getIfPresent(callId) == null){
                        //log.info("走到转人工7");
                        seatManager.addSeatObtainTask(callId,nextStatus.getPhoneDataCache().getTaskId(),nextStatus.getPhoneDataCache().getPhone(),nextStatus.getPhoneDataCache().getPlainPhone(),1000l, LocalDateTime.now().plusMinutes(100l),false,nextStatus.getPhoneDataCache().getCallTeamHandleType(),null,nextStatus.getPhoneDataCache().getSpeechCallId(),fsIp,map.getOrDefault("originCallId","").toString(),map.getOrDefault("bridge_fs_ip","").toString());
                    }
                }
                startWatch19.stop();
                sb.append("19："+startWatch19.getTotalTimeMillis()+"ms\n");
            }catch (Exception e){
                e.printStackTrace();
                if(isPressureTest.equals("1")){
                    return SmartivrTestResponse.response("enter","exception",null,callId);
                }
                return SmartivrResponse.response("enter","exception",null,callId);
            }

            //String audioFileUrl = "http://"+nextStatus.getCallStatus().getFsIp()+ApplicationConstants.FS_BG_MUSIC_DIR+"/"+nextStatus.getScriptId()+"/"+nextStatus.getCurrentCorpus().getAudioPath().split("/")[nextStatus.getCurrentCorpus().getAudioPath().split("/").length-1];
            //String content = nextStatus.getCurrentCorpus().getContent();
            //dialogRecordService.toMQ(callId,audioFileUrl,0,content,0,0, nextStatus.getCurrentCorpus().getName(), nextStatus.getCurrentCorpus().getInterruptType(), nextStatus.getCurrentIntentionType(), nextStatus.getHitExtraPhrase(), null, nextStatus.getHitBranch(),nextStatus.getPhoneDataCache().getTaskName(), System.currentTimeMillis(),nextStatus.getPhoneDataCache().getSpeechCallId());
            dialogRecordService.toMqNew(callId, 0, nextStatus, nextStatus.getCurrentMultiContent().getActiveUnitContents().get(0), map, null);
            if(isPressureTest.equals("1")){
                return SmartivrTestResponse.response(notify,"other",nextStatus,callId);
            }
            StopWatch startWatch20 = new StopWatch();
            startWatch20.start();
            //log.info("调用callback10");
            nextStatus = scriptService.callbackNextStatus(nextStatus);
            startWatch20.stop();
            sb.append("20："+startWatch20.getTotalTimeMillis()+"ms\n");
            //simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+callId, JSONObject.toJSONString(nextStatus, SerializerFeature.WriteClassName),ApplicationConstants.CALL_USER_STATUS_EXPIRE);
            commonStatusService.setLocalStatusCache(callId,nextStatus);
            return SmartivrResponse.response(notify,"other",nextStatus,callId);

        }else if(notify.equals("leave")){
            ScriptStatus currentStatus = getCurrentStatus(callId,"leave","");
            currentStatus.getCallStatus().setCallEndTime(System.currentTimeMillis()/1000);
            String wholeRecordFile = null;
            if(map.containsKey("bridge_fs_ip")){
                wholeRecordFile = "http://"+map.getOrDefault("bridge_fs_ip","").toString().replace(":5060","")+ApplicationConstants.FS_RECORD_FILE_DIR+ApplicationConstants.FS_RECORD_FILE_DIR_WHOLE+"/"+ LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"))+"/"+callId+"."+audioSuffix;
            }else{
                wholeRecordFile = "http://"+currentStatus.getCallStatus().getFsIp()+ApplicationConstants.FS_RECORD_FILE_DIR+ApplicationConstants.FS_RECORD_FILE_DIR_WHOLE+"/"+ LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"))+"/"+callId+"."+audioSuffix;
            }
            CallPhoneProducerMaterial.PhoneDataCache phoneDataCache = currentStatus.getPhoneDataCache();
            currentStatus.getCallStatus().setCallStatus(CallStatusEnum.CALL_SUCCESS.ordinal());//呼叫完成
            Long duration = 0L;
            if(currentStatus.getCallStatus().getCallStartTime()==null || currentStatus.getCallStatus().getCallStartTime()==0){
                LocalDateTime time = LocalDateTime.parse(currentStatus.getCallStatus().getCallOutTime(),DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                Long timestamp = time.toInstant(ZoneOffset.ofHours(8)).toEpochMilli()/1000;
                duration = currentStatus.getCallStatus().getCallEndTime() - timestamp;
            }else{
                duration = currentStatus.getCallStatus().getCallEndTime() - currentStatus.getCallStatus().getCallStartTime();
            }

            currentStatus.getCallStatus().setCallDuration(duration.intValue());
            currentStatus.getCallStatus().setIntentionClass(currentStatus.getFinalIntentionType());
            currentStatus.getCallStatus().setTalkTimeEnd(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            currentStatus.getCallStatus().setIntentionLabelIds(currentStatus.getFinalIntentionLabelIdsString());
            currentStatus.getCallStatus().setIntentionLabels(currentStatus.getFinalIntentionLabelsString());

            currentStatus.getCallStatus().setHitAnswerIds(currentStatus.getHitAnswerIds());
            callRecordService.toMQ(phoneDataCache.getTaskId(),phoneDataCache.getTaskName(),phoneDataCache.getPhone(),phoneDataCache.getLineId(),phoneDataCache.getScriptId(),callId,phoneDataCache.getSpeechCallId(),"",wholeRecordFile,phoneDataCache.getLineCode(),"","",currentStatus.getVersion(),currentStatus.getCallStatus(),currentStatus.getScriptLongId(), JSONObject.toJSONString(currentStatus.getMatchedCorpusIds()), currentStatus.getHitSemanticIdsString(),phoneDataCache.getPlainPhone(), currentStatus.getFinalExtraInfo());

            StopWatch startWatch21 = new StopWatch();
            startWatch21.start();
            /*
            if(ManAiConstants.seatObtainGlobleTaskMap.containsKey(callId)){
                ManAiConstants.seatObtainGlobleTaskRemoveMap.put(callId,callId);
                //ManAiConstants.seatObtainGlobleTaskMap.remove(callId);
            }
             */
            if(LocalCache.seatObtainTaskCache.getIfPresent(callId) != null){
                //LocalCache.seatObtainTaskCache.invalidate(callId);
            }

            startWatch21.stop();
            sb.append("21："+startWatch21.getTotalTimeMillis()+"ms\n");
            if(isPressureTest.equals("1")){
                return SmartivrTestResponse.response(notify,"noop",null,callId);
            }
            return SmartivrResponse.response(notify,"noop",null,callId);
        }else{
            if(isPressureTest.equals("1")){
                return SmartivrTestResponse.response(notify,"noop",null,callId);
            }
            return SmartivrResponse.response(notify,"noop",null,callId);
        }

    }

    private Set<Long> setHitSemanticId(ScriptStatus status, String userContent) {
        if (StringUtils.isNotEmpty(userContent) && !userContent.endsWith(";")) {
            userContent = userContent + ";";
        }
        Map<String, Set<Long>> hitSemanticIdMap = status.getHitSemanticIdMap();
        if (!hitSemanticIdMap.containsKey(userContent)) {
            Set<Long> hitSemanticIds = TriggerUtils.getHitSemanticIds(userContent, status.getSecondIndustryId());
            hitSemanticIdMap.put(userContent, hitSemanticIds);
            return hitSemanticIds;
        } else {
            return hitSemanticIdMap.get(userContent);
        }
    }

    private void interapter(ScriptStatus tempStatus1, String callId,Map<String,Object> msgNotify) {

        InterruptType interruptTypeTemp = null;
        if(tempStatus1.getCurrentCorpus() != null && tempStatus1.getCurrentMultiContent() != null
                && tempStatus1.getCurrentMultiContent().getActiveUnitContents() != null
                && tempStatus1.getCurrentMultiContent().getActiveUnitContents().size() > 0) {
            interruptTypeTemp = tempStatus1.getCurrentMultiContent().getActiveUnitContents().get(0).getUnitContent().getInterruptType();
        }

        if(interruptTypeTemp !=null && (interruptTypeTemp.equals(SUPPORT_SEMANTIC_INTERRUPT_NO_REPLY)
                ||interruptTypeTemp.equals(SUPPORT_SEMANTIC_INTERRUPT_WITH_REPLY))) {
            tempStatus1.setCurrentInterruptContent(InterruptContent.builder()
                    .allowedInterruptTime(tempStatus1.getCurrentMultiContent().getActiveUnitContents().get(0).getUnitContent().getAllowedInterruptTime())
                    .interruptEndTime(tempStatus1.getCurrentMultiContent().getActiveUnitContents().get(0).getUnitContent().getAllowedInterruptTime()+System.currentTimeMillis()/1000)
                    .build());
        }else{
            tempStatus1.setCurrentInterruptContent(null);
        }
        //simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+ callId, JSONObject.toJSONString(tempStatus1, SerializerFeature.WriteClassName),ApplicationConstants.CALL_USER_STATUS_EXPIRE);
        commonStatusService.setLocalStatusCache(callId,tempStatus1);
        //String audioFileUrl = "http://"+ tempStatus1.getCallStatus().getFsIp()+ApplicationConstants.FS_BG_MUSIC_DIR+"/"+ tempStatus1.getScriptId()+"/"+ tempStatus1.getCurrentCorpus().getAudioPath().split("/")[tempStatus1.getCurrentCorpus().getAudioPath().split("/").length-1];
        //String content = tempStatus1.getCurrentCorpus().getContent();
        //dialogRecordService.toMQ(callId,audioFileUrl,0,content,0,0, tempStatus1.getCurrentCorpus().getName(), tempStatus1.getCurrentCorpus().getInterruptType(), tempStatus1.getCurrentIntentionType(), tempStatus1.getHitExtraPhrase(), null, tempStatus1.getHitBranch(), tempStatus1.getPhoneDataCache().getTaskName(), System.currentTimeMillis(),tempStatus1.getPhoneDataCache().getSpeechCallId());
        dialogRecordService.toMqNew(callId, 0, tempStatus1, tempStatus1.getCurrentMultiContent().getActiveUnitContents().get(0), msgNotify, null);
    }

    private ScriptStatus getCurrentTempStatus(String callId, ScriptStatus currentStatus, String userAnswerContent) {
        /*
        String jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+ callId);
        ScriptStatus tempStatus = JSONObject.parseObject(jsonString,ScriptStatus.class);
        if(tempStatus == null){
            jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+ callId);
            tempStatus = JSONObject.parseObject(jsonString,ScriptStatus.class);
            if(tempStatus == null) {
                jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS + callId);
                tempStatus = JSONObject.parseObject(jsonString, ScriptStatus.class);
            }
        }
         */
        ScriptStatus tempStatus = commonStatusService.getCurrentStatus(callId);

        if(tempStatus == null) {
            tempStatus = new ScriptStatus();
            tempStatus.setScriptId(currentStatus.getScriptId());
            tempStatus.setVersion(currentStatus.getVersion());
            tempStatus.setCallId(callId);
        }
        tempStatus.setUserContent(userAnswerContent.replace(".","").replace("1","").replace("2","").replace("3",""));
        tempStatus.setMatchHighestPriority(true);
        return tempStatus;
    }

    private Map<String,Object> messageNotifyResponse(String notify,String type,ScriptStatus scriptStatus,String callId){
        if(isPressureTest.equals("1")){
            return SmartivrTestResponse.response(notify,type,scriptStatus,callId);//不做任何动作
        }
        return SmartivrResponse.response(notify,type,scriptStatus,callId);//不做任何动作
    }


    private ScriptStatus reply(String callId,String userAnswerContent,Map<String,Object> msgNotify){
        ScriptStatus nextStatus = getNextStatus1(callId,userAnswerContent,false,"");
        /*
        String fsIp = nextStatus.getCallStatus().getFsIp();
        if(nextStatus.getCurrentCorpus().isListenInOrTakeOver()){
            if(!ManAiConstants.seatObtainGlobleTaskMap.containsKey(callId)){
                seatManager.addSeatObtainTask(callId,nextStatus.getPhoneDataCache().getTaskId(),nextStatus.getPhoneDataCache().getPhone(),nextStatus.getPhoneDataCache().getPlainPhone(),1000l,LocalDateTime.now().plusMinutes(100l),false,nextStatus.getPhoneDataCache().getCallTeamHandleType(),null,nextStatus.getPhoneDataCache().getSpeechCallId(),fsIp);
            }
        }
        */
        if(nextStatus.getHitPhrase()){
            //String audioFileUrl = "http://"+nextStatus.getCallStatus().getFsIp()+ApplicationConstants.FS_BG_MUSIC_DIR+"/"+nextStatus.getScriptId()+"/"+nextStatus.getCurrentCorpus().getAudioPath().split("/")[nextStatus.getCurrentCorpus().getAudioPath().split("/").length-1];
            //String content = nextStatus.getCurrentCorpus().getContent();
            //dialogRecordService.toMQ(callId,audioFileUrl,0,content,0,0, nextStatus.getCurrentCorpus().getName(), nextStatus.getCurrentCorpus().getInterruptType(), nextStatus.getCurrentIntentionType(), nextStatus.getHitExtraPhrase(), null, nextStatus.getHitBranch(),nextStatus.getPhoneDataCache().getTaskName(), System.currentTimeMillis(),nextStatus.getPhoneDataCache().getSpeechCallId());
            dialogRecordService.toMqNew(callId, 0, nextStatus, nextStatus.getCurrentMultiContent().getActiveUnitContents().get(0), msgNotify, null);
        }
        return nextStatus;
    }


    public void cdr(String uuid,String cdr){

        //log.info("===>cdr："+cdr);
        JSONObject jsonObject = JSONObject.parseObject(cdr,JSONObject.class);
        JSONObject cc = (JSONObject)jsonObject.get("variables");
        String hangupCause= cc.getString("hangup_cause");
        String hangupCauseQ850= cc.getString("hangup_cause_q850");
        int answerSec = cc.getIntValue("answersec");
        int billSec = cc.getIntValue("billsec");
        String speechCallId = cc.getString("speech_call_id");

        String sipCallId = null;
        if(cc.containsKey("sip_call_id"))
            sipCallId = cc.getString("sip_call_id");

        int billmsec = cc.getIntValue("billmsec");
        int mduration = cc.getIntValue("mduration");
        int waitmsec = mduration - billmsec;

        String lastApp = "";
        if(cc.containsKey("last_app")){
            lastApp = cc.getString("last_app");
        }
        Integer whoHangup = 1;
        if(lastApp.contains("hangup")){
            whoHangup = 0;
        }

        toDB(uuid,hangupCause,cdr,billSec,hangupCauseQ850,waitmsec,whoHangup,billmsec,sipCallId);
    }

    public void toDB(String uuid,String hangupCause,String cdr,int billSec,String hangupCauseQ850,int waitmsec,int whoHangup,int billmsec,String sipCallId){
        String callChannelUniqueId = uuid;

        /*
        String cacheString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callChannelUniqueId);
        ScriptStatus userStatus = JSONObject.parseObject(cacheString,ScriptStatus.class);
        if(userStatus == null){
            try{Thread.sleep(20l);}catch (Exception e){}
            cacheString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callChannelUniqueId);
            userStatus = JSONObject.parseObject(cacheString,ScriptStatus.class);
            if(userStatus == null){
                try{Thread.sleep(20l);}catch (Exception e){}
                cacheString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callChannelUniqueId);
                userStatus = JSONObject.parseObject(cacheString,ScriptStatus.class);
            }
        }

         */
        ScriptStatus userStatus = commonStatusService.getCurrentStatus(callChannelUniqueId);

        if(userStatus == null){
            DingDingService.dingDingSendMsgException("Redis缓存中没有获取到用户的状态信息，call_id: " + uuid,"缓存字符串：");
            return ;
        }

        CallPhoneProducerMaterial.PhoneDataCache userInfo = userStatus.getPhoneDataCache();
        userStatus.getCallStatus().setCallDuration(billSec);
        userStatus.getCallStatus().setCallMDuration(billmsec);//通话时长毫秒
        userStatus.getCallStatus().setWaitMsec(waitmsec);//等待时间
        userStatus.getCallStatus().setIsNewRecord("0");//默认记录为非新建
        userStatus.getCallStatus().setWhoHangup(whoHangup);
        if(userInfo.getSendCallErrorRetryTimes() < sendCallErrorRetryTimes && waitmsec <= sendCallErrorRetryDuration && sendCallErrorSwitch.equals("1")){

            userStatus.getCallStatus().setIsNewRecord("1");//记录为新建
            if(StringUtils.isNotEmpty(userInfo.getTaskName()) && !userInfo.getTaskName().contains("话术训练"))
                callService.backQueueSendFailure(ChannelManagerService.channelList1.get(0),userInfo);

        }

        if(userInfo.getSendCallErrorRetryTimes() >= sendCallErrorRetryTimes && waitmsec <= sendCallErrorRetryDuration && sendCallErrorSwitch.equals("1")){
            hangupCause = "呼叫失败";
        }

        callService.getCallRecordService().toMQCdr(userInfo.getTaskId(),userInfo.getTaskName(),userInfo.getPhone(),
                userInfo.getLineId(),userStatus.getScriptId(),callChannelUniqueId,userInfo.getSpeechCallId(),
                hangupCause,"",userInfo.getLineCode(),hangupCauseQ850,hangupCause,userStatus.getVersion(),
                userStatus.getCallStatus(), userStatus.getScriptLongId(), cdr, userStatus.getHitSemanticIdsString(),userInfo.getPlainPhone(),sipCallId);

    }

}

