package com.raipeng.aicall.service;

import com.raipeng.aicall.constant.LocalCache;
import com.raipeng.aicall.feign.AiWorkbenchFeign;
import com.raipeng.common.entity.callsetting.AvailableSeatQueryParam;
import com.raipeng.common.entity.callsetting.SeatTaskData;
import com.raipeng.aicall.service.seat.SeatObtainTask;
import com.raipeng.common.util.StringUtils;
import feign.Request;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.*;

@Service
@Slf4j
public class SeatManager {
    @Autowired
    private AiWorkbenchFeign aiWorkbenchFeign;

    @Autowired
    private CallThirdControlService callThirdControlService;

    @Autowired
    private CurrentServerIpService currentServerIpService;

    static SeatManager seatManager;

    @Autowired
    private void initSeatManager(SeatManager inputSeatManager){
        seatManager = inputSeatManager;
    }

    static DelayQueue<SeatObtainTask> delayQueue = new DelayQueue<>();

    public static void addSeatObtainTask(String callId,String taskId,String phone,String plainPhone,Long delayTime,LocalDateTime startTime,Boolean isComplete,String callTeamHandleType,String takeOverOrListenIn,String speechCallId,String fsIp,String originCallId,String bridgeFsIp){
        if(delayTime == 1000l){
            delayTime = 10l;//改成延迟10ms
        }
        SeatObtainTask task =  new SeatObtainTask(delayTime,callId,taskId,phone,plainPhone,speechCallId,fsIp,seatManager,originCallId,bridgeFsIp);
        task.setExpireTime(startTime);
        task.setIsComplete(isComplete);
        task.setCallTeamHandleType(callTeamHandleType);
        task.setTakeOverOrListenIn(takeOverOrListenIn);
        //ManAiConstants.seatObtainGlobleTaskMap.put(callId,task);
        LocalCache.seatObtainTaskCache.put(callId,task);
        //log.info("====》》》进入转接坐席"+callId+phone+":");
        //count = 0;
        //delayQueue.add(task);
        LocalCache.obtainTaskCache.put(callId,task);
    }

    public static void addSeatObtainTask2(String callId,String taskId,String phone,String plainPhone,Long delayTime,LocalDateTime startTime,Boolean isComplete,String callTeamHandleType,String takeOverOrListenIn,String speechCallId,String originCallId,String bridgeFsIp){
        delayTime = 10l;//改成延迟10ms
        SeatObtainTask task =  new SeatObtainTask(delayTime,callId,taskId,phone,plainPhone,speechCallId,seatManager,originCallId,bridgeFsIp);
        task.setExpireTime(startTime);
        task.setIsComplete(isComplete);
        task.setCallTeamHandleType(callTeamHandleType);
        task.setTakeOverOrListenIn(takeOverOrListenIn);
        LocalCache.seatObtainTaskCache.put(callId,task);
    }


    public static void addSeatObtainTask3(SeatObtainTask seatObtainTask){
        //log.info("循环获取坐席信息"+ JSON.toJSONString(seatObtainTask));
        LocalCache.obtainTaskCache.put(seatObtainTask.getCallId(),seatObtainTask);
    }

    static Integer count = 0;
    // 从队列中获取并移除任务
    public void dealWith(String key,SeatObtainTask task)  {
        if(key.startsWith("ccc")){
            return;
        }
        //log.info("进入dealWith"+task.getCallId());
        if(task.getExpireTime().compareTo(LocalDateTime.now()) < 0){
            //log.info("过期结束1"+task.getCallId()+task.getExpireTime());
            return ;
        }
        String callId = task.getCallId();
        String taskId = task.getTaskId();
        String phone = task.getPhone();
        String plainPhone = task.getPlainPhone();
        String speechCallId = task.getSpeechCallId();
        //SeatObtainTask seatObtainTask = ManAiConstants.seatObtainGlobleTaskMap.get(callId);

        SeatObtainTask seatObtainTask = LocalCache.seatObtainTaskCache.getIfPresent(callId);
        //log.info("获取到的"+JSON.toJSONString(seatObtainTask));
        if(seatObtainTask !=null && !seatObtainTask.getIsComplete() && !seatObtainTask.isEnd()){
            AvailableSeatQueryParam param = new AvailableSeatQueryParam();
            param.setCallId(callId);
            param.setTaskId(taskId);
            param.setPhone(phone);
            param.setAiCallIp(currentServerIpService.getAiCallIp());
            param.setAiCallPort(currentServerIpService.getAiCallPort());
            param.setPlainPhone(task.getPlainPhone());
            param.setSpeechCallId(task.getSpeechCallId());
            SeatTaskData seatTaskData = null;
            try{
                Request.Options options = new Request.Options(5L, TimeUnit.SECONDS, 5L, TimeUnit.SECONDS, true);
                seatTaskData = aiWorkbenchFeign.getOneAvailableCallSeat(param,options);

                //log.info("获取到的坐席信息"+ JSON.toJSONString(param)+"  :  "+JSON.toJSONString(seatTaskData));
            }catch (Exception ee){
                ee.printStackTrace();
            }
            if(seatTaskData != null){
                if(seatObtainTask.getCallTeamHandleType().equals("take_over")){
                    //callThirdControlService.takeOver(callId,seatObtainTask.getFsIp(),seatTaskData.getFsUser(),seatTaskData.getFsIp(),seatTaskData.getFsPort());
                    if(StringUtils.isNotEmpty(seatObtainTask.getOriginCallId())){
                        callThirdControlService.listenInWithFealCallId(seatObtainTask.getOriginCallId(),seatObtainTask.getBridgeFsIp().replace(":5060",""),seatTaskData.getFsUser(),seatTaskData.getFsIp(),seatTaskData.getFsPort(),callId);
                    }else{
                        callThirdControlService.listenIn(callId,seatObtainTask.getFsIp(),seatTaskData.getFsUser(),seatTaskData.getFsIp(),seatTaskData.getFsPort());
                    }
                }else if(seatObtainTask.getCallTeamHandleType().equals("listen_in")){
                    if(StringUtils.isNotEmpty(seatObtainTask.getOriginCallId())){
                        callThirdControlService.listenInWithFealCallId(seatObtainTask.getOriginCallId(),seatObtainTask.getBridgeFsIp().replace(":5060",""),seatTaskData.getFsUser(),seatTaskData.getFsIp(),seatTaskData.getFsPort(),callId);
                    }else {
                        callThirdControlService.listenIn(callId,seatObtainTask.getFsIp(),seatTaskData.getFsUser(),seatTaskData.getFsIp(),seatTaskData.getFsPort());
                    }
                }
                //addSeatObtainTask(callId,taskId,phone,plainPhone,30000l,seatObtainTask.getExpireTime(),seatObtainTask.getIsComplete(),seatObtainTask.getCallTeamHandleType(),seatObtainTask.getTakeOverOrListenIn(),speechCallId,seatObtainTask.getFsIp());//10s,等待坐席接听
            }else{
                addSeatObtainTask3(seatObtainTask);
            }
        }else{
            //ManAiConstants.seatObtainGlobleTaskRemoveMap.remove(callId);
            //log.info("坐席信息失效"+ callId+":"+seatObtainTask.getIsComplete());
            //LocalCache.seatObtainTaskCache.invalidate(callId);
            /*
            if(LocalCache.seatObtainTaskCache.getIfPresent(callId) != null){
                LocalCache.seatObtainTaskCache.getIfPresent(callId).setEnd(true);
                if(LocalCache.seatObtainTaskExpire.getIfPresent(callId) != null){
                    LocalCache.seatObtainTaskExpire.put(callId,1);
                }
            }
            */

        }
    }

    public void dealSeatTakeOverOrListenIn(){
        while(true){
            try{
                while (!delayQueue.isEmpty()) {
                    SeatObtainTask task = delayQueue.poll();
                    if(task != null){
                        //executorService.submit(()->dealWith(task));
                    }
                }
                Thread.sleep(10);
            }catch (Exception e){
                e.printStackTrace();
            }
        }
    }

    /*
    public void removeSeatObtainTask(){
        while(true){
            List<String> deleteId = Lists.newArrayList();
            try{
                ManAiConstants.seatObtainGlobleTaskMap.forEach((k,v)->{
                    if(v.getExpireTime().compareTo(LocalDateTime.now()) < 0){
                        deleteId.add(k);
                    }
                });
                for (String callId : deleteId) {
                    ManAiConstants.seatObtainGlobleTaskMap.remove(callId);
                    ManAiConstants.seatObtainGlobleTaskRemoveMap.remove(callId);
                }
                Thread.sleep(10);
            }catch (Exception e){
                e.printStackTrace();
            }
        }
    }
     */

}
