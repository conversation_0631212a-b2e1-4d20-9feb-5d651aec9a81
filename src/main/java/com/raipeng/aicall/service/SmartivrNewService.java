package com.raipeng.aicall.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.raipeng.aicall.constant.*;
import com.raipeng.aicall.controller.qd.SmartivrResponse;
import com.raipeng.aicall.controller.qd.SmartivrTestResponse;
import com.raipeng.aicall.mq.producter.material.CallPhoneProducerMaterial;
import com.raipeng.aicall.scriptsystem.*;
import com.raipeng.aicall.service.notify.Notify;
import com.raipeng.aicall.service.notify.ai.EnterNotify;
import com.raipeng.aicall.service.notify.ai.ProgressNotify;
import com.raipeng.aicall.utils.ObjectCloneUtils;
import com.raipeng.common.entity.script.AiScript;
import com.raipeng.common.entity.script.intention.IntentionLevel;
import com.raipeng.common.entity.script.intention.IntentionTag;
import com.raipeng.common.enums.InterruptType;
import com.raipeng.common.util.CollectionUtil;
import com.raipeng.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.raipeng.common.enums.InterruptType.*;

/**
 * brightivr 服务
 */
@Slf4j
@Service
@RefreshScope
public class SmartivrNewService {

    @Autowired
    private RobotWavFileService robotWavFileService;

    @Autowired
    private SimpleRedisService simpleRedisService;

    @Autowired
    private ScriptService scriptService;

    @Autowired
    private CallRecordService callRecordService;

    @Autowired
    private DialogRecordService dialogRecordService;

    @Autowired
    private CallService callService;

    @Value("${send.call.error.retry.duration:200}")
    private Integer sendCallErrorRetryDuration;

    @Value("${send.call.error.retry.times:3}")
    private Integer sendCallErrorRetryTimes;

    @Value("${send.call.error.switch:0}")
    private String sendCallErrorSwitch;

    @Value("${is.pressure.test:0}")
    private String isPressureTest;

    @Value("${audio.suffix:wav}")
    private String audioSuffix;

    @Autowired
    private CurrentServerIpService currentServerIpService;

    @Autowired
    private Map<String, Notify> notifyMap;

    @Autowired
    private CommonStatusService commonStatusService;

    @Autowired
    private SmartivrService smartivrService;


    public Map<String,Object> dealNoticeMessage(Map<String,Object> map){
        //StopWatch  stopWatch = new StopWatch();
        String callId = String.valueOf(map.get("callid").toString()).trim();
        String notifyString = String.valueOf(map.get("notify").toString()).trim();
        //stopWatch.start("获取redis缓存");
        ScriptStatus temp = commonStatusService.getCurrentStatus(callId);
        //stopWatch.stop();
        temp.setIsMultiContentVersion(ScriptUtils.isMultiContentVersion(temp.getScriptId(),temp.getVersion(), temp.getTrainPhone()));

        //if(temp.getIsEnd())
        //log.info("当前走的版本: "+temp.getIsMultiContentVersion());
        if(temp.getIsMultiContentVersion()){
            NotifyEnum notifyEnum = NotifyEnum.getNotifyByValue(notifyString);
            Notify notify = notifyMap.get(notifyEnum.getName());
            return notify.notify(callId,map);
        }else{
            //stopWatch.start("交互耗时");
            Map<String,Object> resultMap = smartivrService.dealNoticeMessage(map);
            //stopWatch.stop();
            //if(stopWatch.getTotalTimeMillis() > 50l){
            //    log.info("===>交互耗时：callId："+callId+": "+stopWatch.prettyPrint());
            //}
            return resultMap;

        }
    }


    public void cdr(String uuid,String cdr){
        //String cacheString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+uuid);
        //ScriptStatus userStatus = JSONObject.parseObject(cacheString,ScriptStatus.class);
        ScriptStatus userStatus = commonStatusService.getCurrentStatus(uuid);
        if(userStatus == null){
            return ;
        }

        if(uuid.endsWith("direct_call"))
            userStatus.setIsMultiContentVersion(false);
        else
            userStatus.setIsMultiContentVersion(ScriptUtils.isMultiContentVersion(userStatus.getScriptId(),userStatus.getVersion(), userStatus.getTrainPhone()));
        if(!userStatus.getIsMultiContentVersion()){
            smartivrService.cdr(uuid,cdr);
            return;
        }

        //log.info("===>cdr："+cdr);
        JSONObject jsonObject = JSONObject.parseObject(cdr,JSONObject.class);
        JSONObject cc = (JSONObject)jsonObject.get("variables");
        String hangupCause= cc.getString("hangup_cause");
        String hangupCauseQ850= cc.getString("hangup_cause_q850");
        int answerSec = cc.getIntValue("answersec");
        int billSec = cc.getIntValue("billsec");
        String speechCallId = cc.getString("speech_call_id");

        String sipCallId = null;
        if(cc.containsKey("sip_call_id"))
            sipCallId = cc.getString("sip_call_id");

        int billmsec = cc.getIntValue("billmsec");
        int mduration = cc.getIntValue("mduration");
        int waitmsec = mduration - billmsec;

        String lastApp = "";
        if(cc.containsKey("last_app")){
            lastApp = cc.getString("last_app");
        }
        String sip_hangup_disposition = "";
        if(cc.containsKey("sip_hangup_disposition")){
            sip_hangup_disposition = cc.getString("sip_hangup_disposition");
        }

        Integer whoHangup = 1;
        /*
        if(lastApp.contains("hangup")){
            whoHangup = 0;
        }
        //log.info("返回的cdr-json"+cdr);
        if((uuid.contains("take_over") || uuid.contains("listen_in") || uuid.contains("direct_call")) && whoHangup == 1 && cc.containsKey("bridge_hangup_cause")){
            whoHangup = 2;
        }
         */
        if(sip_hangup_disposition.startsWith("send")){
            whoHangup = 0;
            if(LocalCache.seatObtainTaskCache.getIfPresent(uuid) != null){
                String takeO = LocalCache.seatObtainTaskCache.getIfPresent(uuid).getTakeOverOrListenIn();
                if(takeO!=null && takeO.equals("take_over")){//如果是接管，则直接记录通话记录
                    whoHangup = 2;
                }
            }
        }
        toDB(uuid,hangupCause,cdr,billSec,hangupCauseQ850,waitmsec,whoHangup,billmsec,sipCallId);
    }

    public void toDB(String uuid,String hangupCause,String cdr,int billSec,String hangupCauseQ850,int waitmsec,int whoHangup,int billmsec,String sipCallId){
        String callChannelUniqueId = uuid;
        /*
        String cacheString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callChannelUniqueId);
        ScriptStatus userStatus = JSONObject.parseObject(cacheString,ScriptStatus.class);
        if(userStatus == null){
            try{Thread.sleep(20l);}catch (Exception e){}
            cacheString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callChannelUniqueId);
            userStatus = JSONObject.parseObject(cacheString,ScriptStatus.class);
            if(userStatus == null){
                try{Thread.sleep(20l);}catch (Exception e){}
                cacheString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callChannelUniqueId);
                userStatus = JSONObject.parseObject(cacheString,ScriptStatus.class);
            }
        }
         */
        //log.info("===>缓存字符串："+cacheString);
        //ScriptStatus userStatus = LocalCache.statusCache.getIfPresent(callChannelUniqueId);
        ScriptStatus userStatus = commonStatusService.getCurrentStatus(callChannelUniqueId);
        simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+callChannelUniqueId, JSONObject.toJSONString(userStatus, SerializerFeature.WriteClassName),ApplicationConstants.CALL_USER_STATUS_EXPIRE);

        if(uuid.endsWith("listen_in")||uuid.endsWith("take_over")||uuid.endsWith("direct_call"))
            if(LocalCache.seatObtainTaskCache.getIfPresent(callChannelUniqueId) != null){
                LocalCache.seatObtainTaskCache.getIfPresent(callChannelUniqueId).setEnd(true);
                if(LocalCache.seatObtainTaskExpire.getIfPresent(callChannelUniqueId) != null){
                    LocalCache.seatObtainTaskExpire.put(callChannelUniqueId,1);
                }
            }

        if(userStatus == null){
            //DingDingService.dingDingSendMsgException("Redis缓存中没有获取到用户的状态信息，call_id: " + uuid);
            log.error("Exception 当前callId 在redis缓冲中没找到" + uuid+
                    "当前ai-call: "+currentServerIpService.getAiCallFlag()+
                    "当前ip: "+currentServerIpService.getAiCallIp()+
                    "当前port: "+currentServerIpService.getAiCallPort()
            );
            DingDingService.dingDingSendMsgException(
                    "Exception 当前callId 在redis缓冲中没找到" + uuid,
                    "当前ai-call: "+currentServerIpService.getAiCallFlag(),
                    "当前ip: "+currentServerIpService.getAiCallIp(),
                    "当前port: "+currentServerIpService.getAiCallPort());
            return ;
        }

        CallPhoneProducerMaterial.PhoneDataCache userInfo = userStatus.getPhoneDataCache();
        //callService.getCallRecordService().toMQ(phoneDataCache.getTaskId(),phoneDataCache.getTaskName(),phoneDataCache.getPhone(),phoneDataCache.getLineId(),phoneDataCache.getTaskId(),-4,callChannelUniqueId,phoneDataCache.getSpeechCallId(),0L,"当前外呼用户有问题："+returnMsg,null,phoneDataCache.getLineCode());
        userStatus.getCallStatus().setCallDuration(billSec);
        userStatus.getCallStatus().setCallMDuration(billmsec);//通话时长毫秒
        userStatus.getCallStatus().setWaitMsec(waitmsec);//等待时间
        userStatus.getCallStatus().setIsNewRecord("0");//默认记录为非新建
        userStatus.getCallStatus().setWhoHangup(whoHangup);
        Integer callStatus = 0;
        if(hangupCause.contains("NORMAL_CLEARING"))//正常，其他都是非正常
            callStatus = 7;

        if(callStatus==0 && userStatus.getCallStatus().getCallMDuration()>0)
            callStatus =  7;

        if(hangupCause.contains("NORMAL_CLEARING") && billmsec < 100) {//小于100ms的都改成未呼通
            callStatus = 0;
            userStatus.getCallStatus().setCallStatus(0);
        }

        if(callStatus != 7 && userInfo.getSendCallErrorRetryTimes() < sendCallErrorRetryTimes && waitmsec <= sendCallErrorRetryDuration && sendCallErrorSwitch.equals("1")
        && !callChannelUniqueId.endsWith(TaskTypeEnum.LANDING_PAGE.getName())
        ){
            //
            //log.info("===>等待时长少于200ms，推送到送呼失败队列"+ JSON.toJSONString(userInfo));
            userStatus.getCallStatus().setIsNewRecord("1");//记录为新建
            if(StringUtils.isNotEmpty(userInfo.getTaskName()) && !userInfo.getTaskName().contains("话术训练") && !userInfo.getTaskName().contains("人工直呼"))
                //callService.backQueueSendFailure(ChannelManagerService.channelList1.get(0),userInfo);
                //log.info("=====>当前平台："+currentServerIpService.getCurrentPlatform());
                callService.backQueueSendFailure(ChannelManagerService.getRabbitMqChannelByPlatform(currentServerIpService.getCurrentPlatform(), MqTypeEnum.SECOND),userInfo);

        }



    /*
        if(userInfo.getPhone().contains("###fast-recall")){
            userStatus.getCallStatus().setIsNewRecord("1");//记录为新建
            userInfo.setPhone(userInfo.getPhone().replace("###fast-recall",""));
        }
    */
        if(userInfo.getSendCallErrorRetryTimes() >= sendCallErrorRetryTimes && waitmsec <= sendCallErrorRetryDuration && sendCallErrorSwitch.equals("1")){
            hangupCause = "呼叫失败";
        }

        callService.getCallRecordService().toMQCdr(userInfo.getTaskId(),userInfo.getTaskName(),userInfo.getPhone(),
                userInfo.getLineId(),userStatus.getScriptId(),callChannelUniqueId,userInfo.getSpeechCallId(),
                hangupCause,"",userInfo.getLineCode(),hangupCauseQ850,hangupCause,userStatus.getVersion(),
                userStatus.getCallStatus(), userStatus.getScriptLongId(), cdr, userStatus.getHitSemanticIdsString(),userInfo.getPlainPhone(),sipCallId);

    }

}

