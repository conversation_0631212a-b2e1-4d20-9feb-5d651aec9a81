package com.raipeng.aicall.service.notify.man;

import com.raipeng.aicall.constant.ActionTypeEnum;
import com.raipeng.aicall.constant.ApplicationConstants;
import com.raipeng.aicall.constant.LocalCache;
import com.raipeng.aicall.constant.NotifyEnum;
import com.raipeng.aicall.controller.qd.SmartivrListenNewResponse;
import com.raipeng.aicall.scriptsystem.ScriptStatus;
import com.raipeng.aicall.service.*;
import com.raipeng.aicall.service.notify.ManNotify;
import com.raipeng.common.entity.script.multicontent.ActiveMultiContent;
import com.raipeng.common.entity.script.multicontent.ActiveUnitContent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Service("man_enterNotify")
public class EnterNotify implements ManNotify {

    @Autowired
    private CommonStatusService commonStatusService;

    @Autowired
    private DialogRecordService dialogRecordService;

    @Autowired
    private ScriptService scriptService;

    @Autowired
    private SeatManager seatManager;

    @Autowired
    private SmartivrListenNewResponse smartivrListenNewResponse;

    @Autowired
    private SimpleRedisService simpleRedisService;

    @Override
    public Map notify(String callId,  Map<String,Object> msgNotify) {
        if(callId.startsWith("engine_") || callId.startsWith("C_"))
            simpleRedisService.setValueWithExpire(ApplicationConstants.HELPER_PREFIX+callId,"hangup",60l);
        else
            LocalCache.helpCache.put(callId,"success");
        ScriptStatus next = commonStatusService.getNextStatus(callId,"",false, NotifyEnum.ENTER);
        ActiveMultiContent activeMultiContent = next.getCurrentMultiContent();
        ActiveUnitContent activeUnitContent = null;
        List<ActiveUnitContent> activeUnitContents = activeMultiContent.getActiveUnitContents();
        for (ActiveUnitContent content : activeUnitContents) {
            if(!content.isPlayed()){
                activeUnitContent = content;
                break;
            }
        }
        if(activeUnitContent != null){
            dialogRecordService.toMqNew(callId,0,next,activeUnitContent,msgNotify,null);
        }
        if(!next.getCurrentMultiContent().getIfTriggerCallBack()) {
            next = scriptService.callbackNextStatus(next);
            next.getCurrentMultiContent().setIfTriggerCallBack(true);//已经触发
        }
        String fsIp = next.getCallStatus().getFsIp();
        if(next.getCurrentCorpus().isListenInOrTakeOver()){
            //if(!ManAiConstants.seatObtainGlobleTaskMap.containsKey(callId)){
            if(LocalCache.seatObtainTaskCache.getIfPresent(callId) == null){
                seatManager.addSeatObtainTask(callId,next.getPhoneDataCache().getTaskId(),next.getPhoneDataCache().getPhone(),next.getPhoneDataCache().getPlainPhone(),1000l, LocalDateTime.now().plusMinutes(100l),false,next.getPhoneDataCache().getCallTeamHandleType(),null,next.getPhoneDataCache().getSpeechCallId(),fsIp,msgNotify.getOrDefault("originCallId","").toString(),msgNotify.getOrDefault("bridge_fs_ip","").toString());
            }
        }

        //commonStatusService.setLocalStatusCache(callId,next);
        Map<String,Object> resultMap = smartivrListenNewResponse.response(NotifyEnum.ENTER, ActionTypeEnum.ENTER,next,callId,msgNotify);
        return resultMap;
    }

}
