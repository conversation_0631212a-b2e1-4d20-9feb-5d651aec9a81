package com.raipeng.aicall.service.notify.ai;

import com.alibaba.fastjson.JSONObject;
import com.raipeng.aicall.constant.ActionTypeEnum;
import com.raipeng.aicall.constant.ApplicationConstants;
import com.raipeng.aicall.constant.NotifyEnum;
import com.raipeng.aicall.controller.qd.SmartivrNewResponse;
import com.raipeng.aicall.mq.producter.material.CallPhoneProducerMaterial;
import com.raipeng.aicall.scriptsystem.CallStatusEnum;
import com.raipeng.aicall.scriptsystem.ScriptStatus;
import com.raipeng.aicall.service.CallRecordService;
import com.raipeng.aicall.service.CommonStatusService;
import com.raipeng.aicall.service.DialogRecordService;
import com.raipeng.aicall.service.notify.Notify;
import com.raipeng.common.entity.script.intention.IntentionTag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.stream.Collectors;

@Service("leaveNotify")
public class LeaveNotify implements Notify {

    @Autowired
    private CommonStatusService commonStatusService;

    @Autowired
    private CallRecordService callRecordService;

    @Value("${audio.suffix:mp3}")
    private String audioSuffix;

    @Autowired
    private SmartivrNewResponse smartivrNewResponse;

    @Override
    public Map notify(String callId,  Map<String,Object> msg) {
        ScriptStatus current = commonStatusService.getCurrentStatus(callId);
        current.getCallStatus().setCallEndTime(System.currentTimeMillis()/1000);
        String wholeRecordFile = null;
        if(msg.containsKey("bridge_fs_ip")){
            wholeRecordFile = "http://"+msg.getOrDefault("bridge_fs_ip","").toString().replace(":5060","")+ ApplicationConstants.FS_RECORD_FILE_DIR+ApplicationConstants.FS_RECORD_FILE_DIR_WHOLE+"/"+ LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"))+"/"+callId+"."+audioSuffix;
        }else{
            wholeRecordFile = "http://"+current.getCallStatus().getFsIp()+ ApplicationConstants.FS_RECORD_FILE_DIR+ApplicationConstants.FS_RECORD_FILE_DIR_WHOLE+"/"+ LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"))+"/"+callId+"."+audioSuffix;
        }
        CallPhoneProducerMaterial.PhoneDataCache phoneDataCache = current.getPhoneDataCache();
        current.getCallStatus().setCallStatus(CallStatusEnum.CALL_SUCCESS.ordinal());//呼叫完成
        Long duration = 0L;
        if(current.getCallStatus().getCallStartTime()==null || current.getCallStatus().getCallStartTime()==0){
            LocalDateTime time = LocalDateTime.parse(current.getCallStatus().getCallOutTime(),DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            Long timestamp = time.toInstant(ZoneOffset.ofHours(8)).toEpochMilli()/1000;
            duration = current.getCallStatus().getCallEndTime() - timestamp;
        }else{
            duration = current.getCallStatus().getCallEndTime() - current.getCallStatus().getCallStartTime();
        }
        current.getCallStatus().setCallDuration(duration.intValue());
        current.getCallStatus().setIntentionClass(current.getFinalIntentionType());
        current.getCallStatus().setTalkTimeEnd(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        current.getCallStatus().setIntentionLabelIds(current.getFinalIntentionLabelIdsString());
        current.getCallStatus().setIntentionLabels(current.getFinalIntentionLabelsString());

        current.getCallStatus().setHitAnswerIds(current.getHitAnswerIds());
        //TODO 请求高级标签，获取对应的等级和标签，该过程可能在对话中多次触发
        callRecordService.toMQ(phoneDataCache.getTaskId(),phoneDataCache.getTaskName(),phoneDataCache.getPhone(),phoneDataCache.getLineId(),phoneDataCache.getScriptId(),callId,phoneDataCache.getSpeechCallId(),"",wholeRecordFile,phoneDataCache.getLineCode(),"","",current.getVersion(),current.getCallStatus(),current.getScriptLongId(), JSONObject.toJSONString(current.getMatchedCorpusIds()), current.getHitSemanticIdsString(),phoneDataCache.getPlainPhone(), current.getFinalExtraInfo());

        return smartivrNewResponse.response(NotifyEnum.LEAVE, ActionTypeEnum.NOOP,null,callId,msg);
    }

}
