package com.raipeng.aicall.service.notify.man;

import com.raipeng.aicall.constant.*;
import com.raipeng.aicall.controller.qd.SmartivrListenNewResponse;
import com.raipeng.aicall.scriptsystem.ScriptStatus;
import com.raipeng.aicall.service.CommonStatusService;
import com.raipeng.aicall.service.DialogRecordService;
import com.raipeng.aicall.service.SeatManager;
import com.raipeng.aicall.service.notify.ManNotify;
import com.raipeng.common.entity.script.multicontent.ActiveUnitContent;
import com.raipeng.common.enums.InterruptType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;

import static com.raipeng.common.enums.InterruptType.SUPPORT_SEMANTIC_INTERRUPT_NO_REPLY;
import static com.raipeng.common.enums.InterruptType.SUPPORT_SEMANTIC_INTERRUPT_WITH_REPLY;

@Service("man_progressNotify")
public class ProgressNotify implements ManNotify {

    @Autowired
    private CommonStatusService commonStatusService;

    @Autowired
    private DialogRecordService dialogRecordService;

    @Autowired
    private SeatManager seatManager;

    @Autowired
    private SmartivrListenNewResponse smartivrListenNewResponse;


    @Override
    public Map notify(String callId,  Map<String,Object> msgNotify) {
        String sayContent = String.valueOf(msgNotify.get("message")).trim();
        ScriptStatus current = commonStatusService.getCurrentStatus(callId);
        int currentIndex = current.getCurrentMultiContent().getUnitContentIndex();

        if(current.getCurrentMultiContent().getIsNeedIndexReturn()){
            currentIndex = current.getCurrentMultiContent().getUnitContentIndex()-1;
        }else if(VideoStatusEnum.getNotifyByValue(current.getVideoStatus()) == VideoStatusEnum.STATUS_4 ||
                (VideoStatusEnum.getNotifyByValue(current.getVideoStatus()) == VideoStatusEnum.STATUS_1
                        && current.getCurrentMultiContent().isInPreInterruptMidstCopus())||
                VideoStatusEnum.getNotifyByValue(current.getVideoStatus()) == VideoStatusEnum.STATUS_5
        ){//当前正常语料中
            currentIndex = current.getCurrentMultiContent().getUnitContentIndex()-1;
        }

        ActiveUnitContent activeUnitContent = current.getCurrentMultiContent().getActiveUnitContents().get(
                currentIndex
        );

        dialogRecordService.toMqNew(callId, 1, current,activeUnitContent,msgNotify,sayContent);

        if(LocalCache.seatObtainTaskCache.getIfPresent(callId) != null){
            String takeO = LocalCache.seatObtainTaskCache.getIfPresent(callId).getTakeOverOrListenIn();
            if(takeO != null && takeO.equals("take_over")){//如果是接管，则直接记录通话记录
                return smartivrListenNewResponse.response(NotifyEnum.PROGRESS,ActionTypeEnum.NOOP,null,callId,msgNotify);//如果当前属于
            }
        }

        if(current.getIsEnd() && current.getVideoStatus().intValue() == 4)
            return smartivrListenNewResponse.response(NotifyEnum.PROGRESS, ActionTypeEnum.NOOP_HANGUP,current,callId,msgNotify);//不做任何动作

        InterruptType interruptType = activeUnitContent.getUnitContent().getInterruptType();
        if(interruptType != null && (interruptType.equals(SUPPORT_SEMANTIC_INTERRUPT_NO_REPLY)
                ||interruptType.equals(SUPPORT_SEMANTIC_INTERRUPT_WITH_REPLY))
                && sayContent.length() > 0) {
            Set<Long> isHitSemantics = null;
            try{
                if(current.getCurrentInterruptContent() != null){
                    if(current.getCurrentInterruptContent().getInterruptEndTime() > System.currentTimeMillis()/1000){
                        //不可打断
                        return smartivrListenNewResponse.response(NotifyEnum.PROGRESS,ActionTypeEnum.NOOP,null,callId,msgNotify);
                    }
                }
                isHitSemantics = commonStatusService.setHitSemanticId(current,sayContent);
            }catch (Exception e){
                e.printStackTrace();
                return smartivrListenNewResponse.response(NotifyEnum.PROGRESS,ActionTypeEnum.EXCEPTION,null,callId,msgNotify);
            }

            if(isHitSemantics != null && isHitSemantics.size() > 0){//命中语义
                //判断有没有打断垫句
                //打断垫句和续播垫句播放期间是不允许打断的
                if(VideoStatusEnum.getNotifyByValue(current.getVideoStatus()) == VideoStatusEnum.STATUS_4 &&
                        activeUnitContent.getPlayedCount() < 4){//只有正常播放期间，才判断是否有打断垫句
                    current.setIsEnd(false);
                    current.getCallStatus().getSemanticInterruptNum().incrementAndGet();
                    commonStatusService.setLocalPauseCache(callId);
                    current.getCurrentMultiContent().setPreInterruptCorpusId(activeUnitContent.getUnitContent().getPreInterruptCorpusId());
                    return smartivrListenNewResponse.response(NotifyEnum.PROGRESS, ActionTypeEnum.PAUSE, current,callId,msgNotify);
                }
            }
        }

        Map<String,Object> returnMap = smartivrListenNewResponse.response(NotifyEnum.PROGRESS,ActionTypeEnum.NOOP,null,callId,msgNotify);
        return returnMap;

    }
}
