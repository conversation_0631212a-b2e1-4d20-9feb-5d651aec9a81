package com.raipeng.aicall.service.notify.ai;

import com.raipeng.aicall.constant.ActionTypeEnum;
import com.raipeng.aicall.constant.NotifyEnum;
import com.raipeng.aicall.constant.VideoStatusEnum;
import com.raipeng.aicall.controller.qd.SmartivrNewResponse;
import com.raipeng.aicall.controller.qd.SmartivrNewResponse;
import com.raipeng.aicall.scriptsystem.ScriptStatus;
import com.raipeng.aicall.service.CommonStatusService;
import com.raipeng.aicall.service.DialogRecordService;
import com.raipeng.aicall.service.notify.Notify;
import com.raipeng.common.entity.script.multicontent.ActiveUnitContent;
import com.raipeng.common.enums.InterruptType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;

import static com.raipeng.common.enums.InterruptType.*;

@Service("progressNotify")
@Slf4j
public class ProgressNotify implements Notify {

    @Autowired
    private CommonStatusService commonStatusService;

    @Autowired
    private DialogRecordService dialogRecordService;

    @Autowired
    private SmartivrNewResponse smartivrNewResponse;


    @Override
    public Map notify(String callId,  Map<String,Object> msgNotify) {
        String sayContent = String.valueOf(msgNotify.get("message")).trim();
        ScriptStatus current = commonStatusService.getCurrentStatus(callId);
        //log.info("进来progress当前的索引"+current.getCurrentMultiContent().getUnitContentIndex());
        int currentIndex = current.getCurrentMultiContent().getUnitContentIndex();

        if(current.getCurrentMultiContent().getIsNeedIndexReturn()){
            currentIndex = current.getCurrentMultiContent().getUnitContentIndex()-1;
        }else if(VideoStatusEnum.getNotifyByValue(current.getVideoStatus()) == VideoStatusEnum.STATUS_4 ||
               (VideoStatusEnum.getNotifyByValue(current.getVideoStatus()) == VideoStatusEnum.STATUS_1
                       && current.getCurrentMultiContent().isInPreInterruptMidstCopus())||
                VideoStatusEnum.getNotifyByValue(current.getVideoStatus()) == VideoStatusEnum.STATUS_5
        ){//当前正常语料中
            currentIndex = current.getCurrentMultiContent().getUnitContentIndex()-1;
        }

        ActiveUnitContent activeUnitContent = current.getCurrentMultiContent().getActiveUnitContents().get(
                currentIndex
        );
        //log.info("入库7");
        dialogRecordService.toMqNew(callId, 1, current,activeUnitContent,msgNotify,sayContent);
        if(current.getIsEnd() && current.getVideoStatus().intValue() == 4)
            return smartivrNewResponse.response(NotifyEnum.PROGRESS, ActionTypeEnum.NOOP_HANGUP,current,callId,msgNotify);//不做任何动作
        InterruptType interruptType = activeUnitContent.getUnitContent().getInterruptType();
        if(interruptType != null && (interruptType.equals(SUPPORT_SEMANTIC_INTERRUPT_NO_REPLY)
                ||interruptType.equals(SUPPORT_SEMANTIC_INTERRUPT_WITH_REPLY))
                && sayContent.length() > 0) {
            Set<Long> isHitSemantics = null;
            try{
                if(current.getCurrentInterruptContent() != null){
                    if(current.getCurrentInterruptContent().getInterruptEndTime() > System.currentTimeMillis()/1000){
                        //不可打断
                        return smartivrNewResponse.response(NotifyEnum.PROGRESS,ActionTypeEnum.NOOP,null,callId,msgNotify);
                    }
                }
                isHitSemantics = commonStatusService.setHitSemanticId(current,sayContent);
            }catch (Exception e){
                e.printStackTrace();
                return smartivrNewResponse.response(NotifyEnum.PROGRESS,ActionTypeEnum.EXCEPTION,null,callId,msgNotify);
            }

            if(isHitSemantics != null && isHitSemantics.size() > 0){//命中语义
                //判断有没有打断垫句
                //打断垫句和续播垫句播放期间是不允许打断的

                if(VideoStatusEnum.getNotifyByValue(current.getVideoStatus()) == VideoStatusEnum.STATUS_4 &&
                        activeUnitContent.getPlayedCount() < 4){//只有正常播放期间，才判断是否有打断垫句
                    current.setIsEnd(false);
                    current.getCallStatus().getSemanticInterruptNum().incrementAndGet();
                    commonStatusService.setLocalPauseCache(callId);
                    current.getCurrentMultiContent().setPreInterruptCorpusId(activeUnitContent.getUnitContent().getPreInterruptCorpusId());
                    return smartivrNewResponse.response(NotifyEnum.PROGRESS, ActionTypeEnum.PAUSE, current,callId,msgNotify);
                }
            }
        }

        Map<String,Object> returnMap = smartivrNewResponse.response(NotifyEnum.PROGRESS,ActionTypeEnum.NOOP,null,callId,msgNotify);
        return returnMap;

    }
}
