package com.raipeng.aicall.service.notify.ai;

import com.raipeng.aicall.constant.ActionTypeEnum;
import com.raipeng.aicall.constant.ApplicationConstants;
import com.raipeng.aicall.constant.LocalCache;
import com.raipeng.aicall.constant.NotifyEnum;
import com.raipeng.aicall.controller.qd.SmartivrNewResponse;
import com.raipeng.aicall.controller.qd.SmartivrResponse;
import com.raipeng.aicall.scriptsystem.ScriptStatus;
import com.raipeng.aicall.service.CommonStatusService;
import com.raipeng.aicall.service.DialogRecordService;
import com.raipeng.aicall.service.ScriptService;
import com.raipeng.aicall.service.SimpleRedisService;
import com.raipeng.aicall.service.notify.Notify;
import com.raipeng.common.entity.script.multicontent.ActiveMultiContent;
import com.raipeng.common.entity.script.multicontent.ActiveUnitContent;
import com.raipeng.common.entity.script.multicontent.MultiContent;
import com.raipeng.common.entity.script.multicontent.UnitContent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service("enterNotify")
public class EnterNotify implements Notify {

    @Autowired
    private CommonStatusService commonStatusService;

    @Autowired
    private DialogRecordService dialogRecordService;

    @Autowired
    private ScriptService scriptService;

    @Autowired
    private SmartivrNewResponse smartivrNewResponse;

    @Autowired
    private SimpleRedisService simpleRedisService;

    @Override
    public Map notify(String callId,  Map<String,Object> msgNotify) {
        if(callId.startsWith("engine_") || callId.startsWith("C_"))
            simpleRedisService.setValueWithExpire(ApplicationConstants.HELPER_PREFIX+callId,"hangup",60l);
        else
            LocalCache.helpCache.put(callId,"success");
        ScriptStatus next = commonStatusService.getNextStatus(callId,"",false, NotifyEnum.ENTER);
        ActiveMultiContent activeMultiContent = next.getCurrentMultiContent();
        ActiveUnitContent activeUnitContent = null;
        List<ActiveUnitContent> activeUnitContents = activeMultiContent.getActiveUnitContents();
        for (ActiveUnitContent content : activeUnitContents) {
            if(!content.isPlayed()){
                activeUnitContent = content;
                break;
            }
        }
        if(activeUnitContent != null){
            dialogRecordService.toMqNew(callId,0,next,activeUnitContent,msgNotify,null);
        }
        if(!next.getCurrentMultiContent().getIfTriggerCallBack()) {
            //log.info("调用callback1");
            next = scriptService.callbackNextStatus(next);
            next.getCurrentMultiContent().setIfTriggerCallBack(true);//已经触发
        }
        //commonStatusService.setLocalStatusCache(callId,next);
        Map<String,Object> resultMap = smartivrNewResponse.response(NotifyEnum.ENTER, ActionTypeEnum.ENTER,next,callId,msgNotify);
        return resultMap;
    }

}
