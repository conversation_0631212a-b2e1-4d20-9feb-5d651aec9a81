package com.raipeng.aicall.service.notify.man;

import com.raipeng.aicall.constant.ActionTypeEnum;
import com.raipeng.aicall.constant.LocalCache;
import com.raipeng.aicall.constant.NotifyEnum;
import com.raipeng.aicall.controller.qd.SmartivrListenNewResponse;
import com.raipeng.aicall.controller.qd.SmartivrNewResponse;
import com.raipeng.aicall.scriptsystem.ScriptStatus;
import com.raipeng.aicall.service.CommonStatusService;
import com.raipeng.aicall.service.DialogRecordService;
import com.raipeng.aicall.service.ScriptService;
import com.raipeng.aicall.service.SeatManager;
import com.raipeng.aicall.service.notify.ManNotify;
import com.raipeng.common.entity.script.multicontent.ActiveUnitContent;
import com.raipeng.common.enums.InterruptType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service("man_playbackNotify")
public class PlaybackNotify implements ManNotify {

    @Autowired
    private CommonStatusService commonStatusService;

    @Autowired
    private DialogRecordService dialogRecordService;

    @Autowired
    private ScriptService scriptService;

    @Autowired
    private SeatManager seatManager;

    @Autowired
    private SmartivrListenNewResponse smartivrListenNewResponse;

    @Override
    public Map notify(String callId, Map<String,Object> msgNotify) {
        ScriptStatus current = commonStatusService.getCurrentStatus(callId);
        String sayContent = ((String)msgNotify.get("message")).trim();
        if(sayContent.contains("playback error"))
            return smartivrListenNewResponse.response(NotifyEnum.PLAYBACK, ActionTypeEnum.NOOP,null,callId,msgNotify);//不做任何动作

        ActiveUnitContent activeUnitContent = commonStatusService.getActiveUnitContent(current);
        if(msgNotify.containsKey("asrstate")){
            String asrstate =  String.valueOf(msgNotify.get("asrstate")).trim();
            if(asrstate.equals("true") && activeUnitContent != null
                    && (activeUnitContent.getUnitContent().getInterruptType() == InterruptType.SUPPORT_SOUND_INTERRUPT_NO_REPLY ||
                    activeUnitContent.getUnitContent().getInterruptType() == InterruptType.SUPPORT_SOUND_INTERRUPT_WITH_REPLY )
            )//用户开始说话
                return smartivrListenNewResponse.response(NotifyEnum.PLAYBACK,ActionTypeEnum.NOOP,null,callId,msgNotify);//不做任何动作
        }

        if(current.getIsEnd()  && current.getVideoStatus().intValue() == 4) {//结束语料 支持打断
            if (current.getCurrentMultiContent().getIfTriggerNeedEnd()) {
                return smartivrListenNewResponse.response(NotifyEnum.PLAYBACK, ActionTypeEnum.END, current, callId,msgNotify);//不做任何动作
            }
            return smartivrListenNewResponse.response(NotifyEnum.PLAYBACK, ActionTypeEnum.NOOP_HANGUP, null, callId,msgNotify);//不做任何动作
        }

        if(activeUnitContent != null  && activeUnitContent.getPlayedCount() < 4) {
            if(current.getCurrentMultiContent().getBlankVideo() <= 0
                    && current.getCurrentMultiContent().getPreContinueCorpusId() <= 0
            && current.getCurrentMultiContent().getPreContinueCorpusIdForReturn() <= 0) {
                dialogRecordService.toMqNew(callId, 0, current, activeUnitContent, msgNotify, null);
                if(!current.getCurrentMultiContent().getIfTriggerCallBack()){
                    current = scriptService.callbackNextStatus(current);
                    current.getCurrentMultiContent().setIfTriggerCallBack(true);
                }
            }
            return smartivrListenNewResponse.response(NotifyEnum.PLAYBACK, ActionTypeEnum.OTHER, current, callId,msgNotify);
        }else{
            if(current.getCurrentMultiContent().isFinishedPlaying()){
                current.getCurrentMultiContent().setFinishedPlay(true);
                LocalCache.statusCache.put(callId,current);
            }
            ScriptStatus next =  commonStatusService.getNextStatus(callId,"",true,NotifyEnum.PLAYBACK);
            activeUnitContent = commonStatusService.getActiveUnitContent(next);
            if(activeUnitContent != null) {
                next.getCurrentMultiContent().setBlankVideo(0l);
                /*
                    if(next.getCurrentMultiContent().isReturn() && activeUnitContent.getUnitContent().getPreContinueCorpusIdForReturn() > 0)
                        next.getCurrentMultiContent().setPreContinueCorpusIdForReturn(activeUnitContent.getUnitContent().getPreContinueCorpusIdForReturn());
                    else
                        next.getCurrentMultiContent().setPreContinueCorpusIdForReturn(0l);

                     */
                next.getCurrentMultiContent().setIsNeedIndexReturn(false);//这里需要把是否是指针回拨给去掉，因为返回的已经被回拨了fff
                if(!next.getCurrentMultiContent().isReturn()){
                    next.getCurrentMultiContent().setPreContinueCorpusIdForReturn(0l);
                }

                next.getCurrentMultiContent().setPreContinueCorpusId(0l);
                next.getCurrentMultiContent().setPreInterruptCorpusId(0l);
                if(next.getCurrentMultiContent().getBlankVideo() <= 0
                        && (next.getCurrentMultiContent().getPreContinueCorpusId() == null || next.getCurrentMultiContent().getPreContinueCorpusId() <= 0)
                        && (next.getCurrentMultiContent().getPreContinueCorpusIdForReturn() == null || next.getCurrentMultiContent().getPreContinueCorpusIdForReturn() <= 0)) {
                    dialogRecordService.toMqNew(callId, 0, next, activeUnitContent,msgNotify,null);
                    if(!next.getCurrentMultiContent().getIfTriggerCallBack()){
                        next = scriptService.callbackNextStatus(next);
                        next.getCurrentMultiContent().setIfTriggerCallBack(true);//触发
                    }
                }
                return smartivrListenNewResponse.response(NotifyEnum.PLAYBACK, ActionTypeEnum.OTHER, next, callId,msgNotify);
            }else{
                return smartivrListenNewResponse.response(NotifyEnum.PLAYBACK, ActionTypeEnum.NOOP,null,callId,msgNotify);//不做任何动作
            }
        }

    }
}
