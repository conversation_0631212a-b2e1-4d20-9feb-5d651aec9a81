package com.raipeng.aicall.service.notify.ai;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.raipeng.aicall.constant.*;
import com.raipeng.aicall.controller.qd.SmartivrNewResponse;
import com.raipeng.aicall.scriptsystem.CallStatus;
import com.raipeng.aicall.scriptsystem.ScriptStatus;
import com.raipeng.aicall.service.CommonStatusService;
import com.raipeng.aicall.service.DialogRecordService;
import com.raipeng.aicall.service.ScriptService;
import com.raipeng.aicall.service.notify.Notify;
import com.raipeng.common.entity.script.multicontent.ActiveUnitContent;
import com.raipeng.common.enums.InterruptType;
import com.raipeng.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

import static com.raipeng.common.enums.InterruptType.*;
import static com.raipeng.common.enums.ScriptCorpusTypeEnum.FUNC_PRIOR_QA;

@Service("messageNotify")
@Slf4j
public class MessageNotify implements Notify {

    @Autowired
    private CommonStatusService commonStatusService;

    @Autowired
    private DialogRecordService dialogRecordService;

    @Autowired
    private ScriptService scriptService;

    @Autowired
    private SmartivrNewResponse smartivrNewResponse;



    @Override
    public Map notify(String callId,  Map<String,Object> msgNotify) {
        String sayContent = String.valueOf(msgNotify.get("message")).trim();
        ScriptStatus current = commonStatusService.getCurrentStatus(callId);
        int currentIndex = current.getCurrentMultiContent().getUnitContentIndex();
        //log.info("进入message之前的索引号："+currentIndex);

        if(current.getCurrentMultiContent().getIsNeedIndexReturn()){
            currentIndex = current.getCurrentMultiContent().getUnitContentIndex()-1;
        }else if(VideoStatusEnum.getNotifyByValue(current.getVideoStatus()) == VideoStatusEnum.STATUS_4 ||
                (VideoStatusEnum.getNotifyByValue(current.getVideoStatus()) == VideoStatusEnum.STATUS_1
                        && current.getCurrentMultiContent().isInPreInterruptMidstCopus())||
                VideoStatusEnum.getNotifyByValue(current.getVideoStatus()) == VideoStatusEnum.STATUS_5
        ){//当前正常语料中
            currentIndex = current.getCurrentMultiContent().getUnitContentIndex()-1;
        }

        commonStatusService.setCallStatus(current,callId,sayContent);

        //log.info("当前语料块"+ JSON.toJSONString(current.getCurrentMultiContent().getActiveUnitContents()));
        //log.info("当前索引号"+currentIndex+":空音频播放次数"+current.getCurrentMultiContent().getBlankVideoCount());
        ActiveUnitContent activeUnitContent = current.getCurrentMultiContent().getActiveUnitContents().get(
                currentIndex
        );
        //log.info("入库1");
        dialogRecordService.toMqNew(callId,2,current,activeUnitContent,msgNotify,sayContent);
        String playstate = String.valueOf(msgNotify.getOrDefault("playstate",""));

        InterruptType interruptType = activeUnitContent.getUnitContent().getInterruptType();
        //log.info("当前代码58");
        //Set<Long> isHitSemantics = commonStatusService.setHitSemanticId(current,sayContent);
        if(StringUtils.isEmpty(sayContent) && !current.getIsEnd()){
            //log.info("当前代码61");
            if (playstate.equals("false")) {
                //删除空音频播放次数
                current.getCurrentMultiContent().setBlankVideoCount(0);
                //产生一个等待事件
                if(LocalCache.waitCache.getIfPresent(callId) == null){
                    LocalCache.waitCache.put(callId,1);
                    return smartivrNewResponse.response(NotifyEnum.MESSAGE, ActionTypeEnum.WAIT, current, callId,msgNotify);//直接等2s
                }
            }
            if(interruptType == SUPPORT_SOUND_INTERRUPT_NO_REPLY
                    || interruptType == SUPPORT_SOUND_INTERRUPT_WITH_REPLY
                    || (LocalCache.pauseCache.getIfPresent(callId) != null && current.getVideoStatus() == 0)
            ){
                return smartivrNewResponse.response(NotifyEnum.MESSAGE,ActionTypeEnum.RESUME,current,callId,msgNotify);
            }
            return smartivrNewResponse.response(NotifyEnum.MESSAGE,ActionTypeEnum.NOOP,null,callId,msgNotify);
        }

        if(LocalCache.waitCache.getIfPresent(callId) != null) {//到这里，一定是有识别不为空的一句话
            LocalCache.waitCache.invalidate(callId);
        }

        if(current.getIsEnd() && current.getVideoStatus().intValue() == 4){
            //TODO 挂机语料支持打断，这里的打断是语义打断，挂机语料需要选择指定语料，勾选多个挂机语料就是勾选多个语义了。这里不支持xxs后打断，打断垫句和续播垫句和承接垫句都不支持
            //TODO 所以挂机语料的打断，放到完整一句话中，不放在半句话中，这种好处理一点
            if(current.getCurrentMultiContent().getActiveUnitContents().get(currentIndex).getUnitContent().getInterruptType()
                    == HANG_UP_SPECIAL_CORPUS_INTERRUPT ){
                ScriptStatus temp = commonStatusService.getNextStatusHangup(callId,sayContent,false,NotifyEnum.MESSAGE);
                //log.info("当前挂机语料没命中:"+sayContent+":"+temp.getCurrentMultiContent());
                if(temp.getCurrentMultiContent() != null){
                    //log.info("进入新的允许挂机逻辑");
                    commonStatusService.interrupt(temp,callId);
                    //这里要开启打断等待，通过播放空音频
                    temp.getCurrentMultiContent().setBlankVideo(1l);//说明需要配置空白音频
                    temp.getCurrentMultiContent().setBlankVideoCount(0);//设置空音频个数1
                    //ActiveUnitContent temp1 = temp.getCurrentMultiContent().getActiveUnitContents().get(0);
                    return smartivrNewResponse.response(NotifyEnum.MESSAGE, ActionTypeEnum.END, temp, callId,msgNotify);// 结束语料和正常播放语料，都需要播放空音频
                }
            }

            if(current.getCurrentMultiContent().getBlankVideoing() && current.getCurrentMultiContent().getBlankVideoCount() < 3) {//空音频播放中，继续播放空音频
                current.getCurrentMultiContent().setBlankVideo(1);//设置需要播放空音频，并且设置播放空音频的次数
                return smartivrNewResponse.response(NotifyEnum.MESSAGE, ActionTypeEnum.END, current, callId,msgNotify);// 结束语料和正常播放语料，都需要播放空音频
            }else if(current.getCurrentMultiContent().getBlankVideoing()){
                current.getCurrentMultiContent().setBlankVideo(0);
                return smartivrNewResponse.response(NotifyEnum.MESSAGE, ActionTypeEnum.END, current, callId,msgNotify);
            }

            return smartivrNewResponse.response(NotifyEnum.MESSAGE, ActionTypeEnum.NOOP_HANGUP, null, callId,msgNotify);//直接等2s
        }

        //判断打断次数
        /*
        Integer playCount = current.getCurrentMultiContent().getBlankVideoCount();
        if(playCount > 0)
            interruptType = SUPPORT_SOUND_INTERRUPT_WITH_REPLY;//TODO 这里设置成发声打断且回复
         */


        //匹配最高优先-挂机语料
        ScriptStatus temp = commonStatusService.getNextStatus(callId,sayContent,false,NotifyEnum.MESSAGE);

        //log.info("空音频播放次数120: "+current.getCurrentMultiContent().getBlankVideoCount());
        if(temp.getCurrentCorpus() != null &&  temp.getIsEnd() && temp.getCurrentCorpus().getCorpusType() == FUNC_PRIOR_QA){
            //log.info("当前代码118");
            commonStatusService.interrupt(temp,callId);
            //这里要开启打断等待，通过播放空音频
            temp.getCurrentMultiContent().setBlankVideo(1l);//说明需要配置空白音频
            temp.getCurrentMultiContent().setBlankVideoCount(0);//设置空音频个数1
            temp.getCurrentMultiContent().setPreContinueCorpusId(0L);//命中肯定没有续播垫句
            if(current.getCurrentMultiContent().getPreInterruptCorpusId()>0){
                temp.getCurrentMultiContent().setPreInterruptCorpusId(current.getCurrentMultiContent().getPreInterruptCorpusId());//命中把上一个语料上设置的打断迁移过来
                temp.getCurrentMultiContent().setInPreInterruptMidstCopus(false);
            }
            temp.setVideoStatus(6);//新语料

            //ActiveUnitContent temp1 = temp.getCurrentMultiContent().getActiveUnitContents().get(0);
            return smartivrNewResponse.response(NotifyEnum.MESSAGE, ActionTypeEnum.END, temp, callId,msgNotify);// 结束语料和正常播放语料，都需要播放空音频
        }

        if(current.getVideoStatus().intValue() == 0){
            //log.info("进入代码178:当前的打断类别："+interruptType+"是否命中"+temp.getHitPhrase()+"当前语料"+currentIndex);
            //if(interruptType == SUPPORT_SOUND_INTERRUPT_WITH_REPLY || interruptType == SUPPORT_SEMANTIC_INTERRUPT_WITH_REPLY){
            if(currentIndex == 0){
                if(temp.getHitPhrase()){

                    if(temp.getCurrentMultiContent().getBlankVideo() <= 0
                            && (temp.getCurrentMultiContent().getPreContinueCorpusId() == null || temp.getCurrentMultiContent().getPreContinueCorpusId() <= 0)
                            && (temp.getCurrentMultiContent().getPreContinueCorpusIdForReturn() == null || temp.getCurrentMultiContent().getPreContinueCorpusIdForReturn() <= 0)) {
                        //log.info("入库2");
                        dialogRecordService.toMqNew(callId, 0, temp, activeUnitContent, msgNotify, null);
                        if(!temp.getCurrentMultiContent().getIfTriggerCallBack()){
                            //log.info("调用callback2");
                            temp = scriptService.callbackNextStatus(temp);
                            temp.getCurrentMultiContent().setIfTriggerCallBack(true);
                        }
                    }

                    return smartivrNewResponse.response(NotifyEnum.MESSAGE, ActionTypeEnum.OTHER, temp, callId,msgNotify);// 结束语料和正常播放语料，都需要播放空音频
                }
            }
            //log.info("当前空音频播放次数"+current.getCurrentMultiContent().getBlankVideoCount());
            if(current.getCurrentMultiContent().getBlankVideoCount() < 4){
                //log.info("进入空音频");
                current.getCurrentMultiContent().setBlankVideo(1l);
                //current.getCurrentMultiContent().setBlankVideoCount(0);//设置空音频个数1
                return smartivrNewResponse.response(NotifyEnum.MESSAGE, ActionTypeEnum.OTHER, current, callId,msgNotify);//先播续播垫句，然后重播
            }else{
                //log.info("跳出了空音频");
                //log.info("跳出了空音频的次数"+current.getCurrentMultiContent().getBlankVideoCount());
                return smartivrNewResponse.response(NotifyEnum.MESSAGE, ActionTypeEnum.RESUME, current, callId,msgNotify);//先播续播垫句，然后重播
            }
        }

        //打断不回复
        if(interruptType == SUPPORT_SOUND_INTERRUPT_NO_REPLY
                || interruptType == SUPPORT_SEMANTIC_INTERRUPT_NO_REPLY
                || interruptType == SUPPORT_SOUND_INTERRUPT_WITH_REPLY
                || interruptType == SUPPORT_SEMANTIC_INTERRUPT_WITH_REPLY
                || interruptType == CAN_NOT_BE_INTERRUPTED){
            if(playstate.equals("false")){//
                current.resetCurrentBlankVideoCount();

                ActiveUnitContent needPlayActiveUnitContent = commonStatusService.getActiveUnitContent(current);
                if(needPlayActiveUnitContent != null) {
                    if(current.getCurrentMultiContent().getBlankVideo() <= 0
                            && current.getCurrentMultiContent().getPreContinueCorpusId() <= 0
                            && current.getCurrentMultiContent().getPreContinueCorpusIdForReturn() <= 0) {
                        //log.info("入库3");
                        dialogRecordService.toMqNew(callId, 0, current, activeUnitContent, msgNotify, null);
                        if(!current.getCurrentMultiContent().getIfTriggerCallBack()){
                            //log.info("调用callback3");
                            current = scriptService.callbackNextStatus(current);
                            current.getCurrentMultiContent().setIfTriggerCallBack(true);
                        }
                    }
                    return smartivrNewResponse.response(NotifyEnum.PLAYBACK, ActionTypeEnum.OTHER, current, callId,msgNotify);
                }

                if(current.getCurrentMultiContent().isFinishedPlaying()){
                    current.getCurrentMultiContent().setFinishedPlay(true);
                    LocalCache.statusCache.put(callId,current);
                }

                ScriptStatus next = commonStatusService.getNextStatus(callId,sayContent,false,NotifyEnum.MESSAGE);
                activeUnitContent = commonStatusService.getActiveUnitContent(next);
                if(activeUnitContent != null){
                    next.getCurrentMultiContent().setBlankVideo(0l);
                    //log.info("返回续播垫句："+next.getCurrentMultiContent().getPreContinueCorpusIdForReturn());
                    //log.info("是否是返回："+next.getCurrentMultiContent().isReturn());
                    /*
                    if(next.getCurrentMultiContent().isReturn() && activeUnitContent.getUnitContent().getPreContinueCorpusIdForReturn() > 0)
                        next.getCurrentMultiContent().setPreContinueCorpusIdForReturn(activeUnitContent.getUnitContent().getPreContinueCorpusIdForReturn());
                    else
                        next.getCurrentMultiContent().setPreContinueCorpusIdForReturn(0l);

                     */
                    next.getCurrentMultiContent().setIsNeedIndexReturn(false);//这里需要把是否是指针回拨给去掉，因为返回的已经被回拨了
                    if(!next.getCurrentMultiContent().isReturn()){
                        next.getCurrentMultiContent().setPreContinueCorpusIdForReturn(0l);
                    }
                    next.getCurrentMultiContent().setPreContinueCorpusId(0l);
                    next.getCurrentMultiContent().setPreInterruptCorpusId(0l);
                    if(next.getCurrentMultiContent().getBlankVideo() <= 0
                            && (next.getCurrentMultiContent().getPreContinueCorpusId() == null || next.getCurrentMultiContent().getPreContinueCorpusId() <= 0)
                            && (next.getCurrentMultiContent().getPreContinueCorpusIdForReturn() == null || next.getCurrentMultiContent().getPreContinueCorpusIdForReturn() <= 0)) {
                        //log.info("入库4");
                        dialogRecordService.toMqNew(callId, 0, next, activeUnitContent,msgNotify,null);
                        if(!next.getCurrentMultiContent().getIfTriggerCallBack()){
                            next = scriptService.callbackNextStatus(next);
                            next.getCurrentMultiContent().setIfTriggerCallBack(true);//触发
                        }
                    }
                    return smartivrNewResponse.response(NotifyEnum.MESSAGE, ActionTypeEnum.OTHER, next, callId,msgNotify);

                }

                return smartivrNewResponse.response(NotifyEnum.MESSAGE, ActionTypeEnum.NOOP,null,callId,msgNotify);//不做任何动作
            }
            //判断是否在续播垫句，打断垫句，返回续播垫句中这些句子都不允许打断
            if((current.getVideoStatus() == 1 || current.getVideoStatus() == 2 || current.getVideoStatus() == 3) && playstate == "true"){
                 //TODO 不可打断，只更新目标语料
                return smartivrNewResponse.response(NotifyEnum.MESSAGE, ActionTypeEnum.NOOP,null,callId,msgNotify);//不做任何动作
            }

        }

        if(interruptType == SUPPORT_SEMANTIC_INTERRUPT_NO_REPLY){//TODO 语义打断不回复，播放一句空音频，然后恢复播放
            if(current.getCurrentInterruptContent() != null && current.getCurrentInterruptContent().getInterruptEndTime() > System.currentTimeMillis()/1000){
                return smartivrNewResponse.response(NotifyEnum.MESSAGE, ActionTypeEnum.NOOP, current, callId,msgNotify);
            }
            //TODO 这里要进行恢复播放，恢复播放先播放空音频，然后播放续播垫句，然后将当前语料再播放一次,currentIndex 永远指向没有使用的语料块
            //1、没打断不操作
            if(LocalCache.pauseCache.getIfPresent(callId) == null){
                return smartivrNewResponse.response(NotifyEnum.MESSAGE, ActionTypeEnum.NOOP, current, callId,msgNotify);
            }

            //有打断，播放空音频,播放续播语料
            ActiveUnitContent temp1 = current.getCurrentMultiContent().getActiveUnitContents().get(
                    currentIndex
            );
            //log.info("进入代码188");
            //空音频只能是发声打断，续播垫句不可打断
            if(activeUnitContent.getPlayedCount() < 4){
                if(LocalCache.pauseCache.getIfPresent(callId) != null){
                    temp1.setPlayed(false);
                    temp1.setPlayFinished(false);//恢复先播空音频，然后判断是否有续播垫句，然后再播放语料块
                    current.getCurrentMultiContent().setBlankVideo(1l);
                    current.getCurrentMultiContent().setBlankVideoCount(0);//设置空音频个数1
                    current.getCurrentMultiContent().setPreContinueCorpusId(temp1.getUnitContent().getPreContinueCorpusIdForInterrupt());
                    current.getCurrentMultiContent().setPreContinueCorpusIdForReturn(0l);
                    LocalCache.pauseCache.invalidate(callId);
                    return smartivrNewResponse.response(NotifyEnum.MESSAGE, ActionTypeEnum.OTHER, current, callId,msgNotify);//先播续播垫句，然后重播
                }
            }
            return smartivrNewResponse.response(NotifyEnum.MESSAGE, ActionTypeEnum.NOOP, null, callId,msgNotify);//先播续播垫句，然后重播

        }else if(interruptType == SUPPORT_SEMANTIC_INTERRUPT_WITH_REPLY){
            if(current.getCurrentInterruptContent() != null && current.getCurrentInterruptContent().getInterruptEndTime() > System.currentTimeMillis()/1000){
                return smartivrNewResponse.response(NotifyEnum.MESSAGE, ActionTypeEnum.NOOP, current, callId,msgNotify);
            }
            if(LocalCache.pauseCache.getIfPresent(callId) == null){
                return smartivrNewResponse.response(NotifyEnum.MESSAGE, ActionTypeEnum.NOOP, current, callId,msgNotify);
            }
            //log.info("进入代码189");
            if(!temp.getHitPhrase() && current.getCurrentMultiContent().getBlankVideoCount() < 4 && activeUnitContent.getPlayedCount() < 4) {//没有命中直接恢复播放-播放空音频，播放续播垫句
                //有打断，播放空音频,播放续播语料
                if(LocalCache.pauseCache.getIfPresent(callId) != null){
                    ActiveUnitContent temp1 = current.getCurrentMultiContent().getActiveUnitContents().get(
                            currentIndex
                    );
                    //current.getCurrentMultiContent().setUnitContentIndex(currentIndex);
                    //空音频只能是发声打断，续播垫句不可打断
                    temp1.setPlayed(false);
                    temp1.setPlayFinished(false);//恢复先播空音频，然后判断是否有续播垫句，然后再播放语料块
                    current.getCurrentMultiContent().setBlankVideo(1l);
                    current.getCurrentMultiContent().setBlankVideoCount(0);//设置空音频个数1
                    current.getCurrentMultiContent().setPreContinueCorpusId(temp1.getUnitContent().getPreContinueCorpusIdForInterrupt());
                    current.getCurrentMultiContent().setPreContinueCorpusIdForReturn(0l);
                    LocalCache.pauseCache.invalidate(callId);
                    //log.info("进入代码202");
                    return smartivrNewResponse.response(NotifyEnum.MESSAGE, ActionTypeEnum.OTHER, current, callId,msgNotify);//先播续播垫句，然后重播
                }
            }
            //设置空音频
            //log.info("进入代码207");
            if(!temp.getHitPhrase()){
                return smartivrNewResponse.response(NotifyEnum.MESSAGE, ActionTypeEnum.NOOP, null, callId,msgNotify);//先播续播垫句，然后重播
            }
            temp.getCurrentMultiContent().setBlankVideo(1l);
            temp.getCurrentMultiContent().setBlankVideoCount(0);//设置空音频个数1
            temp.getCurrentMultiContent().setPreContinueCorpusId(0L);//命中肯定没有续播垫句
            if(current.getCurrentMultiContent().getPreInterruptCorpusId()>0){
                temp.getCurrentMultiContent().setPreInterruptCorpusId(current.getCurrentMultiContent().getPreInterruptCorpusId());//命中把上一个语料上设置的打断迁移过来
                temp.getCurrentMultiContent().setInPreInterruptMidstCopus(false);
            }
            temp.setVideoStatus(6);//新语料
            return smartivrNewResponse.response(NotifyEnum.MESSAGE, ActionTypeEnum.OTHER, temp, callId,msgNotify);//先播续播垫句，然后重播
        }else if(interruptType == SUPPORT_SOUND_INTERRUPT_NO_REPLY){
            if(current.getCurrentInterruptContent() != null && current.getCurrentInterruptContent().getInterruptEndTime() > System.currentTimeMillis()/1000){
                return smartivrNewResponse.response(NotifyEnum.MESSAGE, ActionTypeEnum.NOOP, current, callId,msgNotify);
            }
            //有打断，播放空音频,播放续播语料
            ActiveUnitContent temp1 = current.getCurrentMultiContent().getActiveUnitContents().get(
                    currentIndex
            );
            //log.info("进入代码300");
            //空音频只能是发声打断，续播垫句不可打断
            if(activeUnitContent.getPlayedCount() < 4){
                //log.info("进入代码303");
                temp1.setPlayed(false);
                temp1.setPlayFinished(false);//恢复先播空音频，然后判断是否有续播垫句，然后再播放语料块
                current.getCurrentMultiContent().setBlankVideo(1l);
                current.getCurrentMultiContent().setBlankVideoCount(0);//设置空音频个数1
                current.getCurrentMultiContent().setPreContinueCorpusId(temp1.getUnitContent().getPreContinueCorpusIdForInterrupt());
                current.getCurrentMultiContent().setPreContinueCorpusIdForReturn(0l);
                return smartivrNewResponse.response(NotifyEnum.MESSAGE, ActionTypeEnum.OTHER, current, callId,msgNotify);//先播续播垫句，然后重播
            }

            return smartivrNewResponse.response(NotifyEnum.MESSAGE, ActionTypeEnum.RESUME, current, callId,msgNotify);//先播续播垫句，然后重播

        }else if(interruptType == SUPPORT_SOUND_INTERRUPT_WITH_REPLY){
            if(current.getCurrentInterruptContent() != null && current.getCurrentInterruptContent().getInterruptEndTime() > System.currentTimeMillis()/1000){
                return smartivrNewResponse.response(NotifyEnum.MESSAGE, ActionTypeEnum.NOOP, current, callId,msgNotify);
            }
            //log.info("进入代码319");
            if(!temp.getHitPhrase() && current.getCurrentMultiContent().getBlankVideoCount() < 4 && activeUnitContent.getPlayedCount() < 4) {//没有命中直接恢复播放-播放空音频，播放续播垫句
                //有打断，播放空音频,播放续播语料
                ActiveUnitContent temp1 = current.getCurrentMultiContent().getActiveUnitContents().get(
                        currentIndex
                );
                //log.info("进入代码325");
                //空音频只能是发声打断，续播垫句不可打断
                //current.getCurrentMultiContent().setUnitContentIndex(currentIndex);
                temp1.setPlayed(false);
                temp1.setPlayFinished(false);//恢复先播空音频，然后判断是否有续播垫句，然后再播放语料块
                current.getCurrentMultiContent().setBlankVideo(1l);
                current.getCurrentMultiContent().setBlankVideoCount(0);//设置空音频个数1
                current.getCurrentMultiContent().setPreContinueCorpusId(temp1.getUnitContent().getPreContinueCorpusIdForInterrupt());
                current.getCurrentMultiContent().setPreContinueCorpusIdForReturn(0l);
                return smartivrNewResponse.response(NotifyEnum.MESSAGE, ActionTypeEnum.OTHER, current, callId,msgNotify);//先播续播垫句，然后重播

            }

            if(!temp.getHitPhrase()){
                //log.info("进入代码339");
                return smartivrNewResponse.response(NotifyEnum.MESSAGE, ActionTypeEnum.RESUME, current, callId,msgNotify);//先播续播垫句，然后重播
            }
            //log.info("进入代码341");
            //设置空音频
            temp.getCurrentMultiContent().setBlankVideo(1l);
            temp.getCurrentMultiContent().setBlankVideoCount(0);//设置空音频个数1
            temp.getCurrentMultiContent().setPreContinueCorpusId(0L);//命中肯定没有续播垫句
            if(current.getCurrentMultiContent().getPreInterruptCorpusId()>0){
                temp.getCurrentMultiContent().setPreInterruptCorpusId(current.getCurrentMultiContent().getPreInterruptCorpusId());//命中把上一个语料上设置的打断迁移过来
                temp.getCurrentMultiContent().setInPreInterruptMidstCopus(false);
            }
            temp.setVideoStatus(6);//新语料
            return smartivrNewResponse.response(NotifyEnum.MESSAGE, ActionTypeEnum.OTHER, temp, callId,msgNotify);//先播续播垫句，然后重播

        }else if(interruptType == CAN_NOT_BE_INTERRUPTED){
            //log.info("进入代码355");
            return smartivrNewResponse.response(NotifyEnum.MESSAGE, ActionTypeEnum.NOOP, null, callId,msgNotify);
        }
        return null;
    }



}
