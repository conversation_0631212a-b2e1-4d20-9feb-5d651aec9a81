package com.raipeng.aicall.service.notify.man;

import com.raipeng.aicall.constant.ActionTypeEnum;
import com.raipeng.aicall.constant.LocalCache;
import com.raipeng.aicall.constant.NotifyEnum;
import com.raipeng.aicall.constant.VideoStatusEnum;
import com.raipeng.aicall.controller.qd.SmartivrListenNewResponse;
import com.raipeng.aicall.controller.qd.SmartivrNewResponse;
import com.raipeng.aicall.scriptsystem.ScriptStatus;
import com.raipeng.aicall.service.CommonStatusService;
import com.raipeng.aicall.service.DialogRecordService;
import com.raipeng.aicall.service.ScriptService;
import com.raipeng.aicall.service.SeatManager;
import com.raipeng.aicall.service.notify.ManNotify;
import com.raipeng.common.entity.script.multicontent.ActiveUnitContent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service("man_waitNotify")
public class WaitNotify implements ManNotify {

    @Autowired
    private CommonStatusService commonStatusService;

    @Autowired
    private DialogRecordService dialogRecordService;

    @Autowired
    private ScriptService scriptService;

    @Autowired
    SeatManager seatManager;

    @Autowired
    private SmartivrListenNewResponse smartivrListenNewResponse;

    @Override
    public Map notify(String callId,  Map<String,Object> msgNotify) {

        if(LocalCache.waitCache.getIfPresent(callId) == null){
            return smartivrListenNewResponse.response(NotifyEnum.WAIT, ActionTypeEnum.NOOP, null,callId,msgNotify);
        }

        LocalCache.waitCache.invalidate(callId);
        ScriptStatus current = commonStatusService.getCurrentStatus(callId);
        int currentIndex = current.getCurrentMultiContent().getUnitContentIndex();
        if(msgNotify.containsKey("asrstate")){
            String asrstate = String.valueOf(msgNotify.get("asrstate"));
            if(asrstate.equals("true")){
                return smartivrListenNewResponse.response(NotifyEnum.WAIT, ActionTypeEnum.NOOP, null,callId,msgNotify);
            }
        }
        if(current.getIsEnd() && current.getVideoStatus().intValue() == 4){
            return smartivrListenNewResponse.response(NotifyEnum.WAIT, ActionTypeEnum.NOOP_HANGUP, null,callId,msgNotify);
        }

        if(current.getCurrentMultiContent().getIsNeedIndexReturn()){
            currentIndex = current.getCurrentMultiContent().getUnitContentIndex()-1;
        }else if(VideoStatusEnum.getNotifyByValue(current.getVideoStatus()) == VideoStatusEnum.STATUS_4 ||
                (VideoStatusEnum.getNotifyByValue(current.getVideoStatus()) == VideoStatusEnum.STATUS_1
                        && current.getCurrentMultiContent().isInPreInterruptMidstCopus())||
                VideoStatusEnum.getNotifyByValue(current.getVideoStatus()) == VideoStatusEnum.STATUS_5
        ){//当前正常语料中
            currentIndex = current.getCurrentMultiContent().getUnitContentIndex()-1;
        }

        ActiveUnitContent activeUnitContent = current.getCurrentMultiContent().getActiveUnitContents().get(
                currentIndex
        );

        if(activeUnitContent != null && activeUnitContent.getPlayedCount() < 4 && current.getCurrentMultiContent().getUnitContentIndex() < current.getCurrentMultiContent().getActiveUnitContents().size()) {
            if(current.getCurrentMultiContent().getBlankVideo() <= 0
                    && current.getCurrentMultiContent().getPreContinueCorpusId() <= 0
                    && current.getCurrentMultiContent().getPreContinueCorpusIdForReturn() <= 0) {
                dialogRecordService.toMqNew(callId, 0, current, activeUnitContent, msgNotify, null);
                if(!current.getCurrentMultiContent().getIfTriggerCallBack()){
                    current = scriptService.callbackNextStatus(current);
                    current.getCurrentMultiContent().setIfTriggerCallBack(true);
                }
            }
            return smartivrListenNewResponse.response(NotifyEnum.WAIT, ActionTypeEnum.OTHER, current, callId,msgNotify);
        }else{
            if(current.getCurrentMultiContent().isFinishedPlaying()){
                current.getCurrentMultiContent().setFinishedPlay(true);
                LocalCache.statusCache.put(callId,current);
            }
            ScriptStatus next =  commonStatusService.getNextStatus(callId,"",true,NotifyEnum.PLAYBACK);
            activeUnitContent = commonStatusService.getActiveUnitContent(next);
            if(activeUnitContent != null) {
                next.getCurrentMultiContent().setBlankVideo(0l);
                /*
                    if(next.getCurrentMultiContent().isReturn() && activeUnitContent.getUnitContent().getPreContinueCorpusIdForReturn() > 0)
                        next.getCurrentMultiContent().setPreContinueCorpusIdForReturn(activeUnitContent.getUnitContent().getPreContinueCorpusIdForReturn());
                    else
                        next.getCurrentMultiContent().setPreContinueCorpusIdForReturn(0l);

                     */
                next.getCurrentMultiContent().setIsNeedIndexReturn(false);//这里需要把是否是指针回拨给去掉，因为返回的已经被回拨了fff
                if(!next.getCurrentMultiContent().isReturn()){
                    next.getCurrentMultiContent().setPreContinueCorpusIdForReturn(0l);
                }
                next.getCurrentMultiContent().setPreContinueCorpusId(0l);
                next.getCurrentMultiContent().setPreInterruptCorpusId(0l);
                if(next.getCurrentMultiContent().getBlankVideo() <= 0
                        && (next.getCurrentMultiContent().getPreContinueCorpusId() == null || next.getCurrentMultiContent().getPreContinueCorpusId() <= 0)
                        && (next.getCurrentMultiContent().getPreContinueCorpusIdForReturn() == null || next.getCurrentMultiContent().getPreContinueCorpusIdForReturn() <= 0)) {
                    dialogRecordService.toMqNew(callId, 0, next, activeUnitContent,msgNotify,null);
                    if(!next.getCurrentMultiContent().getIfTriggerCallBack()){
                        next = scriptService.callbackNextStatus(next);
                        next.getCurrentMultiContent().setIfTriggerCallBack(true);//触发
                    }
                }
                return smartivrListenNewResponse.response(NotifyEnum.WAIT, ActionTypeEnum.OTHER, next, callId,msgNotify);
            }else{
                return smartivrListenNewResponse.response(NotifyEnum.WAIT, ActionTypeEnum.NOOP,null,callId,msgNotify);//不做任何动作
            }
        }

    }
}
