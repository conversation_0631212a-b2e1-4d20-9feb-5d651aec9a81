package com.raipeng.aicall.service.notify.man;

import com.raipeng.aicall.constant.ActionTypeEnum;
import com.raipeng.aicall.constant.LocalCache;
import com.raipeng.aicall.constant.NotifyEnum;
import com.raipeng.aicall.controller.qd.ManAiSmartivrReactResponse;
import com.raipeng.aicall.service.notify.ManNotify;
import com.raipeng.aicall.service.seat.SeatObtainTask;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
@Service("man_bridgeNotify")
public class BridgeNotify implements ManNotify {




    @Override
    public Map notify(String callId,  Map<String,Object> msg) {
        //SeatObtainTask task = ManAiConstants.seatObtainGlobleTaskMap.get(callId);
        SeatObtainTask task = LocalCache.seatObtainTaskCache.getIfPresent(callId);
        if(task != null){
            task.setIsComplete(true);
            task.setTakeOverOrListenIn("take_over");//已经接管
        }
        Map<String,Object> returnMap = ManAiSmartivrReactResponse.response(NotifyEnum.BRIDGE.getValue(), ActionTypeEnum.NOOP.getValue(),null,callId);
        return returnMap;
    }
}
