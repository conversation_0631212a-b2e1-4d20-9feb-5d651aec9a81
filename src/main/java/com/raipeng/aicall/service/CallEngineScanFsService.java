package com.raipeng.aicall.service;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.Channel;
import com.raipeng.aicall.constant.LocalCache;
import com.raipeng.aicall.constant.MqTypeEnum;
import com.raipeng.aicall.feign.FsConfigFeign;
import com.raipeng.aicall.feign.LineObtainFeign;
import com.raipeng.aicall.model.FSConfig;
import com.raipeng.aicall.repository.FSConfigRepository;
import com.raipeng.aicall.service.els.FsClientData;
import com.raipeng.aicall.service.els.FsEslClient;
import com.raipeng.common.util.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.freeswitch.esl.client.transport.message.EslMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import static com.raipeng.aicall.constant.RabbitMqConstants.FS_ONLINE_COUNT_PUSH_EXCHANGE;
import static com.raipeng.aicall.constant.RabbitMqConstants.FS_ONLINE_COUNT_PUSH_ROUTING;


/**
 * 呼叫引擎服务
 */
@Slf4j
@Service
public class CallEngineScanFsService {

    @Autowired
    private CurrentServerIpService currentServerIpService;

    @Autowired
    FSConfigRepository fsConfigRepository;

    @Autowired
    private LineObtainFeign lineObtainFeign;

    @Autowired
    private FsConfigFeign fsConfigFeign;

    @Autowired
    private SimpleRedisService simpleRedisService;

    @Autowired
    private CallService callService;

    public List<FSConfig> getFs(){
        List<FSConfig> list = null;
        try{
            log.info("获取fs的参数：{}",currentServerIpService.getAiCallFlag());
            list = fsConfigFeign.getCallFsConfig(currentServerIpService.getAiCallFlag());
        }catch (Exception e){
            try{Thread.sleep(100);}catch (Exception e1){};
            try{
                list = fsConfigFeign.getCallFsConfig(currentServerIpService.getAiCallFlag());
            }catch (Exception e2){
                try{Thread.sleep(100);}catch (Exception e1){};
                try{
                    list = fsConfigFeign.getCallFsConfig(currentServerIpService.getAiCallFlag());
                }catch (Exception e3){
                    e3.printStackTrace();
                    return null;
                }
            }
        }

        // 检查获取到的fsConfig配置并发送告警
        if (list != null && !list.isEmpty()) {
            for (FSConfig fsConfig : list) {
                callService.checkBridgeConfigAndAlert(fsConfig, "Engine启动时获取FSConfig");
            }
        }

        return list;
    }

    public Integer getThreshold(){
        Integer threshold = 0;
        try{
            threshold = fsConfigFeign.getCallThreshold(currentServerIpService.getAiCallFlag());
        }catch (Exception e){
            try{Thread.sleep(100);}catch (Exception e1){};
            try{
                threshold = fsConfigFeign.getCallThreshold(currentServerIpService.getAiCallFlag());
            }catch (Exception e2){
                try{Thread.sleep(100);}catch (Exception e1){};
                try{
                    threshold = fsConfigFeign.getCallThreshold(currentServerIpService.getAiCallFlag());
                }catch (Exception e3){
                    e3.printStackTrace();
                    return null;
                }
            }
        }
        return threshold;
    }

    public boolean connectFs(){
        List<FSConfig> fsConfigs = getFs();
        Integer newThreshold = getThreshold();
        log.info("获取到的fs信息{},阈值{}",JSON.toJSONString(fsConfigs),newThreshold);
        currentServerIpService.setThreshold(newThreshold);
        if(CollectionUtil.isEmpty(fsConfigs)){
            DingDingService.dingDingSendMsgException(currentServerIpService.getAiCallFlag(),"没有获取到生效fs,请确认下");
            return false;
        }
        //scan fs online count
        if(!fsConnectNew(fsConfigs)){
            return false;
        }
        currentServerIpService.setPullPhoneSwitch("start");
        for (CallEngineService.CallEngine callEngine : CallEngineService.callEngineList) {
            callEngine.setEngineConsumerStatus(true);
        }
        return true;
    }

    public void onLineCountNew(){
        int tempTopCount = 0;
        long lastUpdateTime = System.currentTimeMillis() - 5000l;
        long lastUpdateTime1 = System.currentTimeMillis();
        log.info("准备进入扫描fs的循环");
        while(!Thread.currentThread().isInterrupted()){
            // 停止后继续上传fs在线数
            try{
                if(currentServerIpService.getPullPhoneSwitch().equals("stop")){
                    try{
                        Thread.sleep(2000);
                    }catch (Exception ee){

                    }
                    continue;
                }
                if(CallEngineService.fsMulClient.size() <= 0) {
                    Thread.sleep(2000);
                    continue;
                }

                Iterator<Map.Entry<String, FsClientData>> iterator = CallEngineService.fsMulClient.entrySet().iterator();
                StringBuilder sb = new StringBuilder();
                EslMessage eslResult = null;
                // 1 获取在线数
                while(iterator.hasNext()) {
                    Map.Entry<String, FsClientData> entry =  iterator.next();
                    String ip = entry.getKey();
                    FsClientData fsClientData = entry.getValue();
                    fsClientData.setTempEnable(true);//设置可用
                    try{

                        if(currentServerIpService.getPullPhoneSwitch().equals("stop")){
                            break;
                        }

                        if (fsClientData.getFsClient().canSend(callService,fsClientData)) {
                            eslResult = fsClientData.getFsClient().sendSyncApiCommand("show","channels count");
                        }else {
                            //重连这台fs-fs连接不上，不立马连接，等一会再连接
                            fsConnect(fsClientData.getFsConfig().getIp());
                            log.error("Exception进入到连接fs连接不上了 engine标识"+currentServerIpService.getAiCallFlag()+"问题FS: "+fsClientData.getFsConfig().getIp());
                            //log.error("Exception进入到连接fs连接不上了 engine标识");
                            DingDingService.dingDingSendMsgException(
                                    "engine标识: "+currentServerIpService.getAiCallFlag(),
                                    "问题FS: "+fsClientData.getFsConfig().getIp());

                            //TODO 直接踢掉当前fs，1分钟以后试着重连，重连上，继续加进去，重连10次，都连不上，去除该fs
                            LocalCache.fsConfigCache.put(fsClientData.getFsConfig().getIp(),fsClientData.getFsConfig());
                            break;
                        }

                        int onlineCount = 0;
                        int cc = 0;
                        while((eslResult.getBodyLines() == null
                                || eslResult.getBodyLines().size() < 2
                                || !eslResult.getBodyLines().get(1).contains("total"))
                                && cc <= 10){
                            eslResult = fsClientData.getFsClient().sendSyncApiCommand("show","channels count");
                            cc++;
                            Thread.sleep(10);
                        }

                        if(eslResult.getBodyLines() == null
                                || eslResult.getBodyLines().size() < 2
                                || !eslResult.getBodyLines().get(1).contains("total")){
                            DingDingService.dingDingSendMsgException("连续10次都没有获取到，fs的在线数，具体FS："+fsClientData.getFsConfig().getIp());
                            log.info("连续10次都没有获取到，fs的在线数，具体FS："+fsClientData.getFsConfig().getIp());
                            fsClientData.setTempEnable(false);//设置临时不可用
                            onlineCount = 0;
                            fsClientData.initRemainCount(0);
                            lineObtainFeign.outOfConcurrentLimit(ip);//没有扫面到在线数，也会通知暂停
                            //continue;
                        }else{
                            onlineCount = Integer.valueOf(eslResult.getBodyLines().get(1).replace("total.","").replace(" ",""));
                            fsClientData.setOnlineCount(onlineCount);
                        }

                        if(onlineCount >= currentServerIpService.getThreshold()){
                            if (System.currentTimeMillis() - lastUpdateTime1 > currentServerIpService.getCapsStopTimeWindow()) {
                                lineObtainFeign.outOfConcurrentLimit(ip);//fs超限制
                                log.info("FS能力不足，通知凡哥："+fsClientData.getFsConfig().getIp());
                                lastUpdateTime1 = System.currentTimeMillis();
                            }
                        }

                    }catch (Exception e){
                        //重连这台fs-fs连接不上，不立马连接，等一会再连接
                        fsConnect(fsClientData.getFsConfig().getIp());
                        String fsResult = eslResult != null ? JSON.toJSONString(eslResult.getBodyLines()):null;
                        log.error("Exception进入到连接fs连接不上了 engine标识"+currentServerIpService.getAiCallFlag()+"问题FS: "+fsClientData.getFsConfig().getIp()+"获取FS在线数报错" + e.getMessage()+"获取FS在线数返回内容：" + fsResult);
                        //log.error("Exception进入到连接fs连接不上了 engine标识");
                        DingDingService.dingDingSendMsgException(
                                "engine标识: "+currentServerIpService.getAiCallFlag(),
                                "问题FS: "+fsClientData.getFsConfig().getIp(),
                                "获取FS在线数报错" + e.getMessage(),
                                "获取FS在线数返回内容：" + fsResult);

                        //TODO 直接踢掉当前fs，1分钟以后试着重连，重连上，继续加进去，重连10次，都连不上，去除该fs
                        LocalCache.fsConfigCache.put(fsClientData.getFsConfig().getIp(),fsClientData.getFsConfig());
                        //iterator.remove();
                        //fsClientData.getFsClient().closeChannel();
                        //fsClientData.getFsClient().shutdown();
                        e.printStackTrace();
                    }
                }
                //2 缓慢上升，时间窗口
                try{
                    if (System.currentTimeMillis() - lastUpdateTime > currentServerIpService.getTimeWindow() && CallEngineService.fsMulClient.entrySet().size() > 0) {
                        //计算平均在线数
                        Iterator<Map.Entry<String,FsClientData>> iterator1 = CallEngineService.fsMulClient.entrySet().iterator();
                        int sumCount = 0;
                        int fsCount = 0;
                        while(iterator1.hasNext()) {
                            Map.Entry<String, FsClientData> entry =  iterator1.next();
                            FsClientData fsClientData = entry.getValue();
                            if(fsClientData.getTempEnable()){
                                //计算平均值
                                sumCount = sumCount + fsClientData.getOnlineCount();
                                fsCount++;
                            }
                        }
                        int averageCount = sumCount / (fsCount == 0 ? 1 : fsCount);
                        if (tempTopCount - averageCount > currentServerIpService.getSubCount()) {
                            tempTopCount = averageCount;
                        }
                        tempTopCount = tempTopCount + currentServerIpService.getIncreaseCount();
                        lastUpdateTime = System.currentTimeMillis();
                    }

                    //3 计算临时阈值
                    Iterator<Map.Entry<String,FsClientData>> iterator2 = CallEngineService.fsMulClient.entrySet().iterator();
                    while(iterator2.hasNext()) {
                        Map.Entry<String, FsClientData> entry =  iterator2.next();
                        String ip = entry.getKey();
                        FsClientData fsClientData = entry.getValue();
                        if(fsClientData.getTempEnable()){
                            //上限
                            int topCount = currentServerIpService.getThreshold();
                            if (tempTopCount < topCount) {
                                topCount = tempTopCount;
                            }
                            int remainCount = topCount - fsClientData.getOnlineCount();
                            fsClientData.initRemainCount(remainCount);

                            sb.append(ip).append(",").append(fsClientData.getOnlineCount()).append("|").append(remainCount).append("; ");

                        }else{
                            fsClientData.initRemainCount(0);//临时不可用，剩余量
                        }
                    }

                }catch (Exception e){
                    e.printStackTrace();
                }

                if(LocalDateTime.now().getSecond() % 10 == 0){
                    String timeSecond = getTimeSecond();
                    if(!simpleRedisService.hasKey(timeSecond)){
                        simpleRedisService.setValueWithExpire(timeSecond,"",10L);
                        log.info("阈值|临时阈值|在线数"+currentServerIpService.getThreshold()+","+tempTopCount+","+sb.toString());
                        Map<String,String> paramMap = Maps.newHashMap();
                        paramMap.put("fsOnlineCount",sb.toString());
                        try{
                            //推送
                            Channel channel = ChannelManagerService.getRabbitMqChannelByPlatform(currentServerIpService.getCurrentPlatform(), MqTypeEnum.MAIN);
                            //log.info("======>归还redis的串"+jsonString);
                            channel.basicPublish(FS_ONLINE_COUNT_PUSH_EXCHANGE,FS_ONLINE_COUNT_PUSH_ROUTING, new AMQP.BasicProperties().builder().deliveryMode(2).build(),sb.toString().getBytes(StandardCharsets.UTF_8));

                        }catch (Exception ee){
                            ee.printStackTrace();
                            DingDingService.dingDingSendMsgException("通知common-monitor有异常");
                        }
                    }

                }

                try{
                    Thread.sleep(currentServerIpService.getScanFsSleepTime());
                }catch (Exception e){
                    //fsConnect();
                    DingDingService.dingDingSendMsgException("获取FS在线数,睡眠的时候报错"+e.getMessage());
                    e.printStackTrace();
                }
            }catch (Exception ee){
                try{
                    Thread.sleep(1000);
                }catch (Exception ee1){

                }

            }
        }
    }

    private String getTimeSecond(){
        StringBuilder timeSecond = new StringBuilder();
        timeSecond.append(currentServerIpService.getAiCallFlag()).append("-").append(LocalDateTime.now().getYear()).append(LocalDateTime.now().getMonth()).append(LocalDateTime.now().getDayOfMonth())
                .append(LocalDateTime.now().getHour()).append(LocalDateTime.now().getMinute()).append(LocalDateTime.now().getSecond());

        return timeSecond.toString();
    }

    public void onLineCount(){
        //scan fs online count
        fsConnect(null);
        int tempTopCount = 0;
        long lastUpdateTime = System.currentTimeMillis() - 5000l;
        long lastUpdateTime1 = System.currentTimeMillis();
        while(true){
            Iterator<Map.Entry<String,FsClientData>> iterator = CallEngineService.fsMulClient.entrySet().iterator();
            StringBuilder sb = new StringBuilder();
            EslMessage eslResult = null;
            // 1 获取在线数
            while(iterator.hasNext()) {
                try{
                    Map.Entry<String, FsClientData> entry =  iterator.next();
                    String ip = entry.getKey();
                    FsClientData fsClientData = entry.getValue();
                    fsClientData.setTempEnable(true);//设置可用
                    eslResult = fsClientData.getFsClient().sendSyncApiCommand("show","channels count");
                    int onlineCount = 0;
                    int cc = 0;
                    while((eslResult.getBodyLines() == null
                            || eslResult.getBodyLines().size() < 2
                            || !eslResult.getBodyLines().get(1).contains("total"))
                            && cc <= 10){
                        eslResult = fsClientData.getFsClient().sendSyncApiCommand("show","channels count");
                        cc++;
                        Thread.sleep(10);
                    }

                    if(eslResult.getBodyLines() == null
                            || eslResult.getBodyLines().size() < 2
                            || !eslResult.getBodyLines().get(1).contains("total")){
                        DingDingService.dingDingSendMsgException("连续10次都没有获取到，fs的在线数，具体FS："+fsClientData.getFsConfig().getIp());
                        log.info("连续10次都没有获取到，fs的在线数，具体FS："+fsClientData.getFsConfig().getIp());
                        fsClientData.setTempEnable(false);//设置临时不可用
                        onlineCount = 0;
                        fsClientData.initRemainCount(0);
                        lineObtainFeign.outOfConcurrentLimit(ip);//没有扫面到在线数，也会通知暂停
                        //continue;
                    }else{
                        onlineCount = Integer.valueOf(eslResult.getBodyLines().get(1).replace("total.","").replace(" ",""));
                        fsClientData.setOnlineCount(onlineCount);
                    }

                    if(onlineCount >= currentServerIpService.getThreshold()){
                        if (System.currentTimeMillis() - lastUpdateTime1 > currentServerIpService.getCapsStopTimeWindow()) {
                            lineObtainFeign.outOfConcurrentLimit(ip);//fs超限制
                            log.info("FS能力不足，通知凡哥："+fsClientData.getFsConfig().getIp());
                            lastUpdateTime1 = System.currentTimeMillis();
                        }
                    }

                }catch (Exception e){
                    fsConnect(null);
                    String fsResult = eslResult != null ? JSON.toJSONString(eslResult.getBodyLines()):null;
                    DingDingService.dingDingSendMsgException("获取FS在线数报错，获取在线数频次过快，没有获取具体值，"+e.getMessage()+"fs返回："+fsResult);
                    e.printStackTrace();
                }
            }
            //2 缓慢上升，时间窗口
            try{
                if (System.currentTimeMillis() - lastUpdateTime > currentServerIpService.getTimeWindow() && CallEngineService.fsMulClient.entrySet().size() > 0) {
                    //计算平均在线数
                    Iterator<Map.Entry<String,FsClientData>> iterator1 = CallEngineService.fsMulClient.entrySet().iterator();
                    int sumCount = 0;
                    int fsCount = 0;
                    while(iterator1.hasNext()) {
                        Map.Entry<String, FsClientData> entry =  iterator1.next();
                        FsClientData fsClientData = entry.getValue();
                        if(fsClientData.getTempEnable()){
                            //计算平均值
                            sumCount = sumCount + fsClientData.getOnlineCount();
                            fsCount++;
                        }
                    }
                    int averageCount = sumCount / (fsCount == 0 ? 1 : fsCount);
                    if (tempTopCount - averageCount > currentServerIpService.getSubCount()) {
                        tempTopCount = averageCount;
                    }
                    tempTopCount = tempTopCount + currentServerIpService.getIncreaseCount();
                    lastUpdateTime = System.currentTimeMillis();
                }

                //3 计算临时阈值
                Iterator<Map.Entry<String,FsClientData>> iterator2 = CallEngineService.fsMulClient.entrySet().iterator();
                while(iterator2.hasNext()) {
                    Map.Entry<String, FsClientData> entry =  iterator2.next();
                    String ip = entry.getKey();
                    FsClientData fsClientData = entry.getValue();
                    if(fsClientData.getTempEnable()){
                        //上限
                        int topCount = currentServerIpService.getThreshold();
                        if (tempTopCount < topCount) {
                            topCount = tempTopCount;
                        }
                        int remainCount = topCount - fsClientData.getOnlineCount();
                        fsClientData.initRemainCount(remainCount);

                        sb.append(ip).append(",").append(fsClientData.getOnlineCount()).append("|").append(remainCount).append("; ");

                    }else{
                        fsClientData.initRemainCount(0);//临时不可用，剩余量
                    }
                }

            }catch (Exception e){
                e.printStackTrace();
            }

            if(LocalDateTime.now().getSecond()%10==0){
                log.info("阈值|临时阈值|在线数"+currentServerIpService.getThreshold()+","+tempTopCount+","+sb.toString());
            }

            try{
                Thread.sleep(currentServerIpService.getScanFsSleepTime());
            }catch (Exception e){
                fsConnect(null);
                DingDingService.dingDingSendMsgException("获取FS在线数,睡眠的时候报错"+e.getMessage());
                e.printStackTrace();
            }

        }
    }

    private void fsConnect(String excludeFsIp){
        try{
            Iterator<Map.Entry<String,FsClientData>> iterator = CallEngineService.fsMulClient.entrySet().iterator();
            while (iterator.hasNext()){
                try{
                    Map.Entry<String, FsClientData> entry =  iterator.next();
                    entry.getValue().getFsClient().closeChannel();
                    entry.getValue().getFsClient().shutdown();
                }catch (Exception e){

                }
            }
        }catch (Exception e){
           //e.printStackTrace();
        }
        CallEngineService.fsMulClient.clear();//
        //List<FSConfig> fsConfigs = fsConfigRepository.findByEnable(1);
        List<FSConfig> fsConfigsFetch = fsConfigFeign.getCallFsConfig(currentServerIpService.getAiCallFlag());
        List<FSConfig> fsConfigs = fsConfigsFetch.stream().filter(p->!p.getIp().equals(excludeFsIp)).collect(Collectors.toList());//排除有问题的engine

        // 检查重置时获取到的fsConfig配置并发送告警
        if (fsConfigs != null && !fsConfigs.isEmpty()) {
            for (FSConfig fsConfig : fsConfigs) {
                callService.checkBridgeConfigAndAlert(fsConfig, "Engine重置FSConfig时");
            }
        }
        //List<FSConfig> fsConfigs = fsConfigRepository.findByEnableAndAiCallEngineIp(1,currentServerIpService.getAiCallEngineIp());
        for (FSConfig fsConfig : fsConfigs) {
            FsEslClient fsClient = new FsEslClient(1,2);
            fsClient.closeChannel();
            try {
                fsClient.connect(fsConfig.getIp(), fsConfig.getPort(), fsConfig.getPwd(), 2);
            }catch (Exception e) {
                e.printStackTrace();
                DingDingService.dingDingSendMsgException("fs没有连接上","ip："+fsConfig.getIp()+",+端口："+fsConfig.getPort());
                return;//退出，修复fs，重启ai-call
            }
            FsClientData fsClientData = new FsClientData(fsConfig,fsClient,0,0);
            CallEngineService.fsMulClient.put(fsConfig.getIp(),fsClientData);
        }
    }

    private boolean fsConnectNew(List<FSConfig> fsConfigs){
        log.info("进入fs连接动作");

        // 检查新连接时的fsConfig配置并发送告警
        if (fsConfigs != null && !fsConfigs.isEmpty()) {
            for (FSConfig fsConfig : fsConfigs) {
                callService.checkBridgeConfigAndAlert(fsConfig, "Engine新建连接时");
            }
        }

        for (FSConfig fsConfig : fsConfigs) {
            FsEslClient fsClient = new FsEslClient(1,2);
            try {
                fsClient.connect(fsConfig.getIp(), fsConfig.getPort(), fsConfig.getPwd(), 2);
            }catch (Exception e) {
                e.printStackTrace();
                DingDingService.dingDingSendMsgException("fs没有连接上","ip："+fsConfig.getIp()+",+端口："+fsConfig.getPort());
                return false;//退出，修复fs，重启ai-call
            }
            FsClientData fsClientData = new FsClientData(fsConfig,fsClient,0,0);
            CallEngineService.fsMulClient.put(fsConfig.getIp(),fsClientData);
            /*
            FsEslAnswerListener listener1 = new FsEslAnswerListener(fsClientData);

            fsClientData.getFsClient().removeAllEventListener();
            fsClientData.getFsClient().addEventListener(listener1);
            fsClientData.getFsClient().setEventSubscriptions("plain", "CHANNEL_ANSWER");

             */
        }
        log.info("进入fs连接之后的大小"+CallEngineService.fsMulClient.size());
        return true;
    }

    public static boolean singleConnect(String fsIp,FSConfig fsConfig){
        if(fsConfig.getTryConCount().get() > 10){
            DingDingService.dingDingSendMsgException(
                    "Engine: "+fsConfig.getAiCallEngineIp(),
                    "FS: "+fsIp+" 重连10次都没有重试成功，将从当前可用fs中移除");
            return false;
        }
        if(CallEngineService.fsMulClient.containsKey(fsIp)){//已经存在，无需重连
            return true;
        }
        FsEslClient fsClient = new FsEslClient(1,2);
        try {
            fsClient.connect(fsConfig.getIp(), fsConfig.getPort(), fsConfig.getPwd(), 2);
            FsClientData fsClientData = new FsClientData(fsConfig,fsClient,0,0);//每次新建之前，重新设定在线数和剩余数，然后将数据加到可用fs中，并扫描
            CallEngineService.fsMulClient.put(fsConfig.getIp(),fsClientData);
            DingDingService.dingDingSendMsgException(
                    "Engine: "+fsConfig.getAiCallEngineIp(),
                    "FS: "+fsIp+" 重连成功，重新添加到可用fs中");
        }catch (Exception e) {
            e.printStackTrace();
            DingDingService.dingDingSendMsgException("fs没有连接上","ip："+fsConfig.getIp()+",+端口："+fsConfig.getPort());
            fsConfig.getTryConCount().incrementAndGet();
            LocalCache.fsConfigCache.put(fsConfig.getIp(),fsConfig);
            return false;
        }
        return true;
    }

}
