package com.raipeng.aicall.service;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.raipeng.aicall.constant.ApplicationConstants;
import com.raipeng.aicall.constant.LocalCache;
import com.raipeng.aicall.constant.NotifyEnum;
import com.raipeng.aicall.scriptsystem.*;
import com.raipeng.common.entity.script.multicontent.ActiveMultiContent;
import com.raipeng.common.entity.script.multicontent.ActiveUnitContent;
import com.raipeng.common.entity.script.multicontent.UnitContent;
import com.raipeng.common.enums.InterruptType;
import com.raipeng.common.util.StringUtils;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import static com.raipeng.common.enums.InterruptType.*;

@Slf4j
@Service
public class CommonStatusService {

    @Autowired
    private SimpleRedisService simpleRedisService;

    @Autowired
    private DialogRecordService dialogRecordService;

    @Autowired
    private ScriptService scriptService;

    @Autowired
    private CurrentServerIpService currentServerIpService;

    public ScriptStatus getCurrentStatus(String callId){
        ScriptStatus temp = null;
        temp = LocalCache.statusCache.getIfPresent(callId);
        if(temp == null){
            String jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callId);
            //log.info("===>从redis中获取到的jsonString"+jsonString);
            temp = JSONObject.parseObject(jsonString,ScriptStatus.class);
            if(temp != null)
                LocalCache.statusCache.put(callId,temp);
        }
        ScriptStatus current = null;
        if(temp != null)
           current = JSONObject.parseObject(JSON.toJSONString(temp, SerializerFeature.WriteClassName),ScriptStatus.class);

        if(temp == null) {
            log.error("Exception 当前callId 在redis缓冲中没找到" + callId+
                    "当前ai-call: "+currentServerIpService.getAiCallFlag()+
                    "当前ip: "+currentServerIpService.getAiCallIp()+
                    "当前port: "+currentServerIpService.getAiCallPort()
            );
            DingDingService.dingDingSendMsgException(
                    "Exception 当前callId 在redis缓冲中没找到" + callId,
                    "当前ai-call: "+currentServerIpService.getAiCallFlag(),
                    "当前ip: "+currentServerIpService.getAiCallIp(),
                    "当前port: "+currentServerIpService.getAiCallPort());
        }
        current.setIsSilence(false);
        return current;
    }


    public ScriptStatus getCurrentStatusForDirect(String callId,NotifyEnum type){
        ScriptStatus temp = null;
        if(type == NotifyEnum.ENTER){
            String jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callId);
            //log.info("===>从redis中获取到的jsonString"+jsonString);
            temp = JSONObject.parseObject(jsonString,ScriptStatus.class);
            LocalCache.statusCache.put(callId,temp);
        }else
            temp = LocalCache.statusCache.getIfPresent(callId);
        ScriptStatus current = JSONObject.parseObject(JSON.toJSONString(temp, SerializerFeature.WriteClassName),ScriptStatus.class);
        return current;
    }

    public String getCurrentStatusString(String callId) {
        return JSON.toJSONString(LocalCache.statusCache.getIfPresent(callId), SerializerFeature.WriteClassName);
    }

    public ScriptStatus getNextStatus(String callId,String userContent,Boolean isSilence,NotifyEnum type){
        if(userContent == null){
            userContent = "";
        }

        ScriptStatus temp = null;
        temp = LocalCache.statusCache.getIfPresent(callId);
        if(temp == null){
            String jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callId);
            temp = JSONObject.parseObject(jsonString,ScriptStatus.class);
        }

        if(temp == null){
            temp = new ScriptStatus();
            temp.setScriptId("88ed99a1-bca3-4206-be7a-a110ce95e56e");
            temp.setVersion(0);
            log.info("进入空temp");
        }

        //通过序列化和反序列化，复制库中数据
        if (temp.getIsCurrentCorpusPlayed()) {temp.setLastStatus(null);}
        String oldString = JSON.toJSONString(temp, SerializerFeature.WriteClassName);
        ScriptStatus old = JSONObject.parseObject(oldString, ScriptStatus.class);
        old.setUserContent(userContent);
        old.setIsSilence(isSilence);
        //log.info("===>获取话术传参："+JSON.toJSONString(old));
        ScriptStatus nextStatus = scriptService.getNextStatus(old, oldString);
        if(nextStatus.getIsEnd()){//
            nextStatus.getCallStatus().setWhoHangup(0);
        }
        if(type == NotifyEnum.ENTER){
            //log.info("进入enter");
            if(nextStatus.getCallStatus() == null){
                nextStatus.setCallStatus(new CallStatus());
                nextStatus.getCallStatus().setCallOutTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                nextStatus.getCallStatus().setCallId(callId);
            }
            nextStatus.getCallStatus().setCallStartTime(System.currentTimeMillis()/1000);//呼叫开始时间
            setCallStatus(nextStatus, CallStatusEnum.CALLING,callId,null);
            String talkTimeStart = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            nextStatus.getCallStatus().setContactTime(talkTimeStart);
            nextStatus.getCallStatus().setTalkTimeStart(talkTimeStart);
            nextStatus.getCallStatus().setCallStatus(CallStatusEnum.CALL_SUCCESS.ordinal());//呼叫完成
        }
        //log.info("当前nextStatus"+JSON.toJSONString(nextStatus));

        //TODO 这里的打断类型和打断时间和打断类型被迁移到multiContents中
        /*
        InterruptType interruptType = null;
        if(nextStatus.getCurrentMultiContent() != null
                && nextStatus.getCurrentMultiContent().getActiveUnitContents() != null
        && nextStatus.getCurrentMultiContent().getActiveUnitContents().size()>0) {
            interruptType = nextStatus.getCurrentMultiContent().getActiveUnitContents().get(0).getUnitContent().getInterruptType();
        }

        if(interruptType != null && (interruptType.equals(SUPPORT_SEMANTIC_INTERRUPT_NO_REPLY)
                ||interruptType.equals(SUPPORT_SEMANTIC_INTERRUPT_WITH_REPLY))) {
            nextStatus.setCurrentInterruptContent(InterruptContent.builder()
                    .allowedInterruptTime(nextStatus.getCurrentCorpus().getAllowedInterruptTime())
                    .interruptEndTime(nextStatus.getCurrentCorpus().getAllowedInterruptTime()+System.currentTimeMillis()/1000)
                    .build());
        }else{
            nextStatus.setCurrentInterruptContent(null);
        }
         */

        //simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+callId, JSONObject.toJSONString(nextStatus, SerializerFeature.WriteClassName),ApplicationConstants.CALL_USER_STATUS_EXPIRE);
        nextStatus.setDialogCorpusTempUUID(UUID.randomUUID().toString());
        return nextStatus;
    }

    public ScriptStatus getNextStatusOld(String callId,String userContent,Boolean isSilence,NotifyEnum type){
        if(userContent == null){
            userContent = "";
        }

        ScriptStatus temp = null;
        /*
        if(type == NotifyEnum.ENTER){
            String jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callId);
            temp = JSONObject.parseObject(jsonString,ScriptStatus.class);
        }else
            temp = LocalCache.statusCache.getIfPresent(callId);
         */
        temp = LocalCache.statusCache.getIfPresent(callId);

        if(temp == null){
            temp = new ScriptStatus();
            temp.setScriptId("88ed99a1-bca3-4206-be7a-a110ce95e56e");
            temp.setVersion(0);
        }

        //通过序列化和反序列化，复制库中数据
        if (temp.getIsCurrentCorpusPlayed()) {temp.setLastStatus(null);}
        String oldString = JSON.toJSONString(temp, SerializerFeature.WriteClassName);
        ScriptStatus old = JSONObject.parseObject(oldString, ScriptStatus.class);
        old.setUserContent(userContent);
        old.setIsSilence(isSilence);
        //log.info("===>获取话术传参："+JSON.toJSONString(old));
        ScriptStatus nextStatus = scriptService.getNextStatus(old, oldString);
        if(nextStatus.getIsEnd()){//
            nextStatus.getCallStatus().setWhoHangup(0);
        }
        if(type == NotifyEnum.ENTER){
            //log.info("进入enter");
            if(nextStatus.getCallStatus() == null){
                nextStatus.setCallStatus(new CallStatus());
                nextStatus.getCallStatus().setCallOutTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                nextStatus.getCallStatus().setCallId(callId);
            }
            nextStatus.getCallStatus().setCallStartTime(System.currentTimeMillis()/1000);//呼叫开始时间
            setCallStatus(nextStatus, CallStatusEnum.CALLING,callId,null);
            String talkTimeStart = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            nextStatus.getCallStatus().setContactTime(talkTimeStart);
            nextStatus.getCallStatus().setTalkTimeStart(talkTimeStart);
            nextStatus.getCallStatus().setCallStatus(CallStatusEnum.CALL_SUCCESS.ordinal());//呼叫完成
        }
        //log.info("当前nextStatus"+JSON.toJSONString(nextStatus));

        //TODO 这里的打断类型和打断时间和打断类型被迁移到multiContents中

        InterruptType interruptType = null;
        if(nextStatus.getCurrentCorpus() !=null && nextStatus.getCurrentMultiContent() != null
                && nextStatus.getCurrentMultiContent().getActiveUnitContents() != null
                && nextStatus.getCurrentMultiContent().getActiveUnitContents().size()>0) {
            interruptType = nextStatus.getCurrentMultiContent().getActiveUnitContents().get(0).getUnitContent().getInterruptType();
        }

        if(interruptType != null && (interruptType.equals(SUPPORT_SEMANTIC_INTERRUPT_NO_REPLY)
                ||interruptType.equals(SUPPORT_SEMANTIC_INTERRUPT_WITH_REPLY))) {
            nextStatus.setCurrentInterruptContent(InterruptContent.builder()
                    .allowedInterruptTime(nextStatus.getCurrentMultiContent().getActiveUnitContents().get(0).getUnitContent().getAllowedInterruptTime())
                    .interruptEndTime(nextStatus.getCurrentMultiContent().getActiveUnitContents().get(0).getUnitContent().getAllowedInterruptTime()+System.currentTimeMillis()/1000)
                    .build());
        }else{
            nextStatus.setCurrentInterruptContent(null);
        }

        //simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+callId, JSONObject.toJSONString(nextStatus, SerializerFeature.WriteClassName),ApplicationConstants.CALL_USER_STATUS_EXPIRE);
        nextStatus.setDialogCorpusTempUUID(UUID.randomUUID().toString());
        return nextStatus;
    }

    public ScriptStatus getNextStatusHangup(String callId,String userContent,Boolean isSilence,NotifyEnum type){
        if(userContent == null){
            userContent = "";
        }
        ScriptStatus temp = null;
        if(type == NotifyEnum.ENTER){
            String jsonString = simpleRedisService.getValue(ApplicationConstants.CALL_USER_STATUS+callId);
            temp = JSONObject.parseObject(jsonString,ScriptStatus.class);
        }else {
            temp = getCurrentStatus(callId);
        }
        if (temp.getIsCurrentCorpusPlayed()) {temp.setLastStatus(null);}
        String oldString = JSON.toJSONString(temp, SerializerFeature.WriteClassName);
        ScriptStatus old = JSONObject.parseObject(oldString, ScriptStatus.class);
        old.setUserContent(userContent);
        old.setIsSilence(isSilence);
        setHitSemanticId(old, userContent);
        ScriptStatus nextStatus = scriptService.getNextStatus(old,oldString);
        if(nextStatus.getIsEnd()){//
            nextStatus.getCallStatus().setWhoHangup(0);
        }
        nextStatus.setDialogCorpusTempUUID(UUID.randomUUID().toString());
        return nextStatus;
    }

    private void setCallStatus(ScriptStatus nextStatus,CallStatusEnum statusEnum,String callId,String userContent){
        CallStatus callStatus = nextStatus.getCallStatus();
        Long duration = System.currentTimeMillis()/1000 - callStatus.getCallStartTime();
        callStatus.setCallDuration(duration.intValue());
        if(userContent!=null){
            if(StringUtils.isNotEmpty(userContent))
                callStatus.getSayCount().incrementAndGet();
            if(callStatus.getUserFullAnswerContent()==null){
                callStatus.setUserFullAnswerContent(new StringBuilder(""));
            }
            callStatus.getUserFullAnswerContent().append(userContent).append("##");
        }
        callStatus.setCallStatus(statusEnum.ordinal());

    }

    public String genAudioFileUrl(ScriptStatus nextStatus, UnitContent unitContent,Map<String,Object> map){
        String audioFileUrl = null;
        if(map.containsKey("bridge_fs_ip")){
             audioFileUrl = "http://"+map.getOrDefault("bridge_fs_ip","").toString().replace(":5060","")+ ApplicationConstants.FS_BG_MUSIC_DIR+"/"+nextStatus.getScriptId()+"/"+unitContent.getAudioPath().split("/")[unitContent.getAudioPath().split("/").length-1];
        }else{
            audioFileUrl = "http://"+nextStatus.getCallStatus().getFsIp()+ ApplicationConstants.FS_BG_MUSIC_DIR+"/"+nextStatus.getScriptId()+"/"+unitContent.getAudioPath().split("/")[unitContent.getAudioPath().split("/").length-1];
        }
        return audioFileUrl;
    }


    public void setLocalStatusCache(String callId,ScriptStatus nextStatus){
        LocalCache.statusCache.put(callId,nextStatus);
        //simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+callId, JSONObject.toJSONString(nextStatus, SerializerFeature.WriteClassName),ApplicationConstants.CALL_USER_STATUS_EXPIRE);
    }
    public void setLocalPauseCache(String callId){
        LocalCache.pauseCache.put(callId,1);
    }


    public ActiveUnitContent getActiveUnitContent(ScriptStatus scriptStatus){
        ActiveMultiContent activeMultiContent = scriptStatus.getCurrentMultiContent();
        ActiveUnitContent activeUnitContent = null;

        List<ActiveUnitContent> activeUnitContents = activeMultiContent.getActiveUnitContents();
        //log.info("当前沉默："+scriptStatus.getCurrentCorpus().getName()+":"+JSON.toJSONString(activeUnitContents));
        for (ActiveUnitContent content : activeUnitContents) {
            if(!content.isPlayed()){
                activeUnitContent = content;
                break;
            }
        }
        setLocalStatusCache(scriptStatus.getCallId(),scriptStatus);
        return activeUnitContent;
    }


    public ScriptStatus getNextStatusInterrupt(String callId,String sayContent){
        if(sayContent == null){
            sayContent = "";
        }
        ScriptStatus temp = getCurrentStatus(callId);
        setHitSemanticId(temp, sayContent);
        temp.setUserContent(sayContent);
        temp.setInterruptOperation(true);
        if (temp.getIsCurrentCorpusPlayed()) {temp.setLastStatus(null);}
        String tempString = JSON.toJSONString(temp, SerializerFeature.WriteClassName);
        ScriptStatus next = scriptService.getNextStatus(temp, tempString);
        return next;
    }

    public Set<Long> setHitSemanticId(ScriptStatus status, String userContent) {
        if (StringUtils.isNotEmpty(userContent) && !userContent.endsWith(";")) {
            userContent = userContent + ";";
        }
        Map<String, Set<Long>> hitSemanticIdMap = status.getHitSemanticIdMap();
        if (!hitSemanticIdMap.containsKey(userContent)) {
            Set<Long> hitSemanticIds = TriggerUtils.getHitSemanticIds(userContent, status.getSecondIndustryId());
            hitSemanticIdMap.put(userContent, hitSemanticIds);
            return hitSemanticIds;
        } else {
            return hitSemanticIdMap.get(userContent);
        }
    }

    public Set<Long> getHitSemanticId(ScriptStatus status, String userContent) {
        if (StringUtils.isNotEmpty(userContent) && !userContent.endsWith(";")) {
            userContent = userContent + ";";
        }
        Map<String, Set<Long>> hitSemanticIdMap = status.getHitSemanticIdMap();
        if (!hitSemanticIdMap.containsKey(userContent)) {
            Set<Long> hitSemanticIds = TriggerUtils.getHitSemanticIds(userContent, status.getSecondIndustryId());
            hitSemanticIdMap.put(userContent, hitSemanticIds);
            status.getHitSemanticIdsCache().add(hitSemanticIds);
            return hitSemanticIds;
        } else {
            Set<Long> hitSemanticIds = hitSemanticIdMap.get(userContent);
            status.getHitSemanticIdsCache().add(hitSemanticIds);
            return hitSemanticIds;
        }
    }


    public void interrupt(ScriptStatus temp, String callId) {
        //time11 = System.currentTimeMillis();
        InterruptType interruptTypeTemp = temp.getCurrentMultiContent().getActiveUnitContents().get(
                temp.getCurrentMultiContent().getUnitContentIndex()
        ).getUnitContent().getInterruptType();


        if(interruptTypeTemp !=null && (interruptTypeTemp == SUPPORT_SEMANTIC_INTERRUPT_NO_REPLY
                ||interruptTypeTemp == SUPPORT_SEMANTIC_INTERRUPT_WITH_REPLY)
        ) {
            temp.setCurrentInterruptContent(InterruptContent.builder()
                    .allowedInterruptTime(
                         temp.getCurrentMultiContent().getActiveUnitContents().get(
                                 temp.getCurrentMultiContent().getUnitContentIndex()
                         ).getUnitContent().getAllowedInterruptTime()
                    )
                    .interruptEndTime(
                            temp.getCurrentMultiContent().getActiveUnitContents().get(
                            temp.getCurrentMultiContent().getUnitContentIndex()
                            ).getUnitContent().getAllowedInterruptTime()+System.currentTimeMillis()/1000
                    ).build());
        }else{
            temp.setCurrentInterruptContent(null);
        }

        setLocalStatusCache(callId,temp);
    }

    public void setCallStatus(ScriptStatus nextStatus,String callId,String userContent){
        CallStatus callStatus = nextStatus.getCallStatus();
        Long duration = System.currentTimeMillis()/1000 - callStatus.getCallStartTime();
        callStatus.setCallDuration(duration.intValue());
        if(StringUtils.isNotEmpty(userContent))//说话内容为空，不记录用户说话次数
            callStatus.getSayCount().incrementAndGet();
        if(callStatus.getUserFullAnswerContent()==null){
            callStatus.setUserFullAnswerContent(new StringBuilder(""));
        }
        callStatus.getUserFullAnswerContent().append(userContent).append("##");
        nextStatus.getCallStatus().getDialogContentsCache().add(DialogContent.builder()
                .content(userContent)
                .fromNumber("1").createTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).build());
        LocalCache.statusCache.put(callId,nextStatus);
    }


}
