package com.raipeng.aicall.constant;

public enum KmlAddressEnum {

    kmlAddress("ctcc","kmlAddress"),//默认1是生效的
    kmlAddressCmcc("cucc","kmlAddressCmcc"),
    kmlAddressCucc("cmcc","kmlAddressCucc"),
    ;//预测式外呼  value=1启用，value=0不启用

    private String name;
    private String value;

    public static KmlAddressEnum getKmlAddressEnum(String name){
        for(KmlAddressEnum kmlAddressEnum : KmlAddressEnum.values()){
            if(kmlAddressEnum.getName().equals(name)){
                return kmlAddressEnum;
            }
        }
        return null;
    }

    KmlAddressEnum(String name, String value){
        this.name = name;
        this.value = value;
    }

    public String getName(){
        return name;
    }
    public void setName(String name){
        this.name = name;
    }

    public String getValue(){
        return value;
    }
    public void setValue(String value){
        this.value = value;
    }

}
