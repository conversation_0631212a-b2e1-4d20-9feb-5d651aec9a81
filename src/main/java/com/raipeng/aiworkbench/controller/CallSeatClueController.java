package com.raipeng.aiworkbench.controller;


import com.raipeng.aidatacommon.model.AIOutboundTask;
import com.raipeng.aidatacommon.model.clue.Clue;
import com.raipeng.aiworkbench.controller.request.CallSeatCluesFollowingParam;
import com.raipeng.aiworkbench.controller.request.CallSeatCluesSuccessOrFailParam;
import com.raipeng.aiworkbench.controller.response.CallSeatStatisticResponse;
import com.raipeng.aiworkbench.controller.response.CallSeatTodayStatisticResponse;
import com.raipeng.aiworkbench.controller.response.Response;
import com.raipeng.aiworkbench.service.CallSeatClueService;
import com.raipeng.aiworkbench.service.RecordSearchCommonService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping("/callSeatClue")
@Api(value = "/callSeatClue", tags = {"坐席工作台-线索"})
public class CallSeatClueController {
    @Autowired
    private CallSeatClueService callSeatClueService;

    @Autowired
    private RecordSearchCommonService recordSearchCommonService;

    @ApiOperation(value = "今日线索")
    @GetMapping("/findSeatBenchStatisticToday")
    public Response<CallSeatTodayStatisticResponse> findSeatBenchStatisticToday() {
        CallSeatTodayStatisticResponse todayStatistic = callSeatClueService.findSeatBenchStatisticToday();
        Response<CallSeatTodayStatisticResponse> response = new Response<>();
        response.setData(todayStatistic);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "坐席下所有线索统计")
    @GetMapping("/findSeatBenchStatisticTotal")
    public Response<CallSeatStatisticResponse> findSeatBenchStatisticTotal() {
        CallSeatStatisticResponse statistic = callSeatClueService.findSeatBenchStatisticTotal();
        Response<CallSeatStatisticResponse> response = new Response<>();
        response.setData(statistic);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "线索列表跟进中")
    @PostMapping("/findSeatBenchCluesFollowing")
    public Response<List<Clue>> findSeatBenchCluesFollowing(@RequestBody CallSeatCluesFollowingParam param) {
        param.setPhone(recordSearchCommonService.convertSingleIntraDayRecordIdToPhone(param.getPhone()));
        List<Clue> seatBenchCluesFollowing = callSeatClueService.findSeatBenchCluesFollowing(param);
        Response<List<Clue>> response = new Response<>();
        response.setData(seatBenchCluesFollowing);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "线索列表跟进成功")
    @PostMapping("/findSeatBenchCluesSuccess")
    public Response<List<Clue>> findSeatBenchCluesSuccess(@RequestBody CallSeatCluesSuccessOrFailParam param) {
        param.setPhone(recordSearchCommonService.convertSingleIntraDayRecordIdToPhone(param.getPhone()));
        List<Clue> seatBenchCluesFollowing = callSeatClueService.findSeatBenchCluesSuccess(param);
        Response<List<Clue>> response = new Response<>();
        response.setData(seatBenchCluesFollowing);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "线索列表跟进失败")
    @PostMapping("/findSeatBenchCluesFail")
    public Response<List<Clue>> findSeatBenchCluesFail(@RequestBody CallSeatCluesSuccessOrFailParam param) {
        param.setPhone(recordSearchCommonService.convertSingleIntraDayRecordIdToPhone(param.getPhone()));
        List<Clue> seatBenchCluesFollowing = callSeatClueService.findSeatBenchCluesFail(param);
        Response<List<Clue>> response = new Response<>();
        response.setData(seatBenchCluesFollowing);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "坐席关联的任务")
    @GetMapping("/findCallSeatRelatedTasks")
    public Response<List<AIOutboundTask>> findCallSeatRelatedTasks() {
        List<AIOutboundTask> callSeatRelatedTasks = callSeatClueService.findCallSeatRelatedTasks();
        Response<List<AIOutboundTask>> response = new Response<>();
        response.setData(callSeatRelatedTasks);
        response.setResponseSuccess();
        return response;
    }
}
