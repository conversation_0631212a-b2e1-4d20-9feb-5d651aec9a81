package com.raipeng.aiworkbench.service;


import com.raipeng.aidatacommon.enums.ClueOperationType;
import com.raipeng.aidatacommon.model.clue.Clue;
import com.raipeng.aidatacommon.model.clue.ClueTransferRecord;
import com.raipeng.aiworkbench.exceptionadvice.exception.ClueException;
import com.raipeng.aiworkbench.repository.ClueRepository;

import com.raipeng.aiworkbench.repository.ClueTransferRecordRepository;
import com.raipeng.common.model.BaseEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ClueService {
    @Autowired
    private ClueRepository clueRepository;
    @Autowired
    private ClueTransferRecordRepository clueTransferRecordRepository;

    public Clue findClueById(Long clueId) {
        Optional<Clue> clueOptional = clueRepository.findById(clueId);
        if (!clueOptional.isPresent()) {
            throw new ClueException("线索不存在");
        }
        return clueOptional.get();
    }

    public void updateFollowUpIdOfClue(Clue clue, Long followUpLogId) {
        List<Long> clueFollowUpLogIds = clue.getClueFollowUpLogIds()==null?new ArrayList<>():clue.getClueFollowUpLogIds();
        clueFollowUpLogIds.add(followUpLogId);
        clue.setClueFollowUpLogIds(clueFollowUpLogIds);
        clue.setFollowCount(clueFollowUpLogIds.size());
    }


    public void updateTransferIdOfCluesForAuto(List<ClueTransferRecord> records, Clue clue) {
        // 排查问题 临时判断是否有重复数据
        clueTransferRecordRepository.saveAll(records);
        // 排查问题 临时判断是否有重复数据
        List<Long> clueTransferRecordsList = records.stream().map(ClueTransferRecord::getId).sorted().collect(Collectors.toList());

        List<Long> recordIds = clue.getClueTransferRecordIds() == null ? new ArrayList<>() : clue.getClueTransferRecordIds();
        recordIds.addAll(clueTransferRecordsList);
        clue.setClueTransferRecordIds(recordIds);
    }


    public ClueTransferRecord createClueTransferRecord(String clueUniqueId, ClueOperationType type, String nowTime,
                                                       Long userId, String account) {
        ClueTransferRecord clueTransferRecord = new ClueTransferRecord();
        clueTransferRecord.setClueUniqueId(clueUniqueId);
        clueTransferRecord.setOperateAccountId(userId);
        clueTransferRecord.setOperateAccount(account);
        clueTransferRecord.setOperationTime(nowTime);
        clueTransferRecord.setOperationType(type);
        return clueTransferRecord;
    }
}
