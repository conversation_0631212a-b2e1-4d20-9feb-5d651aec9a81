package com.raipeng.aiworkbench.service;


import com.alibaba.fastjson.JSONObject;
import com.raipeng.aidatacommon.enums.ClueOperationType;
import com.raipeng.aidatacommon.enums.ExamineStatus;
import com.raipeng.aidatacommon.enums.FollowUpType;
import com.raipeng.aidatacommon.model.AIOutboundTask;
import com.raipeng.aidatacommon.model.CallRecordForHumanMachine;
import com.raipeng.aidatacommon.model.PhoneRecord;
import com.raipeng.aidatacommon.model.callteam.CallSeat;
import com.raipeng.aidatacommon.model.callteam.CallSeatStatisticInfo;
import com.raipeng.aidatacommon.model.clue.Clue;
import com.raipeng.aidatacommon.model.clue.ClueFollowUpLog;
import com.raipeng.aidatacommon.model.clue.ClueTransferRecord;
import com.raipeng.aiworkbench.controller.request.AIMainCallParam;
import com.raipeng.aiworkbench.controller.request.AIManListenOrDialFailParam;
import com.raipeng.aiworkbench.controller.request.AIManPostParam;
import com.raipeng.aiworkbench.controller.response.CallDataForAIMan;
import com.raipeng.aiworkbench.entity.CallDetailInfo;
import com.raipeng.aiworkbench.entity.CallSeatPopWinCache;
import com.raipeng.aiworkbench.entity.CallSeatRedisCache;
import com.raipeng.aiworkbench.entity.RecordForHumanMachineRedisCache;
import com.raipeng.aiworkbench.enums.DingDingMsgType;
import com.raipeng.aiworkbench.exceptionadvice.exception.CallSeatException;
import com.raipeng.aiworkbench.mq.ClueDataPushProducer;
import com.raipeng.aiworkbench.repository.*;
import com.raipeng.aiworkbench.util.AITimeUtil;
import com.raipeng.aiworkbench.util.CallSeatUtil;
import com.raipeng.aiworkbench.util.GroupIDParseUtil;
import com.raipeng.aiworkbench.wrapper.ClueDataPushWrapper;
import com.raipeng.common.constant.CommonConstants;
import com.raipeng.common.enums.*;
import com.raipeng.common.util.StringUtils;
import io.netty.util.Timeout;
import io.netty.util.TimerTask;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import static com.raipeng.aiworkbench.constant.CommonConstant.CALL_RECORD_FOR_HUMAN_MACHINE_CACHE;
import static com.raipeng.aiworkbench.constant.CommonConstant.CALL_RECORD_HUMAN_MACHINE_PREFIX;
import static com.raipeng.aiworkbench.service.CallSeatService.AI_MAN_DETAIL_INFO_MAP;
import static com.raipeng.aiworkbench.service.CallSeatService.TIMER;
import static com.raipeng.common.enums.ClueStatus.BE_ARCHIVED;
import static com.raipeng.common.enums.ClueStatus.BE_DISTRIBUTED;

@Slf4j
@Service
public class CallSeatOperationForAIManService {
    @Autowired
    private ClueService clueService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private ClueDataPushProducer clueDataPushProducer;

    @Autowired
    private CallSettingService callSettingService;

    @Autowired
    private AdminRepository adminRepository;

    @Autowired
    private SmsRecordService smsRecordService;

    @Autowired
    private FormRecordService formRecordService;

    @Autowired
    private ClueRepository clueRepository;

    @Autowired
    private CallSeatService callSeatService;

    @Autowired
    private CallSeatRedisCacheService callSeatRedisCacheService;

    @Autowired
    private DingDingService dingDingService;

    @Autowired
    private PhoneRecordRepository phoneRecordRepository;

    @Autowired
    private CallSeatRepository callSeatRepository;

    @Autowired
    private ClueFollowUpLogService clueFollowUpLogService;

    @Autowired
    private ClueFollowUpLogRepository clueFollowUpLogRepository;

    @Autowired
    private AIOutboundTaskRepository aiOutboundTaskRepository;

    @Autowired
    private CallSeatStatisticInfoRepository callSeatStatisticInfoRepository;

    @Autowired
    private CallSeatStatisticService callSeatStatisticService;


    @Autowired
    private CallRecordForHumanMachineRepository callRecordForHumanMachineRepository;

    /**
     * 开始弹窗
     *
     * @return 坐席信息
     */
    @Transactional
    public CallDataForAIMan startPopWindow() {
        // 1. 获取当前坐席
        CallSeat callSeat = callSeatService.findCurrentAccountCallSeat();

        // 2. 获取坐席缓存
        Long callSeatId = callSeat.getId();
        CallSeatPopWinCache callSeatPopWinCache = callSeatService.getCallSeatPopWinCacheByCallSeatId(callSeatId);

        // 3. 获取当前通话记录
        String recordId = callSeatPopWinCache.getRecordId();
        CallRecordForHumanMachine record = callRecordForHumanMachineRepository.findCallRecordForHumanMachineByRecordId(recordId);

        // 4. 设置开始弹窗的时间
        record.setStartPopWinTime(AITimeUtil.getNowTimeString());

        // 5. 如果当前坐席处于离线这种异常状态, 则更新一下坐席的在线开始时间
        if (CallSeatStatus.OFF_LINE.equals(callSeat.getCallSeatStatus())) {
            callSeatStatisticService.updateCallSeatOnlineTimeAndSave(callSeatId, true);
        }

        // 6. 设置坐席状态
        callSeat.setCallSeatStatus(CallSeatStatus.HUMAN_MACHINE_WINDOW);

        // 7. 存错信息
        callSeatRepository.save(callSeat);
        record = callRecordForHumanMachineRepository.save(record);

        // 8. 生成返回参数
        AIOutboundTask task = aiOutboundTaskRepository.findFirstById(Long.parseLong(record.getTaskId()));
        CallDataForAIMan data = new CallDataForAIMan();
        data.setCallRecordForHumanMachine(getNewRecord(record));
        data.setCallTeamHandleType(task.getCallTeamHandleType());
        data.setAiCallIp(callSeatPopWinCache.getAiCallIp());
        data.setAiCallPort(callSeatPopWinCache.getAiCallPort());
        data.setSpeechCraftId(task.getSpeechCraftId());
        data.setCallId(callSeatPopWinCache.getCallId());
        data.setCallSeatId(callSeatId);
        data.setAccount(callSeat.getAccount());

        // 9. 记录当前坐席这通电话的缓存
        String phone = record.getPhone();
        saveCallSeatInfoTimeOut(phone, callSeatId, recordId, task.getTaskName());
        log.info("人机=>name:{}, account:{}, 弹窗", callSeat.getName(), callSeat.getAccount());
        return data;
    }


    @Transactional
    public CallDataForAIMan startHumanMachineListening(AIMainCallParam param) {
        // 1. 获取当前坐席
        CallSeat callSeat = callSeatService.findCurrentAccountCallSeat();
        Long callSeatId = callSeat.getId();

        // 2. 对recordId进行处理
        String recordId = param.getRecordId();
        if (recordId == null) {
            log.warn("人机[监听]=>name:{},account:{}, 丢失浏览器缓存,尝试获取后台缓存", callSeat.getName(), callSeat.getAccount());
            dingDingService.sedDDMsg(DingDingMsgType.WARN, "人机[监听]=>name:" + callSeat.getName() + ",account:" + callSeat.getAccount() + ", 丢失浏览器缓存,请检查浏览器");
            CallSeatRedisCache callSeatRedisCache = callSeatService.getCallSeatRedisCacheForAIManListening(callSeat);
            recordId = callSeatRedisCache.getRecordId();
        }
        recordId = getActualRecordId(recordId);

        // 3. 获取当前坐席的数据库中的通话记录
        CallRecordForHumanMachine record = callRecordForHumanMachineRepository.findCallRecordForHumanMachineByRecordId(recordId);

        // 4. 设置开始监听时间和结束弹窗时间
        String nowTimeString = AITimeUtil.getNowTimeString();
        record.setStartMonitorTime(nowTimeString);
        record.setEndPopWinTime(nowTimeString);
        record.setIsTransToCallSeat(true);

        // 5. 如果当前坐席处于离线这种异常状态, 则更新一下坐席的在线开始时间
        if (CallSeatStatus.OFF_LINE.equals(callSeat.getCallSeatStatus())) {
            callSeatStatisticService.updateCallSeatOnlineTimeAndSave(callSeatId, true);
        }

        // 6. 设置坐席状态信息
        callSeat.setCallSeatStatus(CallSeatStatus.HUMAN_MACHINE_LISTEN);

        callRecordForHumanMachineRepository.save(record);
        callSeatRepository.save(callSeat);
        log.info("人机=>name:{},account:{},开始监听", callSeat.getName(), callSeat.getAccount());

        CallDataForAIMan data = new CallDataForAIMan();
        data.setCallSeatId(callSeatId);
        data.setRecordIdForCache(recordId);
        return data;
    }

    @Transactional
    public void humanMachineListenOrDialingFail(AIManListenOrDialFailParam param) {
        // 1. 获取当前坐席
        CallSeat callSeat = callSeatService.findCurrentAccountCallSeat();
        Long callSeatId = callSeat.getId();

        // 2. 对recordId进行处理
        String recordId = param.getRecordId();
        if (recordId == null) {
            log.warn("人机[失败]=>name:{},account:{}, 丢失浏览器缓存, 尝试获取后台缓存", callSeat.getName(), callSeat.getAccount());
            dingDingService.sedDDMsg(DingDingMsgType.WARN, "人机[失败]=>name:" + callSeat.getName() + ",account:" + callSeat.getAccount() + ", 丢失浏览器缓存,请检查浏览器");
            CallSeatRedisCache callSeatRedisCache = callSeatService.getCallSeatRedisCacheForAIManFail(callSeat);
            recordId = callSeatRedisCache.getRecordId();
        }
        CallRecordForHumanMachine record = callRecordForHumanMachineRepository.findCallRecordForHumanMachineByRecordId(getActualRecordId(recordId));

        if (!param.isMiss()) {
            record.setNoReceptionReason(param.getNoReceptionReason());
            record.setEndMonitorTime(AITimeUtil.getNowTimeString());
        }

        callSeat.setCallSeatStatus(CallSeatStatus.HUMAN_MACHINE_IDLE);

        cancelCallSeatInfoTimeOut(record.getPhone(), callSeatId);
        callSeatRedisCacheService.clearRedisCacheByCallSeatId(callSeatId);
        log.info("人机=>name:{},account:{}介入或监听失败", callSeat.getName(), callSeat.getAccount());
    }

    @Transactional
    public CallDataForAIMan startHumanMachineDialing(AIMainCallParam param) {
        // 1. 获取当前坐席
        CallSeat callSeat = callSeatService.findCurrentAccountCallSeat();
        Long callSeatId = callSeat.getId();
        CallRecordForHumanMachine record;

        Long userId = callSeat.getAccountId();
        String account = callSeat.getAccount();
        Long callTeamId = callSeat.getCallTeamId();

        // 2. 对recordId进行处理
        String recordId = param.getRecordId();
        if (recordId == null) {
            log.warn("人机[介入]=>name:{},account:{}, 丢失浏览器缓存, 尝试获取后台缓存", callSeat.getName(), callSeat.getAccount());
            dingDingService.sedDDMsg(DingDingMsgType.WARN, "人机[介入]=>name:" + callSeat.getName() + ",account:" + callSeat.getAccount() + ", 丢失浏览器缓存,请检查浏览器");
            CallSeatRedisCache callSeatRedisCache = callSeatService.getCallSeatRedisCacheForAIManDialing(callSeat);
            recordId = callSeatRedisCache.getRecordId();
        }

        // 3. 获取当前通话记录
        record = callRecordForHumanMachineRepository.findCallRecordForHumanMachineByRecordId(getActualRecordId(recordId));
        if (record != null && record.getCallStatus() != null) {
            callSeat.setCallSeatStatus(CallSeatStatus.HUMAN_MACHINE_IDLE);
            cancelCallSeatInfoTimeOut(record.getPhone(), callSeatId);
            callSeatRedisCacheService.clearRedisCacheByCallSeatId(callSeatId);
            throw new CallSeatException("客户已经挂断无法介入");
        }

        // 4. 获取坐席电话信息
        CallDetailInfo callDetailInfo = callSeatService.getCallDetailInfoForAIMain(callSeatId, record.getPhone());

        // 5. 获取坐席统计信息
        CallSeatStatisticInfo callSeatStatisticInfo = callSeatStatisticService.findCallSeatStatisticInfo(callSeatId, LocalDate.now());

        // 6. 更新坐席统计信息
        if (CallSeatStatus.OFF_LINE.equals(callSeat.getCallSeatStatus())) {
            callSeatStatisticService.updateCallSeatOnlineTime(callSeatStatisticInfo, false);
        }
        callSeat.setCallSeatStatus(CallSeatStatus.HUMAN_MACHINE_DIALING);

        // 7. 创建线索
        String groupId = callSeat.getGroupId();
        String phone = record.getPhone();
        String nowTimeString = AITimeUtil.getNowTimeString();
        Clue clue = clueRepository.findClueByGroupIdAndPhone(callSeat.getGroupId(), record.getPhone());

        List<ClueTransferRecord> clueTransferRecordList = new ArrayList<>();

        //跟进记录类型
        if (clue == null) {
            clue = createNewClueFromRecord(
                    record,
                    callDetailInfo,
                    callSeat.getGroupId(),
                    nowTimeString,
                    callSeat.getId(),
                    callSeat.getCallTeamId());
            //  执行导入的流转记录和分配坐席组和下发坐席操作的流转记录
            clueTransferRecordList.add(clueService.createClueTransferRecord(clue.getClueUniqueId(), ClueOperationType.IMPORT, nowTimeString, userId, account));
            clueTransferRecordList.add(clueService.createClueTransferRecord(clue.getClueUniqueId(), ClueOperationType.SEND , nowTimeString, userId, account));
            clueTransferRecordList.add(clueService.createClueTransferRecord(clue.getClueUniqueId(), ClueOperationType.DISTRIBUTE, nowTimeString, userId, account));

        } else {
            // 判断状态 如果是归档数据，则激活 更新线索状态

            if (BE_ARCHIVED.equals(clue.getClueStatus())) {
                //执行激活
                clue.setClueStatus(BE_DISTRIBUTED);
                clue.setCallTeamId(callTeamId);
                clue.setCallSeatId(callSeatId);
                clueTransferRecordList.add(clueService.createClueTransferRecord(clue.getClueUniqueId(), ClueOperationType.ACTIVE, nowTimeString, userId, account));
                clueTransferRecordList.add(clueService.createClueTransferRecord(clue.getClueUniqueId(), ClueOperationType.SEND , nowTimeString, userId, account));
                clueTransferRecordList.add(clueService.createClueTransferRecord(clue.getClueUniqueId(), ClueOperationType.DISTRIBUTE, nowTimeString, userId, account));
            }
        }
        clue.setLatestFollowUpTime(nowTimeString);
        clue.setLatestCallStatus(CallStatus.SUCCESS_CALL);
        clue = clueRepository.save(clue);

        Long clueCallSeatId = clue.getCallSeatId();
        if (clueCallSeatId == null) {
            log.info("人机[介入]=>name:{},account:{}, 线索中没有坐席", callSeat.getName(), callSeat.getAccount());
            clue.setCallSeatId(callSeatId);
        }
        if (clue.getCallSeatId().equals(callSeatId)) {
            // 8. 更新线索信息
            clue.setBeAllocatedTime(nowTimeString);
            clue.setBeSendTime(nowTimeString);
            callSeatService.updateClueIdOfCallSeat(callSeat, clue.getId());

            // 9. 跟新坐席统计信息
            callSeatStatisticService.updateCallSeatReceptionTime(callSeatStatisticInfo, true);
            callSeatStatisticInfo.setDialingCount(callSeatStatisticInfo.getDialingCount() + 1);
            callSeatStatisticInfo.getCallSeatClueStatisticInfo().getFollowingSet().add(clue.getId());
            callSeatStatisticInfo.getCallSeatClueStatisticInfo().getAcquireSet().add(clue.getId());
        }

        // 9. 创建跟进日志
        ClueFollowUpLog clueFollowUpLog = createClueFollowUpLog(record.getRecordId(), clue.getId(), callSeatId, nowTimeString);
        Long clueFollowUpLogId = clueFollowUpLog.getId();

        // 10. 缓存中保存跟进日志ID
        callDetailInfo.setFollowUpLogId(clueFollowUpLogId);

        // 11. 更新线索中跟进日志ID
        clueService.updateFollowUpIdOfClue(clue, clueFollowUpLog.getId());
        clueService.updateTransferIdOfCluesForAuto(clueTransferRecordList, clue);

        // 12. 更新通话记录
        record.setStartAnswerTime(nowTimeString);
        record.setEndPopWinTime(nowTimeString);
        record.setEndMonitorTime(nowTimeString);
        record.setIsTransToCallSeat(true);
        record.setIsConvertToClue(true);
        record.setClueId(clue.getId());
        record.setCallSeatId(callSeat.getId());
        record.setCallTeamId(callSeat.getCallTeamId());
        record.setCallStatus("7");
        record.setClueFollowUpLogId(clueFollowUpLogId);
        record.setFormId(clue.getFormId());

        // 13. 存储信息
        callSeatStatisticInfoRepository.save(callSeatStatisticInfo);
        callRecordForHumanMachineRepository.save(record);
        clueRepository.save(clue);
        callSeatRepository.save(callSeat);
        log.info("人机=>name:{},account:{},介入开始", callSeat.getName(), callSeat.getAccount());

        // 14. 返回结果
        CallDataForAIMan callDataForAIMan = new CallDataForAIMan();
        callDataForAIMan.setClueId(clue.getId());
        callDataForAIMan.setFormId(clue.getFormId());
        callDataForAIMan.setCallSeatId(callSeatId);
        callDataForAIMan.setCallSeatName(callSeat.getName());
        callDataForAIMan.setGroupId(callSeat.getGroupId());
        callDataForAIMan.setDialingTimeForCache(nowTimeString);
        callDataForAIMan.setRecordIdForCache(recordId);
        return callDataForAIMan;
    }

    @Transactional
    public CallDataForAIMan endHumanMachineDialing(AIMainCallParam param) {
        // 1. 获取当前坐席
        CallSeat callSeat = callSeatService.findCurrentAccountCallSeat();
        Long callSeatId = callSeat.getId();

        // 2. 对recordId进行处理
        String recordId = param.getRecordId();
        if (recordId == null) {
            log.warn("人机[挂断]=>name:{},account:{}, 丢失浏览器缓存, 尝试获取后台缓存", callSeat.getName(), callSeat.getAccount());
            dingDingService.sedDDMsg(DingDingMsgType.WARN, "人机[挂断]=>name:" + callSeat.getName() + ",account:" + callSeat.getAccount() + ", 丢失浏览器缓存,请检查浏览器");
            CallSeatRedisCache callSeatRedisCache = callSeatService.getCallSeatRedisCacheForAIManEndDialing(callSeat);
            recordId = callSeatRedisCache.getRecordId();
        }
        recordId = getActualRecordId(recordId);

        CallSeatStatisticInfo callSeatStatisticInfo = callSeatStatisticService.findCallSeatStatisticInfo(callSeatId, LocalDate.now());

        if (CallSeatStatus.OFF_LINE.equals(callSeat.getCallSeatStatus())) {
            callSeatStatisticService.updateCallSeatOnlineTime(callSeatStatisticInfo, true);
        }

        callRecordForHumanMachineRepository.updateEndAnswerTime(AITimeUtil.getNowTimeString(), recordId);

        callSeatStatisticService.updateCallSeatReceptionTime(callSeatStatisticInfo, false);
        callSeatStatisticService.updateCallSeatPostTime(callSeatStatisticInfo, true);

        callSeatStatisticInfoRepository.save(callSeatStatisticInfo);
        callSeatRepository.save(callSeat);
        log.info("人机=>name:{},account:{},通话结束", callSeat.getName(), callSeat.getAccount());

        CallDataForAIMan callDataForAIMan = new CallDataForAIMan();
        callDataForAIMan.setCallSeatId(callSeatId);
        callDataForAIMan.setRecordIdForCache(recordId);
        return callDataForAIMan;
    }

    @Transactional
    public void submitHumanMachinePosting(AIManPostParam param) {
        Long clueId = param.getClueId();
        FollowUpStatus followUpStatus = param.getFollowUpStatus();
        Boolean isStar = param.getIsStar();
        String nextFollowUpTime = param.getNextFollowUpTime();
        String note = param.getNote();
        Boolean isPostingOutOfTime = param.getIsPostingOutOfTime();
        String intentionClass = param.getIntentionClass();
        String intentionTags = param.getIntentionTags();
        String intentionTagIds = param.getIntentionTagIds();

        // 1. 获取当前坐席
        CallSeat callSeat = callSeatService.findCurrentAccountCallSeat();
        Clue clue = clueService.findClueById(clueId);

        Long callSeatId = callSeat.getId();
        String phone = clue.getPhone();
        CallDetailInfo callDetailInfo = callSeatService.getCallDetailInfoForAIMain(callSeatId, phone);

        // 3. 获取当前坐席的数据库中的通话记录
        CallRecordForHumanMachine record = callRecordForHumanMachineRepository.findCallRecordForHumanMachineByRecordId(callDetailInfo.getRecordId());

        // 4. 获取坐席统计信息
        CallSeatStatisticInfo callSeatStatisticInfo = callSeatStatisticService.findCallSeatStatisticInfo(callSeat.getId(), LocalDate.now());

        ClueFollowUpLog clueFollowUpLog = clueFollowUpLogService.findClueFollowUpLogById(callDetailInfo.getFollowUpLogId());
        log.info("人机=>name:{},account:{},查询数据库结束", callSeat.getName(), callSeat.getAccount());

        if (CallSeatStatus.OFF_LINE.equals(callSeat.getCallSeatStatus())) {
            callSeatStatisticService.updateCallSeatOnlineTime(callSeatStatisticInfo, true);
        }

        callSeatStatisticService.updateCallSeatPostTime(callSeatStatisticInfo, false);

        switch (followUpStatus) {
            case SUCCESS:
                callSeatStatisticInfo.getCallSeatClueStatisticInfo().getSuccessSet().add(clueId);
                clue.setExamineStatus(ExamineStatus.NO_EXAMINE);
                break;
            case FAIL:
                callSeatStatisticInfo.getCallSeatClueStatisticInfo().getFailSet().add(clueId);
                clue.setExamineStatus(null);
                break;
            case BEING:
                clue.setExamineStatus(null);
                break;
            default:
                throw new CallSeatException("传入错误的跟进状态");
        }
        log.info("人机=>name:{},account:{},更新统计信息结束", callSeat.getName(), callSeat.getAccount());

        // 8. 更新通坐席状态
        callSeat.setCallSeatStatus(CallSeatStatus.HUMAN_MACHINE_IDLE);

        // 9. 更新通话记录
        record.setFormId(clue.getFormId());
        record.setIsPostingOutOfTime(isPostingOutOfTime);
        record.setPostingDuration(CallSeatUtil.getLastTimeBlockInterval(callSeatStatisticInfo.getPostTimeBlocks()));
        record.setManualIntentionClass(intentionClass);
        record.setManualIntentionLabels(intentionTags);
        record.setManualIntentionLabelIds(intentionTagIds);

        // 处理短信
        Optional<AIOutboundTask> byId = aiOutboundTaskRepository.findById(Long.valueOf(record.getTaskId()));
        if (byId.isPresent()) {
            AIOutboundTask aiOutboundTask = byId.get();
            smsRecordService.sendHumanMachineOnHookMessage(aiOutboundTask, clue, record, intentionTags, intentionClass, callSeat.getId(), callSeat.getCallTeamId());
            record.setTemplateId(aiOutboundTask.getTemplateId());
        }

        // 10. 更新跟进记录
        clueFollowUpLog.setFollowUpStatus(followUpStatus);
        clueFollowUpLog.setNextFollowUpTime(nextFollowUpTime);
        clueFollowUpLog.setNote(note);

        // 11. 更新线索
        clue.setStar(isStar);
        clue.setLatestFollowUpStatus(followUpStatus);
        clue.setNextFollowUpTime(nextFollowUpTime);
        clue.setCanBeClueOperator(true);
        clue.setCanBeSend(true);
        clue.setLatestFollowUpNote(note);
        clue.setLatestCallDuration(record.getCallDuration());

        // 12. 存储操作过程中的信息
        callSeatStatisticInfoRepository.save(callSeatStatisticInfo);
        log.info("人机=>name:{},account:{},保存坐席统计信息结束", callSeat.getName(), callSeat.getAccount());
        clueFollowUpLogRepository.save(clueFollowUpLog);
        log.info("人机=>name:{},account:{},保存跟进日志结束", callSeat.getName(), callSeat.getAccount());
        clueRepository.save(clue);
        log.info("人机=>name:{},account:{},保存线索结束", callSeat.getName(), callSeat.getAccount());
        callSeatRepository.save(callSeat);
        log.info("人机=>name:{},account:{},坐席状态", callSeat.getName(), callSeat.getAccount());
        callRecordForHumanMachineRepository.save(record);
        log.info("人机=>name:{},account:{},保存数据结束", callSeat.getName(), callSeat.getAccount());

        // 13. 更新名单表和任务表中的意向统计
        String finalIntentionClass = CallSeatUtil.getFinalIntentionClass(record);
        String finalIntentionLabel = CallSeatUtil.getFinalIntentionLabel(record);
        updatePhoneRecordAndTask(record, finalIntentionClass, callSeat);
        log.info("人机=>name:{},account:{},更新名单任务结束", callSeat.getName(), callSeat.getAccount());

        String accountId = GroupIDParseUtil.getMasterAccountIdString(callSeat.getGroupId());
        if (callSeatRedisCacheService.getRedisMMessageCacheByRecordId(record.getRecordId())) {
            if (StringUtils.isEmpty(record.getIntentionLabels())) {
                record.setIntentionLabels(CommonConstants.SPECIAL_SEND_MESSAGE_TAG);
            } else {
                record.setIntentionLabels(record.getIntentionLabels() + "," + CommonConstants.SPECIAL_SEND_MESSAGE_TAG);
            }
        }
        ClueDataPushWrapper clueDataPushWrapper = new ClueDataPushWrapper();
        clueDataPushWrapper.setClueId(clueId);
        clueDataPushWrapper.setIntentionLabels(finalIntentionLabel);
        clueDataPushWrapper.setIntentionClass(finalIntentionClass);
        clueDataPushWrapper.setRecord(record);
        adminRepository.findById(Long.valueOf(accountId)).ifPresent(admin -> {
            clueDataPushWrapper.setAdmin(admin);
            clueDataPushWrapper.setIsPushDialogContent(admin.getIsPushDialogContent());
            clueDataPushWrapper.setCallSeatId(callSeat.getId());
            clueDataPushWrapper.setCallSeatName(callSeat.getName());
            clueDataPushWrapper.setFormRecordDTO(formRecordService.findFormRecordByClueId(clueId));
            clueDataPushWrapper.setFollowUpNote(note);
            clueDataPushWrapper.setFollowUpStatus(followUpStatus.toString());
            if (record.getIfTest() != null && record.getIfTest() == 1) {
                log.info("人机=>name:{},account:{},训练号码无须推送8848", callSeat.getName(), callSeat.getAccount());
                return;
            }
            clueDataPushProducer.send(JSONObject.toJSONString(clueDataPushWrapper));
            log.info("人机=>name:{},account:{},推送8848结束", callSeat.getName(), callSeat.getAccount());
        });

        // 14. 清除坐席信息
        cancelCallSeatInfoTimeOut(record.getPhone(), callSeat.getId());
        deleteDialingRecordInRedis(record.getRecordId());
        log.info("人机=>name:{},account:{},清除内存中状态结束", callSeat.getName(), callSeat.getAccount());
    }

    public CallRecordForHumanMachine findHumanMachineRecordByRecordId(String recordId) {
        CallRecordForHumanMachine record = callRecordForHumanMachineRepository.findCallRecordForHumanMachineByRecordId(recordId);
        if (record != null && record.getTaskId() != null) {
            AIOutboundTask scriptNameById = aiOutboundTaskRepository.findFirstById(Long.valueOf(record.getTaskId()));
            if (scriptNameById != null) {
                record.setScriptName(scriptNameById.getSpeechCraftName());
            }
        }
        if (record != null && record.getClueId() != null) {
            Optional<Clue> nameByClueId = clueRepository.findById(record.getClueId());
            nameByClueId.ifPresent(clue -> record.setName(clue.getName()));
        }
        if (record != null) {
            record.setPhone("");
        }
        return record;
    }

    public void saveDialingRecordInRedis(CallDataForAIMan callDataForAIMan) {
        String recordId = callDataForAIMan.getRecordIdForCache();
        recordId = getActualRecordId(recordId);
        Long callSeatId = callDataForAIMan.getCallSeatId();
        String callSeatName = callDataForAIMan.getCallSeatName();
        Long clueId = callDataForAIMan.getClueId();
        String groupId = callDataForAIMan.getGroupId();
        String dialingTime = callDataForAIMan.getDialingTimeForCache();

        Long accountId = GroupIDParseUtil.getMasterAccountId(groupId);

        RecordForHumanMachineRedisCache cache = new RecordForHumanMachineRedisCache();
        ClueDataPushWrapper clueDataPushWrapper = new ClueDataPushWrapper();
        clueDataPushWrapper.setClueId(clueId);

        adminRepository.findById(accountId).ifPresent(admin -> {
            clueDataPushWrapper.setAdmin(admin);
            clueDataPushWrapper.setIsPushDialogContent(admin.getIsPushDialogContent());
            clueDataPushWrapper.setCallSeatId(callSeatId);
            clueDataPushWrapper.setCallSeatName(callSeatName);

        });
        cache.setClueDataPushWrapper(clueDataPushWrapper);
        cache.setDialingTime(dialingTime);
        RMap<String, String> rMap = redissonClient.getMap(CALL_RECORD_FOR_HUMAN_MACHINE_CACHE);
        rMap.put(recordId, JSONObject.toJSONString(cache));
    }

    private void deleteDialingRecordInRedis(String recordId) {
        RMap<String, String> rMap = redissonClient.getMap(CALL_RECORD_FOR_HUMAN_MACHINE_CACHE);
        rMap.remove(recordId);
    }

    private String getActualRecordId(String recordId) {
        if (recordId.startsWith(CALL_RECORD_HUMAN_MACHINE_PREFIX)) {
            return recordId.split(CALL_RECORD_HUMAN_MACHINE_PREFIX)[1];
        } else {
            return recordId;
        }
    }

    private CallRecordForHumanMachine getNewRecord(CallRecordForHumanMachine record) {
        CallRecordForHumanMachine result = new CallRecordForHumanMachine();
        BeanUtils.copyProperties(record, result);
        result.setRecordId(CALL_RECORD_HUMAN_MACHINE_PREFIX + result.getRecordId());
        return result;
    }

    private void saveCallSeatInfoTimeOut(String phone, Long callSeatId, String recordId, String taskName) {
        AI_MAN_DETAIL_INFO_MAP.compute(callSeatId, (k, v) -> {
            if (v == null) {
                v = new ConcurrentHashMap<>();
            }

            CallDetailInfo callDetailInfo = new CallDetailInfo();
            callDetailInfo.setPhone(phone);
            callDetailInfo.setSeatId(String.valueOf(callSeatId));
            callDetailInfo.setRecordId(recordId);
            callDetailInfo.setTaskName(taskName);

            TimerTask timerTask = new TimerTask() {
                @Override
                public void run(Timeout timeout) throws Exception {
                    Map<String, CallDetailInfo> seatInfoMap = AI_MAN_DETAIL_INFO_MAP.get(callSeatId);
                    if (seatInfoMap != null) {
                        seatInfoMap.remove(phone);
                    }
                }
            };

            Timeout timeout = TIMER.newTimeout(timerTask, 100, TimeUnit.MINUTES);
            callDetailInfo.setTimeout(timeout);

            v.put(phone, callDetailInfo);
            return v;
        });
    }

    private void cancelCallSeatInfoTimeOut(String phone, Long callSeatId) {
        Map<String, CallDetailInfo> seatInfoMap = AI_MAN_DETAIL_INFO_MAP.get(callSeatId);
        if (seatInfoMap != null) {
            CallDetailInfo callDetailInfo = seatInfoMap.get(phone);
            if (callDetailInfo != null) {
                Timeout timeout = callDetailInfo.getTimeout();
                if (timeout != null) {
                    timeout.cancel();
                }
            }
            seatInfoMap.remove(phone);
        }
    }

    private Clue createNewClueFromRecord(CallRecordForHumanMachine record, CallDetailInfo info,
                                         String groupId, String now, Long callSeatId, Long callTeamId) {
        Clue clue = new Clue();
        clue.setClueUniqueId(CallSeatUtil.getUniqueID());
        clue.setCanBeClueOperator(true);
        clue.setCanBeSend(false);
        clue.setPhone(record.getPhone());
        clue.setGroupId(groupId);
        clue.setClueStatus(ClueStatus.BE_DISTRIBUTED);
        clue.setFromType(FromType.TASK_IMPORT);
        clue.setImportTime(now);
        clue.setProvince(record.getProvince());
        clue.setProvinceCode(record.getProvinceCode());
        clue.setCity(record.getCity());
        clue.setCityCode(record.getCityCode());
        clue.setOperator(record.getOperator());
        clue.setTaskId(info.getTaskId());
        clue.setTaskName(info.getTaskName());
        clue.setScriptId(record.getSpeechCraftId());
        clue.setScriptStringId(record.getScriptStringId());
        clue.setCallStatus(CallStatus.SUCCESS_CALL);
        clue.setLatestFollowUpStatus(FollowUpStatus.BEING);
        clue.setLatestFollowUpTime(now);
        clue.setCallSeatId(callSeatId);
        clue.setCallTeamId(callTeamId);
        clue.setFormId(CallSeatUtil.getUniqueID());
        clue.setName(record.getName());
        clue.setCompany(record.getCompany());
        clue.setComment(record.getRemarks());
        return clue;
    }

    private ClueFollowUpLog createClueFollowUpLog(String recordId, Long clueId, Long callSeatId,
                                                  String followUpTime) {
        ClueFollowUpLog clueFollowUpLog = new ClueFollowUpLog();
        clueFollowUpLog.setRecordId(recordId);
        clueFollowUpLog.setClueId(clueId);
        clueFollowUpLog.setFollowUpType(FollowUpType.HUMAN_MACHINE_COOPERATION);
        clueFollowUpLog.setFollowUpStatus(FollowUpStatus.BEING);
        clueFollowUpLog.setCallSeatId(callSeatId);
        clueFollowUpLog.setFollowUpTime(followUpTime);
        return clueFollowUpLogRepository.save(clueFollowUpLog);
    }

    public void updatePhoneRecordAndTask(CallRecordForHumanMachine record, String finalIntentionClass, CallSeat callSeat) {
        if (finalIntentionClass != null) {
            if (!finalIntentionClass.equals(record.getIntentionClass())) {
                PhoneRecord phoneRecord = phoneRecordRepository.findPhoneRecordByRecordId(record.getPhoneRecordId());
                if (phoneRecord == null) {
                    throw new CallSeatException("更新名单表出错");
                }
                phoneRecord.setLatestIntentionClass(finalIntentionClass);
                phoneRecordRepository.save(phoneRecord);
                log.info("人机=>name:{},account:{},结束更新名单表", callSeat.getName(), callSeat.getAccount());
                List<String> intentionClassList = callSettingService.getIntentionClassList(callSeat.getGroupId());
                if (intentionClassList.contains(finalIntentionClass) && !intentionClassList.contains(record.getIntentionClass())) {
                    aiOutboundTaskRepository.updateIntentionPhoneNum(Long.valueOf(record.getTaskId()), 1);
                }
                if (!intentionClassList.contains(finalIntentionClass) && intentionClassList.contains(record.getIntentionClass())) {
                    aiOutboundTaskRepository.updateIntentionPhoneNum(Long.valueOf(record.getTaskId()), -1);
                }
                log.info("人机=>name:{},account:{},结束更新任务表", callSeat.getName(), callSeat.getAccount());
            }
        }
    }
}
