package com.raipeng.send.utils;


import com.raipeng.send.constant.RedisKey;

import java.util.Calendar;



public class CommonUtil {
	public static Long getHashKey(String shortUrl) {
		return getHashKey(ShortUrlUtil.reconvert(shortUrl));
	}

	public static String getShortHashKey(String shortUrl) {
		return String.format(RedisKey.SHORT_LONG_URL_MAP_F, getHashKey(ShortUrlUtil.reconvert(shortUrl)));
	}

	public static String getShortHashKey(Long index) {
		return String.format(RedisKey.SHORT_LONG_URL_MAP_F, index);
	}

	public static Long getHashKey(Long index) {
		return HashUtil.generateHashKey(index, RedisKey.MODE);
	}

	public final static long daySecond = 86400L;

	public static Long getSecondBeforeDays(long days) {
		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		return calendar.getTimeInMillis() / 1000L - daySecond * days;
	}

	public static void main(String args[]) {
		System.out.println(getSecondBeforeDays(1));
	}
}
