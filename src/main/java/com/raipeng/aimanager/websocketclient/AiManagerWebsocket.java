package com.raipeng.aimanager.websocketclient;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.raipeng.aimanager.constant.CommonConstants;
import com.raipeng.aimanager.constant.GlobalVariable;
import com.raipeng.aimanager.enums.DingDingMsgType;
import com.raipeng.aimanager.service.ManagerFsInterService;
import com.raipeng.aimanager.utils.DDMsgUtil;
import com.raipeng.aimanager.utils.SpringUtils;
import com.raipeng.common.entity.linemanager.LineManagerConstant;
import com.raipeng.common.entity.linemanager.SupplyLineDTO;
import com.raipeng.common.entity.linemanager.TenantLineDTO;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Set;

@Slf4j
@Component
@ServerEndpoint("/websocket/{name}")
public class AiManagerWebsocket {
    @OnOpen
    public void onOpen(Session session, @PathParam(value = "name") String name) {
        log.info("the websocket is connected...");
    }

    @OnClose
    public void onClose() {
        log.info("the websocket connection is closed...");
    }

    @OnError
    public void onError(Session session, Throwable throwable) throws IOException {
        log.error("the websocket connection occurs error: {}", throwable.getMessage());
        session.close();
    }

    @OnMessage(maxMessageSize=5242880)
    public void onMessage(Session session, String message) throws IOException {
        if ("--end--".equals(message)) {
            log.info("accept a message from ai-speech: {}", message);
            session.getBasicRemote().sendText("--success--");
            session.close();
        } else if (message.startsWith(LineManagerConstant.PUBLISH_TENANT_LINE)) {
            TenantLineDTO tenantLineDTO = JSONObject.parseObject(message.split(LineManagerConstant.SPLIT_SYMBOL)[1], TenantLineDTO.class);
            GlobalVariable.tenantLineContainer.put(tenantLineDTO.getLineNumber(), tenantLineDTO);
            log.info("get one tenantLine structure:{}", tenantLineDTO);
        } else if (message.startsWith(LineManagerConstant.PUBLISH_SUPPLY_LINE)) {
            SupplyLineDTO supplyLineDTO = JSONObject.parseObject(message.split(LineManagerConstant.SPLIT_SYMBOL)[1], SupplyLineDTO.class);
            String supplyLineNumber = supplyLineDTO.getLineNumber();
            SupplyLineDTO oldSupplyLineDTO = GlobalVariable.supplyLineContainer.get(supplyLineNumber);
            if (oldSupplyLineDTO != null
                    && oldSupplyLineDTO.getCaps()!= null
                    && !oldSupplyLineDTO.getCaps().equals(supplyLineDTO.getCaps())
                    && !GlobalVariable.pendingSupplyLines.contains(supplyLineNumber)) {
                SpringUtils.getBean(ManagerFsInterService.class).resetCaps(supplyLineNumber, supplyLineDTO.getCaps());
                log.info("change one supply caps line in count[number:{}, caps:{}]", supplyLineNumber, supplyLineDTO.getCaps());
            }
            if (oldSupplyLineDTO != null
                    && oldSupplyLineDTO.getCaps() == null
                    && supplyLineDTO.getCaps() != null
                    && !GlobalVariable.pendingSupplyLines.contains(supplyLineNumber)) {
                int capsQueueLength = Math.min(supplyLineDTO.getConcurrentLimit() * CommonConstants.capsMultiplier, CommonConstants.capsMultiplierLimit);
                GlobalVariable.supplyLinesCapsMap.put(supplyLineNumber, capsQueueLength);
                log.info("update one supply caps line in count[number:{}, count:{}]", supplyLineNumber, capsQueueLength);
            }
            if (oldSupplyLineDTO == null
                    && supplyLineDTO.getCaps() != null
                    && !GlobalVariable.pendingSupplyLines.contains(supplyLineNumber)) {
                int capsQueueLength = Math.min(supplyLineDTO.getConcurrentLimit() * CommonConstants.capsMultiplier, CommonConstants.capsMultiplierLimit);
                GlobalVariable.supplyLinesCapsMap.put(supplyLineNumber, capsQueueLength);
                log.info("add one supply caps line in count[number:{}, count:{}]",capsQueueLength);
            }
            GlobalVariable.supplyLineContainer.put(supplyLineNumber, supplyLineDTO);
            log.info("get one supplyLine structure:{}", supplyLineDTO);
        } else if (message.startsWith(LineManagerConstant.ADD_PENDING_SUPPLY_LINE)) {
            String supplyLineNumber = message.split(LineManagerConstant.SPLIT_SYMBOL)[1];
            GlobalVariable.pendingSupplyLines.add(supplyLineNumber);
            SpringUtils.getBean(ManagerFsInterService.class).resetCaps(supplyLineNumber, null);
            log.info("add one pending supplyLine:{}", supplyLineNumber);
        } else if (message.startsWith(LineManagerConstant.DELETE_PENDING_SUPPLY_LINE)) {
            String supplyLineNumber = message.split(LineManagerConstant.SPLIT_SYMBOL)[1];
            GlobalVariable.pendingSupplyLines.remove(supplyLineNumber);
            log.info("remove one pending supplyLine:{}", supplyLineNumber);
            SupplyLineDTO supplyLineDTO = GlobalVariable.supplyLineContainer.get(supplyLineNumber);
            if (supplyLineDTO.getCaps() != null) {
                int capsQueueLength = Math.min(supplyLineDTO.getConcurrentLimit() * CommonConstants.capsMultiplier, CommonConstants.capsMultiplierLimit);
                GlobalVariable.supplyLinesCapsMap.put(supplyLineNumber, capsQueueLength);
                log.info("add one supply caps line for remove pending:[number:{}, count:{}]", supplyLineNumber, capsQueueLength);
            }
        } else if (message.startsWith(LineManagerConstant.ADD_PENDING_TENANT_SUPPLY_LINE)) {
            String tenantSupplyLineNumber = message.split(LineManagerConstant.SPLIT_SYMBOL)[1];
            GlobalVariable.pendingTenantSupplyLines.add(tenantSupplyLineNumber);
            log.info("add one pending tenantSupplyLine:{}", tenantSupplyLineNumber);
        } else if (message.startsWith(LineManagerConstant.DELETE_PENDING_TENANT_SUPPLY_LINE)) {
            String tenantSupplyLineNumber = message.split(LineManagerConstant.SPLIT_SYMBOL)[1];
            GlobalVariable.pendingTenantSupplyLines.remove(tenantSupplyLineNumber);
            log.info("remove one pending tenantSupplyLine:{}", tenantSupplyLineNumber);
        } else if (message.startsWith(LineManagerConstant.ADD_PRIORITY_TENANT_SUPPLY_LINE)) {
            String tenantSupplyLineNumber = message.split(LineManagerConstant.SPLIT_SYMBOL)[1];
            GlobalVariable.priorityTenantSupplyLineNumbers.add(tenantSupplyLineNumber);
            log.info("add one priority tenantSupplyLine:{}", tenantSupplyLineNumber);
        } else if (message.startsWith(LineManagerConstant.DELETE_PRIORITY_TENANT_SUPPLY_LINE)) {
            String tenantSupplyLineNumber = message.split(LineManagerConstant.SPLIT_SYMBOL)[1];
            GlobalVariable.priorityTenantSupplyLineNumbers.remove(tenantSupplyLineNumber);
            log.info("remove one priority tenantSupplyLine:{}", tenantSupplyLineNumber);
        } else if (message.startsWith(LineManagerConstant.BATCH_PENDING_SUPPLY_LINES)) {
            String addSetString = message.split(LineManagerConstant.SPLIT_SYMBOL)[1];
            String deleteSetString = message.split(LineManagerConstant.SPLIT_SYMBOL)[2];
            Set<String> addSet = JSONObject.parseObject(addSetString, new TypeReference<Set<String>>() {});
            Set<String> deleteSet = JSONObject.parseObject(deleteSetString, new TypeReference<Set<String>>() {});
            GlobalVariable.pendingSupplyLines.addAll(addSet);
            GlobalVariable.pendingSupplyLines.removeAll(deleteSet);
            log.info("change batch supply lines pending status");
        } else if (message.startsWith(LineManagerConstant.BATCH_PENDING_TENANT_SUPPLY_LINES)) {
            String addSetString = message.split(LineManagerConstant.SPLIT_SYMBOL)[1];
            String deleteSetString = message.split(LineManagerConstant.SPLIT_SYMBOL)[2];
            Set<String> addSet = JSONObject.parseObject(addSetString, new TypeReference<Set<String>>() {});
            Set<String> deleteSet = JSONObject.parseObject(deleteSetString, new TypeReference<Set<String>>() {});
            GlobalVariable.pendingTenantSupplyLines.addAll(addSet);
            GlobalVariable.pendingTenantSupplyLines.removeAll(deleteSet);
            log.info("change batch tenant supply lines pending status");
        } else if (message.startsWith(LineManagerConstant.BATCH_ADD_PENDING_PRIORITY_TENANT_SUPPLY_LINES)) {
            String addSetString = message.split(LineManagerConstant.SPLIT_SYMBOL)[1];
            Set<String> addSet = JSONObject.parseObject(addSetString, new TypeReference<Set<String>>() {
            });
            GlobalVariable.priorityTenantSupplyLineNumbers.addAll(addSet);
            log.info("add batch priority tenant supply lines :{}", addSet);
        } else if (message.startsWith(LineManagerConstant.BATCH_DELETE_PENDING_PRIORITY_TENANT_SUPPLY_LINES)) {
            String deleteSetString = message.split(LineManagerConstant.SPLIT_SYMBOL)[1];
            Set<String> deleteSet = JSONObject.parseObject(deleteSetString, new TypeReference<Set<String>>() {
            });
            GlobalVariable.priorityTenantSupplyLineNumbers.removeAll(deleteSet);
            log.info("remove batch priority tenant supply lines:{}", deleteSet);
        } else {
            log.error("Exception=>manager收到了不能识别的ai-speech信息");
            DDMsgUtil.sedDDMsg(DingDingMsgType.ERROR, "manager收到了不能识别的ai-speech信息");
        }
    }
}
