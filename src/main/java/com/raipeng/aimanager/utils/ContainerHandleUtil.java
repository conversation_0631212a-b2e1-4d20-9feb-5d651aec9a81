package com.raipeng.aimanager.utils;

import com.raipeng.aimanager.constant.CacheVariable;
import com.raipeng.aimanager.constant.CommonConstants;
import com.raipeng.aimanager.constant.GlobalVariable;
import com.raipeng.common.entity.linemanager.LineGatewayDTO;
import com.raipeng.common.entity.linemanager.PriorityLineConfig;
import com.raipeng.common.entity.linemanager.SupplyLineDTO;
import com.raipeng.common.entity.linemanager.TenantLineDTO;

import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

@Slf4j
public class ContainerHandleUtil {
    public static void cleanAllContainers() {
        log.info("start reset all ai manager containers");
        GlobalVariable.supplyLineContainer.clear();
        GlobalVariable.tenantLineContainer.clear();
        GlobalVariable.supplyLinesMap.clear();
        GlobalVariable.tenantLinesMap.clear();
        GlobalVariable.lineGatewayMap.clear();
        GlobalVariable.tenantSupplyLinesMap.clear();
        GlobalVariable.callLineUnitsInUsing.clear();
        GlobalVariable.usingCountMapOfSupplyLine.clear();
        GlobalVariable.usingCountMapOfTenantLine.clear();
        GlobalVariable.usingCountMapOfLineGateway.clear();
        GlobalVariable.usingCountMapOfTenantSupplyLine.clear();
        GlobalVariable.pendingSupplyLines.clear();
        GlobalVariable.pendingTenantSupplyLines.clear();
        GlobalVariable.priorityTenantSupplyLineNumbers.clear();
        GlobalVariable.capsQueue.clear();
        GlobalVariable.usingCountMapOfTask.clear();
        GlobalVariable.supplyLinesCapsMap.clear();
        GlobalVariable.tenantLinesLockConcurrentMap.clear();
        CacheVariable.tenantSupplyGetFormRedisNumberMap.clear();
        CacheVariable.pushPhoneNumberByTimeMap.clear();
        CacheVariable.pushStopPhoneNumberByTimeMap.clear();
        CacheVariable.pushCapsPhoneNumberByTimeMap.clear();
        CacheVariable.pushStopCapsPhoneNumberByTimeMap.clear();
        CacheVariable.executeTimeList.clear();
    }

    public static void resetAllContainers(List<SupplyLineDTO> supplyLines,
                                          List<TenantLineDTO> tenantLines,
                                          List<String> pendingSupplyLineNumbers,
                                          List<String> pendingTenantSupplyLineNumbers,
                                          List<String> priorityTenantSupplyLineNumbers,
                                          Map<String, Integer> taskTenantLinesLockConcurrentMap ) {
        for (SupplyLineDTO supplyLine : supplyLines) {
            log.info("reset supplyLine in containers:{}", supplyLine);
            GlobalVariable.supplyLineContainer.put(supplyLine.getLineNumber(), supplyLine);
        }
        for (TenantLineDTO tenantLine : tenantLines) {
            log.info("reset tenantLine in containers:{}", tenantLine);
            GlobalVariable.tenantLineContainer.put(tenantLine.getLineNumber(), tenantLine);
        }
        GlobalVariable.pendingSupplyLines.addAll(pendingSupplyLineNumbers);
        GlobalVariable.pendingTenantSupplyLines.addAll(pendingTenantSupplyLineNumbers);
        GlobalVariable.priorityTenantSupplyLineNumbers.addAll(priorityTenantSupplyLineNumbers);
        GlobalVariable.tenantLinesLockConcurrentMap.putAll(taskTenantLinesLockConcurrentMap);
    }

    public static void resetCount(List<SupplyLineDTO> supplyLines, List<TenantLineDTO> tenantLines,
                                  List<LineGatewayDTO> lineGateways, Map<String, Integer> tenantSupplyLineLimit) {
        for (SupplyLineDTO supplyLine : supplyLines) {
            String supplyLineLineNumber = supplyLine.getLineNumber();
            Integer concurrentLimit = supplyLine.getConcurrentLimit();
            GlobalVariable.supplyLinesMap.put(supplyLineLineNumber, concurrentLimit);
            if (supplyLine.getCaps() != null) {
                int capsQueueLength = Math.min(concurrentLimit * CommonConstants.capsMultiplier, CommonConstants.capsMultiplierLimit);
                GlobalVariable.supplyLinesCapsMap.put(supplyLineLineNumber, capsQueueLength);
                log.info("reset supplyLine caps in count:{}=>{}", supplyLineLineNumber, capsQueueLength);
            }
            log.info("reset supplyLine in count:{}=>{}", supplyLineLineNumber, concurrentLimit);
        }
        for (TenantLineDTO tenantLine : tenantLines) {
            String tenantLineLineNumber = tenantLine.getLineNumber();
            Integer concurrentLimit = tenantLine.getConcurrentLimit();
            GlobalVariable.tenantLinesMap.put(tenantLineLineNumber, concurrentLimit);
            log.info("reset tenantLine in count:{}=>{}", tenantLineLineNumber, concurrentLimit);
        }
        for (LineGatewayDTO lineGateway : lineGateways) {
            String gatewayNumber = lineGateway.getGatewayNumber();
            Integer concurrentLimit = lineGateway.getConcurrentLimit();
            GlobalVariable.lineGatewayMap.put(gatewayNumber, concurrentLimit);
            log.info("reset lineGateway in count:{}=>{}", gatewayNumber, concurrentLimit);
        }
        for (Map.Entry<String, Integer> entry : tenantSupplyLineLimit.entrySet()) {
            String tenantSupplyLineNumber = entry.getKey();
            Integer concurrentLimit = entry.getValue();
            GlobalVariable.tenantSupplyLinesMap.put(tenantSupplyLineNumber, concurrentLimit);
            log.info("reset tenantSupplyLine limit in count:{}=>{}", tenantSupplyLineNumber, concurrentLimit);
        }
    }

    public static void resetPriorityLockContainers(PriorityLineConfig priorityLock) {
        GlobalVariable.priorityLockMap.put("priorityLock", priorityLock);
    }
}
