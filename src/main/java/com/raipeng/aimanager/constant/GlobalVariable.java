package com.raipeng.aimanager.constant;

import com.google.common.collect.Maps;
import com.raipeng.aimanager.entity.SendCallPhoneMaterial;
import com.raipeng.common.entity.linemanager.CallLineUnit;
import com.raipeng.common.entity.linemanager.PriorityLineConfig;
import com.raipeng.common.entity.linemanager.SupplyLineDTO;
import com.raipeng.common.entity.linemanager.TenantLineDTO;

import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;

public class GlobalVariable {
    public final static Map<String, SupplyLineDTO> supplyLineContainer = Maps.newConcurrentMap();

    public final static Map<String, TenantLineDTO> tenantLineContainer = Maps.newConcurrentMap();

    public final static Map<String, Integer> tenantLinesMap = Maps.newConcurrentMap();

    public final static Map<String, Integer> supplyLinesMap = Maps.newConcurrentMap();

    public final static Map<String, Integer> lineGatewayMap = Maps.newConcurrentMap();

    public final static Map<String, Integer> tenantSupplyLinesMap = Maps.newConcurrentMap();

    public final static Map<String, Integer> supplyLinesCapsMap = Maps.newConcurrentMap();

    public final static Map<String, Integer> usingCountMapOfSupplyLine = Maps.newConcurrentMap();

    public final static Map<String, Integer> usingCountMapOfTenantLine = Maps.newConcurrentMap();

    public final static Map<String, Integer> deductUsingCountMapOfPriorityTenantLine = Maps.newConcurrentMap();

    public final static Map<String, Integer> usingCountMapOfLineGateway = Maps.newConcurrentMap();

    public final static Map<String, Integer> usingCountMapOfTenantSupplyLine = Maps.newConcurrentMap();

    public final static Map<String, Integer> deductUsingCountMapOfTenantSupplyLine = Maps.newConcurrentMap();

    public final static Map<String, Integer> usingCountMapOfTask = Maps.newConcurrentMap();

    public final static Set<CallLineUnit> callLineUnitsInUsing = Collections.synchronizedSet(new HashSet<>());

    public final static Set<String> pendingSupplyLines = Collections.synchronizedSet(new HashSet<>());

    public final static Set<String> pendingTenantSupplyLines = Collections.synchronizedSet(new HashSet<>());

    public final static Set<String> priorityTenantSupplyLineNumbers = Collections.synchronizedSet(new HashSet<>());

    public final static Map<String, ConcurrentLinkedQueue<SendCallPhoneMaterial>> capsQueue = Maps.newConcurrentMap();

    public final static Map<String, Boolean> capsDetectMap = Maps.newConcurrentMap();

    public final static Map<String, PriorityLineConfig> priorityLockMap = Maps.newConcurrentMap();

    public final static Map<String, Integer> tenantLinesLockConcurrentMap = Maps.newConcurrentMap();

}
