package com.raipeng.aimanager.constant;

import com.google.common.collect.Maps;

import java.util.*;

public class CacheVariable {
    // 租户的供应线路 从redis中获取的量级
    public final static Map<String, Integer> tenantSupplyGetFormRedisNumberMap = Maps.newConcurrentMap();
    // 推送到下游外呼的量级
    public final static Map<String, Integer> pushLinePhoneNumberMap = Maps.newConcurrentMap();
    // 推送到下游总量级 按任务
    public final static Map<String, Integer> pushTaskPhoneNumberMap = Maps.newConcurrentMap();
    // 送回的量级 按线路
    public final static Map<String, Integer> pushLineStopPhoneNumberMap = Maps.newConcurrentMap();
    // 推送到下游的量级 按时间
    public final static Map<String, Integer> pushPhoneNumberByTimeMap = Maps.newConcurrentMap();
    // 送回量级 按时间
    public final static Map<String, Integer> pushStopPhoneNumberByTimeMap = Maps.newConcurrentMap();
    // 推送到caps缓存队列中的数
    public final static Map<String, Integer> pushCapsPhoneNumberByTimeMap = Maps.newConcurrentMap();
    // 从caps缓存队列中因为暂停任务重新送回pull的数据 包含了老逻辑的数据
    public final static Map<String, Integer> pushStopCapsPhoneNumberByTimeMap = Maps.newConcurrentMap();

    public final static List<String> executeTimeList = Collections.synchronizedList(new ArrayList<>());

}
