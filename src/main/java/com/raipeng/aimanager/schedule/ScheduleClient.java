package com.raipeng.aimanager.schedule;

import com.raipeng.aimanager.config.HotConfig;
import com.raipeng.aimanager.constant.CacheVariable;
import com.raipeng.aimanager.constant.GlobalVariable;
import com.raipeng.aimanager.entity.SendCallPhoneMaterial;
import com.raipeng.aimanager.enums.DingDingMsgType;
import com.raipeng.aimanager.fsclient.constant.GlobalContainer;
import com.raipeng.aimanager.fsclient.handle.RabbitMQHandler;
import com.raipeng.aimanager.service.CallLineService;
import com.raipeng.aimanager.service.CallPhoneUnitService;
import com.raipeng.aimanager.service.ManagerFsInterService;
import com.raipeng.aimanager.utils.DDMsgUtil;
import com.raipeng.common.entity.linemanager.CallLineUnit;
import com.raipeng.common.entity.linemanager.PriorityLineConfig;
import com.raipeng.common.entity.prioritycall.CallPhoneUnit;
import com.raipeng.common.util.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ScheduledFuture;

@Slf4j
@Service
public class ScheduleClient {
    @Value("${detect.interval.time}")
    private long detectIntervalTime;

    @Autowired
    private CallLineService callLineService;

    @Autowired
    private ManagerFsInterService managerFsInterService;

    @Autowired
    private RabbitMQHandler rabbitMQHandler;
    @Autowired
    private CallPhoneUnitService callPhoneUnitService;

    @Autowired
    private HotConfig hotConfig;

    @Scheduled(cron = "*/30 * 8-22 * * *")
    public void detectUsingLines() {
        log.info("|======================================================================================>");
        log.info("|using allSize container: {}", GlobalVariable.callLineUnitsInUsing.size());
        log.info("|memory caps condition:{} ", GlobalVariable.supplyLinesCapsMap);
        log.info("|using capsQ   condition: {}", managerFsInterService.getCapsQueueMapData());
        log.info("|using task map:{}", GlobalContainer.capsTaskMapOfManager);
        log.info("|using schedule queue :{}", GlobalVariable.capsQueue.keySet());
        log.info("|using schedule priorityTenantSupplyLine queue :{}", GlobalVariable.priorityTenantSupplyLineNumbers);
        if (hotConfig.isNeedPrintLineLog()) {
            log.info("|using supply  condition: {}", GlobalVariable.usingCountMapOfSupplyLine);
            log.info("|using tenant  condition: {}", GlobalVariable.usingCountMapOfTenantLine);
            log.info("|using gateway condition: {}", GlobalVariable.usingCountMapOfLineGateway);
            log.info("|using tenantSupply line condition: {}", GlobalVariable.usingCountMapOfTenantSupplyLine);
            log.info("|memory supply  condition: {}", GlobalVariable.supplyLinesMap);
            log.info("|memory tenant  condition: {}", GlobalVariable.tenantLinesMap);
            log.info("|memory gateway condition: {}", GlobalVariable.lineGatewayMap);
            log.info("|limit tenantSupply line condition:{}", GlobalVariable.tenantSupplyLinesMap);
            log.info("|using pending condition: {}", GlobalVariable.pendingSupplyLines);
            log.info("|using pending tenantSupply line condition:{}", GlobalVariable.pendingTenantSupplyLines);
            log.info("|using schedule queue size:{}", GlobalContainer.capsExecutor.getQueue().size());
        }
        log.info("<======================================================================================|");

        log.info("高优先量级|========================================================================>");
//            log.info("|供应线路 从redis中获取的量级:{}", CacheVariable.tenantSupplyGetFormRedisNumberMap);
//            log.info("|供应线路 推送到下游外呼的量级:{}", CacheVariable.pushLinePhoneNumberMap);
//            log.info("|按任务 推送到下游总量级:{}", CacheVariable.pushTaskPhoneNumberMap);
//            log.info("|按线路 送回的量级:{}", CacheVariable.pushLineStopPhoneNumberMap);
//            log.info("|按时间 推送到下游的量级:{}", CacheVariable.pushPhoneNumberByTimeMap);
//            log.info("|按时间 送回量级:{}", CacheVariable.pushStopPhoneNumberByTimeMap);
        log.info("|从redis取数总量:{}", CacheVariable.tenantSupplyGetFormRedisNumberMap.values().stream()
                .mapToInt(Integer::intValue)
                .sum());
        log.info("|推送下游量级:{}", CacheVariable.pushPhoneNumberByTimeMap.values().stream()
                .mapToInt(Integer::intValue)
                .sum());
        log.info("|推送回量级:{}", CacheVariable.pushStopPhoneNumberByTimeMap.values().stream()
                .mapToInt(Integer::intValue)
                .sum());
        log.info("|推送到caps内存的缓存队列量级:{}", CacheVariable.pushCapsPhoneNumberByTimeMap.values().stream()
                .mapToInt(Integer::intValue)
                .sum());
        log.info("|推送到caps 再次推回量级:{}", CacheVariable.pushStopCapsPhoneNumberByTimeMap.values().stream()
                .mapToInt(Integer::intValue)
                .sum());
        log.info("<======================================================================================|WWWWWWWWWW");
        checkCapsTask();
    }

    @Scheduled(cron = "*/30 * 8-23 * * *")
    public void capsStopQueueSendMQ() {
        List<CallPhoneUnit> stopTaskPhoneUnits = new ArrayList<>();
        int size = GlobalContainer.capsStopQueue.size();
        log.info("cap暂停的缓存有{} 个",size);
        if (size > 0) {
            for (int i = 0; i < size; i++) {
                CallPhoneUnit callPhoneUnit = GlobalContainer.capsStopQueue.poll();
                stopTaskPhoneUnits.add(callPhoneUnit);
            }
        }

        if (CollectionUtil.isNotEmpty(stopTaskPhoneUnits)){
            log.info("cap暂停的缓存取出有{} 个",stopTaskPhoneUnits.size());
            // 存到队列里
            callPhoneUnitService.sendStopTaskPhonesMQ(stopTaskPhoneUnits);
            String localTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            CacheVariable.pushStopCapsPhoneNumberByTimeMap.compute(localTime, (k, v) -> v == null ? stopTaskPhoneUnits.size() : v + stopTaskPhoneUnits.size());
        }

    }

  @Scheduled(cron = "0 */5 * * * *")
    public void lineHealthCheck() {
        CopyOnWriteArrayList<CallLineUnit> callLineUnitsInUsing = new CopyOnWriteArrayList<>(GlobalVariable.callLineUnitsInUsing);
        log.info("start line health check");
        List<CallLineUnit> callLineUnits = new ArrayList<>();
        for (CallLineUnit callLineUnit : callLineUnitsInUsing) {
            if (System.currentTimeMillis() - callLineUnit.getCreateTime() > detectIntervalTime * 60 * 1000) {
                callLineUnits.add(callLineUnit);
            }
        }
        for (CallLineUnit callLineUnit : callLineUnits) {
            callLineService.returnOneLine(callLineUnit);
        }
    }

    private void checkCapsTask() {
        GlobalVariable.supplyLinesCapsMap.forEach((supplyLineNumber, count) -> {
            ConcurrentLinkedQueue<SendCallPhoneMaterial> queue = GlobalVariable.capsQueue.get(supplyLineNumber);
            ScheduledFuture<?> scheduledFuture = GlobalContainer.capsTaskMapOfManager.get(supplyLineNumber);
            if (queue != null && !queue.isEmpty()) {
                Boolean isRunning = GlobalVariable.capsDetectMap.get(supplyLineNumber);
                if (isRunning != null && !isRunning) {
                    log.error("[Exception]=>caps线路{}:消费出现问题，请检查", supplyLineNumber);
                    DDMsgUtil.sedDDMsg(DingDingMsgType.ERROR, "caps线路:" + supplyLineNumber + "消费出现问题, 请及时处理");
                }
            }
            if ((queue == null && scheduledFuture != null) || (queue != null && scheduledFuture == null)) {
                log.error("[Exception]=>caps线路{}:消费可能出现问题, 如果此告警消失,则无需关注", supplyLineNumber);
                DDMsgUtil.sedDDMsg(DingDingMsgType.ERROR, "caps线路:" + supplyLineNumber + "消费可能出现问题, 如果此告警后续消失,则无需关注");
            }
            GlobalVariable.capsDetectMap.put(supplyLineNumber, false);
        });
    }


    @Scheduled(cron = "*/30 * 8-22 * * *")
    public void priorityLog() {
        PriorityLineConfig priorityLock = GlobalVariable.priorityLockMap.get("priorityLock");
        if (priorityLock == null || priorityLock.getPriorityLock()) {
        } else {
            if (CollectionUtil.isNotEmpty(CacheVariable.executeTimeList)) {
                String executeTime = CacheVariable.executeTimeList.get(CacheVariable.executeTimeList.size() - 1);
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                LocalDateTime dateTime = LocalDateTime.parse(executeTime, formatter);
                log.info("优先级线路上次消费时间为 {}", executeTime);
                if (LocalDateTime.now().isAfter(dateTime.plusMinutes(2))) {
                    log.info("Exception 优先级线路停止消费 上次消费时间段为{}", dateTime);
                }
            }

        }
    }
}
