package com.raipeng.aimanager.service;

import com.raipeng.aimanager.constant.CommonConstants;
import com.raipeng.aimanager.constant.GlobalVariable;
import com.raipeng.common.util.CombineLineUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
@Service
public class CallLineCountService {
    public boolean obtainOneLine(Map<String, Integer> linesMap, String lineNumber) {
        AtomicBoolean flag = new AtomicBoolean(false);
        linesMap.compute(lineNumber, (k, v) -> {
            if (v == null) {
                v = 0;
            }
            if (v > 0) {
                flag.set(true);
                return v - 1;
            } else {
                return v;
            }
        });
        return flag.get();
    }

    public void backOneLine(Map<String, Integer> lineMap, String lineNumber) {
        lineMap.compute(lineNumber, (k, v) -> {
            if (v == null) {
                v = 0;
            }
            v = v + 1;
            return v;
        });
    }

    public void backOneLineForDeduct(Map<String, Integer> lineMap, String lineNumber) {
        lineMap.compute(lineNumber, (k, v) -> {
            if (v == null) {
                v = 0;
            }
            v = v - 1;
            return v;
        });
    }

    public boolean checkTenantSupplyLineEnable(String lineNumber) {
        AtomicBoolean flag = new AtomicBoolean(false);
        GlobalVariable.tenantSupplyLinesMap.compute(lineNumber, (k, v) -> {
            if (v == null) {
                flag.set(true);
            } else {
                GlobalVariable.usingCountMapOfTenantSupplyLine.compute(lineNumber, (k1, v1) -> {
                    Integer proCount = GlobalVariable.deductUsingCountMapOfTenantSupplyLine.getOrDefault(lineNumber, 0);
                    int a = null == v1 ? 0 : v1;
                    a += proCount;

                    if (a < v) {
                        flag.set(true);
                    }
                    return v1;
                });
            }
            return v;
        });
        return flag.get();
    }

    public boolean checkTenantLineEnable(String lineNumber) {
        AtomicBoolean flag = new AtomicBoolean(false);
        GlobalVariable.tenantLinesLockConcurrentMap.compute(lineNumber, (k, v) -> {
            if (v == null) {
                flag.set(true);
            } else {
                GlobalVariable.usingCountMapOfTenantLine.compute(lineNumber, (k1, v1) -> {
                    Integer proCount = GlobalVariable.deductUsingCountMapOfPriorityTenantLine.getOrDefault(lineNumber, 0);
                    int a = null == v1 ? 0 : v1;
                    a += proCount;
                    if (a < v) {
                        flag.set(true);
                    }
                    return v1;
                });
            }
            return v;
        });
        return flag.get();
    }

    public boolean checkCapsTenantLineEnable(String tenantLineNumber, String supplyLineNumber) {
        AtomicBoolean flag = new AtomicBoolean(false);
        GlobalVariable.tenantLinesLockConcurrentMap.compute(tenantLineNumber, (k, v) -> {
            if (v == null) {
                flag.set(true);
            } else {
                GlobalVariable.capsQueue.compute(supplyLineNumber, (k1, v1) -> {
                    if (CollectionUtils.isEmpty(v1)) {
                        flag.set(true);
                    } else {
                        if (v1.size() < v) {
                            flag.set(true);
                        }
                    }
                    return v1;
                });
            }
            return v;
        });
        return flag.get();
    }

    public void addOneSupplyLineInCount(String supplyLineNumber, int lineCount) {
        GlobalVariable.supplyLinesMap.put(supplyLineNumber, lineCount);
        log.info("add one supply line in count[number:{}, count:{}]", supplyLineNumber, lineCount);
        if (GlobalVariable.supplyLineContainer.get(supplyLineNumber).getCaps() != null) {
            int capsQueueLength = Math.min(lineCount * CommonConstants.capsMultiplier, CommonConstants.capsMultiplierLimit);
            GlobalVariable.supplyLinesCapsMap.put(supplyLineNumber, capsQueueLength);
            log.info("add one supply line in count[number:{}, count:{}]", supplyLineNumber, capsQueueLength);
        }
    }

    public void addOneTenantLineInCount(String tenantLineNumber, int lineCount) {
        GlobalVariable.tenantLinesMap.put(tenantLineNumber, lineCount);
        log.info("add one tenant line in count:[number:{}, count:{}]", tenantLineNumber, lineCount);
    }

    public void addOneLineGatewayInCount(String lineGatewayNumber, int lineCount) {
        GlobalVariable.lineGatewayMap.put(lineGatewayNumber, lineCount);
        log.info("add one line gateway in count:[number:{}, count:{}]", lineGatewayNumber, lineCount);
    }

    public void addOneTenantLineSupplyLineCount(String tenantLineNumber, String supplyLineNumber, int lineCount) {
        String tenantSupplyLineNumber = CombineLineUtil.getTenantSupplyLineNumber(tenantLineNumber, supplyLineNumber);
        GlobalVariable.tenantSupplyLinesMap.put(tenantSupplyLineNumber, lineCount);
        log.info("add one tenantSupply line in count:[number:{}, count:{}]", tenantSupplyLineNumber, lineCount);
    }

    public void updateOneSupplyLineInCount(String supplyLineNumber, int lineCount) {
        Integer supplyLineCount = GlobalVariable.supplyLinesMap.computeIfPresent(supplyLineNumber, (k, v) -> v + lineCount);
        log.info("update one supply line in count:[number:{}, changed count:{}]", supplyLineNumber, lineCount);
        if (GlobalVariable.supplyLineContainer.get(supplyLineNumber).getCaps() != null && null != supplyLineCount) {
            // 限制最大长度
            Integer supplyLineUsingCount = GlobalVariable.usingCountMapOfSupplyLine.get(supplyLineNumber);
            int usingCount = supplyLineUsingCount == null ? 0 : supplyLineUsingCount;
            Integer capsQueueLength = GlobalVariable.supplyLinesCapsMap.computeIfPresent(supplyLineNumber,
                    (k, v) -> Math.min((supplyLineCount + usingCount) * CommonConstants.capsMultiplier, CommonConstants.capsMultiplierLimit));
            log.info("update one supply caps line in count:[number:{}, changed count:{}] after limit supply caps length count:{}",
                    supplyLineNumber, lineCount, capsQueueLength);
        }
    }

    public void changeSupplyLineConcurrentBatch(Map<String, Integer> updateMap) {
        updateMap.forEach((supplyLineNumber, lineCount) -> {
            Integer supplyLineCount = GlobalVariable.supplyLinesMap.computeIfPresent(supplyLineNumber, (k, v) -> v + lineCount);
            if (GlobalVariable.supplyLineContainer.get(supplyLineNumber).getCaps() != null && null != supplyLineCount) {
                Integer supplyLineUsingCount = GlobalVariable.usingCountMapOfSupplyLine.get(supplyLineNumber);
                int usingCount = supplyLineUsingCount == null ? 0 : supplyLineUsingCount;
                Integer capsQueueLength = GlobalVariable.supplyLinesCapsMap.computeIfPresent(supplyLineNumber,
                        (k, v) -> Math.min((supplyLineCount + usingCount) * CommonConstants.capsMultiplier, CommonConstants.capsMultiplierLimit));
                log.info("number{} 修改后cap限制长度为{}", supplyLineNumber, capsQueueLength);
            }
        });
        log.info("change batch supply line in count");
    }

    public void updateOneTenantLineInCount(String tenantLineNumber, int lineCount) {
        GlobalVariable.tenantLinesMap.computeIfPresent(tenantLineNumber, (k, v) -> v + lineCount);
        log.info("update one tenant line in count:[number:{}, changed count:{}]", tenantLineNumber, lineCount);
    }

    public void updateOneLineGatewayInCount(String lineGatewayNumber, int lineCount) {
        GlobalVariable.lineGatewayMap.computeIfPresent(lineGatewayNumber, (k, v) -> v + lineCount);
        log.info("update one line gateway in count:[number:{}, changed count:{}]", lineGatewayNumber, lineCount);
    }

    public void updateOneTenantLineSupplyLineCount(String tenantLineNumber, String supplyLineNumber, int lineCount) {
        String tenantSupplyLineNumber = CombineLineUtil.getTenantSupplyLineNumber(tenantLineNumber, supplyLineNumber);
        GlobalVariable.tenantSupplyLinesMap.computeIfPresent(tenantSupplyLineNumber, (k, v) -> v + lineCount);
        log.info("update one tenantSupply line in count:[number:{}, changed count:{}]", tenantSupplyLineNumber, lineCount);
    }

    public void deleteOneTenantLineSupplyLineCount(String tenantLineNumber, String supplyLineNumber) {
        String tenantSupplyLineNumber = CombineLineUtil.getTenantSupplyLineNumber(tenantLineNumber, supplyLineNumber);
        GlobalVariable.tenantSupplyLinesMap.remove(tenantSupplyLineNumber);
        log.info("delete one tenantSupply line in count:[number:{}]", tenantSupplyLineNumber);
    }

    public void changeTenantSupplyLineLimitBatch(Map<String, Integer> addMap, Set<String> deleteSet, Map<String, Integer> updateMap) {
        GlobalVariable.tenantSupplyLinesMap.putAll(addMap);
        deleteSet.forEach(GlobalVariable.tenantSupplyLinesMap::remove);
        updateMap.forEach((k, v) -> {
            GlobalVariable.tenantSupplyLinesMap.computeIfPresent(k, (kk, vv) -> vv + v);
        });
        log.info("change batch tenant supply line in count");
    }
}
