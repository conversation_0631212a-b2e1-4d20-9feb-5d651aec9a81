package com.raipeng.aimanager.service;


import com.alibaba.fastjson.JSONObject;
import com.raipeng.aicall.mq.producter.material.CallPhoneProducerMaterial;
import com.raipeng.aimanager.constant.CacheVariable;
import com.raipeng.aimanager.constant.GlobalVariable;
import com.raipeng.aimanager.entity.SendCallPhoneMaterial;
import com.raipeng.aimanager.fsclient.handle.RabbitMQHandler;
import com.raipeng.aimanager.utils.ThreadManagerUtil;
import com.raipeng.common.entity.linemanager.CallLineUnit;
import com.raipeng.common.entity.linemanager.PriorityLineConfig;
import com.raipeng.common.entity.linemanager.SupplyLineDTO;
import com.raipeng.common.entity.prioritycall.CallPhoneUnit;
import com.raipeng.common.util.CombineLineUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RQueue;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.Pipeline;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.Future;

import static com.raipeng.aimanager.constant.CacheVariable.pushCapsPhoneNumberByTimeMap;

@Slf4j
@Service
public class CallPhoneUnitService {
    //    private static final String REDIS_QUEUE_TO_CALL_PHONE = "推送到呼叫模块的队列";
    private static final String REDIS_QUEUE_TO_CALL_PHONE = "wait_send_queue";

    @Autowired
    private JedisPool jedisPool;

    @Autowired
    private RedissonClient redissonPullClient;

    @Autowired
    private RabbitMQHandler rabbitMQHandler;

    @Autowired
    private ManagerFsInterService managerFsInterService;

    @Autowired
    private CallLineService callLineService;

    @Autowired
    private CallLineCountService callLineCountService;
    @Autowired
    private TaskDataService taskDataService;

    /**
     * 最后要用定时任务执行(单独线程加while循环),间隔时间可能是1S,或者根据对方的要求10ms~1000ms中间
     */
    public void requestForPhones() {
        //增加开关 关闭后休眠并返回直到下个循环
        PriorityLineConfig priorityLock = GlobalVariable.priorityLockMap.get("priorityLock");
        if (priorityLock == null || priorityLock.getPriorityLock()) {
            try {
                log.info("优先队列锁已开启,等待");
                Thread.sleep(60000);
                return;
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }

        List<CallPhoneUnit> callPhoneUnits = new ArrayList<>();
        // 判断任务状态 停止时把数据放入这个列表调用接口返回给 pull
        List<CallPhoneUnit> stopTaskPhoneUnits = new ArrayList<>();
        GlobalVariable.priorityTenantSupplyLineNumbers.forEach(lineNumber -> {
            List<CallLineUnit> callLineUnits = pullCallLineUnitByRemainPriorityCountByLineNumber(lineNumber);
            int size = callLineUnits.size();
            RQueue<CallPhoneUnit> xx = redissonPullClient.getQueue(lineNumber);
            List<CallPhoneUnit> poll = xx.poll(size);
            int pollSize = poll.size();
            CacheVariable.tenantSupplyGetFormRedisNumberMap.compute(lineNumber, (k, v) -> v == null ? pollSize : v + pollSize);

            for (int i = 0; i < pollSize; i++) {
                CallPhoneUnit phoneUnit = poll.get(i);
                CallLineUnit callLineUnit = callLineUnits.get(i);
                boolean isExecuteStatus = taskDataService.taskStatusIsExecute(phoneUnit.getTaskId(), phoneUnit.getOperator(), phoneUnit.getCityCode(), phoneUnit.getProvinceCode());
                if (isExecuteStatus) {
                    callLineUnit.setPhone(phoneUnit.getPhone());
                    callLineUnit.setTaskId(phoneUnit.getTaskId());
                    phoneUnit.setCallLineUnit(callLineUnit);
                    callPhoneUnits.add(phoneUnit);
                } else {
                    stopTaskPhoneUnits.add(phoneUnit);
                    callLineService.returnOneLineFormUnitPhone(callLineUnit);
                }
            }
            for (int i = pollSize; i < size; i++) {
                CallLineUnit callLineUnit = callLineUnits.get(i);
                callLineService.returnOneLineFormUnitPhone(callLineUnit);
            }
        });
        String localTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        if (!callPhoneUnits.isEmpty()) {
            callPhonesJedis(callPhoneUnits);
            CacheVariable.pushPhoneNumberByTimeMap.compute(localTime, (k, v) -> v == null ? callPhoneUnits.size() : v + callPhoneUnits.size());
        }
        if (!stopTaskPhoneUnits.isEmpty()) {
            sendStopTaskPhonesMQ(stopTaskPhoneUnits);
            CacheVariable.pushStopPhoneNumberByTimeMap.compute(localTime, (k, v) -> v == null ? stopTaskPhoneUnits.size() : v + stopTaskPhoneUnits.size());
        }
        GlobalVariable.deductUsingCountMapOfPriorityTenantLine.clear();
        GlobalVariable.deductUsingCountMapOfTenantSupplyLine.clear();
    }


    public void requestForPhonesForThread() {
        //增加开关 关闭后休眠并返回直到下个循环
        PriorityLineConfig priorityLock = GlobalVariable.priorityLockMap.get("priorityLock");
        if (priorityLock == null || priorityLock.getPriorityLock()) {
            try {
                log.info("优先队列锁已开启,等待");
                Thread.sleep(60000);
                return;
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        List<CallPhoneUnit> callPhoneUnits = Collections.synchronizedList(new ArrayList<>());
        // 判断任务状态 停止时把数据放入这个列表调用接口返回给 pull
        List<CallPhoneUnit> stopTaskPhoneUnits = Collections.synchronizedList(new ArrayList<>());
        Map<String, Set<String>> map = new HashMap<>();
        Map<String, Set<String>> capMap = new HashMap<>();

        for (String priorityTenantSupplyLineNumber : GlobalVariable.priorityTenantSupplyLineNumbers) {
            String[] s = priorityTenantSupplyLineNumber.split("_");
            String tenantLineNumber = s[0];
            String supplyLineNum = s[1];
            boolean b = GlobalVariable.supplyLinesCapsMap.containsKey(supplyLineNum);
            if (b) {
                Set<String> set = capMap.getOrDefault(tenantLineNumber, new HashSet<>());
                set.add(priorityTenantSupplyLineNumber);
                capMap.put(tenantLineNumber, set);
            } else {
                Set<String> set = map.getOrDefault(tenantLineNumber, new HashSet<>());
                set.add(priorityTenantSupplyLineNumber);
                map.put(tenantLineNumber, set);
            }
        }

        //        log.info("多线程消费{}", map);
        log.info("多线程消费 省略分组详情");
        List<Future<?>> futures = new ArrayList<>();
        map.forEach((k, v) -> {
            futures.add(ThreadManagerUtil.pushPool.submit(() -> {
                detailPhoneUnite(v, callPhoneUnits, stopTaskPhoneUnits);
            }));
        });
        log.info("多线程消费处理cap 省略分组详情");
        List<CallPhoneUnit> callPhoneUnitCaps = Collections.synchronizedList(new ArrayList<>());
        capMap.forEach((k, v) -> {
            futures.add(ThreadManagerUtil.pushPool.submit(() -> {
                detailPhoneUniteCaps(v, callPhoneUnitCaps, stopTaskPhoneUnits);
            }));
        });
        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (Exception e) {
                log.error("Exception:多线程处理高优先消费队列失败", e);
                e.printStackTrace();
                throw new RuntimeException(e);
            }
        }

        String localTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        if (!callPhoneUnits.isEmpty()) {
            callPhonesJedis(callPhoneUnits);
            CacheVariable.pushPhoneNumberByTimeMap.compute(localTime, (k, v) -> v == null ? callPhoneUnits.size() : v + callPhoneUnits.size());
        }
        if (!stopTaskPhoneUnits.isEmpty()) {
            sendStopTaskPhonesMQ(stopTaskPhoneUnits);
            CacheVariable.pushStopPhoneNumberByTimeMap.compute(localTime, (k, v) -> v == null ? stopTaskPhoneUnits.size() : v + stopTaskPhoneUnits.size());
        }
        GlobalVariable.deductUsingCountMapOfPriorityTenantLine.clear();
        GlobalVariable.deductUsingCountMapOfTenantSupplyLine.clear();

    }


    private boolean activeCallLineUnitWithCaps(SupplyLineDTO supplyLineDTO, CallPhoneUnit callPhoneUnit, CallLineUnit callLineUnit) {
//        log.info("查看phoneUnit的数据{}", callPhoneUnit);

        String supplyLineNumber = supplyLineDTO.getLineNumber();
//        fillCallLineUnit(supplyLineDTO, callLineUnit, callPhoneProducerMaterial.getTaskId(), phoneData.getPhone(), phoneData.getHistoryMatchSupplyNumbers(), new ArrayList<>());
        SendCallPhoneMaterial material = new SendCallPhoneMaterial();
//        CallPhoneProducerMaterial.PhoneDataCache phoneDataCache = CallPhoneProducerMaterial.PhoneDataCache.getPhoneDataCache(callPhoneProducerMaterial, phoneData);
        CallPhoneProducerMaterial.PhoneDataCache phoneDataCache = getPhoneDataCache(supplyLineDTO, callPhoneUnit, callLineUnit);
        material.setPhoneDataCache(phoneDataCache);
        //  预先扣减操作中没有对callLineUnit的一些字段进行赋值，caps外呼时填充了
        material.setCallLineUnit(callLineUnit);

//        log.info("查看 material callLine的数据{}", material.getCallLineUnit());

        // 5. material放入队列
        GlobalVariable.capsQueue.computeIfAbsent(supplyLineNumber, k -> {
            ConcurrentLinkedQueue<SendCallPhoneMaterial> queue = new ConcurrentLinkedQueue<>();
            managerFsInterService.startScheduledTaskForOneSupplyLine(supplyLineNumber, 1000 / supplyLineDTO.getCaps(), queue);
            return queue;
        }).offer(material);

        callLineUnit.setStatus(CallLineUnit.CallLineStatus.CAPS_LINE);
        return true;
    }

    private static CallPhoneProducerMaterial.PhoneDataCache getPhoneDataCache(SupplyLineDTO supplyLineDTO, CallPhoneUnit callPhoneUnit, CallLineUnit callLineUnit) {
        String supplyLineNumber = supplyLineDTO.getLineNumber();

        CallPhoneProducerMaterial.PhoneDataCache phoneDataCache = new CallPhoneProducerMaterial.PhoneDataCache();
        phoneDataCache.setPhone(callPhoneUnit.getPhone());
        phoneDataCache.setDate(LocalDate.now());//高优先暂无的数据 默认设置
        phoneDataCache.setSpeechCallId(callPhoneUnit.getSpeechCallId());
        phoneDataCache.setTaskId(callPhoneUnit.getTaskId());
        phoneDataCache.setTaskName(callPhoneUnit.getTaskName());
//        phoneDataCache.setLineId(callPhoneUnit.getLineId());//高优先暂无的数据 后续补充了
        phoneDataCache.setScriptId(callPhoneUnit.getScriptId());
//        phoneDataCache.setLineCode(callPhoneUnit.getLineCode());//高优先暂无的数据 后续补充了
        phoneDataCache.setScriptVersion(callPhoneUnit.getScriptVersion());
        phoneDataCache.setScriptLongId(callPhoneUnit.getScriptLongId());
        phoneDataCache.setPlainPhone(callPhoneUnit.getPlainPhone());
//        phoneDataCache.setLineGroupCode(callPhoneUnit.getLineGroupCode());//高优先暂无的数据
        phoneDataCache.setProvince(callPhoneUnit.getProvince());
        phoneDataCache.setCity(callPhoneUnit.getCity());
        phoneDataCache.setMerchantLineId(callPhoneUnit.getMerchantLineId());
        phoneDataCache.setMerchantLineCode(callPhoneUnit.getMerchantLineCode());
        phoneDataCache.setCityCode(callPhoneUnit.getCityCode());
        phoneDataCache.setOperator(callPhoneUnit.getOperator());
        phoneDataCache.setSendCallErrorRetryTimes(callLineUnit.getSendCallErrorRetryTimes());  //后续补充了
        phoneDataCache.setHistoryMatchSupplyNumbers(callLineUnit.getHistoryMatchSupplyNumbers());
        phoneDataCache.setStartTime(callPhoneUnit.getStartTime());
        phoneDataCache.setEndTime(callPhoneUnit.getEndTime());
        phoneDataCache.setCallingHistory(callPhoneUnit.getCallingHistory());
        phoneDataCache.setDialingHistory(callPhoneUnit.getDialingHistory());
        phoneDataCache.setBlackSupplyLineNumbers(callPhoneUnit.getBlackSupplyLineNumbers());
        phoneDataCache.setLightPhoneIds(callPhoneUnit.getLightPhoneIds());
        phoneDataCache.setProvinceCode(callPhoneUnit.getProvinceCode());
        phoneDataCache.setFirstCallOrReCall(callPhoneUnit.getFirstCallOrReCall());
        phoneDataCache.setTargetTime(callPhoneUnit.getTargetTime());
        phoneDataCache.setGroupId(callPhoneUnit.getGroupId());
        phoneDataCache.setCallTeamHandleType(callPhoneUnit.getCallTeamHandleType());
        phoneDataCache.setExtParams(callPhoneUnit.getExtParams());

        phoneDataCache.setLineCode(supplyLineDTO.getLineName());
        phoneDataCache.setLineId(supplyLineNumber);
        phoneDataCache.setSendCallErrorRetryTimes(phoneDataCache.getSendCallErrorRetryTimes() == null ? 0 : phoneDataCache.getSendCallErrorRetryTimes() + 1);
        phoneDataCache.addHistoryMatchSupplyNumber(supplyLineNumber);
        return phoneDataCache;
    }


    private void detailPhoneUnite(Set<String> tsLineNumber, List<CallPhoneUnit> callPhoneUnits, List<CallPhoneUnit> stopTaskPhoneUnits) {
        //对tsLineNumber打乱
        List<String> list = new ArrayList<>(tsLineNumber); // 转为List
        Collections.shuffle(list); // 随机打乱顺序
        Set<String> shuffledSet = new LinkedHashSet<>(list);
        shuffledSet.forEach(lineNumber -> {
            List<CallLineUnit> callLineUnits = pullCallLineUnitByRemainPriorityCountByLineNumber(lineNumber);
            int size = callLineUnits.size();
            RQueue<CallPhoneUnit> xx = redissonPullClient.getQueue(lineNumber);
            List<CallPhoneUnit> poll = xx.poll(size);
            int pollSize = poll.size();
            CacheVariable.tenantSupplyGetFormRedisNumberMap.compute(lineNumber, (k, v) -> v == null ? pollSize : v + pollSize);

            for (int i = 0; i < pollSize; i++) {
                CallPhoneUnit phoneUnit = poll.get(i);
                CallLineUnit callLineUnit = callLineUnits.get(i);
                boolean isExecuteStatus = taskDataService.taskStatusIsExecute(phoneUnit.getTaskId(), phoneUnit.getOperator(), phoneUnit.getCityCode(), phoneUnit.getProvinceCode());
                if (isExecuteStatus) {
                    callLineUnit.setPhone(phoneUnit.getPhone());
                    callLineUnit.setTaskId(phoneUnit.getTaskId());
                    phoneUnit.setCallLineUnit(callLineUnit);
                    callPhoneUnits.add(phoneUnit);
                } else {
                    stopTaskPhoneUnits.add(phoneUnit);
                    callLineService.returnOneLineFormUnitPhone(callLineUnit);
                }
            }
            for (int i = pollSize; i < size; i++) {
                CallLineUnit callLineUnit = callLineUnits.get(i);
                callLineService.returnOneLineFormUnitPhone(callLineUnit);
            }
        });
    }


    private void detailPhoneUniteCaps(Set<String> tsLineNumber, List<CallPhoneUnit> callPhoneUnits, List<CallPhoneUnit> stopTaskPhoneUnits) {
        //对tsLineNumber打乱
        List<String> list = new ArrayList<>(tsLineNumber); // 转为List
        Collections.shuffle(list); // 随机打乱顺序
        Set<String> shuffledSet = new LinkedHashSet<>(list);
        shuffledSet.forEach(lineNumber -> {
            List<CallLineUnit> callLineUnits = pullCapsCallLineUnitByRemainPriorityCountByLineNumber(lineNumber);
            int size = callLineUnits.size();
            RQueue<CallPhoneUnit> xx = redissonPullClient.getQueue(lineNumber);
            List<CallPhoneUnit> poll = xx.poll(size);
            int pollSize = poll.size();
            CacheVariable.tenantSupplyGetFormRedisNumberMap.compute(lineNumber, (k, v) -> v == null ? pollSize : v + pollSize);

            for (int i = 0; i < pollSize; i++) {
                CallPhoneUnit phoneUnit = poll.get(i);
                CallLineUnit callLineUnit = callLineUnits.get(i);
                boolean isExecuteStatus = taskDataService.taskStatusIsExecute(phoneUnit.getTaskId(), phoneUnit.getOperator(), phoneUnit.getCityCode(), phoneUnit.getProvinceCode());
                if (isExecuteStatus) {
                    callLineUnit.setPhone(phoneUnit.getPhone());
                    callLineUnit.setTaskId(phoneUnit.getTaskId());
                    phoneUnit.setCallLineUnit(callLineUnit);
                    callPhoneUnits.add(phoneUnit);
                    String[] split = lineNumber.split("_");
                    String tenantLineNumber = split[0];
                    String supplyLineNumber = split[1];
                    if (GlobalVariable.supplyLinesCapsMap.containsKey(supplyLineNumber)) {
                        //走caps线路
                        SupplyLineDTO supplyLineDTO = GlobalVariable.supplyLineContainer.get(supplyLineNumber);
                        String localTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                        pushCapsPhoneNumberByTimeMap.compute(localTime, (k, v) -> v == null ? 1 : v + 1);
                        activeCallLineUnitWithCaps(supplyLineDTO, phoneUnit, callLineUnit);
                    }

                } else {
                    stopTaskPhoneUnits.add(phoneUnit);
                    callLineService.returnOneLineFormUnitPhoneCaps(callLineUnit);
                }
            }
            for (int i = pollSize; i < size; i++) {
                CallLineUnit callLineUnit = callLineUnits.get(i);
                callLineService.returnOneLineFormUnitPhoneCaps(callLineUnit);
            }
        });
    }

    public List<CallLineUnit> pullCallLineUnitByRemainPriorityCountByLineNumber(String lineNumber) {
        List<CallLineUnit> priorityUnits = new ArrayList<>();
        Integer v = GlobalVariable.tenantSupplyLinesMap.get(lineNumber);
        if (v != null && v > 0) {
            int i1 = v;
            for (int i = 0; i < i1; i++) {
                String uuid = UUID.randomUUID().toString();
                CallLineUnit callLineUnit = getCallLineUnit(lineNumber, uuid);
                if (callLineUnit == null) {
                    break;
                }
                priorityUnits.add(callLineUnit);
            }
        }
        return priorityUnits;

    }

    public List<CallLineUnit> pullCapsCallLineUnitByRemainPriorityCountByLineNumber(String lineNumber) {
        List<CallLineUnit> priorityUnits = new ArrayList<>();
        Integer v = GlobalVariable.tenantSupplyLinesMap.get(lineNumber);
        if (v != null && v > 0) {
            int i1 = v;
            for (int i = 0; i < i1; i++) {
                String uuid = UUID.randomUUID().toString();
                CallLineUnit callLineUnit = getCapsCallLineUnit(lineNumber, uuid);
                if (callLineUnit == null) {
                    break;
                }
                priorityUnits.add(callLineUnit);
            }
        }
        return priorityUnits;

    }

    public void callPhonesJedis(List<CallPhoneUnit> units) {
        try (Jedis jedis = jedisPool.getResource()) {
            Pipeline pipeline = jedis.pipelined();
            for (CallPhoneUnit unit : units) {
                callLineService.addCallLineUnitInStatistic(unit.getCallLineUnit());
                pipeline.lpush(REDIS_QUEUE_TO_CALL_PHONE, JSONObject.toJSONString(unit));
            }
            pipeline.sync();
        } catch (Exception e) {
            log.info("写一点报错信息");
        }
    }

    public void sendStopTaskPhonesMQ(List<CallPhoneUnit> units) {
        int batchSize = 50;
        int totalSize = units.size();

        for (int i = 0; i < totalSize; i += batchSize) {
            int endIndex = Math.min(i + batchSize, totalSize);
            List<CallPhoneUnit> batch = units.subList(i, endIndex);
            List<CallPhoneUnit> subSendList = new ArrayList<>();
            for (CallPhoneUnit callPhoneUnit : batch) {
                callPhoneUnit.setDialingHistory(null);
                callPhoneUnit.setCallingHistory(null);
                callPhoneUnit.setBlackSupplyLineNumbers(null);
                callPhoneUnit.setLightPhoneIds(null);
                subSendList.add(callPhoneUnit);
            }
            rabbitMQHandler.sendStopTaskPhones(subSendList);
        }
    }

    /**
     * 没有号码的情况下,获取callLineUnit,  phone用临时的uuid填写,获取数据后补上, taskId填写为空
     *
     * @param lineNumber tenantLineNumber_supplyLineNumber
     * @param tempPhone  临时号码
     * @return 呼叫线路单位
     */
    public CallLineUnit getCallLineUnit(String lineNumber, String tempPhone) {
        String[] lineNumbers = CombineLineUtil.parseTenantSupplyLineNumber(lineNumber);
        String tenantLineNumber = lineNumbers[0];
        String supplyLineNumber = lineNumbers[1];
        if (callLineCountService.checkTenantLineEnable(tenantLineNumber)) {
            if (callLineCountService.obtainOneLine(GlobalVariable.tenantLinesMap, tenantLineNumber)) {
                SupplyLineDTO supplyLineDTO = GlobalVariable.supplyLineContainer.get(supplyLineNumber);
                CallLineUnit callLineUnit = new CallLineUnit();
                // 不添加商户线路会报错
                callLineUnit.setTenantLineNumber(tenantLineNumber);
                callLineUnit.setSupplyLineNumber(supplyLineNumber);
                if (callLineService.checkOneSupplyLineCanBeUse(supplyLineDTO, callLineUnit, null, tempPhone, new ArrayList<>(), CallLineUnit.CallLineStatus.SUCCESS, false)) {
                    return callLineUnit;
                }
                callLineCountService.backOneLine(GlobalVariable.tenantLinesMap, tenantLineNumber);
            }
        }
        return null;
    }


    public CallLineUnit getCapsCallLineUnit(String lineNumber, String tempPhone) {
        String[] lineNumbers = CombineLineUtil.parseTenantSupplyLineNumber(lineNumber);
        String tenantLineNumber = lineNumbers[0];
        String supplyLineNumber = lineNumbers[1];
        if (callLineCountService.checkTenantLineEnable(tenantLineNumber)) {
            if (callLineCountService.obtainOneLine(GlobalVariable.tenantLinesMap, tenantLineNumber)) {
                SupplyLineDTO supplyLineDTO = GlobalVariable.supplyLineContainer.get(supplyLineNumber);
                CallLineUnit callLineUnit = new CallLineUnit();
                // 不添加商户线路会报错
                callLineUnit.setTenantLineNumber(tenantLineNumber);
                callLineUnit.setSupplyLineNumber(supplyLineNumber);

                // 如果当前线路锁定并发大于
                if (callLineCountService.checkCapsTenantLineEnable(tenantLineNumber, supplyLineNumber)) {
                    if (checkOneCapsSupplyLineCanBeUse(supplyLineDTO, callLineUnit)) {
                        return callLineUnit;
                    }
                }

                callLineCountService.backOneLine(GlobalVariable.tenantLinesMap, tenantLineNumber);
            }
        }
        return null;
    }

    private boolean checkOneCapsSupplyLineCanBeUse(SupplyLineDTO supplyLineDTO, CallLineUnit callLineUnit) {
        String supplyLineNumber = supplyLineDTO.getLineNumber();
        if (callLineCountService.obtainOneLine(GlobalVariable.supplyLinesCapsMap, supplyLineNumber)) {
            callLineUnit.setStatus(CallLineUnit.CallLineStatus.CAPS_LINE);
            callLineUnit.setSupplyLineNumber(supplyLineNumber);
            // 高优先预先扣减并发
            String tenantLineNumber = callLineUnit.getTenantLineNumber();
            GlobalVariable.deductUsingCountMapOfPriorityTenantLine.compute(tenantLineNumber, (k, v) -> v == null ? 1 : v + 1);
            GlobalVariable.deductUsingCountMapOfTenantSupplyLine.compute(CombineLineUtil.getTenantSupplyLineNumber(tenantLineNumber, supplyLineNumber), (k, v) -> v == null ? 1 : v + 1);
            return true;
        }
        return false;
    }


}
