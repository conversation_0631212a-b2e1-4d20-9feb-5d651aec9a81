package com.raipeng.aimanager.service;

import com.raipeng.aicall.mq.producter.material.CallPhoneProducerMaterial;
import com.raipeng.aicall.mq.producter.material.CallPhoneProducerMaterial.PhoneData;
import com.raipeng.aicall.mq.producter.material.CallPhoneProducerMaterial.PhoneDataCache;
import com.raipeng.aimanager.constant.GlobalVariable;
import com.raipeng.aimanager.entity.*;
import com.raipeng.aimanager.utils.PickSupplyLineUtil;
import com.raipeng.common.entity.linemanager.CallLineUnit;
import com.raipeng.common.entity.linemanager.LineGatewayDTO;
import com.raipeng.common.entity.linemanager.SupplyLineDTO;
import com.raipeng.common.entity.linemanager.TenantLineDTO;
import com.raipeng.common.enums.ServiceProvider;
import com.raipeng.common.util.CombineLineUtil;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;

@Slf4j
@Service
public class CallLineService {
    @Value("${server.external.ip}")
    private String ip;

    @Value("${server.external.port}")
    private String port;

    @Autowired
    private CallLineCountService callLineCountService;

    @Autowired
    private ManagerFsInterService managerFsInterService;

    public boolean returnOneLine(CallLineUnit callLineUnit) {
        return returnOneLineHandler(callLineUnit);
    }

    public void returnOneLineFormUnitPhone(CallLineUnit callLineUnit) {
        String tenantLineNumber = callLineUnit.getTenantLineNumber();
        String supplyLineNumber = callLineUnit.getSupplyLineNumber();
        callLineCountService.backOneLine(GlobalVariable.tenantLinesMap, tenantLineNumber);
        callLineCountService.backOneLine(GlobalVariable.supplyLinesMap, supplyLineNumber);
        callLineUnit.getLineGatewayNumbers().forEach(lineGatewayNumber -> {
            callLineCountService.backOneLine(GlobalVariable.lineGatewayMap, lineGatewayNumber);
        });
        callLineCountService.backOneLineForDeduct(GlobalVariable.deductUsingCountMapOfPriorityTenantLine, tenantLineNumber);
        callLineCountService.backOneLineForDeduct(GlobalVariable.deductUsingCountMapOfTenantSupplyLine, CombineLineUtil.getTenantSupplyLineNumber(tenantLineNumber, supplyLineNumber));
    }
    public void returnOneLineFormUnitPhoneCaps(CallLineUnit callLineUnit) {
        String tenantLineNumber = callLineUnit.getTenantLineNumber();
        String supplyLineNumber = callLineUnit.getSupplyLineNumber();
        callLineCountService.backOneLine(GlobalVariable.tenantLinesMap, tenantLineNumber);
        //应该是不需要删除这个
//        callLineCountService.backOneLine(GlobalVariable.supplyLinesMap, supplyLineNumber);
        callLineCountService.backOneLine(GlobalVariable.supplyLinesCapsMap, supplyLineNumber);
        callLineCountService.backOneLineForDeduct(GlobalVariable.deductUsingCountMapOfPriorityTenantLine, tenantLineNumber);
        callLineCountService.backOneLineForDeduct(GlobalVariable.deductUsingCountMapOfTenantSupplyLine, CombineLineUtil.getTenantSupplyLineNumber(tenantLineNumber, supplyLineNumber));
    }

    public void returnOneLineHandlerFromCaps(CallLineUnit callLineUnit) {
        String tenantLineNumber = callLineUnit.getTenantLineNumber();
        callLineCountService.backOneLine(GlobalVariable.tenantLinesMap, tenantLineNumber);
    }

    public List<CallLineUnit> obtainBundleLines(CallPhoneProducerMaterial callPhoneProducerMaterial) {
        List<CallLineUnit> callLineUnits = new ArrayList<>();
        String tenantLineNumber = callPhoneProducerMaterial.getMerchantLineCode();
        TenantLineDTO tenantLineDTO = GlobalVariable.tenantLineContainer.get(tenantLineNumber);
        List<PhoneData> phoneDataList = callPhoneProducerMaterial.getData();

        // 1. 判断内存中是否存在商户线路
        if (tenantLineDTO == null) {
            for (PhoneData phoneData : phoneDataList) {
                CallLineUnit callLineUnit = new CallLineUnit();
                callLineUnit.setStatus(CallLineUnit.CallLineStatus.NO_TENANT_LINE);
                callLineUnit.setCreateTime(System.currentTimeMillis());
                callLineUnit.setPhone(phoneData.getPhone());
                callLineUnit.setUnitAddress(ip+":"+port);
                log.error("[{}]=>callLine task no tenant line:{}", phoneData.getPhone(), tenantLineNumber);
                callLineUnits.add(callLineUnit);
            }
            return callLineUnits;
        }
//        log.info("获取到的数据{}",callPhoneProducerMaterial);
        // 2. 处理单个电话号码
        for (PhoneData phoneData : phoneDataList) {
            callLineUnits.add(obtainOneLineHandler(callPhoneProducerMaterial, phoneData, tenantLineDTO));
        }
        return callLineUnits;
    }

    private boolean returnOneLineHandler(CallLineUnit callLineUnit) {
        if (GlobalVariable.callLineUnitsInUsing.contains(callLineUnit)) {
            String tenantLineNumber = callLineUnit.getTenantLineNumber();
            String supplyLineNumber = callLineUnit.getSupplyLineNumber();
            callLineCountService.backOneLine(GlobalVariable.tenantLinesMap, tenantLineNumber);
            callLineCountService.backOneLine(GlobalVariable.supplyLinesMap, supplyLineNumber);
            callLineUnit.getLineGatewayNumbers().forEach(lineGatewayNumber -> {
                callLineCountService.backOneLine(GlobalVariable.lineGatewayMap, lineGatewayNumber);
                GlobalVariable.usingCountMapOfLineGateway.computeIfPresent(lineGatewayNumber, (k, v)-> v-1);
            });
            GlobalVariable.usingCountMapOfSupplyLine.computeIfPresent(callLineUnit.getSupplyLineNumber(), (k, v)-> v-1);
            GlobalVariable.usingCountMapOfTenantLine.computeIfPresent(callLineUnit.getTenantLineNumber(), (k, v)-> v-1);
            GlobalVariable.usingCountMapOfTenantSupplyLine.computeIfPresent(
                    CombineLineUtil.getTenantSupplyLineNumber(tenantLineNumber, supplyLineNumber),
                    (k, v)-> v-1);
            String taskId = callLineUnit.getTaskId();
            if (taskId != null) {
                GlobalVariable.usingCountMapOfTask.computeIfPresent(taskId, (k, v)->v-1);
            }
            GlobalVariable.callLineUnitsInUsing.remove(callLineUnit);
            return true;
        } else {
            log.error("[{}]=>line:{} not in using container", callLineUnit.getPhone(), callLineUnit);
            return false;
        }
    }

    private CallLineUnit obtainOneLineHandler(CallPhoneProducerMaterial callPhoneProducerMaterial, PhoneData phoneData, TenantLineDTO tenantLineDTO) {
        CallLineUnit callLineUnit = new CallLineUnit();
        String tenantLineNumber = tenantLineDTO.getLineNumber();
        // 1. 获取可用供应线路
        List<String> supplyLineNumbers = getSupplyNumbersCanBeUsed(
                tenantLineDTO,
                phoneData.getHistoryMatchSupplyNumbers(),
                PickSupplyLineUtil.getType(phoneData.getOperator()),
                phoneData.getCityCode(),
                phoneData.getCallingHistory(),
                phoneData.getDialingHistory(),
                phoneData.getLightPhoneIds(),
                phoneData.getBlackSupplyLineNumbers(),
                callPhoneProducerMaterial.getCallTeamHandleType()
        );

        // 2. 判断是否有可用的供应线路
        if (supplyLineNumbers.isEmpty()) {
            callLineUnit.setSupplyLineNumber(null);
            callLineUnit.setStatus(CallLineUnit.CallLineStatus.NO_CITY);
            callLineUnit.setPhone(phoneData.getPhone());
            callLineUnit.setHistoryMatchSupplyNumbers(phoneData.getHistoryMatchSupplyNumbers());
            callLineUnit.setCreateTime(System.currentTimeMillis());
            callLineUnit.setUnitAddress(ip+":"+port);
            return callLineUnit;
        }
        callLineUnit.setTenantLineNumber(tenantLineNumber);

        if (callLineCountService.checkTenantLineEnable(tenantLineNumber)) {
            if (callLineCountService.obtainOneLine(GlobalVariable.tenantLinesMap, tenantLineNumber)) {
                for (String supplyLineNumber : supplyLineNumbers) {
                    SupplyLineDTO supplyLineDTO = GlobalVariable.supplyLineContainer.get(supplyLineNumber);
                    if (supplyLineDTO.getCaps() == null) {
                        if (checkOneSupplyLineCanBeUse(
                                supplyLineDTO,
                                callLineUnit,
                                callPhoneProducerMaterial.getTaskId(),
                                phoneData.getPhone(),
                                phoneData.getHistoryMatchSupplyNumbers(),
                                CallLineUnit.CallLineStatus.SUCCESS,
                                true)) {
                            return callLineUnit;
                        }
                    } else {
                        if (checkOneCapsSupplyLineCanBeUse(supplyLineDTO, callLineUnit, callPhoneProducerMaterial, phoneData)) {
                            return callLineUnit;
                        }
                    }
                }
                callLineCountService.backOneLine(GlobalVariable.tenantLinesMap, tenantLineNumber);
            }
        }
        return getOneNoLineCallLineUnit(callLineUnit, phoneData);
    }

    public boolean isTenantLineInUsing(String tenantLineNumber) {
        return GlobalVariable.usingCountMapOfTenantLine.getOrDefault(tenantLineNumber, 0) != 0;
    }

    private static List<String> getSupplyNumbersCanBeUsed(TenantLineDTO tenantLineDTO,
                                                          List<String> taskHistoryMatchSupplyNumbers,
                                                          ServiceProvider taskServiceProvider,
                                                          String taskCityCode,
                                                          Map<String, List<LocalDateTime>> taskCallingHistory,
                                                          Map<String, List<LocalDateTime>> taskDialingHistory,
                                                          Set<Long> lightPhoneIds,
                                                          Set<String> blackSupplyLineNumbers,
                                                          String callTeamHandleType) {
        List<String> supplyLineNumbersCanBeUsed = new ArrayList<>();
        Map<ServiceProvider, Map<String, List<String>>> supplyLineGroupMap = tenantLineDTO.getSupplyLineGroupMap();
        Map<String, List<String>> supplyLineGroups = supplyLineGroupMap.containsKey(ServiceProvider.ALL_OPERATOR)?
                supplyLineGroupMap.get(ServiceProvider.ALL_OPERATOR):supplyLineGroupMap.get(taskServiceProvider);
        if (supplyLineGroups != null && !supplyLineGroups.isEmpty()) {
            List<String> supplyLineNumbers = supplyLineGroups.get(taskCityCode);
            if (supplyLineNumbers != null && !supplyLineNumbers.isEmpty()) {
                supplyLineNumbers.forEach(supplyLineNumber -> {
                    if (!PickSupplyLineUtil.isSupplyLineOutOfUsage(
                            tenantLineDTO.getLineNumber(),
                            supplyLineNumber,
                            taskHistoryMatchSupplyNumbers,
                            taskServiceProvider,
                            taskCityCode,
                            taskCallingHistory,
                            taskDialingHistory,
                            lightPhoneIds,
                            blackSupplyLineNumbers,
                            callTeamHandleType
                    )) {
                        supplyLineNumbersCanBeUsed.add(supplyLineNumber);
                    }
                });
            }
        }
        return supplyLineNumbersCanBeUsed;
    }

    public boolean checkOneSupplyLineCanBeUse(SupplyLineDTO supplyLineDTO,
                                              CallLineUnit callLineUnit,
                                              String taskId,
                                              String phone,
                                              List<String> historyMatchSupplyNumbers,
                                              CallLineUnit.CallLineStatus status,
                                              boolean isNeedAddStatistic) {
        String supplyLineNumber = supplyLineDTO.getLineNumber();
        String tenantSupplyLineNumber = CombineLineUtil.getTenantSupplyLineNumber(callLineUnit.getTenantLineNumber(), supplyLineNumber);
        if (callLineCountService.checkTenantSupplyLineEnable(tenantSupplyLineNumber)) {
            if (callLineCountService.obtainOneLine(GlobalVariable.supplyLinesMap, supplyLineNumber)) {
                List<LineGatewayDTO> lineGatewayDTOS = supplyLineDTO.getLineGatewayDTOS();
                if (lineGatewayDTOS != null && !lineGatewayDTOS.isEmpty()) {
                    List<String> lineGatewaysCache = new ArrayList<>();
                    for (LineGatewayDTO lineGatewayDTO : lineGatewayDTOS) {
                        String gatewayNumber = lineGatewayDTO.getGatewayNumber();
                        if (callLineCountService.obtainOneLine(GlobalVariable.lineGatewayMap, gatewayNumber)) {
                            lineGatewaysCache.add(gatewayNumber);
                        } else {
                            break;
                        }
                    }
                    if (lineGatewayDTOS.size() == lineGatewaysCache.size()) {
                        return activeCallLineUnitNow(supplyLineDTO, callLineUnit, taskId, phone, historyMatchSupplyNumbers, lineGatewaysCache, status, isNeedAddStatistic);
                    } else {
                        for (String gatewayNumber : lineGatewaysCache) {
                            callLineCountService.backOneLine(GlobalVariable.lineGatewayMap, gatewayNumber);
                        }
                        callLineCountService.backOneLine(GlobalVariable.supplyLinesMap, supplyLineNumber);
                    }
                } else {
                    return activeCallLineUnitNow(supplyLineDTO, callLineUnit, taskId, phone, historyMatchSupplyNumbers, new ArrayList<>(), status, isNeedAddStatistic);
                }
            }
        }
        return false;
    }

    private boolean checkOneCapsSupplyLineCanBeUse(SupplyLineDTO supplyLineDTO,
                                                   CallLineUnit callLineUnit,
                                                   CallPhoneProducerMaterial material,
                                                   PhoneData phoneData) {
        String supplyLineNumber = supplyLineDTO.getLineNumber();
        if (callLineCountService.obtainOneLine(GlobalVariable.supplyLinesCapsMap, supplyLineNumber)) {
            return activeCallLineUnitWithCaps(supplyLineDTO, callLineUnit, material, phoneData);
        }
        return false;
    }

    private boolean activeCallLineUnitNow(SupplyLineDTO supplyLineDTO,
                                          CallLineUnit callLineUnit,
                                          String taskId,
                                          String phone,
                                          List<String> historyMatchSupplyNumbers,
                                          List<String> lineGatewayNumbers,
                                          CallLineUnit.CallLineStatus status,
                                          boolean isNeedAddStatistic) {
        fillCallLineUnit(supplyLineDTO, callLineUnit, taskId, phone, historyMatchSupplyNumbers, lineGatewayNumbers);
        callLineUnit.setStatus(status);
        if (isNeedAddStatistic){
            addCallLineUnitInStatistic(callLineUnit);
        }else {
            addTenantStatistic(callLineUnit);
        }
        return true;
    }

    private boolean activeCallLineUnitWithCaps(SupplyLineDTO supplyLineDTO,
                                               CallLineUnit callLineUnit,
                                               CallPhoneProducerMaterial callPhoneProducerMaterial,
                                               PhoneData phoneData) {
        String supplyLineNumber = supplyLineDTO.getLineNumber();
        fillCallLineUnit(supplyLineDTO, callLineUnit, callPhoneProducerMaterial.getTaskId(), phoneData.getPhone(), phoneData.getHistoryMatchSupplyNumbers(), new ArrayList<>());
        SendCallPhoneMaterial material = new SendCallPhoneMaterial();
        PhoneDataCache phoneDataCache = PhoneDataCache.getPhoneDataCache(callPhoneProducerMaterial, phoneData);
        phoneDataCache.setLineCode(supplyLineDTO.getLineName());
        phoneDataCache.setLineId(supplyLineNumber);
        phoneDataCache.setSendCallErrorRetryTimes(phoneDataCache.getSendCallErrorRetryTimes()==null?0:phoneDataCache.getSendCallErrorRetryTimes()+1);
        phoneDataCache.addHistoryMatchSupplyNumber(supplyLineNumber);
        material.setPhoneDataCache(phoneDataCache);
        material.setCallLineUnit(callLineUnit);

        // 5. material放入队列
        GlobalVariable.capsQueue.computeIfAbsent(supplyLineNumber, k -> {
            ConcurrentLinkedQueue<SendCallPhoneMaterial> queue = new ConcurrentLinkedQueue<>();
            managerFsInterService.startScheduledTaskForOneSupplyLine(supplyLineNumber, 1000 / supplyLineDTO.getCaps(), queue);
            return queue;
        }).offer(material);

        callLineUnit.setStatus(CallLineUnit.CallLineStatus.CAPS_LINE);
        return true;
    }

    private CallLineUnit getOneNoLineCallLineUnit(CallLineUnit callLineUnit, PhoneData phoneData) {
        callLineUnit.setSupplyLineNumber(null);
        callLineUnit.setStatus(CallLineUnit.CallLineStatus.NO_LINES);
        callLineUnit.setCreateTime(System.currentTimeMillis());
        callLineUnit.setPhone(phoneData.getPhone());
        callLineUnit.setHistoryMatchSupplyNumbers(phoneData.getHistoryMatchSupplyNumbers());
        callLineUnit.setUnitAddress(ip+":"+port);
        return callLineUnit;
    }

    private void fillCallLineUnit(SupplyLineDTO supplyLineDTO,
                                  CallLineUnit callLineUnit,
                                  String taskId,
                                  String phone,
                                  List<String> historyMatchSupplyNumbers,
                                  List<String> lineGatewayNumbers) {
        long currentTimeMillis = System.currentTimeMillis();
        String supplyLineNumber = supplyLineDTO.getLineNumber();
        callLineUnit.setTaskId(taskId);
        callLineUnit.setLineGatewayNumbers(lineGatewayNumbers);
        callLineUnit.setSupplyLineNumber(supplyLineNumber);
        callLineUnit.setSupplyLineName(supplyLineDTO.getLineName());
        callLineUnit.setPrefix(supplyLineDTO.getPrefix());
        callLineUnit.setDisplayCallNumber(supplyLineDTO.getDisplayCallNumber());
        callLineUnit.setMasterCallNumber(supplyLineDTO.getMasterCallNumber());
        callLineUnit.setRegisterIp(supplyLineDTO.getRegisterIp());
        callLineUnit.setRegisterPort(supplyLineDTO.getRegisterPort());
        callLineUnit.setCreateTime(currentTimeMillis);
        callLineUnit.setPhone(phone);
        callLineUnit.setHistoryMatchSupplyNumbers(historyMatchSupplyNumbers);
        callLineUnit.setUnitAddress(ip + ":" + port);
    }

    public void fillCallLineUnitPri(SupplyLineDTO supplyLineDTO,
                                  CallLineUnit callLineUnit,
                                  String taskId,
                                  String phone,
                                  List<String> historyMatchSupplyNumbers,
                                  List<String> lineGatewayNumbers) {
        long currentTimeMillis = System.currentTimeMillis();
        String supplyLineNumber = supplyLineDTO.getLineNumber();
        callLineUnit.setTaskId(taskId);
        callLineUnit.setLineGatewayNumbers(lineGatewayNumbers);
        callLineUnit.setSupplyLineNumber(supplyLineNumber);
        callLineUnit.setSupplyLineName(supplyLineDTO.getLineName());
        callLineUnit.setPrefix(supplyLineDTO.getPrefix());
        callLineUnit.setDisplayCallNumber(supplyLineDTO.getDisplayCallNumber());
        callLineUnit.setMasterCallNumber(supplyLineDTO.getMasterCallNumber());
        callLineUnit.setRegisterIp(supplyLineDTO.getRegisterIp());
        callLineUnit.setRegisterPort(supplyLineDTO.getRegisterPort());
        callLineUnit.setCreateTime(currentTimeMillis);
        callLineUnit.setPhone(phone);
        callLineUnit.setHistoryMatchSupplyNumbers(historyMatchSupplyNumbers);
        callLineUnit.setUnitAddress(ip + ":" + port);
    }

    public void addCallLineUnitInStatistic(CallLineUnit callLineUnit) {
        String tenantLineNumber = callLineUnit.getTenantLineNumber();
        String supplyLineNumber = callLineUnit.getSupplyLineNumber();
        GlobalVariable.usingCountMapOfSupplyLine.compute(supplyLineNumber, (k, v) -> v == null ? 1 : v + 1);
        GlobalVariable.usingCountMapOfTenantLine.compute(tenantLineNumber, (k, v) -> v == null ? 1 : v + 1);
        GlobalVariable.usingCountMapOfTenantSupplyLine.compute(
                CombineLineUtil.getTenantSupplyLineNumber(tenantLineNumber, supplyLineNumber),
                (k, v) -> v == null ? 1 : v + 1);
        callLineUnit.getLineGatewayNumbers().forEach(
                lineGatewayNumber -> GlobalVariable.usingCountMapOfLineGateway.compute(
                        lineGatewayNumber, (k, v) -> v == null ? 1 : v + 1
                ));
        String taskId = callLineUnit.getTaskId();
        if (taskId != null) {
            GlobalVariable.usingCountMapOfTask.compute(taskId, (k, v) -> v == null ? 1 : v + 1);
        }
        GlobalVariable.callLineUnitsInUsing.add(callLineUnit);
    }

    public void addTenantStatistic(CallLineUnit callLineUnit) {
        // 高优先预先扣减并发
        String tenantLineNumber = callLineUnit.getTenantLineNumber();
        String supplyLineNumber = callLineUnit.getSupplyLineNumber();
        GlobalVariable.deductUsingCountMapOfPriorityTenantLine.compute(tenantLineNumber, (k, v) -> v == null ? 1 : v + 1);
        GlobalVariable.deductUsingCountMapOfTenantSupplyLine.compute(
                CombineLineUtil.getTenantSupplyLineNumber(tenantLineNumber, supplyLineNumber),
                (k, v) -> v == null ? 1 : v + 1);

    }
}
