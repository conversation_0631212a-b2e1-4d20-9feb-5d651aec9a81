package com.raipeng.aimanager.fsclient.service;

import com.raipeng.aicall.mq.producter.material.CallPhoneProducerMaterial;
import com.raipeng.aimanager.constant.GlobalVariable;
import com.raipeng.aimanager.entity.SendCallPhoneMaterial;
import com.raipeng.aimanager.enums.DingDingMsgType;
import com.raipeng.aimanager.feign.FsConfigFeign;
import com.raipeng.aimanager.fsclient.client.FsEslClient;
import com.raipeng.aimanager.fsclient.constant.GlobalContainer;
import com.raipeng.aimanager.fsclient.entity.FsClientData;
import com.raipeng.aimanager.fsclient.handle.RabbitMQHandler;
import com.raipeng.aimanager.fsclient.model.CapsFSConfig;
import com.raipeng.aimanager.fsclient.util.FSContainerHandleUtil;
import com.raipeng.aimanager.service.CallLineCountService;
import com.raipeng.aimanager.service.CallLineService;
import com.raipeng.aimanager.service.TaskDataService;
import com.raipeng.aimanager.utils.DDMsgUtil;
import com.raipeng.common.entity.linemanager.CallLineUnit;
import com.raipeng.common.entity.linemanager.SupplyLineDTO;

import com.raipeng.common.entity.prioritycall.CallPhoneUnit;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.locks.ReentrantReadWriteLock;

@Slf4j
@Service
@DependsOn("DDMsgUtil")
public class CallPhoneControlService {
    private final ReentrantReadWriteLock rwLock = new ReentrantReadWriteLock();

    private final ReentrantReadWriteLock.ReadLock readLock = rwLock.readLock();

    private final ReentrantReadWriteLock.WriteLock writeLock = rwLock.writeLock();

    @Autowired
    private CallService callService;

    @Autowired
    private RabbitMQHandler rabbitMQHandler;

    @Autowired
    private CallLineService callLineService;

    @Autowired
    private CallLineCountService callLineCountService;

    @Autowired
    private TaskDataService taskDataService;
    @Autowired
    private FsConfigFeign fsConfigFeign;

    public void receiveCapsQueueOfSupplyLine(ConcurrentLinkedQueue<SendCallPhoneMaterial> queue) {
        SendCallPhoneMaterial material = queue.poll();
        if (material != null) {
            FsClientData fsClientData;
            CallLineUnit callLineUnit = material.getCallLineUnit();
            CallPhoneProducerMaterial.PhoneDataCache phoneDataCache = material.getPhoneDataCache();
            String supplyLineNumber = callLineUnit.getSupplyLineNumber();
            //任务暂停时将数据返回
              boolean isExecuteStatus = taskDataService.taskStatusIsExecute(phoneDataCache.getTaskId(), phoneDataCache.getOperator(),
                    phoneDataCache.getCityCode(), phoneDataCache.getProvinceCode());
            //并将数据占用的商户线路锁定并发
            if (!isExecuteStatus){
                sendCapsStopQueue(phoneDataCache,supplyLineNumber,callLineUnit.getTenantLineNumber());
                return;
            }

            GlobalVariable.capsDetectMap.put(supplyLineNumber, true);
            readLock.lock();
            try {
                fsClientData = FSContainerHandleUtil.getActiveFs();
            } finally {
                readLock.unlock();
            }

            if (fsClientData != null) {
//                CallPhoneProducerMaterial.PhoneDataCache phoneDataCache = material.getPhoneDataCache();
                SupplyLineDTO supplyLineDTO = GlobalVariable.supplyLineContainer.get(supplyLineNumber);
                //   商户线路锁定并发计算
                String tenantLineNumber = callLineUnit.getTenantLineNumber();
                if (callLineCountService.checkTenantLineEnable(tenantLineNumber)) {

                    if (callLineService.checkOneSupplyLineCanBeUse(
                            supplyLineDTO,
                            callLineUnit,
                            callLineUnit.getTaskId(),
                            phoneDataCache.getPhone(),
                            phoneDataCache.getHistoryMatchSupplyNumbers(),
                            CallLineUnit.CallLineStatus.CAPS_LINE,
                            true)) {
                        callLineCountService.backOneLine(GlobalVariable.supplyLinesCapsMap, supplyLineNumber);
                        callService.callPhone(material.getPhoneDataCache(), fsClientData, material.getCallLineUnit());
                        return;
                    }
                }
            }
            queue.offer(material);
        }
    }

    private void sendCapsStopQueue(CallPhoneProducerMaterial.PhoneDataCache phoneDataCache, String supplyLineNumber, String tenantLineNumber) {
        CallPhoneUnit callPhoneUnit = new CallPhoneUnit();
        callPhoneUnit.setPhone(phoneDataCache.getPhone());
        callPhoneUnit.setSpeechCallId(phoneDataCache.getSpeechCallId());
        callPhoneUnit.setTaskId(phoneDataCache.getTaskId());
        callPhoneUnit.setTaskName(phoneDataCache.getTaskName());
        callPhoneUnit.setScriptId(phoneDataCache.getScriptId());
        callPhoneUnit.setScriptVersion(phoneDataCache.getScriptVersion());
        callPhoneUnit.setScriptLongId(phoneDataCache.getScriptLongId());
        callPhoneUnit.setPlainPhone(phoneDataCache.getPlainPhone());
        callPhoneUnit.setProvince(phoneDataCache.getProvince());
        callPhoneUnit.setCity(phoneDataCache.getCity());
        callPhoneUnit.setMerchantLineId(phoneDataCache.getMerchantLineId());
        callPhoneUnit.setMerchantLineCode(phoneDataCache.getMerchantLineCode());
        callPhoneUnit.setCityCode(phoneDataCache.getCityCode());
        callPhoneUnit.setOperator(phoneDataCache.getOperator());

        callPhoneUnit.setStartTime(phoneDataCache.getStartTime());
        callPhoneUnit.setEndTime(phoneDataCache.getEndTime());
        callPhoneUnit.setCallingHistory(phoneDataCache.getCallingHistory());
        callPhoneUnit.setDialingHistory(phoneDataCache.getDialingHistory());
        callPhoneUnit.setBlackSupplyLineNumbers(phoneDataCache.getBlackSupplyLineNumbers());
        callPhoneUnit.setLightPhoneIds(phoneDataCache.getLightPhoneIds());
        callPhoneUnit.setProvinceCode(phoneDataCache.getProvinceCode());
        callPhoneUnit.setFirstCallOrReCall(phoneDataCache.getFirstCallOrReCall());
        callPhoneUnit.setTargetTime(phoneDataCache.getTargetTime());
        callPhoneUnit.setGroupId(phoneDataCache.getGroupId());
        callPhoneUnit.setCallTeamHandleType(phoneDataCache.getCallTeamHandleType());
        callPhoneUnit.setExtParams(phoneDataCache.getExtParams());

        GlobalContainer.capsStopQueue.add(callPhoneUnit);
        callLineCountService.backOneLine(GlobalVariable.supplyLinesCapsMap, supplyLineNumber);
        callLineCountService.backOneLine(GlobalVariable.tenantLinesMap, tenantLineNumber);

    }

    public void returnCapsQueueOfSupplyLine(String supplyLineNumber) {
        ConcurrentLinkedQueue<SendCallPhoneMaterial> materials = GlobalVariable.capsQueue.computeIfAbsent(supplyLineNumber, k -> new ConcurrentLinkedQueue<>());
        rabbitMQHandler.sendLineLackMq(materials);
        materials.forEach(material -> callLineService.returnOneLineHandlerFromCaps(material.getCallLineUnit()));
        materials.clear();
        GlobalVariable.capsQueue.remove(supplyLineNumber);
        GlobalVariable.supplyLinesCapsMap.remove(supplyLineNumber);
        GlobalVariable.capsDetectMap.remove(supplyLineNumber);
    }

    public void returnCapsQueueTotal(List<String> supplyLineNumbers) {
        for (String supplyLineNumber : supplyLineNumbers) {
            returnCapsQueueOfSupplyLine(supplyLineNumber);
        }
    }

    @PostConstruct
    public void connect(){
        writeLock.lock();
        try {
            FSContainerHandleUtil.resetAllFsConnectContainers();
            List<CapsFSConfig> capsFsConfigs = fsConfigFeign.getFsConfig();

            log.info("reconnected 远程获取 fs:{}", capsFsConfigs);
            if (capsFsConfigs == null || capsFsConfigs.size() == 0) {
                log.error("Exception=>ai-manager:未获取到FS配置项");
                DDMsgUtil.sedDDMsg(DingDingMsgType.ERROR, "ai-manager:未获取到FS配置项");
                return;
            }

            int index = 0;
            for (CapsFSConfig capsFsConfig : capsFsConfigs) {
                FsEslClient fsClient = new FsEslClient(1,8);
                //fsClient.closeChannel();
                try {
                    fsClient.connect(capsFsConfig.getIp(), capsFsConfig.getPort(), capsFsConfig.getPwd(), 2);
                }catch (Exception e) {
                    log.error("Exception=>ai-manager:fs没有连接上");
                    e.printStackTrace();
                    DDMsgUtil.sedDDMsg(DingDingMsgType.ERROR, "ai-manager:fs没有连接上=>ip："+ capsFsConfig.getIp()+",+端口："+ capsFsConfig.getPort());
                    return;//退出，修复fs，重启ai-call
                }
                FsClientData fsClientData = new FsClientData(capsFsConfig,fsClient,0,800);
                GlobalContainer.fsClientDataList.add(fsClientData);
                GlobalContainer.fsIpIndexMap.put(fsClientData.getCapsFsConfig().getIp(), index);
                index += 1;
            }
        } finally {
            writeLock.unlock();
        }
    }
}
