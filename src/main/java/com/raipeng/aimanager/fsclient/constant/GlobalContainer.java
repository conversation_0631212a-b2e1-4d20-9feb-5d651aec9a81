package com.raipeng.aimanager.fsclient.constant;

import com.google.common.collect.Maps;
import com.raipeng.aimanager.entity.SendCallPhoneMaterial;
import com.raipeng.aimanager.fsclient.entity.FsClientData;
import com.raipeng.common.entity.prioritycall.CallPhoneUnit;

import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;

public class GlobalContainer {
    public static final List<FsClientData> fsClientDataList = new ArrayList<>();

    public static final Map<String, Integer> fsIpIndexMap = Maps.newConcurrentMap();

    public static final Set<Integer> fsOutOfAbilityIndexSet = Collections.synchronizedSet(new HashSet<>());

    public static final AtomicInteger fsCount = new AtomicInteger(0);

    public final static Map<String, ConcurrentLinkedQueue<SendCallPhoneMaterial>> capsPollQueue = Maps.newConcurrentMap();

    public final static Map<String, ScheduledFuture<?>> capsTaskMapOfManager = Maps.newConcurrentMap();

    public final static ScheduledThreadPoolExecutor capsExecutor = (ScheduledThreadPoolExecutor) Executors.newScheduledThreadPool(30);

    public final static ConcurrentLinkedQueue<CallPhoneUnit> capsStopQueue = new ConcurrentLinkedQueue<>();

}
