package com.raipeng.sms.service.http.builder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.raipeng.sms.controller.param.SmsUploadData;
import com.raipeng.sms.dto.HttpSendRequest;
import com.raipeng.sms.model.HttpChannelApiConfig;
import com.raipeng.sms.model.HttpChannelConfig;
import com.raipeng.sms.model.HttpChannelParamMapping;
import com.raipeng.sms.service.http.signature.SignatureHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Form表单请求构建器
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
public class FormRequestBuilder implements HttpRequestBuilder {

    @Autowired
    private SignatureHandler signatureHandler;

    @Override
    public HttpSendRequest buildRequest(HttpChannelConfig channelConfig,
                                        HttpChannelApiConfig apiConfig,
                                        List<HttpChannelParamMapping> paramMappings,
                                        Object requestData) {
        try {
            // 构建URL
            String url = buildUrl(channelConfig.getBaseUrl(), apiConfig.getApiPath());

            // 构建请求头
            Map<String, String> headers = buildHeaders(channelConfig, paramMappings, requestData);

            // 构建查询参数
            Map<String, Object> queryParams = buildQueryParams(paramMappings, requestData);

            // 构建表单请求体
            String body = buildFormBody(channelConfig, apiConfig, paramMappings, requestData);

            return HttpSendRequest.builder()
                    .url(url)
                    .method(apiConfig.getMethod().name())
                    .headers(headers)
                    .queryParams(queryParams)
                    .body(body)
                    .contentType(apiConfig.getContentType())
                    .timeout(channelConfig.getTimeout())
                    .retryTimes(channelConfig.getRetryTimes())
                    .build();

        } catch (Exception e) {
            log.error("构建Form请求失败, channelId: {}, error: {}", channelConfig.getChannelId(), e.getMessage(), e);
            throw new RuntimeException("构建Form请求失败", e);
        }
    }

    /**
     * 构建URL
     */
    private String buildUrl(String baseUrl, String apiPath) {
        if (baseUrl.endsWith("/") && apiPath.startsWith("/")) {
            return baseUrl + apiPath.substring(1);
        } else if (!baseUrl.endsWith("/") && !apiPath.startsWith("/")) {
            return baseUrl + "/" + apiPath;
        } else {
            return baseUrl + apiPath;
        }
    }

    /**
     * 构建请求头
     */
    private Map<String, String> buildHeaders(HttpChannelConfig channelConfig,
                                             List<HttpChannelParamMapping> paramMappings,
                                             Object requestData) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/x-www-form-urlencoded");
        headers.put("User-Agent", "AI-SMS-HTTP-Client/1.0");

        // 添加认证头
        addAuthHeaders(headers, channelConfig);

        // 添加映射的头参数
        if (paramMappings != null) {
            for (HttpChannelParamMapping mapping : paramMappings) {
                if (mapping.getParamLocation() == HttpChannelParamMapping.ParamLocation.HEADER) {
                    String value = extractValue(mapping, requestData);
                    if (StringUtils.hasText(value)) {
                        headers.put(mapping.getParamName(), value);
                    }
                }
            }
        }

        return headers;
    }

    /**
     * 添加认证头
     */
    private void addAuthHeaders(Map<String, String> headers, HttpChannelConfig channelConfig) {
        if (channelConfig.getAuthType() == HttpChannelConfig.AuthType.BEARER &&
                StringUtils.hasText(channelConfig.getAuthConfig())) {
            try {
                JSONObject authConfig = JSON.parseObject(channelConfig.getAuthConfig());
                String token = authConfig.getString("token");
                if (StringUtils.hasText(token)) {
                    headers.put("Authorization", "Bearer " + token);
                }
            } catch (Exception e) {
                log.warn("解析Bearer认证配置失败: {}", e.getMessage());
            }
        } else if (channelConfig.getAuthType() == HttpChannelConfig.AuthType.BASIC &&
                StringUtils.hasText(channelConfig.getAuthConfig())) {
            try {
                JSONObject authConfig = JSON.parseObject(channelConfig.getAuthConfig());
                String username = authConfig.getString("username");
                String password = authConfig.getString("password");
                if (StringUtils.hasText(username) && StringUtils.hasText(password)) {
                    String credentials = java.util.Base64.getEncoder()
                            .encodeToString((username + ":" + password).getBytes());
                    headers.put("Authorization", "Basic " + credentials);
                }
            } catch (Exception e) {
                log.warn("解析Basic认证配置失败: {}", e.getMessage());
            }
        }
    }

    /**
     * 构建查询参数
     */
    private Map<String, Object> buildQueryParams(List<HttpChannelParamMapping> paramMappings,
                                                 Object requestData) {
        Map<String, Object> queryParams = new HashMap<>();

        if (paramMappings != null) {
            for (HttpChannelParamMapping mapping : paramMappings) {
                if (mapping.getParamLocation() == HttpChannelParamMapping.ParamLocation.QUERY) {
                    String value = extractValue(mapping, requestData);
                    if (StringUtils.hasText(value)) {
                        queryParams.put(mapping.getParamName(), value);
                    }
                }
            }
        }

        return queryParams;
    }

    /**
     * 构建表单请求体
     */
    private String buildFormBody(HttpChannelConfig channelConfig,
                                 HttpChannelApiConfig apiConfig,
                                 List<HttpChannelParamMapping> paramMappings,
                                 Object requestData) {
        Map<String, String> formParams = new HashMap<>();
        // todo作为签名的参数的处理 K
        // 添加映射的参数
        if (paramMappings != null) {
            for (HttpChannelParamMapping mapping : paramMappings) {
                if (mapping.getParamLocation() == HttpChannelParamMapping.ParamLocation.BODY) {
                    String value = extractValue(mapping, requestData);
                    if (StringUtils.hasText(value) || mapping.getRequired()) {
                        formParams.put(mapping.getParamName(), value != null ? value : "");
                    }
                }
            }
        }

        // 添加签名
        if (channelConfig.getSignatureType() != HttpChannelConfig.SignatureType.NONE) {
            String signature = generateFormSignature(channelConfig, formParams, requestData);
            if (StringUtils.hasText(signature)) {
                formParams.put("K", signature); // 使用K作为签名参数名
            }
        }

        return buildFormString(formParams);
    }

    /**
     * 构建表单字符串
     */
    private String buildFormString(Map<String, String> formParams) {
        StringBuilder sb = new StringBuilder();

        for (Map.Entry<String, String> entry : formParams.entrySet()) {
            if (sb.length() > 0) {
                sb.append("&");
            }

            try {
                sb.append(URLEncoder.encode(entry.getKey(), "UTF-8"))
                        .append("=")
                        .append(URLEncoder.encode(entry.getValue() != null ? entry.getValue() : "", "UTF-8"));
            } catch (UnsupportedEncodingException e) {
                log.warn("URL编码失败: key={}, value={}", entry.getKey(), entry.getValue());
                sb.append(entry.getKey()).append("=").append(entry.getValue() != null ? entry.getValue() : "");
            }
        }

        return sb.toString();
    }

    /**
     * 生成Form签名
     */
    private String generateFormSignature(HttpChannelConfig channelConfig, Map<String, String> formParams, Object requestData) {
        try {
            if (channelConfig.getSignatureType() == HttpChannelConfig.SignatureType.CUSTOM_MD5) {
                // 对于自定义MD5签名，使用C参数的值进行签名
                String cValue = formParams.get("C");
                if (StringUtils.hasText(cValue)) {
                    return signatureHandler.generateSignature(channelConfig, cValue);
                }
            } else {
                // 使用标准签名方式
                String signature = signatureHandler.buildSignDataFromParams(
                        new HashMap<>(formParams),
                        getSignSecret(channelConfig),
                        getSignSalt(channelConfig)
                );
                return signature;
            }
        } catch (Exception e) {
            log.error("生成Form签名失败, channelId: {}, error: {}", channelConfig.getChannelId(), e.getMessage(), e);
        }
        return null;
    }

    /**
     * 提取参数值
     */
    private String extractValue(HttpChannelParamMapping mapping, Object requestData) {
        try {
            if (StringUtils.hasText(mapping.getDefaultValue())) {
                return mapping.getDefaultValue();
            }
            // 应用转换表达式
            if (StringUtils.hasText(mapping.getTransformExpression())) {
                return processTransformExpression(mapping.getTransformExpression(), requestData);
            }

            if (StringUtils.hasText(mapping.getSourceField()) && requestData != null) {
                if (requestData instanceof Map) {
                    Map<?, ?> dataMap = (Map<?, ?>) requestData;
                    Object value = dataMap.get(mapping.getSourceField());
                    return value != null ? value.toString() : null;
                } else if (requestData instanceof JSONObject) {
                    JSONObject dataJson = (JSONObject) requestData;
                    return dataJson.getString(mapping.getSourceField());
                }
            }


            return null;

        } catch (Exception e) {
            log.warn("提取参数值失败: paramName={}, sourceField={}, error={}",
                    mapping.getParamName(), mapping.getSourceField(), e.getMessage());
            return mapping.getDefaultValue();
        }
    }

    /**
     * 处理转换表达式，支持变量替换
     */
    private String processTransformExpression(String expression, Object requestData) {
        try {
            if (!StringUtils.hasText(expression)) {
                return expression;
            }

            String result = expression;

            // 处理短信数据列表
            if (requestData instanceof List) {
                List<?> dataList = (List<?>) requestData;
                if (!CollectionUtils.isEmpty(dataList) && dataList.get(0) instanceof SmsUploadData) {
                    SmsUploadData smsData = (SmsUploadData) dataList.get(0);
                    result = replaceVariables(result, smsData);
                }
            } else if (requestData instanceof SmsUploadData) {
                SmsUploadData smsData = (SmsUploadData) requestData;
                result = replaceVariables(result, smsData);
            } else if (requestData instanceof Map) {
                Map<?, ?> dataMap = (Map<?, ?>) requestData;
                result = replaceVariablesFromMap(result, dataMap);
            }

            return result;
        } catch (Exception e) {
            log.warn("处理转换表达式失败, expression: {}, error: {}", expression, e.getMessage());
            return expression;
        }
    }

    /**
     * 替换SmsUploadData中的变量
     */
    private String replaceVariables(String template, SmsUploadData smsData) {
        String result = template;

        if (smsData.getSmsId() != null) {
            result = result.replace("${smsId}", smsData.getSmsId());
        }
        if (smsData.getPhone() != null) {
            result = result.replace("${phone}", smsData.getPhone());
        }
        if (smsData.getContent() != null) {
            result = result.replace("${content}", smsData.getContent());
        }
        if (smsData.getAccount() != null) {
            result = result.replace("${account}", smsData.getAccount());
        }
        if (smsData.getTaskId() != null) {
            result = result.replace("${taskId}", smsData.getTaskId());
        }
        if (smsData.getTaskName() != null) {
            result = result.replace("${taskName}", smsData.getTaskName());
        }
        if (smsData.getChannelId() != null) {
            result = result.replace("${channelId}", smsData.getChannelId());
        }
        if (smsData.getSequenceId() != null) {
            result = result.replace("${sequenceId}", smsData.getSequenceId().toString());
        }

        return result;
    }

    /**
     * 从Map中替换变量
     */
    private String replaceVariablesFromMap(String template, Map<?, ?> dataMap) {
        // 尝试解析为 JSON 并进行智能替换
        if (template.trim().startsWith("{") && template.trim().endsWith("}")) {
            return replaceJsonTemplate(template, dataMap);
        }
        return "";
    }

    /**
     * 替换 JSON 模板中的值
     */
    private String replaceJsonTemplate(String template, Map<?, ?> dataMap) {
        JSONObject templateJson = JSON.parseObject(template);
        JSONObject resultJson = new JSONObject();

        for (Map.Entry<String, Object> entry : templateJson.entrySet()) {
            String jsonKey = entry.getKey();
            String templateValue = entry.getValue().toString();

            // 查找 dataMap 中是否有对应的值
            String actualValue = null;
            for (Map.Entry<?, ?> dataEntry : dataMap.entrySet()) {
                String dataKey = dataEntry.getKey().toString();
                if (dataKey.equals(templateValue)) {
                    actualValue = dataEntry.getValue() != null ? dataEntry.getValue().toString() : "";
                    break;
                }
            }

            // 如果找到对应值则替换，否则保持原值
            resultJson.put(jsonKey, actualValue != null ? actualValue : templateValue);
        }
        return resultJson.toJSONString();
    }

    /**
     * 获取签名密钥
     */
    private String getSignSecret(HttpChannelConfig channelConfig) {
        try {
            if (StringUtils.hasText(channelConfig.getSignatureConfig())) {
                JSONObject config = JSON.parseObject(channelConfig.getSignatureConfig());
                return config.getString("secret");
            }
        } catch (Exception e) {
            log.warn("获取签名密钥失败: {}", e.getMessage());
        }
        return "";
    }

    /**
     * 获取签名盐值
     */
    private String getSignSalt(HttpChannelConfig channelConfig) {
        try {
            if (StringUtils.hasText(channelConfig.getSignatureConfig())) {
                JSONObject config = JSON.parseObject(channelConfig.getSignatureConfig());
                return config.getString("salt");
            }
        } catch (Exception e) {
            log.warn("获取签名盐值失败: {}", e.getMessage());
        }
        return "";
    }

    @Override
    public boolean supports(String contentType) {
        return "application/x-www-form-urlencoded".equalsIgnoreCase(contentType);
    }

    @Override
    public String getBuilderType() {
        return "FORM";
    }
}
