package com.raipeng.sms.service.callbackM;


import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.raipeng.aidatacommon.thirdrequests.enums.CallbackType;
import com.raipeng.common.util.StringUtils;
import com.raipeng.sms.constant.ApplicationConstants;
import com.raipeng.sms.entity.CallbackMTransParam;
import com.raipeng.sms.entity.CallbackMTransWrapper;
import com.raipeng.sms.model.SmsChannel;
import com.raipeng.sms.mq.producer.CallSmsForMProducer;
import com.raipeng.sms.mq.producer.CallbackMMessageProducer;
import com.raipeng.sms.service.AdminService;
import com.raipeng.sms.service.DingDingService;
import com.raipeng.sms.service.RedisStoreService;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
@Service
public class CallbackMSelfSmsMessageHandler {
    @Autowired
    private AdminService adminService;

    @Autowired
    private RedisStoreService redisStoreService;

    @Autowired
    private CallbackMMessageProducer callbackMMessageProducer;

    @Autowired
    private CallSmsForMProducer callSmsForMProducer;

    public void startRedis(SmsChannel smsChannel) {
        String channelId = smsChannel.getChannelId();
        String redisListKey = String.format(ApplicationConstants.getListTopicCallbackMKey(), channelId);
        ExecutorService redisPool = Executors.newSingleThreadExecutor();
        smsChannel.setConnectStatus(new AtomicBoolean(smsChannel.getStatus()));

        redisPool.execute(new Runnable() {
            @Override
            public void run() {
                while (true) {
                    AtomicBoolean connectStatus = smsChannel.getConnectStatus();
                    if (connectStatus.get()) {
                        List<Object> dataList = redisStoreService.rpipeline(redisListKey, 300);
                        List<String> strList = Lists.newArrayList();
                        for (Object data : dataList) {
                            if (data != null) {
                                strList.add(data.toString());
                            }
                        }

                        Map<String, List<CallbackMTransParam>> paramMap = new HashMap<>();
                        List<CallbackMTransParam> params = new ArrayList<>();
                        if (strList.size() > 0) {
                            Map<String, String> map = redisStoreService.getAndSetpipeline(strList);
                            if (map != null && map.size() > 0) {
                                Map<String, CallbackType> callbackTypeMap = adminService.getCallbackTypeMap();
                                for (Map.Entry<String, String> entry : map.entrySet()) {
                                    String value = entry.getValue();
                                    CallbackMTransParam param = parseValue(value);
                                    if (param == null) {
                                        log.error("Exception=>发送8848M标签短信报错, 请停止任务并做短信模板检查");
                                        DingDingService.dingDingSendMsg("发送8848M标签短信报错, 请停止任务并做短信模板检查");
                                        continue;
                                    }
                                    CallbackType callbackType = callbackTypeMap.get(param.getGroupId());
                                    switch (callbackType) {
                                        case PUSH_THIRD:
                                            params.add(param);
                                            break;
                                        case OLD_PUSH:
                                        case PUSH_ANTS:
                                            paramMap.computeIfAbsent(param.getGroupId(), k -> new ArrayList<>()).add(param);
                                            break;
                                        default:
                                            log.error("Exception=>发送M短信混入不可处理的账号");
                                            break;
                                    }
                                }
                            }
                        }
                        if (paramMap.size() > 0) {
                            CallbackMTransWrapper wrapper = new CallbackMTransWrapper();
                            wrapper.setParams(paramMap);
                            callbackMMessageProducer.send(wrapper);
                        }
                        if (!params.isEmpty()) {
                            callSmsForMProducer.send(params);
                        }
                    }
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        });
    }

    private CallbackMTransParam parseValue(String value) {
        if (value == null) {
            log.info("Exception=>发送8848M标签短信报错(value),请检查:null");
            return null;
        }
        String[] valueSplit = value.split(ApplicationConstants.SPLIT_CHART_SPLIT);
        if (valueSplit.length != 2) {
            log.error("Exception=>发送8848M标签短信报错(value),请检查:{}", value);
            return null;
        }

        String jsonString = valueSplit[0];
        CallbackMTransParam param = JSONObject.parseObject(jsonString, CallbackMTransParam.class);
        String taskName = param.getTaskName();
        String plainPhone = param.getMobile();
        String taskId = param.getTaskId();
        String groupId = param.getGroupId();
        if (StringUtils.isEmpty(taskName) || StringUtils.isEmpty(plainPhone) || StringUtils.isEmpty(taskId) || StringUtils.isEmpty(groupId)) {
            log.error("Exception=>:发送8848M标签短信数据缺失{}", value);
            return null;
        }
        param.setPlainPhone(plainPhone);
        return param;
    }
}
