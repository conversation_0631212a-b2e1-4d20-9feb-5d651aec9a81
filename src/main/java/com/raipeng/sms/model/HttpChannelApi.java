package com.raipeng.sms.model;


import com.raipeng.common.model.BaseEntity;
import lombok.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR> http通道api
 */
@Entity
@Table(name = "http_channel_api")
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class HttpChannelApi extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 7834577107577896042L;
    @Column(name = "channel_id", columnDefinition = "varchar(20) DEFAULT NULL COMMENT '通道id'")
    private String channelId;
    @Column(name = "url", columnDefinition = "varchar(200) DEFAULT NULL COMMENT 'http通道链接地址'")
    private String url;

    @Column(name = "request_method", columnDefinition = "varchar(30) DEFAULT NULL COMMENT 'get,post'")
    private String requestMethod;

    @Column(name = "request_type", columnDefinition = "varchar(30) DEFAULT NULL COMMENT 'JSON,FORM表单'")
    private String requestType;

}