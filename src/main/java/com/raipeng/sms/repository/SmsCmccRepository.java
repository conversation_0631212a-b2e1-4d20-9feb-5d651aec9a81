package com.raipeng.sms.repository;


import com.raipeng.sms.model.SmsCmcc;
import com.raipeng.sms.model.SmsCucc;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SmsCmccRepository extends JpaRepository<SmsCmcc,Long>, JpaSpecificationExecutor<SmsCmcc> {

    @Transactional
    @Modifying
    @Query(value = "update sms_cmcc SET submit_status = :submitStatus,submit_channel_time = :submitChannelTime,submit_time = :submitTime WHERE sms_id in (:smsIds)", nativeQuery = true)
    void updateSubmitStatusBySmsId(Integer submitStatus, List<String> smsIds,String submitChannelTime,String submitTime);

    @Transactional
    @Modifying
    @Query(value = "update sms_cmcc SET send_status = :sendStatus,send_time = :sendTime WHERE sms_id in (:smsIds)", nativeQuery = true)
    void updateSendStatusBySmsId(Integer sendStatus, List<String> smsIds,String sendTime);
}
