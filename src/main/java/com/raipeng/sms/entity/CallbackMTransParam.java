package com.raipeng.sms.entity;

import com.raipeng.aidatacommon.model.inter.CallDataForPushThird;
import lombok.Data;

import java.io.Serializable;

@Data
public class CallbackMTransParam implements Serializable, CallDataForPushThird {
    private String mobileSecret;
    private String mobile;
    private String taskId;
    private String taskName;
    private String groupId;
    private String smsId;
    private String plainPhone;

    @Override
    public String getCallOutTime() {
        return "";
    }

    @Override
    public String getCallStatus() {
        return "";
    }

    @Override
    public String getContactTime() {
        return "";
    }

    @Override
    public String getPhone() {
        return "";
    }

    @Override
    public String getId() {
        return "";
    }

    @Override
    public String getIntentionClass() {
        return "";
    }

    @Override
    public Integer getCallDuration() {
        return 0;
    }

    @Override
    public String getTalkTimeEnd() {
        return "";
    }

    @Override
    public String getTalkTimeStart() {
        return "";
    }

    @Override
    public Integer getCycleCount() {
        return 0;
    }

    @Override
    public String getIntentionLabels() {
        return "";
    }

    @Override
    public String getLineId() {
        return "";
    }

    @Override
    public String getMerchantLineCode() {
        return "";
    }

    @Override
    public String getWholeAudioFileUrl() {
        return "";
    }

    @Override
    public String getSipCallId() {
        return "";
    }

    @Override
    public Integer getWhoHangup() {
        return 0;
    }

    @Override
    public String getDialogContents() {
        return "";
    }

    @Override
    public Long getClueFollowUpLogId() {
        return 0L;
    }

    @Override
    public String getManualIntentionClass() {
        return "";
    }

    @Override
    public String getManualIntentionLabels() {
        return "";
    }

    @Override
    public Boolean getIsTransToCallSeat() {
        return null;
    }

    @Override
    public String getStartAnswerTime() {
        return "";
    }

    @Override
    public String getEndAnswerTime() {
        return "";
    }

    @Override
    public Long getCallSeatId() {
        return 0L;
    }

    @Override
    public Long getClueId() {
        return 0L;
    }

    @Override
    public String getAccount() {
        return "";
    }

    @Override
    public String getCallSeatName() {
        return "";
    }

    @Override
    public String getFormRecordDTO() {
        return "";
    }

    @Override
    public String getFollowUpStatus() {
        return "";
    }

    @Override
    public String getFollowUpNote() {
        return "";
    }

    @Override
    public String getBatchId() {
        return "";
    }

    @Override
    public String getRecordId() {
        return "";
    }

    @Override
    public String getTemplateId() {
        return "";
    }

    public String getSupplyLineBelong() {
        return "";
    }

    @Override
    public String getName() {
        return "";
    }

    @Override
    public String getCompany() {
        return "";
    }

    @Override
    public String getRemarks() {
        return "";
    }

    @Override
    public String getIfSendSms() {
        return "";
    }

    @Override
    public String getDialogContentsCache() {
        return "";
    }

    @Override
    public void setDialogContentsCache(String dialogContentsCache) {

    }

    @Override
    public void setIfSendSms(String ifSendSms) {

    }

    @Override
    public void setName(String name) {

    }

    @Override
    public void setCompany(String company) {

    }

    @Override
    public void setRemarks(String remark) {

    }

    @Override
    public void setCallSeatName(String callSeatName) {

    }

    @Override
    public void setFormRecordDTO(String formRecordDTO) {

    }

    @Override
    public void setFollowUpStatus(String followUpStatus) {

    }

    @Override
    public void setFollowUpNote(String followUpNote) {

    }

    @Override
    public void setBatchId(String batchId) {

    }

    public void setTemplateId(String templateId) {

    }

    public void setSupplyLineBelong(String supplyLineBelong) {

    }
}
