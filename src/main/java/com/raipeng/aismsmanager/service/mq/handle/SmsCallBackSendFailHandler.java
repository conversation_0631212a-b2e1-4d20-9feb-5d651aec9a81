package com.raipeng.aismsmanager.service.mq.handle;

import com.raipeng.aismsmanager.enums.SmsHandleType;
import com.raipeng.aismsmanager.service.callresulthandler.impl.BatchCallBackSendFailService;
import com.raipeng.aismsmanager.utils.SmsCallBackHandleUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import static com.raipeng.aismsmanager.config.RabbitConstants.SEND_FAIL_MESSAGE_QUEUE;

@Slf4j
@Component
public class SmsCallBackSendFailHandler {
    @Value("${called.batch.consume.num:100}")
    private Integer calledBatchConsumeNum;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private BatchCallBackSendFailService batchSmsManagerService;

    public void handle() {
        SmsCallBackHandleUtil.handle(SEND_FAIL_MESSAGE_QUEUE,
                SmsHandleType.SMS_SEND_FAIL,
                calledBatchConsumeNum, 1000L, rabbitTemplate, batchSmsManagerService);
    }
}
