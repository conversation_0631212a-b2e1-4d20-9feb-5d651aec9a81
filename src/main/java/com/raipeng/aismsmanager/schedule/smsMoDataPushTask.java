package com.raipeng.aismsmanager.schedule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.raipeng.aidatacommon.model.*;
import com.raipeng.aidatacommon.thirdrequests.IceKreditUtil;
import com.raipeng.aidatacommon.utils.CallbackConfigUtil;
import com.raipeng.aismsmanager.repository.*;
import com.raipeng.aismsmanager.service.BatchService;
import com.raipeng.aismsmanager.service.TenantSecretsService;
import com.raipeng.aismsmanager.utils.RaiYiEncryptionUtil;
import com.raipeng.common.util.AESUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 上行短信数据推送定时任务
 */
@Component
@Slf4j
@RefreshScope
public class smsMoDataPushTask {

    @Autowired
    private AdminRepository adminRepository;

    @Autowired
    private SmsMoRecordRepository smsMoRecordRepository;

    @Autowired
    private TenantSecretsService tenantSecretsService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private BatchService batchService;


    @XxlJob("smsMoDataPushTask")
    public ReturnT<String> execute(String s) {
        try {
            XxlJobLogger.log("XXL-JOB, smsMoDataPushTask run");
            List<Admin> adminList = adminRepository.findBySmsMoCallbackUrlIsNotNull();
            if (adminList.isEmpty()) {
                log.info("未找到配置短信回调地址的管理员，任务结束");
                return ReturnT.SUCCESS;
            }

            // 遍历每个管理员处理上行短信数据推送
            for (Admin admin : adminList) {
                String tenantId = admin.getTenantId().toString();
                TenantSecrets tenantSecrets = tenantSecretsService.findFirstByTenantId(tenantId);
                String account = admin.getAccount();

                if (tenantSecrets == null) {
                    log.error("Exception=>第三方账号:{} AK-CK未配置！上行短信未回调", account);
                    continue;
                }

                List<SmsMoRecord> allMoDataPushList = smsMoRecordRepository.findNonCompensateMoDataPushRecordList(
                        admin.getGroupId(), LocalDate.now().atStartOfDay());
                log.info("上行短信回调数量 {} {}", admin.getAccount(), allMoDataPushList.size());
                smsMoDataPushService(allMoDataPushList, admin, tenantSecrets);
            }
            log.info("上行短信回调结束");
        } catch (Exception e) {
            XxlJobLogger.log("XXL-JOB, smsMoDataPushTask.Error=" + e);
            log.error("上行短信数据推送任务执行异常", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }


    public void smsMoDataPushService(List<SmsMoRecord> allMoDataPushList, Admin admin, TenantSecrets tenantSecrets) {
        if (allMoDataPushList.isEmpty()) {
            return;
        }
        Map<String, String> combinedDecryptMap = new HashMap<>();
        if (admin.getIsForEncryptionPhones() == false) {
            // 批量解密手机号码
            List<String> encryptPhones = allMoDataPushList.stream().map(SmsMoRecord::getPhone).collect(Collectors.toList());
            int batchSize = 3000;
            JSONObject deBatch = new JSONObject();

            for (int i = 0; i < encryptPhones.size(); i += batchSize) {
                int end = Math.min(i + batchSize, encryptPhones.size());
                List<String> batch = encryptPhones.subList(i, end);
                log.info("上行短信开始批量解密手机号, batch:{}", batch.size());
                deBatch = RaiYiEncryptionUtil.getDecryptionResultBatch(String.join(",", batch));
                for (String key : deBatch.keySet()) {
                    String value = deBatch.getString(key);
                    combinedDecryptMap.put(key, value);
                }
            }
            // 再次加密
            if (admin.getIsForEncryptionAgain()) {
                Map<String, String> encryptMap = new HashMap<>();
                combinedDecryptMap.forEach((phone, value) -> {
                    try {
                        encryptMap.put(phone, IceKreditUtil.aesEncrypt(value));
                    } catch (Exception e) {
                        log.info("Exception 冰鉴加密上行短信手机号码异常 ", e);
                    }
                });
                combinedDecryptMap = encryptMap;
            }
        } else {
            combinedDecryptMap = allMoDataPushList.stream().collect(Collectors.toMap(SmsMoRecord::getPhone, SmsMoRecord::getPhone));
        }


        // 构建推送数据
        JSONArray textList = new JSONArray();
        Map<String, Object> map = new HashMap<>();
        List<SmsMoDataPushRecord> moDataPushList = new ArrayList<>();

        for (SmsMoRecord smsMoRecord : allMoDataPushList) {
            JSONObject moCallBackMap = new JSONObject();
            SmsMoDataPushRecord moDataPush = new SmsMoDataPushRecord();

            moCallBackMap.put("recordId", smsMoRecord.getSmsRecordId());
            moCallBackMap.put("phone", combinedDecryptMap.get(smsMoRecord.getPhone()));
            moCallBackMap.put("province", smsMoRecord.getProvinceCode());
            moCallBackMap.put("city", smsMoRecord.getCityCode());
            moCallBackMap.put("operator", smsMoRecord.getOperator());
            moCallBackMap.put("originalContent", smsMoRecord.getOriginalContent());
            moCallBackMap.put("originalTriggerTime", smsMoRecord.getOriginalTriggerTime());
            moCallBackMap.put("userReplyTime", smsMoRecord.getUserReplyTime());
            moCallBackMap.put("systemReceiveTime", smsMoRecord.getSystemReceiveTime());
            moCallBackMap.put("smsMoContent", smsMoRecord.getSmsMoContent());

            List<String> fieldsToPush = CallbackConfigUtil.getFieldsToPush(admin.getCallbackFieldConfig());
            if (fieldsToPush.contains("smsFullName")) {
                moCallBackMap.put("name", smsMoRecord.getName());
            }
            if (fieldsToPush.contains("smsCompany")) {
                moCallBackMap.put("company", smsMoRecord.getCompany());
            }
            if (fieldsToPush.contains("smsRemarks")) {
                moCallBackMap.put("remarks", smsMoRecord.getRemarks());
            }

            textList.add(moCallBackMap);

            moDataPush.setSmsMoId(smsMoRecord.getId());
            moDataPush.setRecordId(smsMoRecord.getSmsRecordId());
            moDataPush.setPhone(smsMoRecord.getPhone());
            moDataPush.setSmsMoContent(smsMoRecord.getSmsMoContent());
            moDataPush.setGroupId(smsMoRecord.getGroupId());
            moDataPushList.add(moDataPush);
        }

        // 数据加密和签名
        map.put("text", AESUtils.AESEncode(tenantSecrets.getAesKey(), textList.toJSONString()));
        map = new TreeMap<>(map);
        String jsonStr = JSON.toJSONString(map, SerializerFeature.SortField);
        jsonStr += tenantSecrets.getSalt();
        String sign = DigestUtils.md5Hex(jsonStr);
        map.put("sign", sign);

        // 推送数据到第三方系统
        Map<String, Object> res = null;
        try {
            long now = System.currentTimeMillis();
            res = restTemplate.postForObject(admin.getSmsMoCallbackUrl(), map, Map.class);
            batchService.batchInsert(moDataPushList);
            log.info("上行短信回调定时任务推送成功{},耗时:{}", admin.getAccount(), System.currentTimeMillis() - now);
        } catch (Exception ex) {
            log.error("Exception 上行短信回调定时任务推送失败  num{} admin{} {}", moDataPushList.size(), admin, res);
            log.error("上行短信回调定时任务推送失败:{}", ex.getMessage());
            ex.printStackTrace();
        }
    }
}