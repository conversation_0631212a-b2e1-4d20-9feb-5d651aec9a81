package com.raipeng.aidatacommon.entity;



import com.raipeng.common.enums.AIOutboundTaskType;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

@Data
public class AiRedisTask implements Serializable {
    private Long id;

    private String groupId;

    private String productId;

    private String industrySecondFieldId;

    private Integer autoReCall;

    private Integer firstRecallTime;

    private Integer secondRecallTime;

    private List<HangUpSmsTriggerPojo> hangUpSms;

    private List<String> hangUpExcluded;

    private String taskName;

    private String programId;

    private Long speechCraftId;

    private String scriptStringId;

    private String speechCraftName;

    private String templateId;

    private AIOutboundTaskType taskType;

    private AtomicInteger calledNumDiff;

    private AtomicInteger finishedNumDiff;

    private AtomicInteger feeMinuteDiff;

    private AtomicInteger callingNumDiff;

    private AtomicInteger recallingNumDiff;

    private AtomicInteger putThroughNumDiff;

    private AtomicInteger callRecordNumDiff;

    private AtomicInteger phoneIntentionNumDiff;

    private AtomicInteger aIntentionNumDiff;
}
