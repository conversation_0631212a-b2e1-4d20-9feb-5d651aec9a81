package com.raipeng.aidatacommon.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskStopTriggerPojo implements Serializable {
    private Integer calledPhoneNumTrigger ;  //拨打数    名单触达数
    private Integer putThroughPhoneNumTrigger ; //接通数  名单接通数
    private Float finishedPhoneRateTrigger ; //完成率
    private Float reachRateTrigger ; //触达率    名单执行进度
    private Integer aClassNumTrigger ; //A类数
    private String stopTimeTrigger ; //外呼到达
    private String stopControlStatus;
}
