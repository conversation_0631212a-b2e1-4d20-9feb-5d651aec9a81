package com.raipeng.aidatacommon.entity;

import lombok.Data;

import java.io.Serializable;

@Data
public class TaskRedisCountEntity implements Serializable {
    private Long id;

    private int calledPhoneNum;

    private int callingPhoneNum;

    private int recallingPhoneNum;

    private int finishedPhoneNum;

    private int feeMinute;

    private int putThroughPhoneNum;

    private int callRecordNum;

    private int phoneIntentionNum;

    private int aIntentionNum;
}
