package com.raipeng.aidatacommon.model;


import com.raipeng.common.model.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Entity
@Table(name = "t_ai_outbound_task_stop_control")
@Data
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class AIOutboundTaskStopControl extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 202503270000L;

    @Column(name = "group_id", nullable = false, columnDefinition = "varchar(20)")
    private String groupId;

    /**
     * 设置类型      ACCOUNT 账号  TASK 任务
     */
    @Column(name = "stop_type", nullable = false, columnDefinition = "varchar(20)")
    private String stopType;

    // ACTIVE/STOPPED
    @Column(name = "stop_control_status", nullable = true, columnDefinition = "varchar(10)")
    private String stopControlStatus;

    /**
     * 任务状态      STOP_ACCOUNT 账号自动暂停  STOP_TASK 任务自动暂停
     */
    @Column(name = "task_status", nullable = true, columnDefinition = "varchar(20)")
    private String taskStatus;

    @Column(name = "task_id", nullable = true, columnDefinition = "bigint")
    private Long taskId;

    @Column(name = "task_name", nullable = true, columnDefinition = "varchar(100)")
    private String taskName;

    //拨打数 名单触达数
    @Column(name = "called_phone_num_trigger", nullable = true, columnDefinition = "int")
    private Integer calledPhoneNumTrigger;

    //接通数
    @Column(name = "put_through_phone_num_trigger", nullable = true, columnDefinition = "int")
    private Integer putThroughPhoneNumTrigger;

    //完成率
    @Column(name = "finished_phone_rate_trigger", nullable = true, columnDefinition = "NUMERIC(10,2)")
    private BigDecimal finishedPhoneRateTrigger;


    //触达率 名单执行进度 calledPhoneNum/phoneNum
    @Column(name = "reach_rate_trigger", nullable = true, columnDefinition = "NUMERIC(10,2)")
    private BigDecimal reachRateTrigger;

    //A类数
    @Column(name = "a_class_num_trigger", nullable = true, columnDefinition = "int")
    private Integer aClassNumTrigger;

    //外呼到达
    @Column(name = "stop_time_trigger", nullable = true, columnDefinition = "varchar(10)")
    private String stopTimeTrigger;

    @Transient
    private List<Long> taskIds;
}
