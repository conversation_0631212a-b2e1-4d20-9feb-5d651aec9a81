package com.raipeng.aidatacommon.model;

import com.raipeng.common.enums.EnableStatus;
import com.raipeng.common.model.BaseEntity;
import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import lombok.*;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

import javax.persistence.*;
import java.util.Map;
import java.util.Set;


@Entity
@Table(name = "call_line_supplier")
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TypeDef(name = "jsonb", typeClass = JsonBinaryType.class)
public class CallLineSupplier extends BaseEntity {
    @Column(name = "supplier_number", nullable = false, unique = true, columnDefinition = "varchar(20)")
    private String supplierNumber;

    @Column(name = "supplier_name", nullable = false, columnDefinition = "varchar(100)")
    private String supplierName;

    @Column(name = "supplier_profile", nullable = true, columnDefinition = "varchar(50)")
    private String supplierProfile;

    @Column(name = "supplier_address", nullable = true, columnDefinition = "varchar(300)")
    private String supplierAddress;

    @Column(name = "contact_name", nullable = true, columnDefinition = "varchar(50)")
    private String contactName;

    @Column(name = "phone_number", nullable = false, columnDefinition = "varchar(20)")
    private String phoneNumber;

    @Column(name = "email", nullable = true, columnDefinition = "varchar(50)")
    private String email;

    @Column(name = "duty", nullable = true, columnDefinition = "varchar(20)")
    private String duty;

    @Column(name = "contact_address", nullable = true, columnDefinition = "varchar(20)")
    private String contactAddress;

    @Enumerated(EnumType.STRING)
    @Column(name = "cooperation_status", nullable = false, columnDefinition = "varchar(20) default 'ENABLE'")
    private EnableStatus cooperationStatus;

    @Column(name = "notes", nullable = true, columnDefinition = "varchar(400)")
    private String notes;

    @Column(name = "business_scope", nullable = false, columnDefinition = "varchar(400)")
    private String businessScope;

    @Type(type = "jsonb")
    @Column(name = "black_group_map", columnDefinition = "jsonb")
    private Map<Long, Set<String>> blackGroupMap;

    @Type(type = "jsonb")
    @Column(name = "light_phone_map", columnDefinition = "jsonb")
    private Map<Long, Set<String>> lightPhoneMap;

    @Type(type = "jsonb")
    @Column(name = "black_group_all_lines_active_map", columnDefinition = "jsonb")
    private Map<Long, Boolean> blackGroupAllLinesActiveMap;

    @Type(type = "jsonb")
    @Column(name = "light_phone_all_lines_active_map", columnDefinition = "jsonb")
    private Map<Long, Boolean> lightPhoneAllLinesActiveMap;

    @Column(name = "is_callback_supply_line", nullable = false, columnDefinition = "bool default false")
    private boolean isCallbackSupplyLine;

    /**
     * XSCS(限时传送), XRZL(仙人指路), DXYS(得心应手), BZZY(白泽自有), KHZY(客户自有), QT(其他)
     */
    @Column(name = "supplier_belong", nullable = true, columnDefinition = "varchar(50)")
    private String supplierBelong;
}
