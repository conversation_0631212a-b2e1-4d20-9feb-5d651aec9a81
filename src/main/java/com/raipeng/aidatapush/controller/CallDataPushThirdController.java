package com.raipeng.aidatapush.controller;


import com.raipeng.aidatacommon.thirdrequests.entity.CallDataEntity;
import com.raipeng.aidatacommon.thirdrequests.enums.PushThirdType;
import com.raipeng.aidatapush.service.HandleCallDataPushThirdForAIManService;
import com.raipeng.aidatapush.service.HandleCallDataPushThirdForHumanService;
import com.raipeng.aidatapush.service.HandleCallDataPushThirdForPureAIService;
import com.raipeng.aidatapush.utils.CallDataHandleUtils;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@RestController
@RequestMapping("/callDataPushThird")
public class CallDataPushThirdController {
    @Autowired
    private HandleCallDataPushThirdForPureAIService handleCallDataPushThirdForPureAIService;

    @Autowired
    private HandleCallDataPushThirdForAIManService handleCallDataPushThirdForAIManService;

    @Autowired
    private HandleCallDataPushThirdForHumanService handleCallDataPushThirdForHumanService;

    @GetMapping("/handleXCallDataForPureAIManual")
    public void handleXCallDataForPureAIManual(@RequestParam(required = false) String groupId) {
        handleCallDataPushThirdForPureAIService.handleXCallDataForPureAIManual(groupId);
        log.info("===>handleXCallDataForPureAIManual");
    }

    @GetMapping("/handleXCallSmsForPureAIManual")
    public void handleXCallSmsForPureAIManual(@RequestParam(required = false) String groupId) {
        handleCallDataPushThirdForPureAIService.handleXCallSmsForPureAIManual(groupId);
        log.info("===>handleXCallSmsForPureAIManual");
    }

    @GetMapping("/handleXCallDataForAIManManual")
    public void handleXCallDataForAIMan(@RequestParam(required = false) String groupId) {
        handleCallDataPushThirdForAIManService.handleXCallDataForAIManManual(groupId);
        log.info("===>handleXCallDataForAIManManual");
    }

    @GetMapping("/handleXCallSmsForAIManManual")
    public void handleXCallSmsForAIMan(@RequestParam(required = false) String groupId) {
        handleCallDataPushThirdForAIManService.handleXCallSmsForAIManManual(groupId);
        log.info("===>handleXCallSmsForAIManManual");
    }

    @GetMapping("/handleXCallDataForHumanManual")
    public void handleXCallDataForHuman(@RequestParam(required = false) String groupId) {
        handleCallDataPushThirdForHumanService.handleXCallDataForHumanManual(groupId);
        log.info("===>handleXCallDataForHumanManual");
    }

    @GetMapping("/monitorXCallDataForPureAI")
    public CallDataEntity monitorXCallDataForPureAI() {
        return handleCallDataPushThirdForPureAIService.monitorXCallDataForPureAI();
    }

    @GetMapping("/monitorXCallSmsForPureAI")
    public CallDataEntity monitorXCallSmsForPureAI() {
        return handleCallDataPushThirdForPureAIService.monitorXCallSmsForPureAI();
    }

    @GetMapping("/monitorXCallSmsForAIMan")
    public CallDataEntity monitorXCallSmsForAIMan() {
        return handleCallDataPushThirdForAIManService.monitorXCallSmsForAIMan();
    }

    @GetMapping("/monitorXCallDataForAIMan")
    public CallDataEntity monitorXCallDataForAIMan() {
        return handleCallDataPushThirdForAIManService.monitorXCallDataForAIMan();
    }

    @GetMapping("/monitorXCallDataForHuman")
    public CallDataEntity monitorXCallDataForHuman() {
        return handleCallDataPushThirdForHumanService.monitorXCallDataForHuman();
    }

    @GetMapping("/checkCallDataCache")
    public List<String> checkCallDataCache() {
        return CallDataHandleUtils.checkCache();
    }

    @GetMapping("/clearWarnCount")
    public void clearWarnCount(@RequestParam PushThirdType type) {
        CallDataHandleUtils.clearWarnCount(type);
    }

    @GetMapping("/decrementWarnCount")
    public Integer decrementWarnCount(@RequestParam PushThirdType type, @RequestParam String groupId) {
        return CallDataHandleUtils.decrementWarnCount(type, groupId);
    }

    @GetMapping("/monitorWarnCount")
    public List<Map<String, AtomicInteger>> monitorWarnCount() {
        return CallDataHandleUtils.monitorWarnCount();
    }
}
