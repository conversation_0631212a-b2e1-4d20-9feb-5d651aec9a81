package com.raipeng.aidatapush.controller.wrapper;

import com.raipeng.aidatacommon.model.dto.CallRecordResult;
import com.raipeng.aidatacommon.model.record.LandingPageCallRecord;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


@Data
public class CallResultHandlerForPureAILandingPageWrapper implements Serializable {
    private List<LandingPageCallRecord> callRecordList;
    private List<CallRecordResult> callRecordResultList;

    public CallResultHandlerForPureAILandingPageWrapper() {
        callRecordList = new ArrayList<>();
        callRecordResultList = new ArrayList<>();
    }
}
