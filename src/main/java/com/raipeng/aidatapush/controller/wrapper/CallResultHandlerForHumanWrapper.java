package com.raipeng.aidatapush.controller.wrapper;

import com.raipeng.aidatacommon.model.CallRecordForManualDirect;
import com.raipeng.aidatacommon.model.dto.CallRecordResult;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class CallResultHandlerForHumanWrapper implements Serializable {
    private boolean ifUncalled;

    private boolean ifCdr;

    private List<CallRecordForManualDirect> pushThirdList;

    private List<CallRecordForManualDirect> callRecordList;

    private List<CallRecordResult> callRecordResultList;

    public CallResultHandlerForHumanWrapper() {
        pushThirdList = new ArrayList<>();
        callRecordList = new ArrayList<>();
        callRecordResultList = new ArrayList<>();
    }
}
