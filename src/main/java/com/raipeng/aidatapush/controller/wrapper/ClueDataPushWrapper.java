package com.raipeng.aidatapush.controller.wrapper;

import com.raipeng.aidatacommon.model.Admin;
import com.raipeng.aidatacommon.model.CallRecordForHumanMachine;

import lombok.Data;

import java.io.Serializable;

@Data
public class ClueDataPushWrapper implements Serializable {
    private Long clueId;
    private String intentionClass;
    private String intentionLabels;
    private CallRecordForHumanMachine record;
    private Admin admin;
    private String isPushDialogContent;
    private Long callSeatId;
    private String callSeatName;
    private String followUpStatus;
    private String followUpNote;
    private String formRecordDTO;
}
