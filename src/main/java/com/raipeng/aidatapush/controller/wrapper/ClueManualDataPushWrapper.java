package com.raipeng.aidatapush.controller.wrapper;

import com.raipeng.aidatacommon.model.Admin;
import com.raipeng.aidatacommon.model.CallRecordForManualDirect;

import lombok.Data;

import java.io.Serializable;

@Data
public class ClueManualDataPushWrapper implements Serializable {
    private Long clueId;
    private String intentionClass;
    private String intentionLabels;
    private CallRecordForManualDirect record;
    private Admin admin;
    private Long callSeatId;
    private String callSeatName;
    private String followUpStatus;
    private String followUpNote;
    private String formRecordDTO;
    private String batchID;
}
