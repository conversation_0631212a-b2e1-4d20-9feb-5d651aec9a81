package com.raipeng.aidatapush.controller.wrapper;

import com.raipeng.aidatacommon.model.CallRecordForHumanMachine;
import com.raipeng.aidatacommon.model.PhoneRecord;
import com.raipeng.aidatacommon.model.dto.CallRecordResult;
import com.raipeng.aidatacommon.entity.AiRedisTask;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class CallResultHandlerForAIManWrapper implements Serializable {
    private boolean ifUncalled;

    private boolean ifCdr;

    private List<CallRecordForHumanMachine> pushThirdList;

    private List<CallRecordForHumanMachine> pushAntList;

    private List<CallRecordForHumanMachine> pushThirdListDelay;

    private List<CallRecordForHumanMachine> callRecordList;

    private List<PhoneRecord> phoneRecordList;

    private List<AiRedisTask> aiRedisTaskList;

    private List<CallRecordResult> callRecordResultList;

    private Map<Long, String> taskGroupIdMap;

    private Map<String, Map<String, Double>> saveRedisKeysMap;

    public CallResultHandlerForAIManWrapper() {
        pushThirdList = new ArrayList<>();
        pushThirdListDelay = new ArrayList<>();
        pushAntList = new ArrayList<>();
        callRecordList = new ArrayList<>();
        phoneRecordList = new ArrayList<>();
        aiRedisTaskList = new ArrayList<>();
        callRecordResultList = new ArrayList<>();
        saveRedisKeysMap = new HashMap<>();
    }
}
