package com.raipeng.aidatapush.controller.wrapper;

import com.raipeng.aidatacommon.model.PhoneRecord;
import com.raipeng.aidatacommon.model.dto.CallRecordResult;
import com.raipeng.aidatacommon.model.record.CallRecord;
import com.raipeng.aidatacommon.entity.AiRedisTask;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class CallResultHandlerForPureAIWrapper implements Serializable {
    private boolean ifUncalled;

    private boolean ifCdr;

    private List<CallRecord> pushThirdList;

    private List<CallRecord> pushThirdListDelay;

    private List<CallRecord> pushAntsList;

    private List<CallRecord> pushAntsListDelay;

    private List<CallRecord> callRecordList;

    private List<PhoneRecord> phoneRecordList;

    private List<AiRedisTask> aiRedisTasks;

    private List<CallRecordResult> callRecordResultList;

    private Map<Long, String> taskGroupIdMap;

    private Map<String, Map<String, Double>> saveRedisKeysMap;

    public CallResultHandlerForPureAIWrapper() {
        pushThirdList = new ArrayList<>();
        pushThirdListDelay = new ArrayList<>();
        pushAntsList = new ArrayList<>();
        pushAntsListDelay = new ArrayList<>();
        callRecordList = new ArrayList<>();
        phoneRecordList = new ArrayList<>();
        aiRedisTasks = new ArrayList<>();
        callRecordResultList = new ArrayList<>();
        saveRedisKeysMap = new HashMap<>();
    }
}
