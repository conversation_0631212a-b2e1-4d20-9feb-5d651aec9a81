package com.raipeng.aidatapush.controller;


import com.raipeng.aidatapush.service.CheckRedisService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/checkRedis")
public class CheckRedisController {
    @Autowired
    private CheckRedisService checkRedisService;

    @GetMapping("/checkAdminRedis")
    public void checkAdminRedis() {
        checkRedisService.checkAdminRedis();
    }

    @GetMapping("/checkTenantSecretsRedis")
    public void checkTenantSecretsRedis() {
        checkRedisService.checkTenantSecretsRedis();
    }
}
