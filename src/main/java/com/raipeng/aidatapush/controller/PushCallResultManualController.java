package com.raipeng.aidatapush.controller;


import com.raipeng.aidatapush.service.callresulthandler.impl.*;
import com.raipeng.aidatapush.service.mq.producer.PushDataToCallQueueManual;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/pushCallResultManual")
public class PushCallResultManualController {
    @Autowired
    private PushDataToCallQueueManual pushDataToCallQueueManual;

    @Autowired
    private CallResultHandleForAIManService callResultHandleForAIManService;

    @Autowired
    private CallResultHandleForHumanService callResultHandleForHumanService;

    @Autowired
    private CallResultHandleForPureAIService callResultHandleForPureAIService;

    @Autowired
    private UnCallResultHandleForAIManService unCallResultHandleForAIManService;

    @Autowired
    private UnCallResultHandleForHumanService unCallResultHandleForHumanService;

    @Autowired
    private UnCallResultHandleForPureAIService unCallResultHandleForPureAIService;

    @Autowired
    private CallResultHandleForLandingPagePureAIService callResultHandleForLandingPagePureAIService;

    @GetMapping("/startChannel")
    public void startChannel() {
        pushDataToCallQueueManual.startChannel();
        log.info("manualPush=>start one channel");
    }

    @GetMapping("/closeChannel")
    public void closeChannel() {
        pushDataToCallQueueManual.closeChannel();
        log.info("manualPush=>start close channel");
    }

    @GetMapping("/sendCallResult")
    public void sendCallResult(@RequestParam String result) {
        pushDataToCallQueueManual.sendCallResult(result);
        log.info("manualPush=>push one call result:{}", result);
    }

    @GetMapping("/sendUnCallResult")
    public void sendUnCallResult(@RequestParam String result) {
        pushDataToCallQueueManual.sendUnCallResult(result);
        log.info("manualPush=>push one unCall result:{}", result);
    }

    @GetMapping("/disPatchExceptCallResultForAIManCall")
    public void disPatchExceptCallResultForAIManCall() {
        callResultHandleForAIManService.disPatchExceptCallResult();
    }

    @GetMapping("/disPatchExceptCallResultForHumanCall")
    public void disPatchExceptCallResultForHumanCall() {
        callResultHandleForHumanService.disPatchExceptCallResult();
    }

    @GetMapping("/disPatchExceptCallResultForPureAICall")
    public void disPatchExceptCallResultForPureAICall() {
        callResultHandleForPureAIService.disPatchExceptCallResult();
    }

    @GetMapping("/disPatchExceptCallResultForAIManUnCall")
    public void disPatchExceptCallResultForAIManUnCall() {
        unCallResultHandleForAIManService.disPatchExceptCallResult();
    }

    @GetMapping("/disPatchExceptCallResultForHumanUnCall")
    public void disPatchExceptCallResultForHumanUnCall() {
        unCallResultHandleForHumanService.disPatchExceptCallResult();
    }

    @GetMapping("/disPatchExceptCallResultForPureAiUnCall")
    public void disPatchExceptCallResultForPureAiUnCall() {
        unCallResultHandleForPureAIService.disPatchExceptCallResult();
    }

    @GetMapping("/disPatchExceptCallResultForLandingPagePureAiCall")
    public void disPatchExceptCallResultForLandingPagePureAiCall() {
        callResultHandleForLandingPagePureAIService.disPatchExceptCallResult();
    }

    @GetMapping("/disPatchExceptCallResultForAIManCallSingle")
    public void disPatchExceptCallResultForAIManCallSingle() {
        callResultHandleForAIManService.disPatchExceptCallResultSingle();
    }

    @GetMapping("/disPatchExceptCallResultForHumanCallSingle")
    public void disPatchExceptCallResultForHumanCallSingle() {
        callResultHandleForHumanService.disPatchExceptCallResultSingle();
    }

    @GetMapping("/disPatchExceptCallResultForPureAICallSingle")
    public void disPatchExceptCallResultForPureAICallSingle() {
        callResultHandleForPureAIService.disPatchExceptCallResultSingle();
    }

    @GetMapping("/disPatchExceptCallResultForAIManUnCallSingle")
    public void disPatchExceptCallResultForAIManUnCallSingle() {
        unCallResultHandleForAIManService.disPatchExceptCallResultSingle();
    }

    @GetMapping("/disPatchExceptCallResultForHumanUnCallSingle")
    public void disPatchExceptCallResultForHumanUnCallSingle() {
        unCallResultHandleForHumanService.disPatchExceptCallResultSingle();
    }

    @GetMapping("/disPatchExceptCallResultForPureAiUnCallSingle")
    public void disPatchExceptCallResultForPureAiUnCallSingle() {
        unCallResultHandleForPureAIService.disPatchExceptCallResultSingle();
    }

    @GetMapping("/disPatchExceptCallResultForLandingPagePureAiCallSingle")
    public void disPatchExceptCallResultForLandingPagePureAiCallSingle() {
        callResultHandleForLandingPagePureAIService.disPatchExceptCallResultSingle();
    }
}
