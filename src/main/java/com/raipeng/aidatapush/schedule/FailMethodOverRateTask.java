package com.raipeng.aidatapush.schedule;


import com.raipeng.aidatapush.service.CallOverRateService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RefreshScope
public class FailMethodOverRateTask {
    @Autowired
    private CallOverRateService callOverRateService;

    @XxlJob("findOverRateDataAndDoRetry")
    public ReturnT<String> findOverRateDataAndDoRetry(String id) {
        XxlJobLogger.log("XXL-JOB, findOverRateDataAndDoRetry.param=" + id);
        log.info("XXL-JOB, failedMethodCallRetryTask.param=" + id);

        try {
            XxlJobLogger.log("findOverRateDataAndDoRetry ----------start");
            log.info("findOverRateDataAndDoRetry ----------start");
            callOverRateService.findOverRateDataAndDoRetry(id);
            XxlJobLogger.log("findOverRateDataAndDoRetry ----------end");
            log.info("findOverRateDataAndDoRetry ----------end");
        } catch (Exception e) {
            XxlJobLogger.log("XXL-JOB, findOverRateDataAndDoRetry.Error=" + e);
            log.error("findOverRateDataAndDoRetry ----------  Error:", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}
