package com.raipeng.aidatapush.schedule;


import com.raipeng.aidatapush.service.HandleCallDataPushThirdForAIManService;
import com.raipeng.aidatapush.service.HandleCallDataPushThirdForHumanService;
import com.raipeng.aidatapush.service.HandleCallDataPushThirdForPureAIService;
import com.raipeng.aidatapush.utils.CallDataHandleUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RefreshScope
public class HandleXConditionTask {
    @Autowired
    private HandleCallDataPushThirdForPureAIService handleCallDataPushThirdForPureAIService;

    @Autowired
    private HandleCallDataPushThirdForAIManService handleCallDataPushThirdForAIManService;

    @Autowired
    private HandleCallDataPushThirdForHumanService handleCallDataPushThirdForHumanService;

    @XxlJob("handleXQueue")
    public ReturnT<String> handleXQueue(String id) {
        XxlJobLogger.log("XXL-JOB, handleXQueue.param=" + id);
        log.info("XXL-JOB, handleXQueue.param=" + id);
        try {
            handleCallDataPushThirdForPureAIService.handleXCallSmsForPureAI();
            handleCallDataPushThirdForPureAIService.handleXCallDataForPureAI();
            handleCallDataPushThirdForAIManService.handleXCallSmsForAIMan();
            handleCallDataPushThirdForAIManService.handleXCallDataForAIMan();
            handleCallDataPushThirdForHumanService.handleXCallDataForHuman();
            log.info("XXL-JOB, handleXQueue.end=" + id);
        } catch (Exception e) {
            XxlJobLogger.log("XXL-JOB, handleXQueue.Error=" + e);
            log.error("handleXQueue ----------  Error:", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }


    @XxlJob("clearCallDataCache")
    public ReturnT<String> clearCallDataCache(String s) {
        XxlJobLogger.log("XXL-JOB, clearCallDataCache.param=" + s);
        log.info("XXL-JOB, clearCallDataCache.param=" + s);
        try {
            CallDataHandleUtils.checkCache();
            CallDataHandleUtils.clearCache();
            handleCallDataPushThirdForPureAIService.checkXQueue();
            handleCallDataPushThirdForAIManService.checkXQueue();
            handleCallDataPushThirdForHumanService.checkXQueue();
            log.info("XXL-JOB, clearCallDataCache.end=" + s);
        } catch (Exception e) {
            XxlJobLogger.log("XXL-JOB, clearCallDataCache.Error=" + e);
            log.error("clearCallDataCache ----------  Error:", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }


}
