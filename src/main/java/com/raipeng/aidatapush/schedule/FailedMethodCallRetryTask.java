package com.raipeng.aidatapush.schedule;

import com.raipeng.aidatapush.service.CallRetryService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RefreshScope
public class FailedMethodCallRetryTask {
    @Autowired
    private CallRetryService callRetryService;


    @XxlJob("failedMethodCallRetryTask")
    public ReturnT<String> execute(String id) {
        XxlJobLogger.log("XXL-JOB, failedMethodCallRetryTask.param=" + id);
        log.info("XXL-JOB, failedMethodCallRetryTask.param=" + id);

        try {
            XxlJobLogger.log("failedMethodCallRetryTask ----------start");
            log.info("failedMethodCallRetryTask ----------start");
            callRetryService.findFailedDataAndDoRetry(id);
            XxlJobLogger.log("failedMethodCallRetryTask ----------end");
            log.info("failedMethodCallRetryTask ----------end");
        } catch (Exception e) {
            XxlJobLogger.log("XXL-JOB, failedMethodCallRetryTask.Error=" + e);
            log.error("failedMethodCallRetryTask ----------  Error:", e);
             return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}
