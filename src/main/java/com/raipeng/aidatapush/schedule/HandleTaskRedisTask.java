package com.raipeng.aidatapush.schedule;


import com.raipeng.aidatapush.service.TaskRedisService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class HandleTaskRedisTask {
    @Autowired
    private TaskRedisService taskRedisService;

    /**
     * 单点运行
     *
     * @param param 参数
     * @return SUCCESS
     */
    @XxlJob("updateTasksInDB")
    public ReturnT<String> updateTasksInDB(String param) {
        XxlJobLogger.log("XXL-JOB, updateTasksInDB.param=" + param);
        log.info("XXL-JOB, updateTasksInDB.param=" + param);
        try {
            taskRedisService.updateTasksInPG();
        } catch (Exception e) {
            XxlJobLogger.log("XXL-JOB, updateTasksInDB.Error=" + e);
            log.error("updateTasksInDB ----------  Error:", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}
