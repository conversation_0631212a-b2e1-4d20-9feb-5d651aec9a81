package com.raipeng.aidatapush.schedule;


import com.raipeng.aidatapush.service.RedisSecondCacheService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class HandleUpdateCacheTask {
    @Autowired
    private RedisSecondCacheService redisSecondCacheService;

    /**
     *
     * @param param 参数
     * @return SUCCESS
     */
    @XxlJob("updateCache")
    public ReturnT<String> updateCache(String param) {
        XxlJobLogger.log("XXL-JOB, updateTasksInDB.param=" + param);
        log.info("XXL-JOB, updateCache.param=" + param);
        try {
            redisSecondCacheService.updateTaskCache();
            redisSecondCacheService.updateCache();
        } catch (Exception e) {
            XxlJobLogger.log("XXL-JOB, updateCache.Error=" + e);
            log.error("updateCache ----------  Error:", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 每晚清空task表缓存
     *
     * @param param 参数
     * @return SUCCESS
     */
    @XxlJob("clearTaskCache")
    public ReturnT<String> clearTaskCache(String param) {
        XxlJobLogger.log("XXL-JOB, clearTaskCache.param=" + param);
        log.info("XXL-JOB, clearTaskCache.param=" + param);
        try {
            redisSecondCacheService.clearTaskCache();
        } catch (Exception e) {
            XxlJobLogger.log("XXL-JOB, clearTaskCache.Error=" + e);
            log.error("clearTaskCache ----------  Error:", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;

    }
}
