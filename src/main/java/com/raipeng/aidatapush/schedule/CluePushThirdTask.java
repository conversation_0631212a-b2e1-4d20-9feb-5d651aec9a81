package com.raipeng.aidatapush.schedule;

import com.raipeng.aidatapush.service.HandlerPushThirdService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class CluePushThirdTask {

    @Autowired
    private HandlerPushThirdService handlerPushThirdService;

    @XxlJob("cluePushThirdTask")
    public ReturnT<String> execute(String id) {
        XxlJobLogger.log("XXL-JOB, cluePushThirdTask.param=" + id);
        log.info("XXL-JOB, failedMethodCallRetryTask.param=" + id);

        try {
            XxlJobLogger.log("cluePushThirdTask ----------start");
            log.info("cluePushThirdTask ----------start");
            handlerPushThirdService.pushClueToThird();
            XxlJobLogger.log("cluePushThirdTask ----------end");
            log.info("cluePushThirdTask ----------end");
        } catch (Exception e) {
            XxlJobLogger.log("XXL-JOB, cluePushThirdTask.Error=" + e);
            log.error("cluePushThirdTask ----------  Error:", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}
