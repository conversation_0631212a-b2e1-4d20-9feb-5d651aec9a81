package com.raipeng.aidatapush.listener;

import com.raipeng.aidatapush.context.AIContext;

import lombok.extern.slf4j.Slf4j;

import javax.servlet.ServletRequestEvent;
import javax.servlet.ServletRequestListener;
import javax.servlet.annotation.WebListener;

/**
 * <AUTHOR>
 * @Description 清理ThreadLocal
 * @Date 2019/12/5 19:28
 **/
@Slf4j
@WebListener
public class ThreadLocalCleanupListener implements ServletRequestListener {

    @Override
    public void requestDestroyed(ServletRequestEvent sre) {
        try {
            AIContext.remove();
            log.debug("Cleanup threadlocals...");
        } catch (Throwable e) {
            log.error("Threadlocals clean up failed!", e);
        }
    }

    @Override
    public void requestInitialized(ServletRequestEvent sre) {
        try {
            AIContext.remove();
            log.debug("Cleanup threadlocals...");
        } catch (Throwable e) {
            log.error("Threadlocals clean up failed!", e);
        }
        log.debug("ThreadLocalCleanupListener initialized!");
    }
}
