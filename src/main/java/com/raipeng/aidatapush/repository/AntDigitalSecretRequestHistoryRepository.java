package com.raipeng.aidatapush.repository;

import com.raipeng.aidatacommon.model.AntDigitalSecretRequestHistory;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface AntDigitalSecretRequestHistoryRepository extends JpaRepository<AntDigitalSecretRequestHistory, Long> {
    List<AntDigitalSecretRequestHistory> findAllByPhoneRecordIdIn(List<String> phoneRecordIds);
}
