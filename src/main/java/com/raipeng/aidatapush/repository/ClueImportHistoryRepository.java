package com.raipeng.aidatapush.repository;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.raipeng.aidatacommon.model.ClueImportRequestHistory;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

@DS("master")
public interface ClueImportHistoryRepository extends JpaRepository<ClueImportRequestHistory, Long> {

    @Query(value = "select t.batch_id  \n" +
            "from clue_import_request_history t " +
            "where  t.group_id = :groupId \n" +
            "and t.phone =:phone order by id desc limit 1", nativeQuery = true)
    String findAllByAccountAndGroupId(String groupId, String phone);

}
