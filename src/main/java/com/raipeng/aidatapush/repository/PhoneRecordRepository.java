package com.raipeng.aidatapush.repository;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.raipeng.aidatacommon.model.PhoneRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@DS("master")
public interface PhoneRecordRepository extends JpaRepository<PhoneRecord, Long> {
    List<PhoneRecord> findAllByRecordIdIn(List<String> recordId);

    @Transactional
    @Modifying
    @Query(value = "update t_phone_record  set called_num = :calledNum, call_status = :newCallStatus, add_time = :addTime, if_recalling = :ifRecalling, latest_record_id = :latestRecordId ,update_time = :updateTime where id = :id", nativeQuery = true)
    void updatePhoneRecordFromCdr(Integer calledNum, String id, String newCallStatus,
                                  String addTime, Integer ifRecalling,
                                  String latestRecordId, LocalDateTime updateTime);

    @Transactional
    @Modifying
    @Query(value = "update t_phone_record  set put_through_num = :putThroughNum, final_call_time = :finalCallTime, latest_intention_class = :latestIntentionClass ,update_time = :updateTime   where id = :id", nativeQuery = true)
    void updatePhoneRecordFromNormalCallback(Integer putThroughNum, String id,
                                             String finalCallTime, String latestIntentionClass, LocalDateTime updateTime);

}
