package com.raipeng.aidatapush.repository;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.raipeng.aidatacommon.model.SupplyLine;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import javax.persistence.Tuple;
import java.util.List;
import java.util.Set;

@DS("master")
public interface SupplyLineRepository extends JpaRepository<SupplyLine, Long> {
    @Query(value = "SELECT " +
            "t.line_number " +
            "FROM supply_line t " +
            "WHERE t.call_line_supplier_id " +
            "   IN (SELECT c.id " +
            "       FROM call_line_supplier c " +
            "       WHERE c.is_callback_supply_line = TRUE)", nativeQuery = true)
    Set<String> findNumbersForCallback();

    @Query(value = "SELECT t.line_number, c.supplier_belong " +
            "FROM supply_line t, call_line_supplier c " +
            "WHERE t.call_line_supplier_id=c.id", nativeQuery = true)
    List<Tuple> findSupplyLineBelong();

    @Query(value = "SELECT " +
            "t.master_call_number " +
            "FROM supply_line t " +
            "WHERE t.line_number = :lineNumber", nativeQuery = true)
    String findMasterNumberForAnt(String lineNumber);
}
