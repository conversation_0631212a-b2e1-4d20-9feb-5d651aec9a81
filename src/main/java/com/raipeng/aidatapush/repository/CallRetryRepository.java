package com.raipeng.aidatapush.repository;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.raipeng.aidatacommon.model.CallRetry;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

@DS("master")
public interface CallRetryRepository extends JpaRepository<CallRetry,Long> {

    @Query(value = "select t.* from t_call_retry t where  t.retry_result =0 and t.current_retry_count < max_retry_count",nativeQuery = true)
    List<CallRetry> findAllFailedAndNotBeMaxData();
}
