package com.raipeng.aidatapush.repository;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.raipeng.aidatacommon.model.AIOutboundTask;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;

@DS("master")
public interface AIOutboundTaskRepository extends JpaRepository<AIOutboundTask,Long> {
    List<AIOutboundTask> findAllByIdIn(List<Long> taskIds);

    List<AIOutboundTask> findAllByIdIn(Set<Long> taskIds);

    @Transactional
    @Modifying
    @Query(value="update t_ai_outbound_task  set phone_num = phone_num + :phoneNum, \n" +
            "called_phone_num = called_phone_num + :calledPhoneNum, \n" +
            "calling_phone_num = calling_phone_num + :callingPhoneNum, \n" +
            "recalling_phone_num = recalling_phone_num + :recallingPhoneNum, \n" +
            "finished_phone_num = finished_phone_num + :finishedPhoneNum, \n" +
            "fee_minute = fee_minute + :feeMinute, \n" +
            "put_through_phone_num = put_through_phone_num + :putThroughPhoneNum,  \n" +
            "call_record_num = call_record_num + :callRecordNum,  \n" +
            "phone_intention_num = phone_intention_num + :phoneIntentionNum  \n" +
            "where id = :taskId",nativeQuery=true)
    void updatePhoneNum(Long taskId, Integer phoneNum,
                            Integer calledPhoneNum,
                            Integer callingPhoneNum,
                            Integer recallingPhoneNum,
                            Integer finishedPhoneNum,
                            Integer putThroughPhoneNum,
                            Integer callRecordNum,
                            Integer phoneIntentionNum,
                        Integer feeMinute);

    @Transactional
    @Modifying
    @Query(value="update t_ai_outbound_task  set call_status = :callStatus\n" +
            "where id = :taskId",nativeQuery=true)
    void updateStatus(Long taskId, String callStatus);

    @Transactional
    @Modifying
    @Query(value="update t_ai_outbound_task  set call_status = :callStatus, ai_answer_num = 0 \n" +
            "where id = :taskId",nativeQuery=true)
    void updateAiManualStatus(Long taskId, String callStatus);
}
