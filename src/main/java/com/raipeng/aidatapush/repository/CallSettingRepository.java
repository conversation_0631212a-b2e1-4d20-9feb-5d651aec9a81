package com.raipeng.aidatapush.repository;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.raipeng.aidatacommon.model.CallSetting;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

@DS("master")
public interface CallSettingRepository extends JpaRepository<CallSetting, Long> {


    @Query(value = "select * from t_call_setting t " +
            "where t.group_id in :groupIdList", nativeQuery = true)
    List<CallSetting> findAllByGroupIdList(List<String> groupIdList);
}
