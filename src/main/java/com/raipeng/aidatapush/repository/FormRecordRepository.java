package com.raipeng.aidatapush.repository;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.raipeng.aidatacommon.model.clue.FormRecord;

import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Set;

@DS("master")
public interface FormRecordRepository extends JpaRepository<FormRecord, Long> {
    List<FormRecord> findFormRecordsByClueIdIn(Set<Long> clueIds);
}
