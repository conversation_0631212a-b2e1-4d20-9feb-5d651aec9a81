package com.raipeng.aidatapush.repository;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.raipeng.aidatacommon.model.Admin;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import javax.persistence.Tuple;
import java.util.List;

/**
 * <AUTHOR>
 */
@DS("master")
public interface AdminRepository extends JpaRepository<Admin, Long> {
    Admin findFirstById(Long id);

    @Query(value = "SELECT " +
            "t.id AS id," +
            "t.account AS account," +
            "CAST(t.call_back_range AS TEXT) AS callbackRange, " +
            "t.call_data_call_back_url AS callDataCallBackUrl, " +
            "t.call_sms_call_back_url AS callSmsCallBackUrl," +
            "t.call_update_call_back_url AS callUpdateCallBackUrl, " +
            "t.call_m_call_back_url AS callMCallBackUrl, " +
            "t.clue_callback_url AS clueCallbackUrl, " +
            "t.ant_access_key AS antAccessKey, " +
            "t.group_id AS groupId, " +
            "s.aes_key AS aesKey, " +
            "s.salt AS salt, " +
            "CAST(t.callback_status_config AS TEXT) AS callbackStatusConfig, " +
            "CAST(t.callback_field_config AS TEXT) AS callbackFieldConfig " +
            "FROM t_admin t LEFT JOIN tenant_secrets s ON CAST(t.tenant_id AS TEXT) = s.tenant_id " +
            "WHERE t.group_id = :groupId " +
            "AND   t.is_tenant_manager = TRUE ", nativeQuery = true)
    Tuple getCallbackEntityByGroupId(String groupId);

    @Query(value = "SELECT * FROM t_admin t WHERE t.is_tenant_manager = TRUE ", nativeQuery = true)
    List<Admin> getTenantManagerAdmins();
}
