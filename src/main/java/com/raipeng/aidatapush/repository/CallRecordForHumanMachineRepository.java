package com.raipeng.aidatapush.repository;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.raipeng.aidatacommon.model.CallRecordForHumanMachine;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@DS("master")
public interface CallRecordForHumanMachineRepository extends JpaRepository<CallRecordForHumanMachine, String> {
    List<CallRecordForHumanMachine> findAllByRecordIdIn(List<String> ids);

    @Transactional
    @Modifying
    @Query(value="update call_record_for_human_machine set " +
            "call_duration_sec = :callDurationSec, " +
            "call_duration = :callDuration, " +
            "call_status = :callStatus, " +
            "call_status_str = :callStatusStr, " +
            "error_code = :errorCode, " +
            "cause = :cause, " +
            "call_id = :callId, " +
            "line_id = :lineId, " +
            "line_code = :lineCode, " +
            "merchant_line_id = :merchantLineId, " +
            "merchant_line_code = :merchantLineCode, " +
            "fs_ip = :fsIp, " +
            "ai_call_ip = :aiCallIp, " +
            "who_hangup = :whoHangUp, " +
            "script_string_id = :scriptStringId, "+
            "speech_craft_id = :speechCraftId, "+
            "waitmsec = :waitmsec, "+
            "call_out_time  = :callOutTime, " +
            "merchant_id = :sipCallId, " +
            "update_time  = :updateTime " +
            "where record_id = :id",nativeQuery=true)
    void saveByCdr(Integer callDurationSec,
                   Integer callDuration,
                   String callStatus,
                   String callStatusStr,
                   String errorCode,
                   String cause,
                   String callId,
                   String lineId,
                   String lineCode,
                   String merchantLineId,
                   String merchantLineCode,
                   String id,
                   String fsIp,
                   String aiCallIp,
                   String callOutTime,
                   Integer whoHangUp,
                   String scriptStringId,
                   Long speechCraftId,
                   Integer waitmsec,
                   String sipCallId,
                   LocalDateTime updateTime);

    @Transactional
    @Modifying
    @Query(value="update call_record_for_human_machine set " +
            "whole_audio_file_url = :wholeAudioFileUrl, " +
            "cycle_count = :cycleCount, " +
            "say_count = :sayCount, " +
            "user_full_answer_content = :userFullAnswerContent, " +
            "intention_class = :intentionClass, " +
            "intention_label_ids = :intentionLabelIds, " +
            "intention_labels = :intentionLabels, " +
            "call_out_time = :callOutTime, " +
            "contact_time = :contactTime, " +
            "talk_time_end = :talkTimeEnd, " +
            "talk_time_start = :talkTimeStart, " +
            "hit_answer_ids = :hitAnswerIds, " +
            "script_string_id = :scriptStringId, "+
            "speech_craft_id = :speechCraftId, "+
            "hit_advance_ids = :hitAdvanceIds, "+
            "corpus_ids = :corpusIds, "+
            "hit_semantic_ids = :hitSemanticIds, "+
            "extra_info = :extraInfo, "+
            "line_gateway_numbers = :lineGatewayNumbers, "+
            "update_time  = :updateTime" +
            " where record_id = :id",nativeQuery=true)
    void saveByCalledResult(String wholeAudioFileUrl,
                            Integer cycleCount,
                            Integer sayCount,
                            String userFullAnswerContent,
                            String intentionClass,
                            String intentionLabelIds,
                            String intentionLabels,
                            String callOutTime,
                            String contactTime,
                            String talkTimeEnd,
                            String talkTimeStart,
                            String hitAnswerIds,
                            String scriptStringId,
                            Long speechCraftId,
                            String id,
                            String hitAdvanceIds,
                            String corpusIds,
                            String hitSemanticIds,
                            String extraInfo,
                            String lineGatewayNumbers,
                            LocalDateTime updateTime);
}
