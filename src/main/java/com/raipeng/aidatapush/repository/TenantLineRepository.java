package com.raipeng.aidatapush.repository;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.raipeng.aidatacommon.model.TenantLine;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

@DS("master")
public interface TenantLineRepository extends JpaRepository<TenantLine, Long> {
    @Transactional
    @Modifying
    @Query(value="update tenant_line  set line_remain_concurrent = :newNum  where id = :id",nativeQuery=true)
    Integer updateRemainConcurrent(Long id,Integer newNum);
}
