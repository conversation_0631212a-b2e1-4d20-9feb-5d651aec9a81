package com.raipeng.aidatapush.repository;

import com.raipeng.aidatacommon.model.AITenant;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

public interface AITenantRepository extends JpaRepository<AITenant,Long>, JpaSpecificationExecutor<AITenant> {
    AITenant findFirstById(Long id);

    @Query(value = "SELECT t.* FROM t_admin a\n" +
            "JOIN t_ai_tenant t ON a.tenant_id = t.id\n" +
            "WHERE a.group_id = :groupId \n" +
            "AND a.is_tenant_manager = TRUE ", nativeQuery = true)
    AITenant getAITenantByGroupId(String groupId);
}
