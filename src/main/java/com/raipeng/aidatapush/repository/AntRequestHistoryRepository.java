package com.raipeng.aidatapush.repository;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.raipeng.aidatacommon.model.AntRequestHistory;

import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

@DS("master")
public interface AntRequestHistoryRepository extends JpaRepository<AntRequestHistory, Long> {
    List<AntRequestHistory> findAllByPhoneRecordIdIn(List<String> phoneRecordIds);
}
