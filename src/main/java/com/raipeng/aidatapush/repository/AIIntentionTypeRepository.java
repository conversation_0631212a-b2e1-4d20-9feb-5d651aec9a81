package com.raipeng.aidatapush.repository;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.raipeng.aidatacommon.model.AIIntentionType;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

@DS("master")
public interface AIIntentionTypeRepository extends JpaRepository<AIIntentionType, Long> {

    @Query(value = "select * " +
            "  from t_ai_intention_type t  where t.script_id=:id  order by t.sequence ",nativeQuery=true)
    List<AIIntentionType> findAllByScriptId(Long id);
}
