package com.raipeng.aidatapush.repository;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.raipeng.aidatacommon.model.CallRecordForManualDirect;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@DS("master")
public interface CallRecordForManualDirectRepository extends JpaRepository<CallRecordForManualDirect, String> {
    List<CallRecordForManualDirect> findAllByRecordIdIn(List<String> ids);

    @Transactional
    @Modifying
    @Query(value="update call_record_for_manual_direct set " +
            "call_duration_sec = :callDurationSec, " +
            "call_duration = :callDuration, " +
            "call_status = :callStatus, " +
            "call_status_str = :callStatusStr, " +
            "error_code = :errorCode, " +
            "cause = :cause, " +
            "call_id = :callId, " +
            "line_id = :lineId, " +
            "line_code = :lineCode, " +
            "merchant_line_id = :merchantLineId, " +
            "merchant_line_code = :merchantLineCode, " +
            "fs_ip = :fsIp, " +
            "ai_call_ip = :aiCallIp, " +
            "who_hangup = :whoHangUp, " +
            "waitmsec = :waitmsec, "+
            "call_out_time  = :callOutTime, " +
            "merchant_id = :sipCallId, " +
            "update_time = :updateTime " +
            "where record_id = :id",nativeQuery=true)
    void saveByCdr(Integer callDurationSec,
                   Integer callDuration,
                   String callStatus,
                   String callStatusStr,
                   String errorCode,
                   String cause,
                   String callId,
                   String lineId,
                   String lineCode,
                   String merchantLineId,
                   String merchantLineCode,
                   String id,
                   String fsIp,
                   String aiCallIp,
                   String callOutTime,
                   Integer whoHangUp,
                   Integer waitmsec,
                   String sipCallId,
                   LocalDateTime updateTime);

    @Transactional
    @Modifying
    @Query(value="update call_record_for_manual_direct set " +
            "whole_audio_file_url = :wholeAudioFileUrl, " +
            "user_full_answer_content = :userFullAnswerContent, " +
            "call_out_time = :callOutTime, " +
            "contact_time = :contactTime, " +
            "talk_time_end = :talkTimeEnd, " +
            "talk_time_start = :talkTimeStart, " +
            "line_gateway_numbers = :lineGatewayNumbers," +
            "update_time = :updateTime " +
            "where record_id = :recordId",nativeQuery=true)
    void saveByCalledResult(String wholeAudioFileUrl,
                            String userFullAnswerContent,
                            String callOutTime,
                            String contactTime,
                            String talkTimeEnd,
                            String talkTimeStart,
                            String recordId,
                            String lineGatewayNumbers,
                            LocalDateTime updateTime);
}
