package com.raipeng.aidatapush.repository;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.raipeng.aidatacommon.model.script.FinalIntentionRules;

import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

@DS("master")
public interface FinalIntentionRulesRepository extends JpaRepository<FinalIntentionRules, Long> {
    List<FinalIntentionRules> findAllByScriptId(Long id);
}
