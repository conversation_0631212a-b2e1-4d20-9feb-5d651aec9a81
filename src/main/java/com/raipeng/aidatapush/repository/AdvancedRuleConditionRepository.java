package com.raipeng.aidatapush.repository;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.raipeng.aidatacommon.model.script.AdvancedRuleCondition;

import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

@DS("master")
public interface AdvancedRuleConditionRepository extends JpaRepository<AdvancedRuleCondition, Long> {

    List<AdvancedRuleCondition> findAllByScriptId(Long id);

}
