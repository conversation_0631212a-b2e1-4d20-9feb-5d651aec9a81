package com.raipeng.aidatapush.annotation;

import com.raipeng.aidatacommon.enums.retry.RetryType;
import com.raipeng.aidatacommon.enums.retry.WaitStrategyType;

import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

/**
 * 重试
 */
@Documented
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface CallRetry {
    /**
     * 重试类型
     */
    RetryType retryType() default RetryType.WHOLE_METHOD;

    /**
     * 重试间次数
     */
    int retryTimes() default 3;

    /**
     * 重试间隔单位
     */
    TimeUnit waitTimeUnit() default TimeUnit.SECONDS;

    /**
     * 重试间隔
     */
    int retryInterval() default 5;

    /**
     * 重试策略
     */
    WaitStrategyType waitStrategy() default WaitStrategyType.FIXED_WAIT_STRATEGY;
}