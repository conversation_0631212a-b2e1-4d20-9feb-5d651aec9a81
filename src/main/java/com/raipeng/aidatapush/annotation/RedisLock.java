package com.raipeng.aidatapush.annotation;

import java.lang.annotation.*;

/**
 * redis分布式锁
 */
@Documented
@Inherited
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface RedisLock {
    // 参数名，获取对应名称的参数值
    String paramName();
    int leaseTime() default -1;
    // 如果获取不到锁，是否等待
    boolean isBlock() default true;
    // wait为false的时候最多等待多久
    int waitTime() default 0;
}
