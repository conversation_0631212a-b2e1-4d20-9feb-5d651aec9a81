package com.raipeng.aidatapush.repositorymulti;

import com.raipeng.aidatacommon.model.record.CallRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

public interface CallRecordRepository extends JpaRepository<CallRecord, Long> {
    List<CallRecord> findAllByRecordIdIn(List<String> ids);

    @Transactional
    @Modifying
    @Query(value="update t_call_record  set \n" +
            "call_duration_sec = :callDurationSec, \n" +
            "call_duration = :callDuration,\n" +
            "call_status = :callStatus,\n" +
            "call_status_str = :callStatusStr,\n" +
            "error_code = :errorCode,\n" +
            "cause = :cause,\n" +
            "call_id = :callId,\n" +
            "line_id = :lineId,\n" +
            "line_code = :lineCode,\n" +
            "merchant_line_id = :merchantLineId,\n" +
            "merchant_line_code = :merchantLineCode,\n" +
            "fs_ip = :fsIp,\n" +
            "ai_call_ip = :aiCallIp,\n" +
            "who_hangup = :whoHangUp,\n" +
            "script_string_id = :scriptStringId,\n"+
            "speech_craft_id = :speechCraftId,\n"+
            "waitmsec = :waitmsec,\n"+
            "call_out_time  = :callOutTime,\n" +
            "merchant_id = :sipCallId,\n" +
            "update_time  = :updateTime\n" +
            " where record_id = :id",nativeQuery=true)
    void saveByCdr(Integer callDurationSec,
                   Integer callDuration,
                   String callStatus,
                   String callStatusStr,
                   String errorCode,
                   String cause,
                   String callId,
                   String lineId,
                   String lineCode,
                   String merchantLineId,
                   String merchantLineCode,
                   String id,
                   String fsIp,
                   String aiCallIp,String callOutTime,
                   Integer whoHangUp,
                   String scriptStringId,
                   Long speechCraftId,
                   Integer waitmsec,
                   String sipCallId,
                   LocalDateTime updateTime);

    @Transactional
    @Modifying
    @Query(value="update t_call_record  set \n" +
            "whole_audio_file_url = :wholeAudioFileUrl, \n" +
            "cycle_count = :cycleCount,\n" +
            "say_count = :sayCount,\n" +
            "user_full_answer_content = :userFullAnswerContent,\n" +
            "intention_class = :intentionClass,\n" +
            "intention_label_ids = :intentionLabelIds,\n" +
            "intention_labels = :intentionLabels,\n" +
            "call_out_time = :callOutTime,\n" +
            "contact_time = :contactTime,\n" +
            "talk_time_end = :talkTimeEnd,\n" +
            "talk_time_start = :talkTimeStart,\n" +
            "hit_answer_ids = :hitAnswerIds,\n" +
            "script_string_id = :scriptStringId,\n"+
            "speech_craft_id = :speechCraftId,\n"+
            "hit_advance_ids = :hitAdvanceIds,\n"+
            "corpus_ids = :corpusIds,\n"+
            "hit_semantic_ids = :hitSemanticIds,\n"+
            "extra_info = :extraInfo,\n"+
            "line_gateway_numbers = :lineGatewayNumbers,\n"+
            "update_time = :updateTime\n"+
            " where record_id = :id",nativeQuery=true)
    void saveByCalledResult(String wholeAudioFileUrl,
                            Integer cycleCount,
                            Integer sayCount,
                            String userFullAnswerContent,
                            String intentionClass,
                            String intentionLabelIds,
                            String intentionLabels,
                            String callOutTime,
                            String contactTime,
                            String talkTimeEnd,
                            String talkTimeStart,
                            String hitAnswerIds,
                            String scriptStringId,
                            Long speechCraftId,
                            String id,
                            String hitAdvanceIds,
                            String corpusIds,
                            String hitSemanticIds,
                            String extraInfo,
                            String lineGatewayNumbers,
                            LocalDateTime updateTime);
}
