package com.raipeng.aidatapush.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.raipeng.aidatacommon.entity.TaskRedisCountEntity;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import javax.persistence.*;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;



@DS("master")
@Service
@Transactional
public class BatchService {

    @PersistenceContext
    private EntityManager entityManager;

    public void batchUpdateTask(List<TaskRedisCountEntity> entities) {
        String sql = "UPDATE t_ai_outbound_task t " +
                "SET called_phone_num = t.called_phone_num + data.called, " +
                "    calling_phone_num = t.calling_phone_num + data.calling, " +
                "    recalling_phone_num = t.recalling_phone_num + data.recalling, " +
                "    finished_phone_num = t.finished_phone_num + data.finished, " +
                "    put_through_phone_num = t.put_through_phone_num + data.put_through, " +
                "    call_record_num = t.call_record_num + data.call_record, " +
                "    phone_intention_num = t.phone_intention_num + data.phone_intention, " +
                "    a_class_num = t.a_class_num + data.a_intention, " +
                "    fee_minute = t.fee_minute + data.fee " +
                "FROM (" +
                "    SELECT " +
                "        unnest(cast(? AS bigint[])) AS id, " +
                "        unnest(cast(? AS integer[])) AS called, " +
                "        unnest(cast(? AS integer[])) AS calling, " +
                "        unnest(cast(? AS integer[])) AS recalling, " +
                "        unnest(cast(? AS integer[])) AS finished, " +
                "        unnest(cast(? AS integer[])) AS put_through, " +
                "        unnest(cast(? AS integer[])) AS call_record, " +
                "        unnest(cast(? AS integer[])) AS phone_intention, " +
                "        unnest(cast(? AS integer[])) AS a_intention, " +
                "        unnest(cast(? AS integer[])) AS fee " +
                ") AS data " +
                "WHERE t.id = data.id";

        Query query = entityManager.createNativeQuery(sql)
                .setParameter(1,  convertToPostgresArray(getIdsArray(entities, TaskRedisCountEntity::getId)))  // 显式指定数组类型
                .setParameter(2, convertToPostgresArray(getFieldArray(entities, TaskRedisCountEntity::getCalledPhoneNum)))
                .setParameter(3, convertToPostgresArray(getFieldArray(entities, TaskRedisCountEntity::getCallingPhoneNum)))
                .setParameter(4, convertToPostgresArray(getFieldArray(entities, TaskRedisCountEntity::getRecallingPhoneNum)))
                .setParameter(5, convertToPostgresArray(getFieldArray(entities, TaskRedisCountEntity::getFinishedPhoneNum)))
                .setParameter(6, convertToPostgresArray(getFieldArray(entities, TaskRedisCountEntity::getPutThroughPhoneNum)))
                .setParameter(7, convertToPostgresArray(getFieldArray(entities, TaskRedisCountEntity::getCallRecordNum)))
                .setParameter(8, convertToPostgresArray(getFieldArray(entities, TaskRedisCountEntity::getPhoneIntentionNum)))
                .setParameter(9, convertToPostgresArray(getFieldArray(entities, TaskRedisCountEntity::getAIntentionNum)))
                .setParameter(10, convertToPostgresArray(getFieldArray(entities, TaskRedisCountEntity::getFeeMinute))); // 数值类型特殊处理

        query.executeUpdate();
        entityManager.flush();
        entityManager.clear();
    }

    private List<Integer> getFieldArray(List<TaskRedisCountEntity> list,
                                    Function<TaskRedisCountEntity, Integer> getter) {
        return list.stream().map(getter).collect(Collectors.toList());
    }

    private List<Long> getIdsArray(List<TaskRedisCountEntity> list,
                                    Function<TaskRedisCountEntity, Long> getter) {
        return list.stream().map(getter).collect(Collectors.toList());
    }

    private <T> String convertToPostgresArray(List<T> list) {
        if (list == null || list.isEmpty()) {
            return "{}";
        }
        return list.stream()
                .map(item -> item == null ? "NULL" : item.toString())
                .collect(Collectors.joining(",", "{", "}"));
    }
}
