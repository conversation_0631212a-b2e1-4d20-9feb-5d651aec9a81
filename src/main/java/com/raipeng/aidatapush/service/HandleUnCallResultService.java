package com.raipeng.aidatapush.service;

import com.raipeng.aidatacommon.model.*;
import com.raipeng.aidatapush.controller.wrapper.CallResultHandlerForAIManWrapper;
import com.raipeng.aidatapush.controller.wrapper.CallResultHandlerForHumanWrapper;
import com.raipeng.aidatapush.repository.*;

import com.raipeng.aidatacommon.entity.AiRedisTask;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class HandleUnCallResultService {
    @Autowired
    private PhoneRecordRepository phoneRecordRepository;

    @Autowired
    private CallRecordForHumanMachineRepository callRecordForHumanMachineRepository;

    @Autowired
    private CallRecordForManualDirectRepository callRecordForManualDirectRepository;

    @Autowired
    private TaskRedisService taskRedisService;

    public void saveUnCallResultForAIMan(CallResultHandlerForAIManWrapper callResultHandlerWrapper) {
        List<CallRecordForHumanMachine> callRecordList = callResultHandlerWrapper.getCallRecordList();
        List<PhoneRecord> phoneRecordList = callResultHandlerWrapper.getPhoneRecordList();
        List<AiRedisTask> aiRedisTasks = callResultHandlerWrapper.getAiRedisTaskList();
        if (CollectionUtils.isNotEmpty(callRecordList)) {
            callRecordForHumanMachineRepository.saveAll(callRecordList);
        }
        if (CollectionUtils.isNotEmpty(phoneRecordList)) {
            phoneRecordRepository.saveAll(phoneRecordList);
        }
        if (CollectionUtils.isNotEmpty(aiRedisTasks)) {
            taskRedisService.updateTaskInRedisQueue(aiRedisTasks);
        }
    }

    public void saveUnCallResultForHuman(CallResultHandlerForHumanWrapper callResultHandlerWrapper) {
        List<CallRecordForManualDirect> callRecordList = callResultHandlerWrapper.getCallRecordList();
        if (CollectionUtils.isNotEmpty(callRecordList)) {
            callRecordForManualDirectRepository.saveAll(callRecordList);
        }
    }
}
