package com.raipeng.aidatapush.service;

import com.raipeng.aidatacommon.thirdrequests.entity.CallDataEntity;
import com.raipeng.aidatacommon.thirdrequests.enums.PushThirdType;
import com.raipeng.aidatapush.config.HotConfig;
import com.raipeng.aidatapush.service.mq.consumer.newCallback.EnhancedCallDataPushService;
import com.raipeng.aidatapush.service.mq.producer.newCallback.PreXCallDataPushThirdForPureAIProducer;
import com.raipeng.aidatapush.service.mq.producer.newCallback.PreXCallSmsPushThirdForPureAIProducer;
import com.raipeng.aidatapush.service.mq.producer.newCallback.XCallDataPushThirdForPureAIProducer;
import com.raipeng.aidatapush.service.mq.producer.newCallback.XCallSmsPushThirdForPureAIProducer;
import com.raipeng.aidatapush.utils.CallDataHandleUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.raipeng.aidatapush.config.RabbitConstants.X_CALL_DATA_PUSH_THIRD_FOR_PURE_AI_QUEUE;
import static com.raipeng.aidatapush.config.RabbitConstants.X_CALL_SMS_PUSH_THIRD_FOR_PURE_AI_QUEUE;


@Slf4j
@Service
public class HandleCallDataPushThirdForPureAIService {
    @Autowired
    private HotConfig hotConfig;

    @Autowired
    private RabbitAdmin rabbitAdmin;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private RestForThirdTemplateService restForThirdTemplateService;

    @Autowired
    private PreXCallDataPushThirdForPureAIProducer preXCallDataPushThirdForPureAIProducer;

    @Autowired
    private PreXCallSmsPushThirdForPureAIProducer preXCallSmsPushThirdForPureAIProducer;

    @Autowired
    private XCallDataPushThirdForPureAIProducer xCallDataPushThirdForPureAIProducer;

    @Autowired
    private XCallSmsPushThirdForPureAIProducer xCallSmsPushThirdForPureAIProducer;

    @Autowired
    private DingDingHighPriorityService dingDingHighPriorityService;

    @Autowired
    private EnhancedCallDataPushService enhancedCallDataPushService;

    public void pushCallDataToThirdForAI(CallDataEntity entity) {
        enhancedCallDataPushService.pushCallDataForPureAI(entity);
    }

    public void pushCallSmsToThirdForAI(CallDataEntity entity) {
        enhancedCallDataPushService.pushCallSmsForPureAI(entity);
    }

    public void collectPreXCallDataToThirdForAI(CallDataEntity entity) {
        preXCallDataPushThirdForPureAIProducer.send(entity);

    }

    public void collectPreXCallSmsToThirdForAI(CallDataEntity entity) {
        preXCallSmsPushThirdForPureAIProducer.send(entity);
    }

    public void collectXCallDataToThirdForAI(CallDataEntity entity) {
        xCallDataPushThirdForPureAIProducer.send(entity);
    }

    public void collectXCallSmsToThirdForAI(CallDataEntity entity) {
        xCallSmsPushThirdForPureAIProducer.send(entity);
    }

    public void handleXCallDataForPureAI() {
        CallDataHandleUtils.handleXQueue(rabbitTemplate,
                X_CALL_DATA_PUSH_THIRD_FOR_PURE_AI_QUEUE,
                PushThirdType.CALL_DATA_FOR_PURE_AI,
                CallDataHandleUtils.PURE_AI_DATA_WARN_COUNT,
                this::pushCallDataToThirdForAI,
                this::collectXCallDataToThirdForAI,
                hotConfig.getMaxPushFailSize(),
                dingDingHighPriorityService);
    }

    public void handleXCallSmsForPureAI() {
        CallDataHandleUtils.handleXQueue(rabbitTemplate,
                X_CALL_SMS_PUSH_THIRD_FOR_PURE_AI_QUEUE,
                PushThirdType.CALL_SMS_FOR_PURE_AI,
                CallDataHandleUtils.PURE_AI_SMS_WARN_COUNT,
                this::pushCallSmsToThirdForAI,
                this::collectXCallSmsToThirdForAI,
                hotConfig.getMaxPushFailSize(),
                dingDingHighPriorityService);
    }

    public void handleXCallDataForPureAIManual(String groupId) {
        CallDataHandleUtils.handleXQueueManual(rabbitTemplate,
                X_CALL_DATA_PUSH_THIRD_FOR_PURE_AI_QUEUE,
                PushThirdType.CALL_DATA_FOR_PURE_AI,
                CallDataHandleUtils.PURE_AI_DATA_WARN_COUNT,
                this::pushCallDataToThirdForAI,
                this::collectXCallDataToThirdForAI,
                hotConfig.getMaxPushFailSize(),
                groupId,
                dingDingHighPriorityService
        );
    }

    public void handleXCallSmsForPureAIManual(String groupId) {
        CallDataHandleUtils.handleXQueueManual(rabbitTemplate,
                X_CALL_SMS_PUSH_THIRD_FOR_PURE_AI_QUEUE,
                PushThirdType.CALL_SMS_FOR_PURE_AI,
                CallDataHandleUtils.PURE_AI_SMS_WARN_COUNT,
                this::pushCallSmsToThirdForAI,
                this::collectXCallSmsToThirdForAI,
                hotConfig.getMaxPushFailSize(),
                groupId,
                dingDingHighPriorityService
        );
    }

    public CallDataEntity monitorXCallDataForPureAI() {
        return CallDataHandleUtils.monitorXQueue(rabbitTemplate,
                X_CALL_DATA_PUSH_THIRD_FOR_PURE_AI_QUEUE,
                this::collectXCallDataToThirdForAI
        );
    }

    public CallDataEntity monitorXCallSmsForPureAI() {
        return CallDataHandleUtils.monitorXQueue(rabbitTemplate,
                X_CALL_SMS_PUSH_THIRD_FOR_PURE_AI_QUEUE,
                this::collectXCallSmsToThirdForAI
        );
    }

    public void checkXQueue() {
        CallDataHandleUtils.checkXQueue(rabbitAdmin, X_CALL_DATA_PUSH_THIRD_FOR_PURE_AI_QUEUE, dingDingHighPriorityService);
        CallDataHandleUtils.checkXQueue(rabbitAdmin, X_CALL_SMS_PUSH_THIRD_FOR_PURE_AI_QUEUE, dingDingHighPriorityService);
    }
}
