package com.raipeng.aidatapush.service;

import com.raipeng.aidatacommon.model.TenantSecrets;
import com.raipeng.aidatapush.repository.TenantSecretsRepository;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import static com.raipeng.common.constant.AIRedisCacheKeyConstant.CACHE_FOR_TENANT_SECRETS;

@Slf4j
@Service
public class TenantSecretsService {
    @Autowired
    private TenantSecretsRepository tenantSecretsRepository;

    @Cacheable(value = CACHE_FOR_TENANT_SECRETS)
    public TenantSecrets findFirstByTenantId(String tenantId){
        return tenantSecretsRepository.findFirstByTenantId(tenantId);
    }
}
