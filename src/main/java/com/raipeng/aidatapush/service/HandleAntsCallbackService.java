package com.raipeng.aidatapush.service;


import com.alibaba.fastjson.JSONObject;
import com.antgroup.antchain.openapi.riskplus.Client;
import com.antgroup.antchain.openapi.riskplus.models.*;
import com.antgroup.antchain.openapi.unicontact.models.CallbackRobotcallRequest;
import com.antgroup.antchain.openapi.unicontact.models.CallbackRobotcallResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.RemovalNotification;
import com.raipeng.aidatacommon.enums.retry.WaitStrategyType;
import com.raipeng.aidatacommon.exception.CallFailedException;
import com.raipeng.aidatacommon.exception.CallOverRateException;
import com.raipeng.aidatacommon.model.*;
import com.raipeng.aidatacommon.model.record.CallRecord;
import com.raipeng.aidatapush.annotation.CallOverRate;
import com.raipeng.aidatapush.annotation.CallRetry;
import com.raipeng.aidatapush.controller.wrapper.ClueDataPushWrapper;
import com.raipeng.aidatapush.entity.AntsErrorLogWrapper;
import com.raipeng.aidatapush.entity.AntsRequestLog;
import com.raipeng.aidatapush.enums.DingDingMsgType;
import com.raipeng.aidatapush.repository.SupplyLineRepository;
import com.raipeng.aidatapush.service.mq.producer.AntsErrorLogProducer;
import com.raipeng.aidatapush.service.mq.producer.AntsRequestProducer;
import com.raipeng.aidatapush.utils.StringUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class HandleAntsCallbackService {
    @Autowired
    private AntsRequestProducer antsRequestProducer;

    @Autowired
    private AntsErrorLogProducer antsErrorLogProducer;

    @Autowired
    private DingDingHighPriorityService dingDingHighPriorityService;

    private static final String UPDATE_ANT_M_NAME = "event-M";

    @Autowired
    private SupplyLineRepository supplyLineRepository;

    private final Cache<String, String> expiringNumberMap = CacheBuilder.newBuilder()
            .expireAfterWrite(30, TimeUnit.SECONDS)
            .removalListener((RemovalNotification<String, String> notification) -> {
                log.info("Key {} expired from map, cause: {}", notification.getKey(), notification.getCause());
            })
            .build();

    private String getMasterNumber(String lineNumber) {
        String masterNumber = expiringNumberMap.getIfPresent(lineNumber);
        if (masterNumber != null) {
            return masterNumber;
        } else {
            masterNumber = supplyLineRepository.findMasterNumberForAnt(lineNumber);
            if (StringUtils.isNotEmpty(masterNumber)) {
                expiringNumberMap.put(lineNumber, masterNumber);
            }
            return masterNumber;
        }
    }

    @CallOverRate
    @CallRetry(retryTimes = 5, waitStrategy = WaitStrategyType.INCREMENTING_WAIT_STRATEGY)
    public void pushResultToAnts(CallRecord callRecord, Admin admin, Map<String, AntRequestHistory> antRequestMap, boolean isDelay) {
        AntRequestHistory antRequestHistory = antRequestMap.get(callRecord.getPhoneRecordId());
        CallbackUmktRobotcallRequest request = new CallbackUmktRobotcallRequest();
        if (antRequestHistory != null) {
            request.setBatchId(antRequestHistory.getBatchId()); // 导入号码的批次号
            request.setTaskId(Long.valueOf(antRequestHistory.getAntTaskId()));
            request.setTaskName(antRequestHistory.getAntTaskName() == null ? "" : antRequestHistory.getAntTaskName());
            request.setImportTime(antRequestHistory.getImportTime());
            if (isDelay) {
                request.setKeywords(UPDATE_ANT_M_NAME);
            }
            request.setProperties(antRequestHistory.getProperties());
            request.setBizProperties(antRequestHistory.getBizProperties());
        } else {
            AntsErrorLogWrapper antsErrorLogWrapper = new AntsErrorLogWrapper();
            antsErrorLogWrapper.setPhoneRecordId(callRecord.getPhoneRecordId());
            antsErrorLogWrapper.setCallRecordId(callRecord.getRecordId());
            antsErrorLogWrapper.setAccountId(admin.getId());
            antsErrorLogProducer.send(antsErrorLogWrapper);
            log.error("Exception=>蚂蚁账号数据，没有找到batchId");
            dingDingHighPriorityService.sedDDMsg(DingDingMsgType.ERROR, "蚂蚁账号数据，没有找到batchId");
            return;
        }
        request.setCustomerKey(callRecord.getPlainPhone());
        request.setCurrentCallTimes(callRecord.getCalledNum() == null ? 0 : Long.valueOf(callRecord.getCalledNum())); // 呼叫次数，第一次呼叫为1，第二次重试为2
        request.setKeyTemplate("MOBILE");  // 号码模板
        request.setCallType(2002L);  // 外呼类型
        request.setCallId(callRecord.getCallId() == null ? "" : callRecord.getCallId());
        request.setStatusCode("7".equals(callRecord.getCallStatus()) ? 1L : 8L);                           // 外呼状态
        request.setStatusDescription("7".equals(callRecord.getCallStatus()) ? "已接听" : "无法接通");         // 外呼状态解释
        request.setCallBeginTime(callRecord.getCallOutTime() == null ? "" : callRecord.getCallOutTime());
        request.setHangupTime(callRecord.getTalkTimeEnd() == null ? "" : callRecord.getTalkTimeEnd());
        request.setRingTime(callRecord.getWaitmsec() == null ? 0 : Long.valueOf(callRecord.getWaitmsec()));
        request.setSpeakingTime(callRecord.getCallDurationSec() == null ? "0" : secondsToChineseMMSS(callRecord.getCallDurationSec()));   // 通话时长字符串
        request.setSpeakingDuration(callRecord.getCallDurationSec() == null ? 0 : Long.valueOf(callRecord.getCallDurationSec()));  // 通话时长秒
        request.setSpeakingTurns(callRecord.getCycleCount() == null ? 0 : Long.valueOf(callRecord.getCycleCount()));
        request.setIntentTag(callRecord.getIntentionClass() == null ? "其他" : callRecord.getIntentionClass());
        request.setIntentDescription(callRecord.getIntentionClassDescription() == null ? "意向空" : callRecord.getIntentionClassDescription());
        request.setHungupType(callRecord.getWhoHangup() == 0 ? 1L : 3L);
        request.setTransferStatusCode("");
        request.setTransferStatus("");
        request.setAgentSpeakingTime("");
        request.setAgentSpeakingDuration(0L);
        request.setSms(2L);
        request.setAnswerRecall(0L);
        request.setProductInstanceId(admin.getProductInstanceId());
        request.setChatRecord(callRecord.getWholeAudioFileUrl() == null ? "" : callRecord.getWholeAudioFileUrl());
        request.setChats("");
        CallbackUmktRobotcallResponse response = null;
        LocalDateTime startTime = LocalDateTime.now();
        try {
            Config config = new Config()
                    .setAccessKeyId(admin.getAntAccessKey())
                    .setAccessKeySecret(admin.getAntSecretKey())
                    .setEndpoint(admin.getEndPoint())
                    .setProtocol(admin.getProtocol());
            Client client = new Client(config);
            response = client.callbackUmktRobotcall(request);
            String resultCode = response.getResultCode();
            if ("OVER_RATE_LIMIT".equals(resultCode)) {
                throw new CallOverRateException("手机号推送蚂蚁超频", "call_record_push_for_ant");
            }
            if (!"OK".equals(resultCode)) {
                throw new CallFailedException("手机号推送蚂蚁报错", "call_record_push_for_ant");
            }
        } catch (CallOverRateException e) {
            log.error("Exception=>手机号推送蚂蚁超频");
            throw new CallOverRateException(e, "手机号推送蚂蚁超频", "call_record_push_for_ant");
        } catch (Exception ex) {
            log.error("Exception=>手机号推送蚂蚁报错:{}", ex.getMessage());
            throw new CallFailedException(ex, "手机号推送蚂蚁报错", "call_record_push_for_ant");
        } finally {
            AntsRequestLog antsRequestLog = new AntsRequestLog();
            antsRequestLog.setRequest(request);
            antsRequestLog.setStartTime(startTime);
            antsRequestLog.setEndTime(LocalDateTime.now());
            antsRequestLog.setResponse(response);
            antsRequestProducer.send(JSONObject.toJSONString(antsRequestLog));
        }
    }

    @CallOverRate
    @CallRetry(retryTimes = 5, waitStrategy = WaitStrategyType.INCREMENTING_WAIT_STRATEGY)
    public void pushResultToAntsSecond(CallRecord callRecord, Admin admin, Map<String, AntDigitalRequestHistory> antRequestMap) {
        AntDigitalRequestHistory antRequestHistory = antRequestMap.get(callRecord.getPhoneRecordId());
        CallbackQmpRobotcallRequest request = new CallbackQmpRobotcallRequest();
        if (antRequestHistory != null) {
            request.setBatchId(antRequestHistory.getBatchId()); // 导入号码的批次号
            request.setTaskId(Long.valueOf(antRequestHistory.getAntTaskId()));
            request.setTaskName(antRequestHistory.getAntTaskName() == null ? "" : antRequestHistory.getAntTaskName());
            request.setImportTime(antRequestHistory.getImportTime());
            request.setProperties(antRequestHistory.getProperties());
            request.setBizProperties(antRequestHistory.getBizProperties());
        } else {
            AntsErrorLogWrapper antsErrorLogWrapper = new AntsErrorLogWrapper();
            antsErrorLogWrapper.setPhoneRecordId(callRecord.getPhoneRecordId());
            antsErrorLogWrapper.setCallRecordId(callRecord.getRecordId());
            antsErrorLogWrapper.setAccountId(admin.getId());
            antsErrorLogProducer.send(antsErrorLogWrapper);
            log.error("Exception=>蚂蚁Qmp账号数据，没有找到batchId");
            dingDingHighPriorityService.sedDDMsg(DingDingMsgType.ERROR, "蚂蚁Qmp账号数据，没有找到batchId");
            return;
        }
        request.setCustomerKey(callRecord.getPlainPhone());
        request.setCurrentCallTimes(callRecord.getCalledNum() == null ? 0 : Long.valueOf(callRecord.getCalledNum())); // 呼叫次数，第一次呼叫为1，第二次重试为2
        request.setKeyTemplate("MOBILE");  // 号码模板
        request.setCallType(2002L);  // 外呼类型
        request.setCallId(callRecord.getCallId() == null ? "" : callRecord.getCallId());
        request.setStatusCode("7".equals(callRecord.getCallStatus()) ? 1L : 8L);                           // 外呼状态
        request.setStatusDescription("7".equals(callRecord.getCallStatus()) ? "已接听" : "无法接通");        // 外呼状态解释
        request.setCallBeginTime(callRecord.getCallOutTime() == null ? "" : callRecord.getCallOutTime());
        request.setHangupTime(callRecord.getTalkTimeEnd() == null ? "" : callRecord.getTalkTimeEnd());
        request.setRingTime(callRecord.getWaitmsec() == null ? 0 : Long.valueOf(callRecord.getWaitmsec()));
        request.setSpeakingTime(callRecord.getCallDurationSec() == null ? "0" : secondsToChineseMMSS(callRecord.getCallDurationSec()));   // 通话时长字符串
        request.setSpeakingDuration(callRecord.getCallDurationSec() == null ? 0 : Long.valueOf(callRecord.getCallDurationSec()));  // 通话时长秒
        request.setSpeakingTurns(callRecord.getCycleCount() == null ? 0 : Long.valueOf(callRecord.getCycleCount()));
        request.setIntentTag(convertIntentionClass(callRecord.getIntentionClass(), callRecord.getCallStatus()).getIntentionClass());
        request.setIntentDescription(convertIntentionClass(callRecord.getIntentionClass(), callRecord.getCallStatus()).getIntentionClassDescription());
        request.setHungupType(callRecord.getWhoHangup() == 0 ? 1L : 3L);
        request.setTransferStatusCode("");
        request.setTransferStatus("");
        request.setAgentSpeakingTime("");
        request.setAgentSpeakingDuration(0L);
        request.setSms(2L);
        request.setAnswerRecall(0L);
        request.setProductInstanceId(admin.getProductInstanceId());
        request.setChatRecord(callRecord.getWholeAudioFileUrl() == null ? "" : callRecord.getWholeAudioFileUrl());
        request.setChats("7".equals(callRecord.getCallStatus()) ? callRecord.getDialogContentsCache() : "");
        request.setExtInfo(convertCallRecordToExtInfo(callRecord.getCity(), callRecord.getProvince(), callRecord.getOperator()));
        request.setAnswerTime(callRecord.getContactTime());
        CallbackQmpRobotcallResponse response = null;
        LocalDateTime startTime = LocalDateTime.now();
        try {
            Config config = new Config()
                    .setAccessKeyId(admin.getAntAccessKey())
                    .setAccessKeySecret(admin.getAntSecretKey())
                    .setEndpoint(admin.getEndPoint())
                    .setProtocol(admin.getProtocol());
            Client client = new Client(config);
            response = client.callbackQmpRobotcall(request);
            String resultCode = response.getResultCode();
            if ("OVER_RATE_LIMIT".equals(resultCode)) {
                throw new CallOverRateException("手机号推送蚂蚁Qmp超频", "call_record_push_for_ant");
            }
            if (!"OK".equals(resultCode)) {
                throw new CallFailedException("手机号推送蚂蚁Qmp报错", "call_record_push_for_ant");
            }
        } catch (CallOverRateException e) {
            log.error("Exception=>手机号推送蚂蚁Qmp超频");
            throw new CallOverRateException(e, "手机号推送蚂蚁Qmp超频", "call_record_push_for_ant");
        } catch (Exception ex) {
            log.error("Exception=>手机号推送蚂蚁Qmp报错:{}", ex.getMessage());
            throw new CallFailedException(ex, "手机号推送蚂蚁Qmp报错", "call_record_push_for_ant");
        } finally {
            CallbackUmktRobotcallResponse umktRobotcallResponse = new CallbackUmktRobotcallResponse();
            CallbackUmktRobotcallRequest umktRobotcallRequest = new CallbackUmktRobotcallRequest();
            BeanUtils.copyProperties(request, umktRobotcallRequest);
            if (response != null) {
                BeanUtils.copyProperties(response, umktRobotcallResponse);
            }
            AntsRequestLog antsRequestLog = new AntsRequestLog();
            antsRequestLog.setRequest(umktRobotcallRequest);
            antsRequestLog.setStartTime(startTime);
            antsRequestLog.setEndTime(LocalDateTime.now());
            antsRequestLog.setResponse(umktRobotcallResponse);
            antsRequestProducer.send(JSONObject.toJSONString(antsRequestLog));
        }
    }

    @CallOverRate
    @CallRetry(retryTimes = 5, waitStrategy = WaitStrategyType.INCREMENTING_WAIT_STRATEGY)
    public void pushResultToAntsThird(CallRecord callRecord, Admin admin, Map<String, AntDigitalSecretRequestHistory> antRequestMap) {
        AntDigitalSecretRequestHistory antRequestHistory = antRequestMap.get(callRecord.getPhoneRecordId());
        CallbackRobotcallRequest request = new CallbackRobotcallRequest();
        if (antRequestHistory != null) {
            request.setBatchId(antRequestHistory.getBatchId()); // 导入号码的批次号
            request.setTaskId(Long.valueOf(antRequestHistory.getAntTaskId()));
            request.setTaskName(antRequestHistory.getAntTaskName() == null ? "" : antRequestHistory.getAntTaskName());
            request.setImportTime(antRequestHistory.getImportTime());
            JSONObject properties = new JSONObject();
            try {
                properties = JSONObject.parseObject(antRequestHistory.getProperties());
            } catch (Exception e) {
                log.error("蚂蚁third账号数据，解析properties报错:{}", e.getMessage());
            }
            if (antRequestHistory.getProperties() != null) {
                properties.put("电话号码", callRecord.getPlainPhone());
            }
            if (StringUtils.isNotEmpty(callRecord.getLineId()))  {
                properties.put("lineCallingNumber", getMasterNumber(callRecord.getLineId()));
            }
            request.setProperties(properties.toJSONString());
            request.setBizProperties(antRequestHistory.getBizProperties());
        } else {
            AntsErrorLogWrapper antsErrorLogWrapper = new AntsErrorLogWrapper();
            antsErrorLogWrapper.setPhoneRecordId(callRecord.getPhoneRecordId());
            antsErrorLogWrapper.setCallRecordId(callRecord.getRecordId());
            antsErrorLogWrapper.setAccountId(admin.getId());
            antsErrorLogProducer.send(antsErrorLogWrapper);
            log.error("Exception=>蚂蚁third账号数据，没有找到batchId");
            dingDingHighPriorityService.sedDDMsg(DingDingMsgType.ERROR, "蚂蚁third账号数据，没有找到batchId");
            return;
        }
        request.setCustomerKey(callRecord.getPlainPhone());
        request.setCurrentCallTimes(callRecord.getCalledNum() == null ? 0 : Long.valueOf(callRecord.getCalledNum())); // 呼叫次数，第一次呼叫为1，第二次重试为2
        request.setKeyTemplate("MOBILE_AES");  // 号码模板
        request.setCallType(2002L);  // 外呼类型
        request.setCallId(callRecord.getCallId() == null ? "" : callRecord.getCallId());
        request.setStatusCode("7".equals(callRecord.getCallStatus()) ? 1L : 8L);                           // 外呼状态
        request.setStatusDescription("7".equals(callRecord.getCallStatus()) ? "已接听" : "无法接通");        // 外呼状态解释
        request.setCallBeginTime(callRecord.getCallOutTime() == null ? "" : callRecord.getCallOutTime());
        request.setHangupTime(callRecord.getTalkTimeEnd() == null ? LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : callRecord.getTalkTimeEnd());
        request.setRingTime(callRecord.getWaitmsec() == null ? 0 : Long.valueOf(callRecord.getWaitmsec()));
        request.setSpeakingTime(callRecord.getCallDurationSec() == null ? "0" : secondsToChineseMMSS(callRecord.getCallDurationSec()));   // 通话时长字符串
        request.setSpeakingDuration(callRecord.getCallDurationSec() == null ? 0 : Long.valueOf(callRecord.getCallDurationSec()));  // 通话时长秒
        request.setSpeakingTurns(callRecord.getCycleCount() == null ? 0 : Long.valueOf(callRecord.getCycleCount()));
        request.setIntentTag(convertIntentionClassForThird(callRecord.getIntentionClass(), callRecord.getCallStatus(),
                callRecord.getIntentionLabels(), callRecord.getCallDurationSec(), callRecord.getSayCount(), callRecord.getCycleCount()).getIntentionClass());
        request.setIntentDescription(convertIntentionClassForThird(callRecord.getIntentionClass(), callRecord.getCallStatus(),
                callRecord.getIntentionLabels(), callRecord.getCallDurationSec(), callRecord.getSayCount(), callRecord.getCycleCount()).getIntentionClassDescription());
        request.setHungupType(callRecord.getWhoHangup() == 0 ? 1L : 3L);
        request.setTransferStatusCode("0");
        request.setTransferStatus("未触发");
        request.setAgentSpeakingTime("0");
        request.setAgentSpeakingDuration(0L);
        request.setSms(2L);
        request.setAnswerRecall(0L);
        request.setProductInstanceId(admin.getProductInstanceId());
        request.setChatRecord(callRecord.getWholeAudioFileUrl() == null ? "" : callRecord.getWholeAudioFileUrl());
        request.setChats("");
        request.setExtInfo(convertCallRecordToExtInfo(callRecord.getCity(), callRecord.getProvince(), callRecord.getOperator()));
        CallbackRobotcallResponse response = null;
        LocalDateTime startTime = LocalDateTime.now();
        try {
            com.antgroup.antchain.openapi.unicontact.models.Config config =
                    new com.antgroup.antchain.openapi.unicontact.models.Config()
                    .setAccessKeyId(admin.getAntAccessKey())
                    .setAccessKeySecret(admin.getAntSecretKey())
                    .setEndpoint(admin.getEndPoint())
                    .setProtocol(admin.getProtocol());

            com.antgroup.antchain.openapi.unicontact.Client client1 = new com.antgroup.antchain.openapi.unicontact.Client(config);
            response = client1.callbackRobotcall(request);
            String resultCode = response.getResultCode();
            if ("OVER_RATE_LIMIT".equals(resultCode)) {
                throw new CallOverRateException("手机号推送蚂蚁third超频", "call_record_push_for_ant");
            }
            if (!"OK".equals(resultCode)) {
                throw new CallFailedException("手机号推送蚂蚁third报错", "call_record_push_for_ant");
            }
        } catch (CallOverRateException e) {
            log.error("Exception=>手机号推送蚂蚁third超频");
            throw new CallOverRateException(e, "手机号推送蚂蚁third超频", "call_record_push_for_ant");
        } catch (Exception ex) {
            log.error("Exception=>手机号推送蚂蚁third报错:{}", ex.getMessage());
            throw new CallFailedException(ex, "手机号推送蚂蚁third报错", "call_record_push_for_ant");
        } finally {
            CallbackUmktRobotcallResponse umktRobotcallResponse = new CallbackUmktRobotcallResponse();
            CallbackUmktRobotcallRequest umktRobotcallRequest = new CallbackUmktRobotcallRequest();
            BeanUtils.copyProperties(request, umktRobotcallRequest);
            if (response != null) {
                BeanUtils.copyProperties(response, umktRobotcallResponse);
            }
            AntsRequestLog antsRequestLog = new AntsRequestLog();
            antsRequestLog.setRequest(umktRobotcallRequest);
            antsRequestLog.setStartTime(startTime);
            antsRequestLog.setEndTime(LocalDateTime.now());
            antsRequestLog.setResponse(umktRobotcallResponse);
            antsRequestProducer.send(JSONObject.toJSONString(antsRequestLog));
        }
    }

    @CallOverRate
    @CallRetry(retryTimes = 5, waitStrategy = WaitStrategyType.INCREMENTING_WAIT_STRATEGY)
    public void pushResultToAntsForAIManSecond(CallRecordForHumanMachine callRecord, Admin admin, Map<String, AntDigitalRequestHistory> antRequestMap, ClueDataPushWrapper wrapper) {
        AntDigitalRequestHistory antRequestHistory = antRequestMap.get(callRecord.getPhoneRecordId());
        CallbackQmpRobotcallRequest request = new CallbackQmpRobotcallRequest();
        if (antRequestHistory != null) {
            request.setBatchId(antRequestHistory.getBatchId()); // 导入号码的批次号
            request.setTaskId(Long.valueOf(antRequestHistory.getAntTaskId()));
            request.setTaskName(antRequestHistory.getAntTaskName() == null ? "" : antRequestHistory.getAntTaskName());
            request.setImportTime(antRequestHistory.getImportTime());
            request.setProperties(antRequestHistory.getProperties());
            request.setBizProperties(antRequestHistory.getBizProperties());
        } else {
            AntsErrorLogWrapper antsErrorLogWrapper = new AntsErrorLogWrapper();
            antsErrorLogWrapper.setPhoneRecordId(callRecord.getPhoneRecordId());
            antsErrorLogWrapper.setCallRecordId(callRecord.getRecordId());
            antsErrorLogWrapper.setAccountId(admin.getId());
            antsErrorLogProducer.send(antsErrorLogWrapper);
            log.error("Exception=>蚂蚁人机协同Qmp账号数据，没有找到batchId");
            dingDingHighPriorityService.sedDDMsg(DingDingMsgType.ERROR, "蚂蚁人机协同Qmp账号数据，没有找到batchId");
            return;
        }
        if (wrapper != null) {
            request.setAgentId(wrapper.getCallSeatId());
            request.setAgentTag(wrapper.getCallSeatName());
        }
        request.setCustomerKey(callRecord.getPlainPhone());
        request.setCurrentCallTimes(callRecord.getCalledNum() == null ? 0 : Long.valueOf(callRecord.getCalledNum())); // 呼叫次数，第一次呼叫为1，第二次重试为2
        request.setKeyTemplate("MOBILE");  // 号码模板
        request.setCallType(2002L);  // 外呼类型
        request.setCallId(callRecord.getCallId() == null ? "" : callRecord.getCallId());
        request.setStatusCode("7".equals(callRecord.getCallStatus()) ? 1L : 8L);                           // 外呼状态
        request.setStatusDescription("7".equals(callRecord.getCallStatus()) ? "已接听" : "无法接通");        // 外呼状态解释
        request.setCallBeginTime(callRecord.getCallOutTime() == null ? "" : callRecord.getCallOutTime());
        request.setHangupTime(callRecord.getTalkTimeEnd() == null ? "" : callRecord.getTalkTimeEnd());
        request.setRingTime(callRecord.getWaitmsec() == null ? 0 : Long.valueOf(callRecord.getWaitmsec()));
        request.setSpeakingTime(callRecord.getCallDurationSec() == null ? "0" : secondsToChineseMMSS(callRecord.getCallDurationSec()));   // 通话时长字符串
        request.setSpeakingDuration(callRecord.getCallDurationSec() == null ? 0 : Long.valueOf(callRecord.getCallDurationSec()));  // 通话时长秒
        request.setSpeakingTurns(callRecord.getCycleCount() == null ? 0 : Long.valueOf(callRecord.getCycleCount()));
        request.setIntentTag(convertIntentionClass(callRecord.getIntentionClass(), callRecord.getCallStatus()).getIntentionClass());
        request.setIntentDescription(convertIntentionClass(callRecord.getIntentionClass(), callRecord.getCallStatus()).getIntentionClassDescription());
        request.setHungupType(callRecord.getWhoHangup() == 0 ? 1L : 3L);
        request.setTransferStatusCode("");
        request.setTransferStatus("");
        request.setAgentSpeakingTime("");
        request.setAgentSpeakingDuration(0L);
        request.setSms(2L);
        request.setAnswerRecall(0L);
        request.setProductInstanceId(admin.getProductInstanceId());
        request.setChatRecord(callRecord.getWholeAudioFileUrl() == null ? "" : callRecord.getWholeAudioFileUrl());
        request.setChats("7".equals(callRecord.getCallStatus()) ? callRecord.getDialogContentsCache() : "");
        request.setExtInfo(convertCallRecordToExtInfo(callRecord.getCity(), callRecord.getProvince(), callRecord.getOperator()));
        CallbackQmpRobotcallResponse response = null;
        LocalDateTime startTime = LocalDateTime.now();
        try {
            Config config = new Config()
                    .setAccessKeyId(admin.getAntAccessKey())
                    .setAccessKeySecret(admin.getAntSecretKey())
                    .setEndpoint(admin.getEndPoint())
                    .setProtocol(admin.getProtocol());
            Client client = new Client(config);
            response = client.callbackQmpRobotcall(request);
            String resultCode = response.getResultCode();
            if ("OVER_RATE_LIMIT".equals(resultCode)) {
                throw new CallOverRateException("手机号推送蚂蚁人机协同Qmp超频", "call_record_push_for_ant");
            }
            if (!"OK".equals(resultCode)) {
                throw new CallFailedException("手机号推送蚂蚁人机协同Qmp报错", "call_record_push_for_ant");
            }
        } catch (CallOverRateException e) {
            log.error("Exception=>手机号推送蚂蚁人机协同Qmp超频");
            throw new CallOverRateException(e, "手机号推送蚂蚁人机协同Qmp超频", "call_record_push_for_ant");
        } catch (Exception ex) {
            log.error("Exception=>手机号推送蚂蚁人机协同Qmp报错:{}", ex.getMessage());
            throw new CallFailedException(ex, "手机号推送蚂蚁人机协同Qmp报错", "call_record_push_for_ant");
        } finally {
            CallbackUmktRobotcallResponse umktRobotcallResponse = new CallbackUmktRobotcallResponse();
            CallbackUmktRobotcallRequest umktRobotcallRequest = new CallbackUmktRobotcallRequest();
            BeanUtils.copyProperties(request, umktRobotcallRequest);
            if (response != null) {
                BeanUtils.copyProperties(response, umktRobotcallResponse);
            }
            AntsRequestLog antsRequestLog = new AntsRequestLog();
            antsRequestLog.setRequest(umktRobotcallRequest);
            antsRequestLog.setStartTime(startTime);
            antsRequestLog.setEndTime(LocalDateTime.now());
            antsRequestLog.setResponse(umktRobotcallResponse);
            antsRequestProducer.send(JSONObject.toJSONString(antsRequestLog));
        }
    }

    @CallOverRate
    @CallRetry(retryTimes = 5, waitStrategy = WaitStrategyType.INCREMENTING_WAIT_STRATEGY)
    public void pushResultToAntsForAIManThird(CallRecordForHumanMachine callRecord, Admin admin, Map<String, AntDigitalSecretRequestHistory> antRequestMap, ClueDataPushWrapper wrapper) {
        AntDigitalSecretRequestHistory antRequestHistory = antRequestMap.get(callRecord.getPhoneRecordId());
        CallbackRobotcallRequest request = new CallbackRobotcallRequest();
        if (antRequestHistory != null) {
            request.setBatchId(antRequestHistory.getBatchId()); // 导入号码的批次号
            request.setTaskId(Long.valueOf(antRequestHistory.getAntTaskId()));
            request.setTaskName(antRequestHistory.getAntTaskName() == null ? "" : antRequestHistory.getAntTaskName());
            request.setImportTime(antRequestHistory.getImportTime());
            JSONObject properties = new JSONObject();
            try {
                properties = JSONObject.parseObject(antRequestHistory.getProperties());
            } catch (Exception e) {
                log.error("蚂蚁third账号数据，解析properties报错:{}", e.getMessage());
            }
            if (antRequestHistory.getProperties() != null) {
                properties.put("电话号码", callRecord.getPlainPhone());
            }
            if (StringUtils.isNotEmpty(callRecord.getLineCode()))  {
                properties.put("lineCallingNumber", getMasterNumber(callRecord.getLineId()));
            }
            request.setProperties(properties.toJSONString());
            request.setBizProperties(antRequestHistory.getBizProperties());
        } else {
            AntsErrorLogWrapper antsErrorLogWrapper = new AntsErrorLogWrapper();
            antsErrorLogWrapper.setPhoneRecordId(callRecord.getPhoneRecordId());
            antsErrorLogWrapper.setCallRecordId(callRecord.getRecordId());
            antsErrorLogWrapper.setAccountId(admin.getId());
            antsErrorLogProducer.send(antsErrorLogWrapper);
            log.error("Exception=>蚂蚁人机协同账号数据，没有找到batchId");
            dingDingHighPriorityService.sedDDMsg(DingDingMsgType.ERROR, "蚂蚁人机协同账号数据，没有找到batchId");
            return;
        }
        if (wrapper != null) {
            request.setAgentId(wrapper.getCallSeatId());
            request.setAgentTag(wrapper.getCallSeatName());
            request.setAgentSpeakingTime(getAngentSpeakingTime(callRecord));
            request.setAgentSpeakingDuration(getAgentSpeakingDuration(callRecord));
        } else {
            request.setAgentSpeakingTime("0");
            request.setAgentSpeakingDuration(0L);
        }
        request.setCustomerKey(callRecord.getPlainPhone());
        request.setCurrentCallTimes(callRecord.getCalledNum() == null ? 0 : Long.valueOf(callRecord.getCalledNum())); // 呼叫次数，第一次呼叫为1，第二次重试为2
        request.setKeyTemplate("MOBILE_AES");  // 号码模板
        request.setCallType(wrapper == null ? 2002L : 2003L);  // 外呼类型 纯ai或者转人工
        request.setCallId(callRecord.getCallId() == null ? "" : callRecord.getCallId());
        request.setStatusCode("7".equals(callRecord.getCallStatus()) ? 1L : 8L);                           // 外呼状态
        request.setStatusDescription("7".equals(callRecord.getCallStatus()) ? "已接听" : "无法接通");        // 外呼状态解释
        request.setCallBeginTime(callRecord.getCallOutTime() == null ? "" : callRecord.getCallOutTime());
        request.setHangupTime(callRecord.getTalkTimeEnd() == null ? LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : callRecord.getTalkTimeEnd());
        request.setRingTime(callRecord.getWaitmsec() == null ? 0 : Long.valueOf(callRecord.getWaitmsec()));
        request.setSpeakingTime(callRecord.getCallDurationSec() == null ? "0" : secondsToChineseMMSS(callRecord.getCallDurationSec()));   // 通话时长字符串
        request.setSpeakingDuration(callRecord.getCallDurationSec() == null ? 0 : Long.valueOf(callRecord.getCallDurationSec()));  // 通话时长秒
        request.setSpeakingTurns(callRecord.getCycleCount() == null ? 0 : Long.valueOf(callRecord.getCycleCount()));
        request.setIntentTag(convertIntentionClassForThird(callRecord.getIntentionClass(), callRecord.getCallStatus(),
                callRecord.getIntentionLabels(), callRecord.getCallDurationSec(), callRecord.getSayCount(), callRecord.getCycleCount()).getIntentionClass());
        request.setIntentDescription(convertIntentionClassForThird(callRecord.getIntentionClass(), callRecord.getCallStatus(),
                callRecord.getIntentionLabels(), callRecord.getCallDurationSec(), callRecord.getSayCount(), callRecord.getCycleCount()).getIntentionClassDescription());
        request.setHungupType(callRecord.getWhoHangup() == 0 ? 1L : 3L);
        request.setTransferStatusCode(getTransferCodeForAIMan(callRecord, wrapper).getTransferCode());
        request.setTransferStatus(getTransferCodeForAIMan(callRecord, wrapper).getTransferCodeStatus());
        request.setSms(2L);
        request.setAnswerRecall(0L);
        request.setProductInstanceId(admin.getProductInstanceId());
        request.setChatRecord(callRecord.getWholeAudioFileUrl() == null ? "" : callRecord.getWholeAudioFileUrl());
        request.setChats("");
        request.setExtInfo(convertCallRecordToExtInfo(callRecord.getCity(), callRecord.getProvince(), callRecord.getOperator()));
        CallbackRobotcallResponse response = null;
        LocalDateTime startTime = LocalDateTime.now();
        try {
            com.antgroup.antchain.openapi.unicontact.models.Config config =
                    new com.antgroup.antchain.openapi.unicontact.models.Config()
                    .setAccessKeyId(admin.getAntAccessKey())
                    .setAccessKeySecret(admin.getAntSecretKey())
                    .setEndpoint(admin.getEndPoint())
                    .setProtocol(admin.getProtocol());
            com.antgroup.antchain.openapi.unicontact.Client client = new com.antgroup.antchain.openapi.unicontact.Client(config);
            response = client.callbackRobotcall(request);
            String resultCode = response.getResultCode();
            if ("OVER_RATE_LIMIT".equals(resultCode)) {
                throw new CallOverRateException("手机号推送蚂蚁人机协同超频", "call_record_push_for_ant");
            }
            if (!"OK".equals(resultCode)) {
                throw new CallFailedException("手机号推送蚂蚁人机协同报错", "call_record_push_for_ant");
            }
        } catch (CallOverRateException e) {
            log.error("Exception=>手机号推送蚂蚁人机协同超频");
            throw new CallOverRateException(e, "手机号推送蚂蚁人机协同超频", "call_record_push_for_ant");
        } catch (Exception ex) {
            log.error("Exception=>手机号推送蚂蚁人机协同报错:{}", ex.getMessage());
            throw new CallFailedException(ex, "手机号推送蚂蚁人机协同报错", "call_record_push_for_ant");
        } finally {
            CallbackUmktRobotcallResponse umktRobotcallResponse = new CallbackUmktRobotcallResponse();
            CallbackUmktRobotcallRequest umktRobotcallRequest = new CallbackUmktRobotcallRequest();
            BeanUtils.copyProperties(request, umktRobotcallRequest);
            if (response != null) {
                BeanUtils.copyProperties(response, umktRobotcallResponse);
            }
            AntsRequestLog antsRequestLog = new AntsRequestLog();
            antsRequestLog.setRequest(umktRobotcallRequest);
            antsRequestLog.setStartTime(startTime);
            antsRequestLog.setEndTime(LocalDateTime.now());
            antsRequestLog.setResponse(umktRobotcallResponse);
            antsRequestProducer.send(JSONObject.toJSONString(antsRequestLog));
        }
    }

    private Long getAgentSpeakingDuration(CallRecordForHumanMachine callRecord) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String start = callRecord.getStartAnswerTime();
        String end = callRecord.getEndAnswerTime();
        if (start != null && end != null) {
            LocalDateTime startTime = LocalDateTime.parse(start, formatter);
            LocalDateTime endTime = LocalDateTime.parse(end, formatter);
            return ChronoUnit.SECONDS.between(startTime, endTime);
        } else {
            return 0L;
        }
    }

    private String getAngentSpeakingTime(CallRecordForHumanMachine callRecord) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String start = callRecord.getStartAnswerTime();
        String end = callRecord.getEndAnswerTime();
        if (start != null && end != null) {
            LocalDateTime startTime = LocalDateTime.parse(start, formatter);
            LocalDateTime endTime = LocalDateTime.parse(end, formatter);
            long hours = ChronoUnit.HOURS.between(startTime, endTime) % 24;
            long minutes = ChronoUnit.MINUTES.between(startTime, endTime) % 60;
            long seconds = ChronoUnit.SECONDS.between(startTime, endTime) % 60;
            return hours + "时" + minutes + "分" + seconds + "秒";
        } else {
            return "0";
        }
    }

    private TransferCode getTransferCodeForAIMan(CallRecordForHumanMachine callRecord, ClueDataPushWrapper wrapper) {
        TransferCode transferCode = new TransferCode();
        String callStatus = callRecord.getCallStatus();
        Boolean isTransToCallSeat = callRecord.getIsTransToCallSeat();
        String startMonitorTime = callRecord.getStartMonitorTime();
        String startAnswerTime = callRecord.getStartAnswerTime();

        if ("7".equals(callStatus)) {
            if (Objects.nonNull(isTransToCallSeat)) {
                if (!isTransToCallSeat) {
                    transferCode.setTransferCode("0");
                    transferCode.setTransferCodeStatus("未触发");
                    return transferCode;
                }
                if (startMonitorTime == null) {
                    transferCode.setTransferCode("14");
                    transferCode.setTransferCodeStatus("监听失败");
                    return transferCode;
                }
                if (startAnswerTime == null) {
                    transferCode.setTransferCode("20");
                    transferCode.setTransferCodeStatus("监听未接听");
                    return transferCode;
                }
            }
            if (startAnswerTime != null) {
                transferCode.setTransferCode("30");
                transferCode.setTransferCodeStatus("已接听");
            } else {
                if (startMonitorTime != null) {
                    transferCode.setTransferCode("20");
                    transferCode.setTransferCodeStatus("监听未接听");
                } else {
                    transferCode.setTransferCode("0");
                    transferCode.setTransferCodeStatus("未触发");
                }
            }
        } else {
            transferCode.setTransferCode("");
            transferCode.setTransferCodeStatus("");
        }
        return transferCode;
    }


    @Data
        private static final class TransferCode {
            String transferCode;
            String transferCodeStatus;
        }

    @Data
    private static final class IntentionInfo {
        String intentionClass;
        String intentionClassDescription;
    }

    private IntentionInfo convertIntentionClassForThird(String intentionClass, String callStatus, String intentionLabels, Integer callDurationSec, Integer getSayCounts, Integer cycleCounts) {
        int callDuration = callDurationSec == null ? 0 : callDurationSec;
        int getSayCount = getSayCounts == null ? 0 : getSayCounts;
        int cycleCount = cycleCounts == null ? 0 : cycleCounts;
        IntentionInfo intentionInfo = new IntentionInfo();
        if (Objects.equals(intentionClass, "其他") && "7".equals(callStatus)) {
            intentionInfo.setIntentionClass("");
            intentionInfo.setIntentionClassDescription("意向空");
        } else if (intentionLabels != null && intentionLabels.contains("小助理") && "7".equals(callStatus)) {
            intentionInfo.setIntentionClass("K1");
            intentionInfo.setIntentionClassDescription("已接通无意向");
        } else if (callDuration >= 15 && cycleCount == 0 && "7".equals(callStatus)) {
            intentionInfo.setIntentionClass("K2");
            intentionInfo.setIntentionClassDescription("已接通无意向");
        } else if (getSayCount == 0 && "7".equals(callStatus)) {
            intentionInfo.setIntentionClass("K3");
            intentionInfo.setIntentionClassDescription("已接通无意向");
        } else if (Objects.equals(intentionClass, "其他") && !"7".equals(callStatus)) {
            intentionInfo.setIntentionClass("F");
            intentionInfo.setIntentionClassDescription("未接通");
        } else if (Objects.equals(intentionClass, "D")) {
            intentionInfo.setIntentionClass("M");
            intentionInfo.setIntentionClassDescription("无意向");
        } else if (Objects.equals(intentionClass, "E") || Objects.equals(intentionClass, "F")) {
            intentionInfo.setIntentionClass("D");
            intentionInfo.setIntentionClassDescription("明确拒绝");
        } else if (Objects.equals(intentionClass, "G")) {
            intentionInfo.setIntentionClass("E");
            intentionInfo.setIntentionClassDescription("投诉");
        } else if (Objects.equals(intentionClass, "A")) {
            intentionInfo.setIntentionClass("A");
            intentionInfo.setIntentionClassDescription("高意向");
        } else if (Objects.equals(intentionClass, "B")) {
            intentionInfo.setIntentionClass("B");
            intentionInfo.setIntentionClassDescription("有意向");
        } else if (Objects.equals(intentionClass, "C")) {
            intentionInfo.setIntentionClass("C");
            intentionInfo.setIntentionClassDescription("低意向");
        } else {
            intentionInfo.setIntentionClass(null);
            intentionInfo.setIntentionClassDescription("");
        }
        return intentionInfo;
    }

    private IntentionInfo convertIntentionClass(String intentionClass, String callStatus) {
        IntentionInfo intentionInfo = new IntentionInfo();
        if (Objects.equals(intentionClass, "其他") && "7".equals(callStatus)) {
            intentionInfo.setIntentionClass("");
            intentionInfo.setIntentionClassDescription("意向空");
        } else if (Objects.equals(intentionClass, "其他") && !"7".equals(callStatus)) {
            intentionInfo.setIntentionClass("F");
            intentionInfo.setIntentionClassDescription("未接通");
        } else if (Objects.equals(intentionClass, "D")) {
            intentionInfo.setIntentionClass("M");
            intentionInfo.setIntentionClassDescription("无意向");
        } else if (Objects.equals(intentionClass, "E") || Objects.equals(intentionClass, "F")) {
            intentionInfo.setIntentionClass("D");
            intentionInfo.setIntentionClassDescription("明确拒绝");
        } else if (Objects.equals(intentionClass, "G")) {
            intentionInfo.setIntentionClass("E");
            intentionInfo.setIntentionClassDescription("投诉");
        } else if (Objects.equals(intentionClass, "A")) {
            intentionInfo.setIntentionClass("A");
            intentionInfo.setIntentionClassDescription("高意向");
        } else if (Objects.equals(intentionClass, "B")) {
            intentionInfo.setIntentionClass("B");
            intentionInfo.setIntentionClassDescription("有意向");
        } else if (Objects.equals(intentionClass, "C")) {
            intentionInfo.setIntentionClass("C");
            intentionInfo.setIntentionClassDescription("低意向");
        } else {
            intentionInfo.setIntentionClass(null);
            intentionInfo.setIntentionClassDescription("");
        }
        return intentionInfo;
    }

    private String convertCallRecordToExtInfo(String city, String province, String operator) {
        Map<String, String> data = new HashMap<>();
        data.put("city", StringUtils.isEmpty(city) ? "未知" : city + "市");
        data.put("province", StringUtils.isEmpty(province) ? "未知" : province);
        data.put("service_provider", StringUtils.isEmpty(operator) ? "未知" : "中国" + operator);
        String json = "";
        ObjectMapper mapper = new ObjectMapper();
        try {
            json = mapper.writeValueAsString(data);
        } catch (JsonProcessingException e) {
            log.warn("手机号推送蚂蚁Qmp转换json格式extra字段错误: {}", e.getMessage());
        }
        return json;
    }

    private static String secondsToChineseMMSS(int seconds) {
        int minutes = seconds / 60;
        seconds = seconds % 60;
        String formattedMinutes = String.format("%02d", minutes) + "分";
        String formattedSeconds = String.format("%02d", seconds) + "秒";
        return formattedMinutes + formattedSeconds;
    }
}
