package com.raipeng.aidatapush.service;

import com.alibaba.fastjson.JSONObject;
import com.raipeng.aidatapush.utils.RequestWrapper;
import com.raipeng.aidatapush.utils.StringUtils;
import com.raipeng.aidatapush.utils.SurveillanceShieldCityPushUtils;

import com.raipeng.aidatapush.utils.WxWorkRobotWarningUtils;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RefreshScope
public class DingDingService {


    @Autowired
    DingDingTokenService dingDingTokenService;

    private static DingDingTokenService staticDingDingTokenService;

    @PostConstruct
    private void init(){
        staticDingDingTokenService = dingDingTokenService;
    }


    //@Value("${ai.call.monitor.phone:18351121313}")
    private static String aiCallMonitorPhone="18351121313";

    private static String methodRetryMonitorPhone = "18463103089";

    //https://oapi.dingtalk.com/robot/send?access_token=de70519d80f36eb50b7844b65f6e89ca63427a71f5144e479989d5657c8f6453
    //@Value("${ai.call.monitor.token:6ec22ad2f69a8137d6ba02ae44b731b1d94b69e45dfebd61a89043bf95442c6d}")
    private static String aiCallMonitorToken="6ec22ad2f69a8137d6ba02ae44b731b1d94b69e45dfebd61a89043bf95442c6d";
    /**
     * 临时告警
     */
    //private static String aiCallMonitorToken="de70519d80f36eb50b7844b65f6e89ca63427a71f5144e479989d5657c8f6453";


    public static void dingDingSendMsg(String... contents){
        StringBuilder contentSb = new StringBuilder("");
        for (String c : contents) {
            contentSb.append(c).append("\n");
        }
        contentSb.append("time: "+ LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        sendDingDingMsg(contentSb.toString());
    }



    private static void sendDingDingMsg(String content){
        Map<String,String> phoneMap = Arrays.stream(aiCallMonitorPhone.split(",")).collect(Collectors.toMap(p->p,p->p,(k,v)->k));
        String result = SurveillanceShieldCityPushUtils.dingDingRobotMessagePushWithToken(new RequestWrapper("text", "【告警】"+content, "【告警】"+content ,
                null, null, false,phoneMap),staticDingDingTokenService.getToken());

        if(result == null || !JSONObject.parseObject(result,JSONObject.class).getString("errcode").equals("0")){
            String wxResult = WxWorkRobotWarningUtils.wxWorkPush("text", content, new ArrayList<>(), new ArrayList<>(), false, staticDingDingTokenService.getQiWeiToken());
            log.error("Exception 钉钉发送消息失败！,已经推送给企微了,失败原因："+result+"企微的返回结果"+wxResult);
        }
    }

    public static void dingDingSendMsgException(String... contents){
        StringBuilder contentSb = new StringBuilder("");
        for (String c : contents) {
            contentSb.append(c).append("\n");
        }
        contentSb.append("time: "+ LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        sendDingDingMsgException(contentSb.toString());
    }

    private static void sendDingDingMsgException(String content){
        Map<String,String> phoneMap = Arrays.stream(aiCallMonitorPhone.split(",")).collect(Collectors.toMap(p->p,p->p,(k,v)->k));
        String result = SurveillanceShieldCityPushUtils.dingDingRobotMessagePushWithToken(new RequestWrapper("text", "【告警】"+content, "【告警】"+content ,
                null, null, false,phoneMap),staticDingDingTokenService.getExceptionToken());
        if(result == null || !JSONObject.parseObject(result,JSONObject.class).getString("errcode").equals("0")){
            String wxResult = WxWorkRobotWarningUtils.wxWorkPush("text", content, new ArrayList<>(), new ArrayList<>(), false, staticDingDingTokenService.getQiWeiToken());
            log.error("Exception 钉钉发送消息失败！,已经推送给企微了,失败原因："+result+"企微的返回结果"+wxResult);
        }
    }

    public static void sendDingDingMsgRetryException(String content) {
        sendDingDingMsgRetryException(methodRetryMonitorPhone, content);
    }

    public static void sendDingDingMsgRetryException(String phone, String content) {
        if (StringUtils.isEmpty(phone)) {
            phone = methodRetryMonitorPhone;
        }
        if(StringUtils.isNotEmpty(content)){
            content =content.replaceAll("\"", "'");
        }
        Map<String, String> phoneMap = Arrays.stream(phone.split(",")).collect(Collectors.toMap(p -> p, p -> p, (k, v) -> k));
        String result = SurveillanceShieldCityPushUtils.dingDingRobotMessagePushWithToken(new RequestWrapper("text", "【告警】" + content, "【告警】" + content,
                null, null, false, phoneMap), staticDingDingTokenService.getRetryToken());

        if(result == null || !JSONObject.parseObject(result,JSONObject.class).getString("errcode").equals("0")){
            String wxResult = WxWorkRobotWarningUtils.wxWorkPush("text", content, new ArrayList<>(), new ArrayList<>(), false, staticDingDingTokenService.getQiWeiToken());
            log.error("Exception 钉钉发送消息失败！,已经推送给企微了,失败原因："+result+"企微的返回结果"+wxResult);
        }
    }

}
