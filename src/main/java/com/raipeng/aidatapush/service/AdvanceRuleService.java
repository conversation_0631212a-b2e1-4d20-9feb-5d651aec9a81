package com.raipeng.aidatapush.service;


import com.raipeng.aidatacommon.model.AdvancedRules;
import com.raipeng.aidatacommon.model.CallRecordForHumanMachine;
import com.raipeng.aidatacommon.model.record.CallRecord;
import com.raipeng.aidatacommon.model.script.AdvancedRuleCondition;
import com.raipeng.aidatacommon.model.script.FinalIntentionRules;
import com.raipeng.aidatapush.entity.MatchRuleInfo;
import com.raipeng.aidatapush.entity.ScriptAdvanceRuleEntity;
import com.raipeng.aidatapush.repository.AdvancedRuleConditionRepository;
import com.raipeng.aidatapush.repository.AdvancedRulesRepository;
import com.raipeng.aidatapush.repository.FinalIntentionRulesRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.raipeng.common.constant.AIRedisCacheKeyConstant.NEW_ADVANCED_RULES_BY_SCRIPT_ID;

@Slf4j
@Service
public class AdvanceRuleService {
    @Autowired
    private AdvancedRulesRepository advancedRulesRepository;

    @Autowired
    private AdvancedRuleConditionRepository advancedRuleConditionRepository;

    @Autowired
    private AdvancedRuleConditionService advancedRuleConditionService;

    @Autowired
    private FinalIntentionRulesRepository finalIntentionRulesRepository;

    @Cacheable(value = "CACHE::ADVANCED_RULES_BY_SCRIPT_ID")
    public List<AdvancedRules> findAllByScriptId(Long id) {
        return advancedRulesRepository.findAllByScriptId(id);
    }

    @Cacheable(value = "CACHE::ADVANCED_RULE_CONDITION_BY_SCRIPT_ID")
    public Map<String, List<AdvancedRuleCondition>> findAllConditionByScriptId(Long id) {
        List<AdvancedRuleCondition> allByScriptId = advancedRuleConditionRepository.findAllByScriptId(id);
        Map<String, List<AdvancedRuleCondition>> groupedByConditionUniqueId = allByScriptId.stream()
                .collect(Collectors.groupingBy(AdvancedRuleCondition::getConditionUniqueId));
        return groupedByConditionUniqueId;
    }

    @Cacheable(value = NEW_ADVANCED_RULES_BY_SCRIPT_ID)
    public ScriptAdvanceRuleEntity findAllAdvanceRuleByScriptId(Long id) {
        List<AdvancedRules> advancedRulesList = advancedRulesRepository.findAllByScriptId(id);
        List<FinalIntentionRules> finalIntentionRulesList = finalIntentionRulesRepository.findAllByScriptId(id);
        List<AdvancedRuleCondition> ruleConditionByScriptId = advancedRuleConditionRepository.findAllByScriptId(id);
        ScriptAdvanceRuleEntity scriptAdvanceRuleEntity = new ScriptAdvanceRuleEntity();
        scriptAdvanceRuleEntity.setAdvancedRulesList(advancedRulesList);
        scriptAdvanceRuleEntity.setFinalIntentionRulesList(finalIntentionRulesList);
        Map<String, List<AdvancedRuleCondition>> groupedByConditionUniqueId = ruleConditionByScriptId.stream()
                .collect(Collectors.groupingBy(AdvancedRuleCondition::getConditionUniqueId));
        scriptAdvanceRuleEntity.setAdvancedRuleConditionMap(groupedByConditionUniqueId);
        return scriptAdvanceRuleEntity;
    }

    public List<MatchRuleInfo> tagCallRecord(CallRecord callRecord, boolean isOrderFirst,
                                             List<AdvancedRules> advancedRulesList,
                                             Map<String, List<AdvancedRuleCondition>> advancedRuleConditionMap,
                                             Map<String, List<String>> allAISemanticLabelRelationMap) {
        List<String> hitAdvancedIds = new ArrayList<>();
        List<MatchRuleInfo> matchRuleInfos = new ArrayList<>();
        if (isOrderFirst) {
            for (AdvancedRules advancedRule : advancedRulesList) {
                MatchRuleInfo matchRuleInfo = matchAdvanceRule(callRecord, advancedRule, advancedRuleConditionMap, allAISemanticLabelRelationMap);
                if (matchRuleInfo != null) {
                    hitAdvancedIds.add(advancedRule.getId().toString());
                    callRecord.setHitAdvanceIds(String.join(",", hitAdvancedIds));
                    matchRuleInfos.add(matchRuleInfo);
                    break;
                }
            }
        } else {
            for (AdvancedRules advancedRule : advancedRulesList) {
                // 收集全部的匹配的意向
                MatchRuleInfo matchRuleInfo = matchAdvanceRule(callRecord, advancedRule, advancedRuleConditionMap, allAISemanticLabelRelationMap);
                if (matchRuleInfo != null) {
                    hitAdvancedIds.add(advancedRule.getId().toString());
                    matchRuleInfos.add(matchRuleInfo);
                }
            }
        }

        callRecord.setHitAdvanceIds(String.join(",", hitAdvancedIds));
        return matchRuleInfos;
    }

    public List<MatchRuleInfo> matchRuleCondition(CallRecordForHumanMachine callRecord, boolean isOrderFirst,
                                                  List<AdvancedRules> advancedRulesList,
                                                  Map<String, List<AdvancedRuleCondition>> advancedRuleConditionMap,

                                                  Map<String, List<String>> allAISemanticLabelRelationMap) {
        List<String> hitAdvancedIds = new ArrayList<>();
        List<MatchRuleInfo> matchRuleInfos = new ArrayList();
        if (isOrderFirst) {
            for (AdvancedRules advancedRule : advancedRulesList) {
                MatchRuleInfo matchRuleInfo = matchAdvanceRule(callRecord, advancedRule, advancedRuleConditionMap, allAISemanticLabelRelationMap);
                if (matchRuleInfo != null) {
                    hitAdvancedIds.add(advancedRule.getId().toString());
                    callRecord.setHitAdvanceIds(String.join(",", hitAdvancedIds));
                    matchRuleInfos.add(matchRuleInfo);
                    break;
                }
            }
        } else {
            for (AdvancedRules advancedRule : advancedRulesList) {
                // 最终意向将收集全部的匹配的意向
                MatchRuleInfo matchRuleInfo = matchAdvanceRule(callRecord, advancedRule, advancedRuleConditionMap, allAISemanticLabelRelationMap);
                if (matchRuleInfo != null) {
                    hitAdvancedIds.add(advancedRule.getId().toString());
                    matchRuleInfos.add(matchRuleInfo);
                }
            }
        }
        callRecord.setHitAdvanceIds(String.join(",", hitAdvancedIds));
        return matchRuleInfos;
    }


    public MatchRuleInfo matchAdvanceRule(CallRecord callRecord, AdvancedRules advancedRule, Map<String, List<AdvancedRuleCondition>> advancedRuleConditionMap,
                                          Map<String, List<String>> allAISemanticLabelRelationMap) {
        if (StringUtils.isNotBlank(advancedRule.getExcludeConditionUniqueIds())) {
            List<String> excludeConditionUniqueIdList = Arrays.asList(advancedRule.getExcludeConditionUniqueIds().split(","));
            if (CollectionUtils.isNotEmpty(excludeConditionUniqueIdList)) {
                boolean isMatch = advancedRuleConditionService.matchRuleCondition(callRecord, excludeConditionUniqueIdList, advancedRuleConditionMap, allAISemanticLabelRelationMap);
                if (isMatch) {
                    //满足排除条件
                    return null;
                }
            }
        }

        if (StringUtils.isNotBlank(advancedRule.getMatchConditionUniqueIds())) {
            List<String> matchConditionUniqueIdList = Arrays.asList(advancedRule.getMatchConditionUniqueIds().split(","));
            if (CollectionUtils.isNotEmpty(matchConditionUniqueIdList)) {
                boolean isMatch = advancedRuleConditionService.matchRuleCondition(callRecord, matchConditionUniqueIdList, advancedRuleConditionMap, allAISemanticLabelRelationMap);
                //不满足匹配条件
                if (!isMatch) {
                    return null;
                }
            }
        }
        return MatchRuleInfo.builder()
                .intentionLevelName(advancedRule.getIntentionLevelName())
                .intentionTagId(advancedRule.getIntentionTagId())
                .intentionTagName(advancedRule.getIntentionTagName())
                .build();
    }


    public MatchRuleInfo matchAdvanceRule(CallRecordForHumanMachine callRecord, AdvancedRules advancedRule,
                                          Map<String, List<AdvancedRuleCondition>> advancedRuleConditionMap,
                                          Map<String, List<String>> allAISemanticLabelRelationMap) {

        if (StringUtils.isNotBlank(advancedRule.getExcludeConditionUniqueIds())) {
            List<String> excludeConditionUniqueIdList = Arrays.asList(advancedRule.getExcludeConditionUniqueIds().split(","));
            if (CollectionUtils.isNotEmpty(excludeConditionUniqueIdList)) {
                boolean isExclude = advancedRuleConditionService.matchRuleCondition(callRecord, excludeConditionUniqueIdList, advancedRuleConditionMap, allAISemanticLabelRelationMap);
                if (isExclude) {
                    //满足排除条件
                    return null;
                }
            }
        }

        if (StringUtils.isNotBlank(advancedRule.getMatchConditionUniqueIds())) {
            List<String> matchConditionUniqueIdList = Arrays.asList(advancedRule.getMatchConditionUniqueIds().split(","));
            if (CollectionUtils.isNotEmpty(matchConditionUniqueIdList)) {
                boolean isMatch = advancedRuleConditionService.matchRuleCondition(callRecord, matchConditionUniqueIdList, advancedRuleConditionMap,
                        allAISemanticLabelRelationMap);
                //不满足匹配条件
                if (!isMatch) {
                    return null;
                }
            }
        }
        return MatchRuleInfo.builder()
                .intentionLevelName(advancedRule.getIntentionLevelName())
                .intentionTagId(advancedRule.getIntentionTagId())
                .intentionTagName(advancedRule.getIntentionTagName())
                .build();
    }


    public String combineTags(String s1, String s2) {
        Set<String> sSet = new HashSet<>();
        if (StringUtils.isNotBlank(s1)) {
            sSet.addAll(Arrays.asList(s1.split(",")));
        }
        if (StringUtils.isNotBlank(s2)) {
            sSet.addAll(Arrays.asList(s2.split(",")));
        }
        return String.join(",", sSet);
    }
}
