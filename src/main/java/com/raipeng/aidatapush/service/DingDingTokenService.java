package com.raipeng.aidatapush.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RefreshScope
public class DingDingTokenService {
    @Value("${ding.ding.token:6ec22ad2f69a8137d6ba02ae44b731b1d94b69e45dfebd61a89043bf95442c6d}")
    private String token;

    @Value("${ding.ding.token.exception:908f26ca9d3163d26968a84a09974d6ccf4db38947b11baa4fb42240c0ece924}")
    private String exceptionToken;
    @Value("${ding.ding.token.retry:165e9be7c09e46341a9d0d4a52ce28f6e97e2bc2589ef7738e47eaf91366375e}")
    private String retryToken;

    @Value("${qi.wei.token:715711ac-e8d6-4581-9b96-2b458849eb94}")
    private String qiWeiToken;

    public String getToken() {
        return token;
    }

    public String getExceptionToken() {
        return exceptionToken;
    }

    public String getRetryToken() {
        return retryToken;
    }

    public String getQiWeiToken(){
        return qiWeiToken;
    }

}
