package com.raipeng.aidatapush.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.raipeng.aidatacommon.enums.retry.WaitStrategyType;
import com.raipeng.aidatacommon.exception.CallFailedException;
import com.raipeng.aidatacommon.model.*;
import com.raipeng.aidatacommon.model.record.CallRecord;
import com.raipeng.aidatacommon.utils.CallbackConfigUtil;
import com.raipeng.aidatapush.annotation.CallRetry;
import com.raipeng.aidatapush.config.HotConfig;
import com.raipeng.aidatapush.controller.wrapper.ClueDataPushWrapper;
import com.raipeng.aidatapush.controller.wrapper.ClueManualDataPushWrapper;
import com.raipeng.aidatapush.repository.AntDigitalRequestHistoryRepository;
import com.raipeng.aidatapush.repository.AntDigitalSecretRequestHistoryRepository;
import com.raipeng.aidatapush.repository.AntRequestHistoryRepository;
import com.raipeng.aidatapush.repository.ClueImportHistoryRepository;
import com.raipeng.aidatapush.service.mq.producer.DialogPushProducer;
import com.raipeng.aidatapush.utils.PushThirdUtil;
import com.raipeng.aidatapush.utils.RaiYiEncryptionUtil;
import com.raipeng.aidatapush.utils.ThreadManagerUtil;
import com.raipeng.common.constant.CommonConstants;
import com.raipeng.common.enums.AccountOperatorType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.raipeng.aidatacommon.utils.AESUtils.AESEncode;


@Slf4j
@Service
@RefreshScope
public class HandlerPushThirdService {
    public static final String CLUE_PUSH_THIRD_MAP = "CLUE_PUSH_THIRD_MAP::";

    @Autowired
    private HotConfig hotConfig;

    @Autowired
    private AdminService adminService;

    @Autowired
    private RequestLogService requestLogService;

    @Autowired
    private TenantSecretsService tenantSecretsService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private HandlerCluePushThirdService handlerCluePushThirdService;

    @Autowired
    private DialogPushProducer dialogPushProducer;

    @Autowired
    private SupplyLineService supplyLineService;

    @Autowired
    private HandleAntsCallbackService handleAntsCallbackService;

    @Autowired
    private AntRequestHistoryRepository antRequestHistoryRepository;

    @Autowired
    private AntDigitalRequestHistoryRepository antDigitalRequestHistoryRepository;

    @Autowired
    private AntDigitalSecretRequestHistoryRepository antDigitalSecretRequestHistoryRepository;

    @Autowired
    private ClueImportHistoryRepository clueImportHistoryRepository;

    public void pushResultToAntsForAI(List<CallRecord> callRecordList, String accountId, boolean isDelay) {
        Admin admin = adminService.findById(Long.valueOf(accountId));
        AntAdmin antAdmin = adminService.findByAdminId(admin.getId());
        if (antAdmin == null) {
            log.info("Exception---> 蚂蚁账号分类没有配置，admin：{}", admin);
            return;
        }

        List<AccountOperatorType> callBackRange = AccountOperatorType.getEnumsList(admin.getCallBackRange());
        if (CollectionUtils.isEmpty(callBackRange) || !callBackRange.contains(AccountOperatorType.PURE_AI)) {
            return;
        }

        List<String> phoneRecordIds = callRecordList.stream().map(CallRecord::getPhoneRecordId).collect(Collectors.toList());

        if (Objects.equals(antAdmin.getAccountIdentifier(), "AntDigital")) { //蚂蚁2
            List<AntDigitalRequestHistory> antRequestHistories = antDigitalRequestHistoryRepository.findAllByPhoneRecordIdIn(phoneRecordIds);
            Map<String, AntDigitalRequestHistory> antRequestMap = antRequestHistories.stream().collect(Collectors.toMap(AntDigitalRequestHistory::getPhoneRecordId, Function.identity()));
            for (CallRecord callRecord : callRecordList) {
                ThreadManagerUtil.executeCallbackPool.execute(() -> {
                    handleAntsCallbackService.pushResultToAntsSecond(callRecord, admin, antRequestMap);
                });
            }
        } else if (Objects.equals(antAdmin.getAccountIdentifier(), "AntDigitalSecret")) { //蚂蚁3
            List<AntDigitalSecretRequestHistory> antRequestHistories = antDigitalSecretRequestHistoryRepository.findAllByPhoneRecordIdIn(phoneRecordIds);
            Map<String, AntDigitalSecretRequestHistory> antRequestMap = antRequestHistories.stream().collect(Collectors.toMap(AntDigitalSecretRequestHistory::getPhoneRecordId, Function.identity()));
            for (CallRecord callRecord : callRecordList) {
                ThreadManagerUtil.executeCallbackPool.execute(() -> {
                    handleAntsCallbackService.pushResultToAntsThird(callRecord, admin, antRequestMap);
                });
            }
        } else if (Objects.equals(antAdmin.getAccountIdentifier(), "Ant")) { //蚂蚁1
            List<AntRequestHistory> antRequestHistories = antRequestHistoryRepository.findAllByPhoneRecordIdIn(phoneRecordIds);
            Map<String, AntRequestHistory> antRequestMap = antRequestHistories.stream().collect(Collectors.toMap(AntRequestHistory::getPhoneRecordId, Function.identity()));
            for (CallRecord callRecord : callRecordList) {
                ThreadManagerUtil.executeCallbackPool.execute(() -> {
                    handleAntsCallbackService.pushResultToAnts(callRecord, admin, antRequestMap, isDelay);
                });
            }
        }

    }

    public void pushResultToAntsForAIMAN(ClueDataPushWrapper wrapper, List<CallRecordForHumanMachine> callRecordList, String tenantId, String accountId) {
        Admin admin;
        if (wrapper == null) { //人工未介入
            admin = adminService.findById(Long.valueOf(accountId));
        } else { //人工介入
            admin = wrapper.getAdmin();
            callRecordList = Collections.singletonList(wrapper.getRecord());
        }
        AntAdmin antAdmin = adminService.findByAdminId(admin.getId());
        if (antAdmin == null) {
            log.info("Exception---> 蚂蚁账号分类没有配置，admin：{}", admin);
            return;
        }
        List<AccountOperatorType> callBackRange = AccountOperatorType.getEnumsList(admin.getCallBackRange());
        if (CollectionUtils.isEmpty(callBackRange) || !callBackRange.contains(AccountOperatorType.HUMAN_MACHINE)) {
            return;
        }

        List<String> phoneRecordIds = callRecordList.stream().map(CallRecordForHumanMachine::getPhoneRecordId).collect(Collectors.toList());
        if (Objects.equals(antAdmin.getAccountIdentifier(), "AntDigitalSecret")) { //蚂蚁3
            List<AntDigitalSecretRequestHistory> antRequestHistories = antDigitalSecretRequestHistoryRepository.findAllByPhoneRecordIdIn(phoneRecordIds);
            Map<String, AntDigitalSecretRequestHistory> antRequestMap = antRequestHistories.stream().collect(Collectors.toMap(AntDigitalSecretRequestHistory::getPhoneRecordId, Function.identity()));
            for (CallRecordForHumanMachine callRecord : callRecordList) {
                //蚂蚁3号码是密文
                callRecord.setPlainPhone(callRecord.getPhone());
                ThreadManagerUtil.executeCallbackPool.execute(() -> {
                    handleAntsCallbackService.pushResultToAntsForAIManThird(callRecord, admin, antRequestMap, wrapper);
                });
            }
        } else if (Objects.equals(antAdmin.getAccountIdentifier(), "AntDigital")) { //蚂蚁2
            List<AntDigitalRequestHistory> antRequestHistories = antDigitalRequestHistoryRepository.findAllByPhoneRecordIdIn(phoneRecordIds);
            Map<String, AntDigitalRequestHistory> antRequestMap = antRequestHistories.stream().collect(Collectors.toMap(AntDigitalRequestHistory::getPhoneRecordId, Function.identity()));
            for (CallRecordForHumanMachine callRecord : callRecordList) {
                if (callRecord.getIsForEncryptionPhones() != null && callRecord.getIsForEncryptionPhones()) {
                    callRecord.setPlainPhone(callRecord.getPhone());
                } else {
                    JSONObject decryptionResultBatch = null;
                    try {
                        decryptionResultBatch = RaiYiEncryptionUtil.getDecryptionResultBatch(callRecord.getPhone());
                    } catch (Exception e) {
                        log.error("Exception===号码解析失败，请重新提交");
                    }
                    if (decryptionResultBatch == null) {
                        log.error("Exception===号码解析失败，请重新提交");
                        callRecord.setPlainPhone(callRecord.getPhone());
                    } else {
                        String plainPhone = decryptionResultBatch.getString(callRecord.getPhone());
                        if (plainPhone!=null && plainPhone.length() < 7) {
                            log.info("plainPhone is null or short, continue+++++++");
                            continue;
                        } else {
                            callRecord.setPlainPhone(plainPhone);
                        }
                    }
                }
                ThreadManagerUtil.executeCallbackPool.execute(() -> {
                    handleAntsCallbackService.pushResultToAntsForAIManSecond(callRecord, admin, antRequestMap, wrapper);
                });
            }
        }
    }

    @CallRetry(retryTimes = 5, waitStrategy = WaitStrategyType.INCREMENTING_WAIT_STRATEGY)
    public void pushResultToThirdForAI(List<CallRecord> callRecordList, String tenantId, String accountId, boolean isDelay){
        TenantSecrets tenantSecrets = tenantSecretsService.findFirstByTenantId(tenantId);
        Admin admin = adminService.findById(Long.valueOf(accountId));
        Set<String> callbackSupplyLineNumbers = supplyLineService.getCallbackSupplyLineNumbers();

        List<AccountOperatorType> callBackRange = AccountOperatorType.getEnumsList(admin.getCallBackRange());
        String callbackUrl = admin.getCallbackUrl();
        String account = admin.getAccount();
        if (CollectionUtils.isEmpty(callBackRange) || !callBackRange.contains(AccountOperatorType.PURE_AI)) {
            return;
        }
        if (StringUtils.isBlank(callbackUrl)) {
            log.error("Exception=>账号:{}未配置回调地址！", account);
            throw new CallFailedException("未填写回调地址,手机号推送8848报错", "call_record_push");
        }
        if(tenantSecrets == null){
            log.error("Exception=>账号:{}AK-CK未配置！", account);
            throw new CallFailedException("AK-CK未配置,手机号推送8848报错", "call_record_push");
        }

        JSONArray textList = new JSONArray();
        for(CallRecord callRecord : callRecordList){
            String plainPhone = callRecord.getPlainPhone();
            if (plainPhone!=null && plainPhone.length() < 7) {
                continue;
            }
            JSONObject callRecordMap = new JSONObject();
            if (!CallbackConfigUtil.shouldPushByCallStatus(admin.getCallbackStatusConfig(), callRecord.getCallStatus())) {
                continue;
            }
            callRecordMap = CallbackConfigUtil.addFields(callRecord, admin.getCallbackFieldConfig(), callRecordMap);

            callRecordMap.put("callOutTime", callRecord.getCallOutTime());
            callRecordMap.put("callStatus", callRecord.getCallStatus());
            callRecordMap.put("contactTime", callRecord.getContactTime());
            callRecordMap.put("customerMobile", plainPhone==null?callRecord.getPhone():plainPhone);
            callRecordMap.put("id", callRecord.getId());
            callRecordMap.put("ruleName", StringUtils.isEmpty(callRecord.getIntentionClass()) ? "其他" : callRecord.getIntentionClass());
            callRecordMap.put("talkTime", callRecord.getCallDurationSec());
            callRecordMap.put("talkTimeEnd", callRecord.getTalkTimeEnd());
            callRecordMap.put("talkTimeStart", callRecord.getTalkTimeStart());
            callRecordMap.put("taskId", callRecord.getTaskId());
            callRecordMap.put("interactNum", callRecord.getCycleCount());
            callRecordMap.put("keypress", callRecord.getIntentionLabels());
            callRecordMap.put("sipCallId", callRecord.getSipCallId());
            callRecordMap.put("ifUpdate", isDelay?1:0);
            String supplyLineNumber = callRecord.getLineId();
            if (StringUtils.isNotEmpty(supplyLineNumber) && callbackSupplyLineNumbers.contains(supplyLineNumber)) {
                callRecordMap.put("tenantLineNumber", supplyLineNumber);
            } else {
                callRecordMap.put("tenantLineNumber", callRecord.getMerchantLineCode());
            }
            if(StringUtils.isNotBlank(callRecord.getWholeAudioFileUrl())){
                callRecordMap.put("voiceUrl", callRecord.getWholeAudioFileUrl());
            }
            textList.add(callRecordMap);
            if(StringUtils.isNotEmpty(admin.getIsPushDialogContent()) && admin.getIsPushDialogContent().equals("Push") && callRecord.getCallStatus().equals("7")){
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("customerMobile",plainPhone==null?callRecord.getPhone():plainPhone);
                jsonObject.put("taskId", callRecord.getTaskId());
                jsonObject.put("ifUpdate", 1);
                jsonObject.put("aesKey", tenantSecrets.getAesKey());
                jsonObject.put("salt", tenantSecrets.getSalt());
                jsonObject.put("callBackUrl", admin.getCallbackUrl());
                jsonObject.put("callId", callRecord.getCallId());
                dialogPushProducer.send(jsonObject);
            }
        }
        if(CollectionUtils.isEmpty(textList)){
            return;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("size",callRecordList.size());
        map.put("text", AESEncode(tenantSecrets.getAesKey(), textList.toJSONString()));
        map = new TreeMap<>(map);
        String jsonStr = JSON.toJSONString(map, SerializerFeature.SortField);
        jsonStr += tenantSecrets.getSalt();
        String sign = DigestUtils.md5Hex(jsonStr);
        map.put("sign", sign);
        map.put("ifUpdate", isDelay ? 1 : 0);
        Map<String, Object> res = null;
        LocalDateTime startTime = LocalDateTime.now();
        LocalDateTime endTime = LocalDateTime.now();
        // 临时注释代码
        try {
            startTime = LocalDateTime.now();
            res = restTemplate.postForObject(admin.getCallbackUrl(), map, Map.class);
            endTime = LocalDateTime.now();
        } catch (Exception ex){
            log.error("Exception 手机号推送8848报错！！");
            DingDingService.dingDingSendMsgException("手机号回调8848报错");
            log.error("Exception push third error:{}", ex.getMessage());
            ex.printStackTrace();
            throw new CallFailedException(ex, "手机号推送8848报错", "call_record_push");
        }finally {
            requestLogService.addLog(textList.toJSONString() + "[" + admin.getCallbackUrl() + "]", res, "call_record_push", startTime, endTime);
        }
    }

    @CallRetry(retryTimes = 5, waitStrategy = WaitStrategyType.INCREMENTING_WAIT_STRATEGY)
    public void pushResultToThirdForAIMan(List<CallRecordForHumanMachine> callRecordList, String tenantId, String accountId, boolean isDelay){
        TenantSecrets tenantSecrets = tenantSecretsService.findFirstByTenantId(tenantId);
        Admin admin = adminService.findById(Long.valueOf(accountId));
        Set<String> callbackSupplyLineNumbers = supplyLineService.getCallbackSupplyLineNumbers();

        List<AccountOperatorType> callBackRange = AccountOperatorType.getEnumsList(admin.getCallBackRange());
        String callbackUrl = admin.getCallbackUrl();
        String account = admin.getAccount();
        if (CollectionUtils.isEmpty(callBackRange) || !callBackRange.contains(AccountOperatorType.HUMAN_MACHINE)) {
            return;
        }
        if (StringUtils.isBlank(callbackUrl)) {
            log.error("Exception=>账号:{}未配置回调地址！", account);
            throw new CallFailedException("未填写回调地址,手机号推送8848报错", "call_record_push");
        }
        if(tenantSecrets == null){
            log.error("Exception=>账号:{}AK-CK未配置！", account);
            throw new CallFailedException("AK-CK未配置,手机号推送8848报错", "call_record_push");
        }

        JSONArray textList = new JSONArray();
        for(CallRecordForHumanMachine callRecord : callRecordList){
            String plainPhone = callRecord.getPlainPhone();
            if (plainPhone!=null && plainPhone.length() < 7) {
                continue;
            }
            JSONObject callRecordMap = new JSONObject();
            if (!CallbackConfigUtil.shouldPushByCallStatus(admin.getCallbackStatusConfig(), callRecord.getCallStatus())) {
                continue;
            }
            callRecordMap = CallbackConfigUtil.addFields(callRecord, admin.getCallbackFieldConfig(), callRecordMap);
            callRecordMap.put("callOutTime", callRecord.getCallOutTime());
            callRecordMap.put("callStatus", callRecord.getCallStatus());
            callRecordMap.put("contactTime", callRecord.getContactTime());
            callRecordMap.put("customerMobile", plainPhone==null?callRecord.getPhone():plainPhone);
            callRecordMap.put("id", callRecord.getId());
            callRecordMap.put("ruleName", StringUtils.isEmpty(callRecord.getIntentionClass()) ? "其他" : callRecord.getIntentionClass());
            callRecordMap.put("talkTime", callRecord.getCallDurationSec());
            callRecordMap.put("talkTimeEnd", callRecord.getTalkTimeEnd());
            callRecordMap.put("talkTimeStart", callRecord.getTalkTimeStart());
            callRecordMap.put("taskId", callRecord.getTaskId());
            callRecordMap.put("interactNum", callRecord.getCycleCount());
            callRecordMap.put("keypress", callRecord.getIntentionLabels());
            callRecordMap.put("sipCallId", callRecord.getSipCallId());
            callRecordMap.put("ifUpdate", isDelay?1:0);
            String supplyLineNumber = callRecord.getLineId();
            if (StringUtils.isNotEmpty(supplyLineNumber) && callbackSupplyLineNumbers.contains(supplyLineNumber)) {
                callRecordMap.put("tenantLineNumber", supplyLineNumber);
            } else {
                callRecordMap.put("tenantLineNumber", callRecord.getMerchantLineCode());
            }
            if(StringUtils.isNotBlank(callRecord.getWholeAudioFileUrl())){
                callRecordMap.put("voiceUrl", callRecord.getWholeAudioFileUrl());
            }
            textList.add(callRecordMap);
            if(StringUtils.isNotEmpty(admin.getIsPushDialogContent()) && admin.getIsPushDialogContent().equals("Push") && callRecord.getCallStatus().equals("7")){
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("customerMobile",plainPhone==null?callRecord.getPhone():plainPhone);
                jsonObject.put("taskId", callRecord.getTaskId());
                jsonObject.put("ifUpdate", 1);
                jsonObject.put("aesKey", tenantSecrets.getAesKey());
                jsonObject.put("salt", tenantSecrets.getSalt());
                jsonObject.put("callBackUrl", admin.getCallbackUrl());
                jsonObject.put("callId", callRecord.getCallId());
                dialogPushProducer.send(jsonObject);
            }
        }
        if(CollectionUtils.isEmpty(textList)){
            return;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("size",callRecordList.size());
        map.put("text", AESEncode(tenantSecrets.getAesKey(), textList.toJSONString()));
        map = new TreeMap<>(map);
        String jsonStr = JSON.toJSONString(map, SerializerFeature.SortField);
        jsonStr += tenantSecrets.getSalt();
        String sign = DigestUtils.md5Hex(jsonStr);
        map.put("sign", sign);
        map.put("ifUpdate", isDelay ? 1 : 0);
        Map<String, Object> res = null;
        LocalDateTime startTime = LocalDateTime.now();
        LocalDateTime endTime = LocalDateTime.now();
        try {
            startTime = LocalDateTime.now();
            res = restTemplate.postForObject(callbackUrl, map, Map.class);
            endTime = LocalDateTime.now();
        } catch (Exception ex){
            log.error("Exception 手机号推送8848报错！！");
            DingDingService.dingDingSendMsgException("手机号回调8848报错");
            log.error("Exception push third error:{}", ex.getMessage());
            ex.printStackTrace();
            throw new CallFailedException(ex, "手机号推送8848报错", "call_record_push");
        }finally {
            requestLogService.addLog(textList.toJSONString() + "[" + callbackUrl + "]", res, "call_record_push", startTime, endTime);
        }
    }

    @CallRetry(retryTimes = 5, waitStrategy = WaitStrategyType.INCREMENTING_WAIT_STRATEGY)
    public void pushResultToThirdForHuman(List<CallRecordForManualDirect> callRecordList, String groupId, String tenantId, String accountId) {
        TenantSecrets tenantSecrets = tenantSecretsService.findFirstByTenantId(tenantId);
        Admin admin = adminService.findById(Long.valueOf(accountId));
        Set<String> callbackSupplyLineNumbers = supplyLineService.getCallbackSupplyLineNumbers();

        List<AccountOperatorType> callBackRange = AccountOperatorType.getEnumsList(admin.getCallBackRange());
        String callbackUrl = admin.getCallbackUrl();
        String account = admin.getAccount();
        if (CollectionUtils.isEmpty(callBackRange) || !callBackRange.contains(AccountOperatorType.MANUAL_DIRECT)) {
            return;
        }
        if (StringUtils.isBlank(callbackUrl)) {
            log.error("Exception=>账号:{}未配置回调地址！", account);
            throw new CallFailedException("未填写回调地址,手机号推送8848报错", "call_record_push");
        }
        if(tenantSecrets == null){
            log.error("Exception=>账号:{}AK-CK未配置！", account);
            throw new CallFailedException("AK-CK未配置,手机号推送8848报错", "call_record_push");
        }
        JSONArray textList = new JSONArray();
        for(CallRecordForManualDirect callRecord : callRecordList){
            JSONObject callRecordMap = new JSONObject();
            if (!CallbackConfigUtil.shouldPushByCallStatus(admin.getCallbackStatusConfig(), callRecord.getCallStatus())) {
                continue;
            }
            callRecordMap = CallbackConfigUtil.addFields(callRecord, admin.getCallbackFieldConfig(), callRecordMap);
            callRecordMap.put("callOutTime", callRecord.getCallOutTime());
            callRecordMap.put("callStatus", callRecord.getCallStatus());
            callRecordMap.put("contactTime", callRecord.getContactTime());
            String batchID = clueImportHistoryRepository.findAllByAccountAndGroupId(groupId, callRecord.getPhone());
            String plainPhone = callRecord.getPlainPhone();
            if (plainPhone!=null && plainPhone.length() < 7) {
                continue;
            }

            callRecordMap.put("customerMobile", plainPhone==null?callRecord.getPhone():plainPhone);
            callRecordMap.put("id", callRecord.getId());
            callRecordMap.put("batchID", batchID);
            callRecordMap.put("talkTime", callRecord.getCallDurationSec());
            callRecordMap.put("callSeatId", callRecord.getCallSeatId());
            callRecordMap.put("talkTimeEnd", callRecord.getTalkTimeEnd());
            callRecordMap.put("talkTimeStart", callRecord.getTalkTimeStart());
            callRecordMap.put("sipCallId", callRecord.getSipCallId());
            String supplyLineNumber = callRecord.getLineId();
            if (StringUtils.isNotEmpty(supplyLineNumber) && callbackSupplyLineNumbers.contains(supplyLineNumber)) {
                callRecordMap.put("tenantLineNumber", supplyLineNumber);
            } else {
                callRecordMap.put("tenantLineNumber", callRecord.getMerchantLineCode());
            }
            callRecordMap.put("ifUpdate", 0);
            if(StringUtils.isNotBlank(callRecord.getWholeAudioFileUrl())){
                callRecordMap.put("voiceUrl", callRecord.getWholeAudioFileUrl());
            }
            textList.add(callRecordMap);
            if(StringUtils.isNotEmpty(admin.getIsPushDialogContent()) && admin.getIsPushDialogContent().equals("Push") && callRecord.getCallStatus().equals("7")){
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("customerMobile",plainPhone==null?callRecord.getPhone():plainPhone);
                jsonObject.put("ifUpdate", 1);
                jsonObject.put("aesKey", tenantSecrets.getAesKey());
                jsonObject.put("salt", tenantSecrets.getSalt());
                jsonObject.put("callBackUrl", admin.getCallbackUrl());
                jsonObject.put("callId", callRecord.getCallId());
                dialogPushProducer.send(jsonObject);
            }
        }
        if(CollectionUtils.isEmpty(textList)){
            return;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("size",callRecordList.size());
        map.put("text", AESEncode(tenantSecrets.getAesKey(), textList.toJSONString()));
        map = new TreeMap<>(map);
        String jsonStr = JSON.toJSONString(map, SerializerFeature.SortField);
        jsonStr += tenantSecrets.getSalt();
        String sign = DigestUtils.md5Hex(jsonStr);
        map.put("sign", sign);
        Map<String, Object> res = null;
        LocalDateTime startTime = LocalDateTime.now();
        LocalDateTime endTime = LocalDateTime.now();
        try {
            startTime = LocalDateTime.now();
            res = restTemplate.postForObject(callbackUrl, map, Map.class);
            endTime = LocalDateTime.now();
        } catch (Exception ex){
            log.error("Exception 手机号推送8848报错！！");
            DingDingService.dingDingSendMsgException("手机号回调8848报错");
            log.error("Exception push third error:{}", ex.getMessage());
            ex.printStackTrace();
            throw new CallFailedException(ex, "手机号推送8848报错", "call_record_push");
        }finally {
            requestLogService.addLog(textList.toJSONString() + "[" + callbackUrl + "]", res, "call_record_push", startTime, endTime);
        }
    }

    @CallRetry(retryTimes = 5, waitStrategy = WaitStrategyType.INCREMENTING_WAIT_STRATEGY)
    public void pushManualIntentionToThird(ClueDataPushWrapper wrapper) {
        String intentionLabels = wrapper.getIntentionLabels();
        CallRecordForHumanMachine record = wrapper.getRecord();
        Admin admin = wrapper.getAdmin();
        Long tenantId = admin.getTenantId();
        TenantSecrets tenantSecrets = tenantSecretsService.findFirstByTenantId(String.valueOf(tenantId));
        Set<String> callbackSupplyLineNumbers = supplyLineService.getCallbackSupplyLineNumbers();

        List<AccountOperatorType> callBackRange = AccountOperatorType.getEnumsList(admin.getCallBackRange());
        String callbackUrl = admin.getCallbackUrl();
        String account = admin.getAccount();
        if (CollectionUtils.isEmpty(callBackRange) || !callBackRange.contains(AccountOperatorType.HUMAN_MACHINE)) {
            return;
        }
        if (StringUtils.isBlank(callbackUrl)) {
            log.error("Exception=>账号:{}未配置回调地址！", account);
            throw new CallFailedException("未填写回调地址,手机号推送8848报错", "call_record_push");
        }
        if(tenantSecrets == null){
            log.error("Exception=>账号:{}AK-CK未配置！", account);
            throw new CallFailedException("AK-CK未配置,手机号推送8848报错", "call_record_push");
        }

        JSONObject decryptionResultBatch = RaiYiEncryptionUtil.getDecryptionResultBatch(record.getPhone());
        if (decryptionResultBatch == null) {
            throw new CallFailedException("号码解析失败，请重新提交");
        }
        String plainPhone = decryptionResultBatch.getString(record.getPhone());

        String clueCallBackUrl = admin.getClueCallbackUrl();
        if (StringUtils.isNotBlank(clueCallBackUrl)) {
            int timeSlot = PushThirdUtil.getTimeSlot(hotConfig.getClueTimeMinuteInterval());
            String key = PushThirdUtil.getClueBackKey(tenantSecrets, clueCallBackUrl);
            RMap<String, String> redisMap = redissonClient.getMap(CLUE_PUSH_THIRD_MAP + timeSlot);
            String clueDataPushWrapperJsonList = redisMap.computeIfAbsent(key, k -> JSONObject.toJSONString(new ArrayList<>()));
            List<ClueDataPushWrapper> clueDataPushWrapperList = JSONObject.parseObject(clueDataPushWrapperJsonList, new TypeReference<List<ClueDataPushWrapper>>(){});
            clueDataPushWrapperList.add(wrapper);
            redisMap.put(key, JSONObject.toJSONString(clueDataPushWrapperList));
        }

        JSONArray textList = new JSONArray();
        JSONObject callRecordMap = new JSONObject();

        callRecordMap.put("callOutTime", record.getCallOutTime());
        callRecordMap.put("callStatus", record.getCallStatus());
        callRecordMap.put("contactTime", record.getContactTime());

        callRecordMap.put("customerMobile", plainPhone==null?record.getPhone():plainPhone);
        callRecordMap.put("id", record.getId());
        callRecordMap.put("ruleName", wrapper.getIntentionClass());
        callRecordMap.put("talkTime", record.getCallDurationSec());
        callRecordMap.put("talkTimeEnd", record.getTalkTimeEnd());
        callRecordMap.put("talkTimeStart", record.getTalkTimeStart());
        callRecordMap.put("taskId", record.getTaskId());
        callRecordMap.put("interactNum", record.getCycleCount());
        callRecordMap.put("keypress", wrapper.getIntentionLabels());
        callRecordMap.put("sipCallId", record.getSipCallId());
        callRecordMap.put("manualIntentionClass", record.getManualIntentionClass());
        callRecordMap.put("manualIntentionLabels", record.getManualIntentionLabels());
        callRecordMap.put("isTransToCallSeat", record.getIsTransToCallSeat());
        callRecordMap.put("startAnswerTime", record.getStartAnswerTime());
        callRecordMap.put("endAnswerTime", record.getEndAnswerTime());
        callRecordMap.put("callSeatId", record.getCallSeatId());
        callRecordMap.put("callSeatName", wrapper.getCallSeatName());
        callRecordMap.put("formRecordDTO", wrapper.getFormRecordDTO());
        callRecordMap.put("followUpStatus", wrapper.getFollowUpStatus());
        callRecordMap.put("followUpNote", wrapper.getFollowUpNote());
        if (StringUtils.isNotBlank(intentionLabels) && intentionLabels.contains(CommonConstants.SPECIAL_SEND_MESSAGE_TAG)) {
            callRecordMap.put("ifUpdate", 1);
        } else {
            callRecordMap.put("ifUpdate", 0);
        }
        String supplyLineNumber = record.getLineId();
        if (StringUtils.isNotEmpty(supplyLineNumber) && callbackSupplyLineNumbers.contains(supplyLineNumber)) {
            callRecordMap.put("tenantLineNumber", supplyLineNumber);
        } else {
            callRecordMap.put("tenantLineNumber", record.getMerchantLineCode());
        }
        if(StringUtils.isNotBlank(record.getWholeAudioFileUrl())){
            callRecordMap.put("voiceUrl", record.getWholeAudioFileUrl());
        }
        textList.add(callRecordMap);
        if(StringUtils.isNotEmpty(wrapper.getIsPushDialogContent()) && wrapper.getIsPushDialogContent().equals("Push") && record.getCallStatus().equals("7")){
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("customerMobile",plainPhone==null?record.getPhone():plainPhone);
            jsonObject.put("taskId", record.getTaskId());
            jsonObject.put("ifUpdate", 1);
            jsonObject.put("aesKey", tenantSecrets.getAesKey());
            jsonObject.put("salt", tenantSecrets.getSalt());
            jsonObject.put("callBackUrl", admin.getCallbackUrl());
            jsonObject.put("callId", record.getCallId());
            dialogPushProducer.send(jsonObject);
        }

        Map<String, Object> map = new HashMap<>();
        map.put("size", 1);
        map.put("text", AESEncode(tenantSecrets.getAesKey(), textList.toJSONString()));
        map = new TreeMap<>(map);
        String jsonStr = JSON.toJSONString(map, SerializerFeature.SortField);
        jsonStr += tenantSecrets.getSalt();
        String sign = DigestUtils.md5Hex(jsonStr);
        map.put("sign", sign);
        Map<String, Object> res = null;
        LocalDateTime startTime = LocalDateTime.now();
        LocalDateTime endTime = LocalDateTime.now();
        try {
            startTime = LocalDateTime.now();
            res = restTemplate.postForObject(callbackUrl, map, Map.class);
            endTime = LocalDateTime.now();
        } catch (Exception ex){
            log.error("Exception 手机号推送8848报错！！");
            log.error("Exception push third error:{}", ex.getMessage());
            throw new CallFailedException(ex, "手机号推送8848报错", "call_record_push");
        }finally {
            requestLogService.addLog(textList.toJSONString() + "[" + callbackUrl + "]", res, "call_record_push", startTime, endTime);
        }
    }

    public void pushClueToThird() {
        List<Integer> timeSlotsNeedPush = PushThirdUtil.getTimeSlotsNeedPush(hotConfig.getClueTimeMinuteInterval(), hotConfig.getClueWaitTimeSlotInterval());
        for (Integer slotsNeedPush : timeSlotsNeedPush) {
            RMap<String, String> map = redissonClient.getMap(CLUE_PUSH_THIRD_MAP + slotsNeedPush);
            if (map.isExists()) {
                HashMap<String, String> stringStringHashMap = new HashMap<>(map);
                stringStringHashMap.forEach((k, v) -> {
                    String[] info = k.split("_");
                    List<ClueDataPushWrapper> clueDataPushWrapperList = JSONObject.parseObject(v, new TypeReference<List<ClueDataPushWrapper>>(){});
                    handlerCluePushThirdService.handleClueDataPushWrappers(info[0], info[1], info[2], clueDataPushWrapperList);
                    map.remove(k);
                });
            }
        }
    }

    @CallRetry(retryTimes = 5, waitStrategy = WaitStrategyType.INCREMENTING_WAIT_STRATEGY)
    public void pushClueManualDataToThird(ClueManualDataPushWrapper wrapper){

        Admin admin = wrapper.getAdmin();
        Long tenantId = admin.getTenantId();
        TenantSecrets tenantSecrets = tenantSecretsService.findFirstByTenantId(String.valueOf(tenantId));
        Set<String> callbackSupplyLineNumbers = supplyLineService.getCallbackSupplyLineNumbers();

        List<AccountOperatorType> callBackRange = AccountOperatorType.getEnumsList(admin.getCallBackRange());
        String callbackUrl = admin.getCallbackUrl();
        String account = admin.getAccount();
        if (CollectionUtils.isEmpty(callBackRange) || !callBackRange.contains(AccountOperatorType.MANUAL_DIRECT)) {
            return;
        }
        if (StringUtils.isBlank(callbackUrl)) {
            log.error("Exception=>账号:{}未配置回调地址！", account);
            throw new CallFailedException("未填写回调地址,手机号推送8848报错", "call_record_push");
        }
        if(tenantSecrets == null){
            log.error("Exception=>账号:{}AK-CK未配置！", account);
            throw new CallFailedException("AK-CK未配置,手机号推送8848报错", "call_record_push");
        }

        String intentionClass = wrapper.getIntentionClass();
        String batchID = wrapper.getBatchID();
        String intentionLabels = wrapper.getIntentionLabels();
        CallRecordForManualDirect record = wrapper.getRecord();

        JSONObject decryptionResultBatch = RaiYiEncryptionUtil.getDecryptionResultBatch(record.getPhone());
        if (decryptionResultBatch == null) {
            throw new CallFailedException("号码解析失败，请重新提交");
        }
        String plainPhone = decryptionResultBatch.getString(record.getPhone());

        JSONArray textList = new JSONArray();
        JSONObject callRecordMap = new JSONObject();

        callRecordMap.put("callOutTime", record.getCallOutTime());
        callRecordMap.put("callStatus", record.getCallStatus());
        callRecordMap.put("contactTime", record.getContactTime());
        callRecordMap.put("batchID", batchID);

        callRecordMap.put("customerMobile", plainPhone==null?record.getPhone():plainPhone);
        callRecordMap.put("id", record.getId());
        callRecordMap.put("ruleName", intentionClass);
        // ! 注意此处的CallDuration数据为ai-speech中传过来的，是毫秒单位，正确的，使用该方法处理为对账用的秒级。
        // ! 前面几处方法中的外呼流程中的CallDuration数据是秒级的（历史遗留问题）
        callRecordMap.put("talkTime", getCalculateSeconds(record.getCallDuration()));
        callRecordMap.put("talkTimeEnd", record.getTalkTimeEnd());
        callRecordMap.put("talkTimeStart", record.getTalkTimeStart());
        callRecordMap.put("sipCallId", record.getSipCallId());

        callRecordMap.put("callSeatId", record.getCallSeatId());
        callRecordMap.put("callSeatName", wrapper.getCallSeatName());
        callRecordMap.put("formRecordDTO", wrapper.getFormRecordDTO());
        callRecordMap.put("followUpStatus", wrapper.getFollowUpStatus());
        callRecordMap.put("followUpNote", wrapper.getFollowUpNote());
        String supplyLineNumber = record.getLineId();
        if (StringUtils.isNotEmpty(supplyLineNumber) && callbackSupplyLineNumbers.contains(supplyLineNumber)) {
            callRecordMap.put("tenantLineNumber", supplyLineNumber);
        } else {
            callRecordMap.put("tenantLineNumber", record.getMerchantLineCode());
        }
        callRecordMap.put("ifUpdate", 0);

        if(StringUtils.isNotBlank(record.getWholeAudioFileUrl())){
            callRecordMap.put("voiceUrl", record.getWholeAudioFileUrl());
        }
        textList.add(callRecordMap);

        Map<String, Object> map = new HashMap<>();
        map.put("size", 1);
        map.put("text", AESEncode(tenantSecrets.getAesKey(), textList.toJSONString()));
        map = new TreeMap<>(map);
        String jsonStr = JSON.toJSONString(map, SerializerFeature.SortField);
        jsonStr += tenantSecrets.getSalt();
        String sign = DigestUtils.md5Hex(jsonStr);
        map.put("sign", sign);

        Map<String, Object> res = null;
        LocalDateTime startTime = LocalDateTime.now();
        LocalDateTime endTime = LocalDateTime.now();
        try {
            startTime = LocalDateTime.now();
            res = restTemplate.postForObject(callbackUrl, map, Map.class);
            endTime = LocalDateTime.now();
        } catch (Exception ex){
            log.error("Exception 手机号推送8848报错！！");
            log.error("Exception push third error:{}", ex.getMessage());
            throw new CallFailedException(ex, "手机号推送8848报错", "call_record_push");
        }finally {
            requestLogService.addLog(textList.toJSONString() + "[" + callbackUrl + "]", res, "call_record_push", startTime, endTime);
        }
    }

    /**
     *
     * @param callDuration 毫秒单位
     * @return 对账用的秒级 小于100s约去; 大于等于100ms视为一秒
     */
    private static Integer getCalculateSeconds(Integer callDuration) {
        if (callDuration == null) {
            return 0;
        } else {
            if (callDuration % 1000 < 100) {
                return callDuration/1000;
            } else {
                return (int)Math.ceil(((double)callDuration)/1000);
            }
        }
    }
}
