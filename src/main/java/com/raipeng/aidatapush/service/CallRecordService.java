package com.raipeng.aidatapush.service;

import com.raipeng.aidatacommon.enums.CallStatusEnum;
import com.raipeng.aidatacommon.model.dto.CallRecordResult;
import com.raipeng.aidatacommon.model.record.LandingPageCallRecord;
import com.raipeng.aidatapush.repository.CallRecordForHumanMachineRepository;
import com.raipeng.aidatapush.repository.CallRecordForManualDirectRepository;
import com.raipeng.aidatapush.repository.LandingPageCallRecordRepository;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;


@Slf4j
@Service
public class CallRecordService {
    @Autowired
    private CallRecordMultiService callRecordMultiService;

    @Autowired
    private CallRecordForHumanMachineRepository callRecordForHumanMachineRepository;

    @Autowired
    private CallRecordForManualDirectRepository callRecordForManualDirectRepository;

    @Autowired
    private LandingPageCallRecordRepository landingPageCallRecordRepository;

    public void saveByCdr(CallRecordResult callRecordResult){
        callRecordMultiService.saveByCdr(callRecordResult.getCallDurationSec(),
                callRecordResult.getCallDuration(),
                callRecordResult.getCallStatus().toString(),
                CallStatusEnum.codeOf(callRecordResult.getCallStatus()).getField(),
                callRecordResult.getErrorCode(),
                callRecordResult.getCause(),
                callRecordResult.getCallId(),
                callRecordResult.getLineId(),
                callRecordResult.getLineCode(),
                callRecordResult.getMerchantLineId(),
                callRecordResult.getMerchantLineCode(),
                callRecordResult.getSpeechCallId(),
                callRecordResult.getFsIp(),
                callRecordResult.getAiCallIp(),
                callRecordResult.getCallOutTime(),
                (callRecordResult.getCallStatus() != null && callRecordResult.getCallStatus() == 7 && callRecordResult.getCallDurationSec() <= 1) ? 1 : callRecordResult.getWhoHangup(),
                callRecordResult.getScriptId(),
                callRecordResult.getScriptLongId(), callRecordResult.getWaitmsec()==null?0:callRecordResult.getWaitmsec(),
                callRecordResult.getSipCallId(),
                LocalDateTime.now());
    }

    public void saveByCdrForAIMan(CallRecordResult callRecordResult) {
        callRecordForHumanMachineRepository.saveByCdr(callRecordResult.getCallDurationSec(),
                callRecordResult.getCallDuration(),
                callRecordResult.getCallStatus().toString(),
                CallStatusEnum.codeOf(callRecordResult.getCallStatus()).getField(),
                callRecordResult.getErrorCode(),
                callRecordResult.getCause(),
                callRecordResult.getCallId(),
                callRecordResult.getLineId(),
                callRecordResult.getLineCode(),
                callRecordResult.getMerchantLineId(),
                callRecordResult.getMerchantLineCode(),
                callRecordResult.getSpeechCallId(),
                callRecordResult.getFsIp(),
                callRecordResult.getAiCallIp(),
                callRecordResult.getCallOutTime(),
                (callRecordResult.getCallStatus() != null && callRecordResult.getCallStatus() == 7 && callRecordResult.getCallDurationSec() <= 1) ? 1 : callRecordResult.getWhoHangup(),
                callRecordResult.getScriptId(),
                callRecordResult.getScriptLongId(), callRecordResult.getWaitmsec()==null?0:callRecordResult.getWaitmsec(),
                callRecordResult.getSipCallId(),
                LocalDateTime.now());
    }

    public void saveByCdrForHuman(CallRecordResult callRecordResult) {
        callRecordForManualDirectRepository.saveByCdr(
                callRecordResult.getCallDurationSec(),
                callRecordResult.getCallDuration(),
                callRecordResult.getCallStatus().toString(),
                CallStatusEnum.codeOf(callRecordResult.getCallStatus()).getField(),
                callRecordResult.getErrorCode(),
                callRecordResult.getCause(),
                callRecordResult.getCallId(),
                callRecordResult.getLineId(),
                callRecordResult.getLineCode(),
                callRecordResult.getMerchantLineId(),
                callRecordResult.getMerchantLineCode(),
                callRecordResult.getSpeechCallId(),
                callRecordResult.getFsIp(),
                callRecordResult.getAiCallIp(),
                callRecordResult.getCallOutTime(),
                (callRecordResult.getCallStatus() != null && callRecordResult.getCallStatus() == 7 && callRecordResult.getCallDurationSec() <= 1) ? 1 : callRecordResult.getWhoHangup(),
                callRecordResult.getWaitmsec()==null?0:callRecordResult.getWaitmsec(),
                callRecordResult.getSipCallId(),
                LocalDateTime.now()
        );
    }

    public void saveByCdrForLandingPage(CallRecordResult callRecordResult) {
        LandingPageCallRecord record = new LandingPageCallRecord();
        record.setTaskName(callRecordResult.getTaskName());
        record.setTaskId(callRecordResult.getTaskId());
        record.setProvince("未知");
        record.setCity("未知");
        record.setCityCode("未知");
        record.setProvinceCode("未知");
        record.setFsIp(callRecordResult.getFsIp());
        record.setFsUser(callRecordResult.getPlainPhone());
        record.setPhone(callRecordResult.getPhone());
        record.setCallId(callRecordResult.getCallId());
        record.setSipCallId(callRecordResult.getSipCallId());
        record.setCause(callRecordResult.getCause());
        record.setWaitmsec(callRecordResult.getWaitmsec());
        record.setWholeAudioFileUrl(callRecordResult.getWholeAudioFileUrl());
        record.setCycleCount(record.getCycleCount());
        record.setSayCount(callRecordResult.getSayCount());
        record.setUserFullAnswerContent(callRecordResult.getUserFullAnswerContent());
        record.setIntentionClass(callRecordResult.getIntentionClass());
        record.setIntentionLabelIds(callRecordResult.getIntentionLabelIds());
        record.setIntentionLabels(callRecordResult.getIntentionLabels());
        record.setCallOutTime(callRecordResult.getCallOutTime());
        record.setContactTime(callRecordResult.getContactTime());
        record.setTalkTimeEnd(callRecordResult.getTalkTimeEnd());
        record.setTalkTimeStart(callRecordResult.getTalkTimeStart());
        record.setHitAnswerIds(callRecordResult.getHitAnswerIds());
        record.setScriptStringId(callRecordResult.getScriptId());
        record.setSpeechCraftId(callRecordResult.getScriptLongId());
        record.setHitAdvanceIds(callRecordResult.getHitAdvanceIds());
        record.setCorpusIds(callRecordResult.getCorpusIds());
        record.setHitSemanticIds(callRecordResult.getHitSemanticIds());
        record.setExtraInfo(callRecordResult.getExtraInfo());
        record.setCallStatus(String.valueOf(callRecordResult.getCallStatus()));
        record.setCallDuration(callRecordResult.getCallDuration());
        record.setCallDurationSec(callRecordResult.getCallDurationSec());
        record.setWhoHangup(callRecordResult.getWhoHangup());
        record.setOperator("未知");
        record.setAiCallIp(callRecordResult.getAiCallIp());
        record.setSipCallId(callRecordResult.getSipCallId());
        landingPageCallRecordRepository.save(record);
    }
}
