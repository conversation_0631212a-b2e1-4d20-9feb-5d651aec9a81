package com.raipeng.aidatapush.service;

import com.raipeng.aidatacommon.entity.HangUpSmsTriggerPojo;
import com.raipeng.aidatacommon.entity.VariableSmsValuePojo;
import com.raipeng.aidatacommon.enums.retry.WaitStrategyType;
import com.raipeng.aidatacommon.exception.CallFailedException;
import com.raipeng.aidatacommon.model.PhoneRecord;
import com.raipeng.aidatacommon.model.dto.CallRecordResult;
import com.raipeng.aidatapush.annotation.CallRetry;
import com.raipeng.aidatapush.feign.AiSmsManagerFeign;
import com.raipeng.common.constant.VariableConstants;
import com.raipeng.aidatacommon.entity.AiRedisTask;
import com.raipeng.common.entity.smsentity.SmsDataPushWrapper;
import com.raipeng.common.enums.MessageType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;


@Slf4j
@Service
public class SmsService {

    @Autowired
    private AiSmsManagerFeign aiSmsManagerFeign;

    /**
     * @param aiRedisTask
     * @param callRecordResult
     * @param phoneRecord
     * @param smsDataList
     */
    public void sendMessage(AiRedisTask aiRedisTask, CallRecordResult callRecordResult,
                            String recordId,
                            String intentionLabels,
                            String intentionClass,
                            String account,
                            String city,
                            String operator,
                            String cityCode,
                            String province,
                            String provinceCode,
                            PhoneRecord phoneRecord, List<SmsDataPushWrapper> smsDataList) {
        // 根据标签意向，判断是否需要推送短信
        List<HangUpSmsTriggerPojo> hangUpSms = aiRedisTask.getHangUpSms();
        if (CollectionUtils.isEmpty(hangUpSms)) {
            return;
        }
        hangUpSms.sort(Comparator.comparing(HangUpSmsTriggerPojo::getTriggerOrder));
        List<String> hangUpExcluded = aiRedisTask.getHangUpExcluded();

        Set<String> intentionLabelSet = new HashSet<>();
        if (StringUtils.isNotBlank(intentionLabels)) {
            intentionLabelSet.addAll(Arrays.asList(intentionLabels.split(",")));
        }
        if (CollectionUtils.isNotEmpty(hangUpExcluded)) {
            boolean b = hangUpExcluded.stream().anyMatch(a -> intentionLabelSet.contains(a.toString()));
            if (b) {
                return;
            }
        }
        Long templateId = chooseTemplateByRule(intentionClass, hangUpSms, intentionLabelSet);
        if (null == templateId) {
            return;
        }
        //根据任务的规则找到需要发送短信的模板
        String plainPhone = callRecordResult.getPlainPhone();
        HashMap<String, String> objectObjectHashMap = new HashMap<>();
        List<VariableSmsValuePojo> variableSmsPojoList = phoneRecord.getVariableSmsValue();
        if(CollectionUtils.isNotEmpty(variableSmsPojoList)){
            for (VariableSmsValuePojo variableSmsPojo : variableSmsPojoList) {
                if (StringUtils.isNotBlank(variableSmsPojo.getVariableName()) && StringUtils.isNotBlank(variableSmsPojo.getVariableValue())){
                    objectObjectHashMap.put(variableSmsPojo.getVariableName(), variableSmsPojo.getVariableValue());
                }
            }
        }
        if (StringUtils.isNotBlank(plainPhone) && plainPhone.length() == 11) {
            objectObjectHashMap.put(VariableConstants.SYS_LAST_FOUR_DIGITS, plainPhone.substring(plainPhone.length() - 4));
        }
        objectObjectHashMap.put(VariableConstants.SYS_CITY, city);
        LocalDate localDate = LocalDate.now();
        objectObjectHashMap.put(VariableConstants.SYS_ISSUE_DATE, localDate.format(DateTimeFormatter.ofPattern("MM月dd日")));

        SmsDataPushWrapper smsDataPushWrapper = new SmsDataPushWrapper();
        smsDataPushWrapper.setTriggerTime(LocalDateTime.now());
        smsDataPushWrapper.setAccount(account);
        smsDataPushWrapper.setCity(city);
        smsDataPushWrapper.setOperator(operator);
        smsDataPushWrapper.setCityCode(cityCode);
        smsDataPushWrapper.setProvince(province);
        smsDataPushWrapper.setProvinceCode(provinceCode);
        smsDataPushWrapper.setProgramId(aiRedisTask.getProgramId());
        smsDataPushWrapper.setProductId(aiRedisTask.getProductId());
        smsDataPushWrapper.setSecondIndustryId(aiRedisTask.getIndustrySecondFieldId());
        smsDataPushWrapper.setTaskId(aiRedisTask.getId().toString());
        smsDataPushWrapper.setTaskName(aiRedisTask.getTaskName());
        smsDataPushWrapper.setGroupId(aiRedisTask.getGroupId());
        smsDataPushWrapper.setScriptLongId(aiRedisTask.getSpeechCraftId());
        smsDataPushWrapper.setScriptStringId(aiRedisTask.getScriptStringId());
        smsDataPushWrapper.setScriptName(aiRedisTask.getSpeechCraftName());
        smsDataPushWrapper.setSmsTemplateId(templateId);
        smsDataPushWrapper.setCallSeatId(null);
        smsDataPushWrapper.setCallTeamId(null);
        smsDataPushWrapper.setClueUniqueId(null);
        smsDataPushWrapper.setCallRecordId(recordId);
        smsDataPushWrapper.setPhoneRecordId(phoneRecord.getRecordId());
        smsDataPushWrapper.setMessageType(MessageType.ON_HOOK);
        smsDataPushWrapper.setRecordType(aiRedisTask.getTaskType().toString());
        smsDataPushWrapper.setPhone(plainPhone);
        smsDataPushWrapper.setEncryptPhone(callRecordResult.getPhone());
        smsDataPushWrapper.setVariables(objectObjectHashMap);
        smsDataPushWrapper.setName(phoneRecord.getName());
        smsDataPushWrapper.setCompany(phoneRecord.getCompany());
        smsDataPushWrapper.setRemarks(phoneRecord.getRemarks());
        smsDataList.add(smsDataPushWrapper);

    }

    private Long chooseTemplateByRule(String intentionClass, List<HangUpSmsTriggerPojo> hangUpSms, Set<String> intentionLabelSet) {
        for (HangUpSmsTriggerPojo hangUpSmsTriggerPojo : hangUpSms) {
            boolean equals = true;
            if (StringUtils.isNotBlank(hangUpSmsTriggerPojo.getIntentionType())) {
                equals = hangUpSmsTriggerPojo.getIntentionType().equals(intentionClass);
            }
            boolean labelBoo = true;
            if (CollectionUtils.isNotEmpty(hangUpSmsTriggerPojo.getLabelIds())) {
                List<String> labelIds = hangUpSmsTriggerPojo.getLabelIds();

                labelBoo = labelIds.stream()
                        .map(String::valueOf)
                        .allMatch(intentionLabelSet::contains);
            }
            if (equals && labelBoo) {
                return hangUpSmsTriggerPojo.getSmsTemplateId();
            }
        }
        return null;
    }

    @CallRetry(retryTimes = 5, waitStrategy = WaitStrategyType.INCREMENTING_WAIT_STRATEGY)
    public void sendHangUpSms(List<SmsDataPushWrapper> param) {
        try {
            log.info("通话挂短:{}", param);
            aiSmsManagerFeign.sendHangUpSms(param);
        } catch (Exception e) {
            log.error("Exception=>通话:{},挂短:{}失败,reason:{}", param);
            DingDingService.dingDingSendMsgException("通话"
                    + "挂短:"
                    + "失败, reason:"
                    + e.getMessage());
            throw new CallFailedException(e, "手机号挂短调用消息关系系统报错", "sms_manager_error");
        }
    }
}
