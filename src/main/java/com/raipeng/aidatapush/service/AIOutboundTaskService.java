package com.raipeng.aidatapush.service;

import com.raipeng.aidatacommon.model.*;
import com.raipeng.aidatacommon.model.dto.AIOutboundQueryDto;
import com.raipeng.aidatapush.repository.*;
import com.raipeng.aidatapush.service.mq.producer.TaskStatusNoticeProducer;
import com.raipeng.aidatapush.utils.TenantLineUtils;

import com.raipeng.common.model.dto.TaskStatusNoticeDTO;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.redisson.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

@Service
@RefreshScope
@Slf4j
public class AIOutboundTaskService {
    @Autowired
    private AIOutboundTaskRepository aiOutboundTaskRepository;

    @Autowired
    private RedissonClient redissonClient;

    @Value("${whole.url:http://ai.system.bountech.com/marketfront/file/audio}")
    private String wholeUrl;

    @Value("${ip.prefix.no.need.replace:192.168.215}")
    private String ipPrefixNoNeedReplace;

    @Autowired
    private TenantLineUtils tenantLineUtils;

    @Autowired
    private TaskStatusNoticeProducer taskStatusNoticeProducer;

    private static final String REDIS_TASK_TMP = "REDIS_TASK_TMP::";

    public void checkIfTaskNeedStop(Long taskId, long finishedNum){
        RAtomicLong phoneNum = redissonClient.getAtomicLong(REDIS_TASK_TMP + taskId + "_phoneNum");
        if(phoneNum.get() == 0){
            phoneNum.delete();
            return;
        }
        long remainNum = phoneNum.addAndGet(-finishedNum);
        if(remainNum <= 0){
            phoneNum.delete();
            AIOutboundQueryDto aiOutboundQueryDto = new AIOutboundQueryDto();
            aiOutboundQueryDto.setCallStatus("已停止");
            aiOutboundQueryDto.setTaskId(taskId);
            stopTask(aiOutboundQueryDto);
        }
    }

    public void stopTask(AIOutboundQueryDto aiOutboundQueryDto){
        AIOutboundTask aiOutboundTask = aiOutboundTaskRepository.findById(aiOutboundQueryDto.getTaskId()).get();
        log.info("开始停止任务:{}", aiOutboundTask.getId());
        if("进行中".equals(aiOutboundTask.getCallStatus())){
            aiOutboundTask.setCallStatus("已停止");
            if(aiOutboundTask.getLineId() != null){
                tenantLineUtils.updateRemainConcurrentNum(aiOutboundTask.getLineId(), aiOutboundTask.getAiAnswerNum());
            }
        }else{
            aiOutboundTask.setCallStatus("已停止");
        }
        if(aiOutboundTask.getCallStatus().equals("已停止") && aiOutboundTask.getTaskType().toString().equals("AI_MANUAL")){
            aiOutboundTaskRepository.updateAiManualStatus(aiOutboundTask.getId(), aiOutboundTask.getCallStatus());
        }else{
            aiOutboundTaskRepository.updateStatus(aiOutboundTask.getId(), aiOutboundTask.getCallStatus());
        }
        sendTaskStatus(String.valueOf(aiOutboundTask.getId()),aiOutboundTask.getTaskName(),aiOutboundTask.getCallStatus(),aiOutboundTask.getGroupId(),"AiDataPush.stopTask");
        log.info("结束停止任务:{}", aiOutboundTask.getId());
    }

    public String replaceUrl(String url){
        if(StringUtils.isBlank(url)){
            return null;
        }
        String ip = url.substring(url.indexOf("//") + 2, url.indexOf("/home"));

        if(!ip.contains(ipPrefixNoNeedReplace)){
            return wholeUrl + ip.split("\\.")[2]+ip.split("\\.")[3] + url.substring(url.indexOf("/whole"));
        }

        return wholeUrl + ip.split("\\.")[3] + url.substring(url.indexOf("/whole"));
    }

    public void sendTaskStatus(String taskId, String taskName, String taskStatus, String groupId, String location ){
        TaskStatusNoticeDTO taskNotice = new TaskStatusNoticeDTO();
        taskNotice.setTaskId(taskId);
        taskNotice.setTaskName(taskName);
        taskNotice.setTaskStatus(taskStatus);
        taskNotice.setGroupId(groupId);
        taskNotice.setLocation(location);
        taskStatusNoticeProducer.send(taskNotice);
    }
}
