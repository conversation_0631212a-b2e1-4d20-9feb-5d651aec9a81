package com.raipeng.aidatapush.service;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.raipeng.aidatacommon.model.CallRecordForHumanMachine;
import com.raipeng.aidatacommon.model.record.CallRecord;
import com.raipeng.aidatacommon.model.script.AdvancedRuleCondition;
import com.raipeng.common.entity.script.rule.CallRecordExtraInfo;
import com.raipeng.common.enums.FieldConditionType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AdvancedRuleConditionService {

    public boolean matchRuleCondition(CallRecord callRecord, List<String> conditionUniqueIdList,
                                      Map<String, List<AdvancedRuleCondition>> advancedRuleConditionMap,
                                      Map<String, List<String>> allAISemanticLabelRelationMap) {
        String extraInfo = callRecord.getExtraInfo();
        CallRecordExtraInfo callRecordExtraInfo = getCallRecordExtraInfo(extraInfo);
        String corpusIds = callRecord.getCorpusIds();
        List<String> corpusIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(corpusIds)) {
            corpusIdList = JSON.parseArray(corpusIds, String.class);
        }
        for (String conditionUniqueId : conditionUniqueIdList) {
            List<AdvancedRuleCondition> advancedRuleConditions = advancedRuleConditionMap.get(conditionUniqueId);
            if (CollectionUtils.isEmpty(advancedRuleConditions)) {
                log.error("Exception : 话术{}中存在已被删除的规则条件{}，及时处理并清除缓存", callRecord.getSpeechCraftId(), conditionUniqueId);
                return false;
            }
            //这个conditionUniqueIdList的关系是or的关系存在一个为true的就可以视为满足条件
            boolean isMatch = checkOrCondition(callRecord, allAISemanticLabelRelationMap, callRecordExtraInfo, corpusIdList, advancedRuleConditions);
            if (isMatch) {
                return true;
            }
        }
        return false;
    }

    private boolean checkOrCondition(CallRecord callRecord, Map<String, List<String>> allAISemanticLabelRelationMap, CallRecordExtraInfo callRecordExtraInfo, List<String> corpusIdList, List<AdvancedRuleCondition> advancedRuleConditions) {
        for (AdvancedRuleCondition advancedRuleCondition : advancedRuleConditions) {
            // 区间得两个值可能会为空
            int minNum = advancedRuleCondition.getMinNum() == null ? 0 : advancedRuleCondition.getMinNum();
            int maxNum = advancedRuleCondition.getMaxNum() == null ? Integer.MAX_VALUE : advancedRuleCondition.getMaxNum();

            if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_ANSWER_NUM.toString())) {
                // 1. 问答命中匹配
                if (StringUtils.isNotBlank(advancedRuleCondition.getHitAnswerIds())) {
                    if (countMatchedNum(callRecord.getHitAnswerIds(), advancedRuleCondition.getHitAnswerIds()) < advancedRuleCondition.getNum()) {
                        return false;
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_ANSWER_TYPE.toString())) {
                // 2. 问答类型命中匹配
                if (StringUtils.isNotBlank(advancedRuleCondition.getHitAnswerType())) {
                    List<String> answerTypes = callRecordExtraInfo.getAnswerTypes();
                    int count = 0;
                    for (String answerType : answerTypes) {
                        if (answerType.equals(advancedRuleCondition.getHitAnswerType())) {
                            count++;
                        }
                    }
                    if (count < minNum || count > maxNum) {
                        return false;
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_ANSWER_GROUP.toString())) {
                // 3. 问答分组命中匹配 知识库分组
                if (StringUtils.isNotBlank(advancedRuleCondition.getHitKnowledgeIds())) {
                    List<String> knowledgeGroupIds = CollectionUtils.isNotEmpty(callRecordExtraInfo.getKnowledgeGroupIds()) ? callRecordExtraInfo.getKnowledgeGroupIds() : new ArrayList<>();
                    int count = countMatchedTimes(knowledgeGroupIds, advancedRuleCondition.getHitKnowledgeIds());
                    if (count < minNum || count > maxNum) {
                        return false;
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_TAG_NUM.toString())) {
                // 4.标签个数命中匹配
                if (StringUtils.isNotBlank(advancedRuleCondition.getHitTagIds())) {
                    if (countMatchedNum(callRecord.getIntentionLabelIds(), advancedRuleCondition.getHitTagIds()) < advancedRuleCondition.getNum()) {
                        return false;
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_TAG_TIMES.toString())) {
                // 5.标签次数命中匹配
                if (StringUtils.isNotBlank(advancedRuleCondition.getHitTagIds())) {
                    int count = countMatchedTimes(callRecord.getIntentionLabelIds(), advancedRuleCondition.getHitTagIds());
                    if (count < minNum || count > maxNum) {
                        return false;
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_SEMANTIC_NUM.toString())) {
                // 6. 语义命中个数匹配
                if (StringUtils.isNotBlank(advancedRuleCondition.getHitSemanticIds())) {
                    if (countMatchedNum(callRecord.getHitSemanticIds(), advancedRuleCondition.getHitSemanticIds()) < advancedRuleCondition.getNum()) {
                        return false;
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_SEMANTIC_TIMES.toString())) {
                // 7. 语义命中次数匹配
                if (StringUtils.isNotBlank(advancedRuleCondition.getHitSemanticIds())) {
                    int count = countMatchedTimes(callRecord.getHitSemanticIds(), advancedRuleCondition.getHitSemanticIds());
                    if (count < minNum || count > maxNum) {
                        return false;
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_SEMANTIC_LABEL.toString())) {
                // 8.语义标签
                if (StringUtils.isNotBlank(advancedRuleCondition.getHitSemanticLabelIds())) {
                    List<String> hitSemanticLabelIdList = new ArrayList<>();
                    if (StringUtils.isNotBlank(callRecord.getHitSemanticIds())) {
                        String[] hitSemanticIds = callRecord.getHitSemanticIds().split(",");
                        for (String hitSemanticId : hitSemanticIds) {
                            List<String> longs = allAISemanticLabelRelationMap.get(hitSemanticId);
                            if (CollectionUtils.isNotEmpty(longs)) {
                                hitSemanticLabelIdList.addAll(longs);
                            }
                        }
                    }
                    int count = countMatchedTimes(hitSemanticLabelIdList, advancedRuleCondition.getHitSemanticLabelIds());
                    if (count < minNum || count > maxNum) {
                        return false;
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_MASTER_PROCESS.toString())) {
                // 9.命中主动流程 语料判断是否命中头节点
                if (StringUtils.isNotBlank(advancedRuleCondition.getHitMasterProcessIds())) {
                    boolean b = containMatchedId(corpusIdList, advancedRuleCondition.getHitMasterProcessIds());
                    if (!b) {
                        return false;
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_DEEP_COMMUNICATION.toString())) {
                // 10.命中深层沟通 语料判断是否命中深层沟通的头节点
                if (StringUtils.isNotBlank(advancedRuleCondition.getHitDeepCommunicationIds())) {
                    boolean b = containMatchedId(corpusIdList, advancedRuleCondition.getHitDeepCommunicationIds());
                    if (!b) {
                        return false;
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_PROCESS_CORPUS.toString())) {
                // 11.命中主动流程 语料判断是否命中头节点
                if (StringUtils.isNotBlank(advancedRuleCondition.getHitCorpusIds())) {
                    boolean b = containMatchedId(corpusIdList, advancedRuleCondition.getHitCorpusIds());
                    if (!b) {
                        return false;
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_BRANCH.toString())) {
                // 12.命中主动流程 语料 分支
                if (StringUtils.isNotBlank(advancedRuleCondition.getHitCorpusIds()) && StringUtils.isNotBlank(advancedRuleCondition.getHitBranchIds())) {
                    List<String> branchIds = CollectionUtils.isNotEmpty(callRecordExtraInfo.getBranchIds()) ? callRecordExtraInfo.getBranchIds() : new ArrayList<>();
                    if (!containMatchedId(corpusIdList, advancedRuleCondition.getHitCorpusIds())
                            || !containMatchedId(branchIds, advancedRuleCondition.getHitBranchIds())) {
                        return false;
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_MATCH_WORDS.toString())) {
                // 14.命中客户回复
                String userFullAnswerContent = callRecord.getUserFullAnswerContent();
                String matchContent = userFullAnswerContent == null ? "" : userFullAnswerContent.replace("##", "");
                if (StringUtils.isNotBlank(advancedRuleCondition.getMatchWords()) && !matchContent.matches(advancedRuleCondition.getMatchWords())) {
                    return false;
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_SEMANTICS_INTERRUPT_TIMES.toString())) {
                // 16. 语义打断
                int count = callRecordExtraInfo.getSemanticInterruptNum() == null ? 0 : callRecordExtraInfo.getSemanticInterruptNum();
                if (count < minNum || count > maxNum) {
                    return false;
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_INTERACT_NUM.toString())) {
                // 18. 交互次数
                Integer count = callRecord.getCycleCount();
                if (count < minNum || count > maxNum) {
                    return false;
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_SPEAK_NUM.toString())) {
                // 19. 说话次数
                Integer count = callRecord.getSayCount();
                if (count < minNum || count > maxNum) {
                    return false;
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_SPEAK_INTERACT.toString())) { // 5. 呼叫时长匹配
                // 20. 对比
                String speakInteractOperator = advancedRuleCondition.getSpeakInteractOperator();
                Integer speakInteractNum = advancedRuleCondition.getNum();
                if (StringUtils.isNotBlank(speakInteractOperator) && speakInteractNum != null) {
                    if ("≥".equals(advancedRuleCondition.getSpeakInteractOperator())) {
                        if (!(callRecord.getSayCount() >= callRecord.getCycleCount() * speakInteractNum)) {
                            return false;
                        }
                    } else if ("≤".equals(advancedRuleCondition.getSpeakInteractOperator())) {
                        if (!(callRecord.getSayCount() <= callRecord.getCycleCount() * speakInteractNum)) {
                            return false;
                        }
                    } else if ("=".equals(advancedRuleCondition.getSpeakInteractOperator())) {
                        if (!(callRecord.getSayCount().equals(callRecord.getCycleCount() * speakInteractNum))) {
                            return false;
                        }
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_CALL_DURATION.toString())) {
                // 21.通话时长命中
                Integer callDurationSec = callRecord.getCallDurationSec();
                if (callDurationSec < minNum || callDurationSec > maxNum) {
                    return false;
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_HANG_UP.toString())) {
                // 22. 挂机方
                if (StringUtils.isNotBlank(advancedRuleCondition.getHangUp())) {
                    int res = -1;
                    if ("AI".equals(advancedRuleCondition.getHangUp())) {
                        res = 0;
                    } else if ("用户".equals(advancedRuleCondition.getHangUp())) {
                        res = 1;
                    }
                    if (!callRecord.getWhoHangup().equals(res)) {
                        return false;
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_TRANS_TO_HUMAN.toString())) {
                // 23. ai外呼无触发转人工
                return false;
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_LISTEN_IN.toString())) {
                // 24. ai外呼无被人工监听
                return false;
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_TAKE_OVER.toString())) {
                // 25. ai外呼无被人工接听
                return false;
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_TRIGGER_SMS.toString())) {
                // 26. 语料触发了发短信
                Boolean yn = advancedRuleCondition.getYn();
                String hitTriggerCorpusIds = advancedRuleCondition.getHitTriggerCorpusIds();
                if (null != yn && StringUtils.isNotBlank(hitTriggerCorpusIds)) {
                    int count = countMatchedTimes(corpusIdList, advancedRuleCondition.getHitTriggerCorpusIds());
                    boolean triggerSms = count > 0;
                    boolean b = advancedRuleCondition.getYn() == triggerSms;
                    if (!b) {
                        return false;
                    }
                }
            } else {
                return false;
            }
        }
        return true;
    }

    @NotNull
    private static CallRecordExtraInfo getCallRecordExtraInfo(String extraInfo) {
        CallRecordExtraInfo callRecordExtraInfo = new CallRecordExtraInfo();
        if (StringUtils.isNotBlank(extraInfo)) {
            CallRecordExtraInfo callRecordExtraInfo1 = JSONObject.parseObject(extraInfo, CallRecordExtraInfo.class);
            if (CollectionUtils.isNotEmpty(callRecordExtraInfo1.getBranchIds())) {
                callRecordExtraInfo.setBranchIds(callRecordExtraInfo1.getBranchIds().stream().filter(Objects::nonNull).collect(Collectors.toList()));
            } else {
                callRecordExtraInfo.setBranchIds(new ArrayList<>());
            }
            if (CollectionUtils.isNotEmpty(callRecordExtraInfo1.getAnswerTypes())) {
                callRecordExtraInfo.setAnswerTypes(callRecordExtraInfo1.getAnswerTypes().stream().filter(Objects::nonNull).collect(Collectors.toList()));
            } else {
                callRecordExtraInfo.setAnswerTypes(new ArrayList<>());
            }
            if (CollectionUtils.isNotEmpty(callRecordExtraInfo1.getKnowledgeGroupIds())) {
                callRecordExtraInfo.setKnowledgeGroupIds(callRecordExtraInfo1.getKnowledgeGroupIds().stream().filter(Objects::nonNull).collect(Collectors.toList()));
            } else {
                callRecordExtraInfo.setKnowledgeGroupIds(new ArrayList<>());
            }
            int semanticInterruptNum = callRecordExtraInfo1.getSemanticInterruptNum() == null ? 0 : callRecordExtraInfo1.getSemanticInterruptNum();
            callRecordExtraInfo.setSemanticInterruptNum(semanticInterruptNum);
        }
        return callRecordExtraInfo;
    }

    public boolean matchRuleCondition(CallRecordForHumanMachine callRecord, List<String> conditionUniqueIdList,
                                      Map<String, List<AdvancedRuleCondition>> advancedRuleConditionMap,
                                      Map<String, List<String>> allAISemanticLabelRelationMap) {
        String extraInfo = callRecord.getExtraInfo();
        CallRecordExtraInfo callRecordExtraInfo = getCallRecordExtraInfo(extraInfo);
        String corpusIds = callRecord.getCorpusIds();
        List<String> corpusIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(corpusIds)) {
            corpusIdList = JSON.parseArray(corpusIds, String.class);
        }
        //这个conditionUniqueIdList的关系是or的关系存在一个为true的就可以视为满足条件
        for (String conditionUniqueId : conditionUniqueIdList) {
            List<AdvancedRuleCondition> advancedRuleConditions = advancedRuleConditionMap.get(conditionUniqueId);
            if (CollectionUtils.isEmpty(advancedRuleConditions)) {
                log.error("Exception : 话术{}中存在已被删除的规则条件{}，及时处理并清除缓存", callRecord.getSpeechCraftId(), conditionUniqueId);
                return false;
            }
            boolean isMatch = checkOrCondition(callRecord, allAISemanticLabelRelationMap, callRecordExtraInfo, corpusIdList, advancedRuleConditions);
            if (isMatch) {
                return true;
            }
        }
        return false;
    }

    private boolean checkOrCondition(CallRecordForHumanMachine callRecord, Map<String, List<String>> allAISemanticLabelRelationMap, CallRecordExtraInfo callRecordExtraInfo, List<String> corpusIdList, List<AdvancedRuleCondition> advancedRuleConditions) {
        for (AdvancedRuleCondition advancedRuleCondition : advancedRuleConditions) {
            // 区间得两个值可能会为空
            int minNum = advancedRuleCondition.getMinNum() == null ? 0 : advancedRuleCondition.getMinNum();
            int maxNum = advancedRuleCondition.getMaxNum() == null ? Integer.MAX_VALUE : advancedRuleCondition.getMaxNum();

            if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_ANSWER_NUM.toString())) {
                // 1. 问答命中匹配
                if (StringUtils.isNotBlank(advancedRuleCondition.getHitAnswerIds())) {
                    if (countMatchedNum(callRecord.getHitAnswerIds(), advancedRuleCondition.getHitAnswerIds()) < advancedRuleCondition.getNum()) {
                        return false;
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_ANSWER_TYPE.toString())) {
                // 2. 问答类型命中匹配
                if (StringUtils.isNotBlank(advancedRuleCondition.getHitAnswerType())) {
                    List<String> answerTypes = callRecordExtraInfo.getAnswerTypes();
                    int count = countMatchedTimes(answerTypes, advancedRuleCondition.getHitAnswerType());
                    if (count < minNum || count > maxNum) {
                        return false;
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_ANSWER_GROUP.toString())) {
                // 3. 问答分组命中匹配 知识库分组
                if (StringUtils.isNotBlank(advancedRuleCondition.getHitKnowledgeIds())) {
                    List<String> knowledgeGroupIds = CollectionUtils.isNotEmpty(callRecordExtraInfo.getKnowledgeGroupIds()) ? callRecordExtraInfo.getKnowledgeGroupIds() : new ArrayList<>();
                    int count = countMatchedTimes(knowledgeGroupIds, advancedRuleCondition.getHitKnowledgeIds());
                    if (count < minNum || count > maxNum) {
                        return false;
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_TAG_NUM.toString())) {
                // 4.标签个数命中匹配
                if (StringUtils.isNotBlank(advancedRuleCondition.getHitTagIds())) {
                    if (countMatchedNum(callRecord.getIntentionLabelIds(), advancedRuleCondition.getHitTagIds()) < advancedRuleCondition.getNum()) {
                        return false;
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_TAG_TIMES.toString())) {
                // 5.标签次数命中匹配
                if (StringUtils.isNotBlank(advancedRuleCondition.getHitTagIds())) {
                    int count = countMatchedTimes(callRecord.getIntentionLabelIds(), advancedRuleCondition.getHitTagIds());
                    if (count < minNum || count > maxNum) {
                        return false;
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_SEMANTIC_NUM.toString())) {
                // 6. 语义命中个数匹配
                if (StringUtils.isNotBlank(advancedRuleCondition.getHitSemanticIds())) {
                    if (countMatchedNum(callRecord.getHitSemanticIds(), advancedRuleCondition.getHitSemanticIds()) < advancedRuleCondition.getNum()) {
                        return false;
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_SEMANTIC_TIMES.toString())) {
                // 7. 语义命中次数匹配
                if (StringUtils.isNotBlank(advancedRuleCondition.getHitSemanticIds())) {
                    int count = countMatchedTimes(callRecord.getHitSemanticIds(), advancedRuleCondition.getHitSemanticIds());
                    if (count < minNum || count > maxNum) {
                        return false;
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_SEMANTIC_LABEL.toString())) {
                // 8.语义标签
                if (StringUtils.isNotBlank(advancedRuleCondition.getHitSemanticLabelIds())) {
                    List<String> hitSemanticLabelIdList = new ArrayList<>();
                    if (StringUtils.isNotBlank(callRecord.getHitSemanticIds())) {
                        String[] hitSemanticIds = callRecord.getHitSemanticIds().split(",");
                        for (String hitSemanticId : hitSemanticIds) {
                            List<String> longs = allAISemanticLabelRelationMap.get(hitSemanticId);
                            if (CollectionUtils.isNotEmpty(longs)) {
                                hitSemanticLabelIdList.addAll(longs);
                            }
                        }
                    }
                    int count = countMatchedTimes(hitSemanticLabelIdList, advancedRuleCondition.getHitSemanticLabelIds());
                    if (count < minNum || count > maxNum) {
                        return false;
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_MASTER_PROCESS.toString())) {
                // 9.命中主动流程 语料判断是否命中头节点
                if (StringUtils.isNotBlank(advancedRuleCondition.getHitMasterProcessIds())) {
                    boolean b = containMatchedId(corpusIdList, advancedRuleCondition.getHitMasterProcessIds());
                    if (!b) {
                        return false;
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_DEEP_COMMUNICATION.toString())) {
                // 10.命中深层沟通 语料判断是否命中深层沟通的头节点
                if (StringUtils.isNotBlank(advancedRuleCondition.getHitDeepCommunicationIds())) {
                    boolean b = containMatchedId(corpusIdList, advancedRuleCondition.getHitDeepCommunicationIds());
                    if (!b) {
                        return false;
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_PROCESS_CORPUS.toString())) {
                // 11.命中主动流程 语料判断是否命中头节点
                if (StringUtils.isNotBlank(advancedRuleCondition.getHitCorpusIds())) {
                    boolean b = containMatchedId(corpusIdList, advancedRuleCondition.getHitCorpusIds());
                    if (!b) {
                        return false;
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_BRANCH.toString())) {
                // 12.命中主动流程 语料 分支
                if (StringUtils.isNotBlank(advancedRuleCondition.getHitCorpusIds()) && StringUtils.isNotBlank(advancedRuleCondition.getHitBranchIds())) {
                    List<String> branchIds = CollectionUtils.isNotEmpty(callRecordExtraInfo.getBranchIds()) ? callRecordExtraInfo.getBranchIds() : new ArrayList<>();
                    if (!containMatchedId(corpusIdList, advancedRuleCondition.getHitCorpusIds())
                            || !containMatchedId(branchIds, advancedRuleCondition.getHitBranchIds())) {
                        return false;
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_MATCH_WORDS.toString())) {
                // 14.命中客户回复
                String userFullAnswerContent = callRecord.getUserFullAnswerContent();
                String matchContent = userFullAnswerContent == null ? "" : userFullAnswerContent.replace("##", "");
                if (StringUtils.isNotBlank(advancedRuleCondition.getMatchWords()) && !matchContent.matches(advancedRuleCondition.getMatchWords())) {
                    return false;
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_SEMANTICS_INTERRUPT_TIMES.toString())) {
                // 16. 语义打断
                int count = callRecordExtraInfo.getSemanticInterruptNum() == null ? 0 : callRecordExtraInfo.getSemanticInterruptNum();
                if (count < minNum || count > maxNum) {
                    return false;
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_INTERACT_NUM.toString())) {
                // 18. 交互次数
                Integer count = callRecord.getCycleCount();
                if (count < minNum || count > maxNum) {
                    return false;
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_SPEAK_NUM.toString())) {
                // 19. 说话次数
                Integer count = callRecord.getSayCount();
                if (count < minNum || count > maxNum) {
                    return false;
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_SPEAK_INTERACT.toString())) { // 5. 呼叫时长匹配
                // 20. 对比
                String speakInteractOperator = advancedRuleCondition.getSpeakInteractOperator();
                Integer speakInteractNum = advancedRuleCondition.getNum();
                if (StringUtils.isNotBlank(speakInteractOperator) && speakInteractNum != null) {
                    if ("≥".equals(advancedRuleCondition.getSpeakInteractOperator())) {
                        if (!(callRecord.getSayCount() >= callRecord.getCycleCount() * speakInteractNum)) {
                            return false;
                        }
                    } else if ("≤".equals(advancedRuleCondition.getSpeakInteractOperator())) {
                        if (!(callRecord.getSayCount() <= callRecord.getCycleCount() * speakInteractNum)) {
                            return false;
                        }
                    } else if ("=".equals(advancedRuleCondition.getSpeakInteractOperator())) {
                        if (!(callRecord.getSayCount().equals(callRecord.getCycleCount() * speakInteractNum))) {
                            return false;
                        }
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_CALL_DURATION.toString())) {
                // 21.通话时长命中
                Integer callDurationSec = callRecord.getCallDurationSec();
                if (callDurationSec < minNum || callDurationSec > maxNum) {
                    return false;
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_HANG_UP.toString())) {
                // 22. 挂机方
                if (StringUtils.isNotBlank(advancedRuleCondition.getHangUp())) {
                    int res = -1;
                    if ("AI".equals(advancedRuleCondition.getHangUp())) {
                        res = 0;
                    } else if ("用户".equals(advancedRuleCondition.getHangUp())) {
                        res = 1;
                    } else if ("坐席".equals(advancedRuleCondition.getHangUp())) {
                        res = 2;
                    }
                    if (!callRecord.getWhoHangup().equals(res)) {
                        return false;
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_TRANS_TO_HUMAN.toString())) {
                // 23. ai外呼无触发转人工
                Boolean yn = advancedRuleCondition.getYn();
                String hitTriggerCorpusIds = advancedRuleCondition.getHitTriggerCorpusIds();
                if (null != yn && StringUtils.isNotBlank(hitTriggerCorpusIds)) {
                    int count = countMatchedTimes(corpusIdList, advancedRuleCondition.getHitTriggerCorpusIds());
                    boolean triggerSms = count > 0;
                    boolean b = advancedRuleCondition.getYn() == triggerSms;
                    if (!b) {
                        return false;
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_LISTEN_IN.toString())) {
                // 24. ai外呼无被人工监听
                if (null != advancedRuleCondition.getYn()) {
                    Boolean yn = advancedRuleCondition.getYn();
                    boolean monitor = StringUtils.isNotBlank(callRecord.getStartMonitorTime());
                    boolean b = monitor == yn;
                    if (!b) {
                        return false;
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_TAKE_OVER.toString())) {
                // 25. ai外呼无被人工接听
                if (null != advancedRuleCondition.getYn()) {
                    Boolean yn = advancedRuleCondition.getYn();
                    boolean startAnswer = StringUtils.isNotBlank(callRecord.getStartAnswerTime());
                    boolean b = startAnswer == yn;
                    if (!b) {
                        return false;
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_TRIGGER_SMS.toString())) {
                // 26. 语料触发了发短信
                Boolean yn = advancedRuleCondition.getYn();
                String hitTriggerCorpusIds = advancedRuleCondition.getHitTriggerCorpusIds();
                if (null != yn && StringUtils.isNotBlank(hitTriggerCorpusIds)) {
                    int count = countMatchedTimes(corpusIdList, advancedRuleCondition.getHitTriggerCorpusIds());
                    boolean triggerSms = count > 0;
                    boolean b = advancedRuleCondition.getYn() == triggerSms;
                    if (!b) {
                        return false;
                    }
                }
            } else {
                return false;
            }
        }
        return true;
    }


    private int countMatchedNum(String callRecordIds, String advancedIds) {
        Set<String> set1 = new HashSet<>();
        if (StringUtils.isNotBlank(callRecordIds)) {
            set1.addAll(Arrays.asList(callRecordIds.split(",")));
        }
        Set<String> set2 = new HashSet<>();
        if (StringUtils.isNotBlank(advancedIds)) {
            set2.addAll(Arrays.asList(advancedIds.split(",")));
        }
        set1.retainAll(set2);
        return set1.size();
    }

    private int countMatchedTimes(String callRecordIds, String advancedIds) {
        List<String> list = new ArrayList<>();
        if (StringUtils.isNotBlank(callRecordIds)) {
            list.addAll(Arrays.asList(callRecordIds.split(",")));
        }
        Set<String> set = new HashSet<>();
        if (StringUtils.isNotBlank(advancedIds)) {
            set.addAll(Arrays.asList(advancedIds.split(",")));
        }
        int count = 0;
        for (String s : list) {
            if (set.contains(s)) count++;
        }
        return count;
    }

    private int countMatchedTimes(List<String> list, String advancedIds) {
        Set<String> set = new HashSet<>();
        if (StringUtils.isNotBlank(advancedIds)) {
            set.addAll(Arrays.asList(advancedIds.split(",")));
        }
        int count = 0;
        for (String s : list) {
            if (set.contains(s)) count++;
        }
        return count;
    }

    private boolean containMatchedId(List<String> callRecordIds, String advancedIds) {
        if (StringUtils.isBlank(advancedIds)) {
            return false;
        }
        return callRecordIds.contains(advancedIds);
    }

}
