package com.raipeng.aidatapush.service;


import com.raipeng.aidatacommon.model.CallRecordForHumanMachine;
import com.raipeng.aidatacommon.model.CallRecordForManualDirect;
import com.raipeng.aidatacommon.model.record.CallRecord;
import com.raipeng.aidatacommon.thirdrequests.entity.CallDataEntity;
import com.raipeng.aidatapush.config.HotConfig;
import com.raipeng.aidatapush.utils.CallDataHandleUtils;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ConsumePushThirdDataService {
    @Autowired
    private HotConfig hotConfig;

    @Autowired
    private HandleCallDataPushThirdForAIManService handleCallDataPushThirdForAIManService;

    @Autowired
    private HandleCallDataPushThirdForHumanService handleCallDataPushThirdForHumanService;

    @Autowired
    private HandleCallDataPushThirdForPureAIService handleCallDataPushThirdForPureAIService;

    public void consumeAIManPushData(List<CallRecordForHumanMachine> callRecords, String consumerType) {
        Map<String, List<CallRecordForHumanMachine>> recordMap = callRecords.stream()
                .filter(callRecordForHumanMachine -> callRecordForHumanMachine.getGroupId() != null)
                .collect(Collectors.groupingBy(CallRecordForHumanMachine::getGroupId));
        Set<Map.Entry<String, List<CallRecordForHumanMachine>>> entries = recordMap.entrySet();
        for (Map.Entry<String, List<CallRecordForHumanMachine>> entry : entries) {
            String groupId = entry.getKey();
            List<CallRecordForHumanMachine> records = entry.getValue();
            CallDataEntity entity = CallDataEntity.builder()
                    .callRecordList(records)
                    .groupId(groupId)
                    .build();
            try {
                CallDataHandleUtils.AIMAN_DATA_WARN_COUNT.compute(groupId, (key, value) -> {
                    if (value == null || value.get() <= hotConfig.getMaxPushFailSize()) {
                        handleCallDataPushThirdForAIManService.pushCallDataToThirdForAIMan(entity);
                    } else {
                        handleCallDataPushThirdForAIManService.collectXCallDataToThirdForAIMan(entity);
                        value.incrementAndGet();
                    }
                    return value;
                });
            } catch (Exception e) {
                log.warn("Exception=>{}数据初次回传失败提醒", consumerType, e);
                CallDataHandleUtils.AIMAN_SMS_WARN_COUNT.compute(groupId, (key, value) -> {
                    if (value == null) {
                        return new AtomicInteger(1);
                    } else {
                        value.incrementAndGet();
                        return value;
                    }
                });
                handleCallDataPushThirdForAIManService.collectPreXCallDataToThirdForAIMan(entity);
            }
        }
    }

    public void consumeHumanPushData(List<CallRecordForManualDirect> callRecords, String consumerType) {
        Map<String, List<CallRecordForManualDirect>> recordMap = callRecords.stream()
                .filter(callRecordForManualDirect -> callRecordForManualDirect.getGroupId() != null)
                .collect(Collectors.groupingBy(CallRecordForManualDirect::getGroupId));
        Set<Map.Entry<String, List<CallRecordForManualDirect>>> entries = recordMap.entrySet();
        for (Map.Entry<String, List<CallRecordForManualDirect>> entry : entries) {
            String groupId = entry.getKey();
            List<CallRecordForManualDirect> records = entry.getValue();
            CallDataEntity entity = CallDataEntity.builder()
                    .callRecordList(records)
                    .groupId(groupId)
                    .build();
            try {
                CallDataHandleUtils.HUMAN_DATA_WARN_COUNT.compute(groupId, (key, value) -> {
                    if (value == null || value.get() <= hotConfig.getMaxPushFailSize()) {
                        handleCallDataPushThirdForHumanService.pushCallDataToThirdForHuman(entity);
                    } else {
                        handleCallDataPushThirdForHumanService.collectXCallDataToThirdForHuman(entity);
                        value.incrementAndGet();
                    }
                    return value;
                });
            } catch (Exception e) {
                log.warn("Exception=>{}数据初次回传失败提醒", consumerType, e);
                CallDataHandleUtils.HUMAN_DATA_WARN_COUNT.compute(groupId, (key, value) -> {
                    if (value == null) {
                        return new AtomicInteger(1);
                    } else {
                        value.incrementAndGet();
                        return value;
                    }
                });
                handleCallDataPushThirdForHumanService.collectPreXCallDataToThirdForHuman(entity);
            }
        }
    }

    public void consumePureAIPushData(List<CallRecord> callRecords, String consumerType) {
        Map<String, List<CallRecord>> recordMap = callRecords.stream()
                .filter(callRecord -> callRecord.getGroupId() != null)
                .collect(Collectors.groupingBy(CallRecord::getGroupId));
        Set<Map.Entry<String, List<CallRecord>>> entries = recordMap.entrySet();
        for (Map.Entry<String, List<CallRecord>> entry : entries) {
            String groupId = entry.getKey();
            List<CallRecord> records = entry.getValue();
            CallDataEntity entity = CallDataEntity.builder()
                    .callRecordList(records)
                    .groupId(groupId)
                    .build();
            try {
                CallDataHandleUtils.PURE_AI_DATA_WARN_COUNT.compute(groupId, (key, value) -> {
                    if (value == null || value.get() <= hotConfig.getMaxPushFailSize()) {
                        handleCallDataPushThirdForPureAIService.pushCallDataToThirdForAI(entity);
                    } else {
                        handleCallDataPushThirdForPureAIService.collectXCallDataToThirdForAI(entity);
                        value.incrementAndGet();
                    }
                    return value;
                });
            } catch (Exception e) {
                log.warn("Exception=>{}数据初次回传失败提醒", consumerType, e);
                CallDataHandleUtils.PURE_AI_DATA_WARN_COUNT.compute(groupId, (key, value) -> {
                    if (value == null) {
                        return new AtomicInteger(1);
                    } else {
                        value.incrementAndGet();
                        return value;
                    }
                });
                handleCallDataPushThirdForPureAIService.collectPreXCallDataToThirdForAI(entity);
            }
        }
    }
}
