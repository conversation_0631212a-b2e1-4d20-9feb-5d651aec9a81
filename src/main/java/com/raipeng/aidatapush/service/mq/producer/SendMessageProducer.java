package com.raipeng.aidatapush.service.mq.producer;

import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.raipeng.aidatapush.config.RabbitConstants.SEND_MESSAGE_EXCHANGE;
import static com.raipeng.aidatapush.config.RabbitConstants.SEND_MESSAGE_ROUTING;

@Component
public class SendMessageProducer {
    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void send(String dataPushWrapper) {
        this.rabbitTemplate.convertAndSend(SEND_MESSAGE_EXCHANGE, SEND_MESSAGE_ROUTING, dataPushWrapper);
    }
}
