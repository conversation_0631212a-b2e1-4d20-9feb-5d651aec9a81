package com.raipeng.aidatapush.service.mq.producer;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.raipeng.aidatapush.config.RabbitConstants.*;

@Slf4j
@Component
public class PreXCallForLandingPagePureAIProducer {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void send(String callResultsString) {
        try {
            rabbitTemplate.convertAndSend(
                    PRE_X_CALL_PHONE_RESULT_NOTICE_SPEECH_LANDING_PAGE_EXCHANGE,
                    PRE_X_CALL_PHONE_RESULT_NOTICE_SPEECH_LANDING_PAGE_ROUTING,
                    callResultsString);
            log.info("落地页纯AI异常记录已发送到PreX队列");
        } catch (Exception e) {
            log.error("发送落地页纯AI异常记录到PreX队列失败: {}", e.getMessage(), e);
        }
    }
}
