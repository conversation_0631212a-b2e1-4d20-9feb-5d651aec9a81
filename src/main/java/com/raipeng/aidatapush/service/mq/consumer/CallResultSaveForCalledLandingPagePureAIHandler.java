package com.raipeng.aidatapush.service.mq.consumer;

import com.raipeng.aidatacommon.model.dto.CallRecordResult;
import com.raipeng.aidatapush.controller.wrapper.CallResultHandlerForPureAILandingPageWrapper;
import com.raipeng.aidatapush.service.CallRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.raipeng.aidatapush.config.RabbitConstants.RESULT_SAVE_FOR_CALLED_LANDING_PAGE_PURE_AI_QUEUE;

@Slf4j
@Component
@RabbitListener(queues = {RESULT_SAVE_FOR_CALLED_LANDING_PAGE_PURE_AI_QUEUE}, concurrency = "5")
public class CallResultSaveForCalledLandingPagePureAIHandler {

    @Autowired
    private CallRecordService callRecordService;

    @RabbitHandler
    public void process(CallResultHandlerForPureAILandingPageWrapper callResultHandlerForPureAILandingPageWrapper) {
        List<CallRecordResult> callRecordResultList = callResultHandlerForPureAILandingPageWrapper.getCallRecordResultList();
        if(CollectionUtils.isNotEmpty(callRecordResultList)){
            for(CallRecordResult callRecordResult : callRecordResultList){
                callRecordService.saveByCdrForLandingPage(callRecordResult);
            }
        }
    }
}
