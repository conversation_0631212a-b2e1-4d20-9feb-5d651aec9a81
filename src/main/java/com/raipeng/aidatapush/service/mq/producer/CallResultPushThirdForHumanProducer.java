package com.raipeng.aidatapush.service.mq.producer;

import com.raipeng.aidatapush.controller.wrapper.CallResultHandlerForHumanWrapper;

import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.raipeng.aidatapush.config.RabbitConstants.CALL_RESULT_PUSH_THIRD_FOR_HUMAN_EXCHANGE;
import static com.raipeng.aidatapush.config.RabbitConstants.CALL_RESULT_PUSH_THIRD_FOR_HUMAN_ROUTING;

@Component
public class CallResultPushThirdForHumanProducer {
    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void send(CallResultHandlerForHumanWrapper callResultHandlerForHumanWrapper) {
        this.rabbitTemplate.convertAndSend(CALL_RESULT_PUSH_THIRD_FOR_HUMAN_EXCHANGE,
                CALL_RESULT_PUSH_THIRD_FOR_HUMAN_ROUTING, callResultHandlerForHumanWrapper);
    }
}
