package com.raipeng.aidatapush.service.mq.consumer;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.raipeng.aidatacommon.model.Admin;
import com.raipeng.aidatacommon.model.TenantSecrets;
import com.raipeng.aidatapush.service.AdminService;
import com.raipeng.aidatapush.service.TenantSecretsService;
import com.raipeng.common.model.dto.TaskStatusNoticeDTO;
import com.raipeng.common.util.AESUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

import static com.raipeng.aidatapush.config.RabbitConstants.TASK_STATUS_NOTICE_QUEUE;

@Slf4j
@Component
@RabbitListener(queues = {TASK_STATUS_NOTICE_QUEUE})
public class TaskStatusNoticeHandler {

    @Autowired
    private AdminService adminService;

    @Autowired
    private TenantSecretsService tenantSecretsService;

    @Autowired
    private RestTemplate restTemplate;

    @RabbitHandler
    public void process(TaskStatusNoticeDTO data) {
        try {
            String groupId = data.getGroupId();
            String[] groupIdArray = groupId.split("_");
            String adminId = groupIdArray[2];
            Admin adminById = adminService.findById(Long.valueOf(adminId));

            TenantSecrets tenantSecrets = tenantSecretsService.findFirstByTenantId(groupIdArray[1]);
            long startTime = System.currentTimeMillis();
            if (adminById != null && StringUtils.isNotBlank(adminById.getTaskCallbackUrl()) && tenantSecrets != null) {
                Map<String, Object> map = new HashMap<>();
                JSONObject taskStatusMap = new JSONObject();
                taskStatusMap.put("taskId", data.getTaskId());
                taskStatusMap.put("taskName", data.getTaskName());
                taskStatusMap.put("taskStatus", data.getTaskStatus());
                map.put("text", AESUtils.AESEncode(tenantSecrets.getAesKey(), taskStatusMap.toJSONString()));
                map = new TreeMap<>(map);
                String jsonStr = JSON.toJSONString(map, SerializerFeature.SortField);
                jsonStr += tenantSecrets.getSalt();
                String sign = DigestUtils.md5Hex(jsonStr);
                map.put("sign", sign);
                Map<String, Object> res = null;
                try {
                    res = restTemplate.postForObject(adminById.getTaskCallbackUrl(), map, Map.class);
                } catch (Exception ex) {
                    log.error("Ai-data-push任务状态推送失败 data{} adminById{} tenantSecrets{} {}", data, adminById, tenantSecrets, res);
                    log.error("Ai-data-push任务状态推送失败:{}", ex.getMessage());
                    ex.printStackTrace();
                }
                log.info("Ai-data-push 任务状态推送結束 " + res + data + " 耗时:" + (System.currentTimeMillis() - startTime));
            }
        } catch (Exception e) {
            log.error("任务状态推送失败2 data{}", data);

        }
    }
}
