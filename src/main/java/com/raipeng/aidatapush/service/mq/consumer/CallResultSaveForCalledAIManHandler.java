package com.raipeng.aidatapush.service.mq.consumer;

import com.raipeng.aidatacommon.model.PhoneRecord;
import com.raipeng.aidatacommon.model.dto.CallRecordResult;
import com.raipeng.aidatapush.controller.wrapper.CallResultHandlerForAIManWrapper;
import com.raipeng.aidatapush.repository.CallRecordForHumanMachineRepository;
import com.raipeng.aidatapush.repository.PhoneRecordRepository;
import com.raipeng.aidatapush.service.CallRecordService;
import com.raipeng.aidatapush.service.TaskRedisService;

import com.raipeng.aidatacommon.entity.AiRedisTask;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static com.raipeng.aidatapush.config.RabbitConstants.RESULT_SAVE_FOR_CALLED_AI_MAN_QUEUE;

@Slf4j
@Component
@RabbitListener(queues = {RESULT_SAVE_FOR_CALLED_AI_MAN_QUEUE}, concurrency = "3")
public class CallResultSaveForCalledAIManHandler {
    @Autowired
    private PhoneRecordRepository phoneRecordRepository;

    @Autowired
    private CallRecordForHumanMachineRepository callRecordForHumanMachineRepository;

    @Autowired
    private CallRecordService callRecordService;

    @Autowired
    private TaskRedisService taskRedisService;

    @RabbitHandler
    public void process(CallResultHandlerForAIManWrapper callResultHandlerWrapper) {
        if (callResultHandlerWrapper.isIfCdr()){
            List<CallRecordResult> callRecordResultList = callResultHandlerWrapper.getCallRecordResultList();
            List<PhoneRecord> phoneRecordList = callResultHandlerWrapper.getPhoneRecordList();
            if(CollectionUtils.isNotEmpty(callRecordResultList)){
                for(CallRecordResult callRecordResult : callRecordResultList){
                    callRecordService.saveByCdrForAIMan(callRecordResult);
                }
            }
            if(CollectionUtils.isNotEmpty(phoneRecordList)){
                for(PhoneRecord phoneRecord : phoneRecordList){
                    phoneRecordRepository.updatePhoneRecordFromCdr(phoneRecord.getCalledNum(),
                            phoneRecord.getId(), phoneRecord.getCallStatus(), phoneRecord.getAddTime(),
                            phoneRecord.getIfRecalling(), phoneRecord.getLatestRecordId(), LocalDateTime.now());
                }
            }
        }else{
            List<CallRecordResult> callRecordResultList = callResultHandlerWrapper.getCallRecordResultList();
            List<PhoneRecord> phoneRecordList = callResultHandlerWrapper.getPhoneRecordList();
            if(CollectionUtils.isNotEmpty(callRecordResultList)){
                for(CallRecordResult callRecordResult : callRecordResultList){
                    callRecordForHumanMachineRepository.saveByCalledResult(callRecordResult.getWholeAudioFileUrl(),
                            callRecordResult.getCycleCount(),
                            callRecordResult.getSayCount(),
                            callRecordResult.getUserFullAnswerContent(),
                            StringUtils.isEmpty(callRecordResult.getIntentionClass()) ? "其他" : callRecordResult.getIntentionClass(),
                            callRecordResult.getIntentionLabelIds(),
                            callRecordResult.getIntentionLabels(),
                            callRecordResult.getCallOutTime(),
                            callRecordResult.getContactTime(),
                            callRecordResult.getTalkTimeEnd(),
                            callRecordResult.getTalkTimeStart(),
                            callRecordResult.getHitAnswerIds(),
                            callRecordResult.getScriptId(),
                            callRecordResult.getScriptLongId(),
                            callRecordResult.getSpeechCallId(), callRecordResult.getHitAdvanceIds(),
                            callRecordResult.getCorpusIds(),
                            callRecordResult.getHitSemanticIds(),
                            callRecordResult.getExtraInfo(),
                            callRecordResult.getLineGatewayNumbers(),
                            LocalDateTime.now()
                    );
                }
            }
            if(CollectionUtils.isNotEmpty(phoneRecordList)){
                for(PhoneRecord phoneRecord : phoneRecordList){
                    phoneRecordRepository.updatePhoneRecordFromNormalCallback(phoneRecord.getPutThroughNum(), phoneRecord.getId(),
                            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), phoneRecord.getLatestIntentionClass(),
                            LocalDateTime.now());
                }
            }
        }
        List<AiRedisTask> aiRedisTaskList = callResultHandlerWrapper.getAiRedisTaskList();
        if(CollectionUtils.isNotEmpty(aiRedisTaskList)){
            taskRedisService.updateTaskInRedisQueue(aiRedisTaskList);
        }
    }
}
