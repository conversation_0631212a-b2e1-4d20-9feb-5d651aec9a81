package com.raipeng.aidatapush.service.mq.producer;


import com.raipeng.aidatapush.controller.wrapper.CallResultHandlerForAIManWrapper;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.raipeng.aidatapush.config.RabbitConstants.CALL_RESULT_PUSH_ANTS_FOR_AI_MAN_EXCHANGE;
import static com.raipeng.aidatapush.config.RabbitConstants.CALL_RESULT_PUSH_ANTS_FOR_AI_MAN_ROUTING;


@Component
public class CallResultPushAntsForAIManProducer {
    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void send(CallResultHandlerForAIManWrapper callResultHandlerForAIMANWrapper) {
        this.rabbitTemplate.convertAndSend(CALL_RESULT_PUSH_ANTS_FOR_AI_MAN_EXCHANGE,
                CALL_RESULT_PUSH_ANTS_FOR_AI_MAN_ROUTING, callResultHandlerForAIMANWrapper);
    }
}
