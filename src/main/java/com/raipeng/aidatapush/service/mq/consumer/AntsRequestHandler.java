package com.raipeng.aidatapush.service.mq.consumer;


import com.alibaba.fastjson.JSONObject;
import com.antgroup.antchain.openapi.riskplus.models.CallbackUmktRobotcallResponse;
import com.raipeng.aidatapush.entity.AntsRequestLog;
import com.raipeng.aidatapush.service.RequestLogService;

import lombok.extern.slf4j.Slf4j;

import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.raipeng.aidatapush.config.RabbitConstants.ANT_CALL_BACK_RESULT_QUEUE;

@Slf4j
@Component
@RabbitListener(queues = {ANT_CALL_BACK_RESULT_QUEUE})
public class AntsRequestHandler {
    public static final Object LOCK = new Object();

    public static final List<AntsRequestLog> antsRequestLogList = new ArrayList<>();

    @Value("${ants.request.consume.num:400}")
    private Integer antsRequestConsumeNum;

    @Autowired
    private RequestLogService requestLogService;

    @RabbitHandler
    public void process(String antsRequestStringLog) {
        AntsRequestLog antsRequestLog = JSONObject.parseObject(antsRequestStringLog, AntsRequestLog.class);
        synchronized (LOCK) {
            antsRequestLogList.add(antsRequestLog);
            if (antsRequestLogList.size() >= antsRequestConsumeNum) {
                addRequestLog();
            }
        }
    }

    @Scheduled(cron = "* */2 * * * *")
    public void processAuto() {
        synchronized (LOCK) {
            if (antsRequestLogList.size() > 0) {
                addRequestLog();
            }
        }
    }

    private void addRequestLog() {
        List<AntsRequestLog> successRequest = new ArrayList<>();
        List<AntsRequestLog> failedRequest  = new ArrayList<>();
        for (AntsRequestLog antsRequestLog : antsRequestLogList) {
            CallbackUmktRobotcallResponse response = antsRequestLog.getResponse();
            if (response != null && "OK".equals(response.getResultCode())) {
                successRequest.add(antsRequestLog);
            } else {
                failedRequest.add(antsRequestLog);
            }
        }
        if (successRequest.size() > 0) {
            addSuccessRequest(successRequest);
        }
        if (failedRequest.size() > 0) {
            addFailedRequest(failedRequest);
        }
        antsRequestLogList.clear();
    }

    private void addSuccessRequest(List<AntsRequestLog> requestLogs) {
        LocalDateTime startTime = requestLogs.get(0).getStartTime();
        LocalDateTime endTime = antsRequestLogList.get(requestLogs.size() - 1).getEndTime();
        Map<String, Object> response = new HashMap<>();
        response.put("code", "200");
        response.put("message", "推送蚂蚁成功");
        requestLogService.addLog(JSONObject.toJSONString(requestLogs), response,"call_record_push_for_ants",startTime, endTime);
    }

    private void addFailedRequest(List<AntsRequestLog> requestLogs) {
        LocalDateTime startTime = requestLogs.get(0).getStartTime();
        LocalDateTime endTime = requestLogs.get(requestLogs.size() - 1).getEndTime();
        Map<String, Object> response = new HashMap<>();
        response.put("code", "400");
        response.put("message", "推送蚂蚁失败");
        requestLogService.addLog(JSONObject.toJSONString(requestLogs), response,"call_record_push_for_ants",startTime, endTime);

    }
}
