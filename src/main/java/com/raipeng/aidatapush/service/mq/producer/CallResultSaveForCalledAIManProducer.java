package com.raipeng.aidatapush.service.mq.producer;

import com.raipeng.aidatapush.controller.wrapper.CallResultHandlerForAIManWrapper;

import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.raipeng.aidatapush.config.RabbitConstants.RESULT_SAVE_FOR_CALLED_AI_MAN_EXCHANGE;
import static com.raipeng.aidatapush.config.RabbitConstants.RESULT_SAVE_FOR_CALLED_AI_MAN_ROUTING;


@Component
public class CallResultSaveForCalledAIManProducer {
    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void send(CallResultHandlerForAIManWrapper callResultHandlerForAIManWrapper) {
        this.rabbitTemplate.convertAndSend(RESULT_SAVE_FOR_CALLED_AI_MAN_EXCHANGE,
                RESULT_SAVE_FOR_CALLED_AI_MAN_ROUTING, callResultHandlerForAIManWrapper);
    }
}
