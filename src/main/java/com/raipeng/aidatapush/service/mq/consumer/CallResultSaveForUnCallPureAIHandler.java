package com.raipeng.aidatapush.service.mq.consumer;

import com.raipeng.aidatacommon.model.PhoneRecord;
import com.raipeng.aidatacommon.model.record.CallRecord;
import com.raipeng.aidatapush.controller.wrapper.CallResultHandlerForPureAIWrapper;
import com.raipeng.aidatapush.repository.PhoneRecordRepository;
import com.raipeng.aidatapush.service.CallRecordMultiService;
import com.raipeng.aidatapush.service.TaskRedisService;

import com.raipeng.aidatacommon.entity.AiRedisTask;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;

import static com.raipeng.aidatapush.config.RabbitConstants.RESULT_SAVE_FOR_UN_CALL_PURE_AI_QUEUE;


@Slf4j
@Component
@RabbitListener(queues = {RESULT_SAVE_FOR_UN_CALL_PURE_AI_QUEUE}, concurrency = "5")
public class CallResultSaveForUnCallPureAIHandler {
    @Autowired
    private CallRecordMultiService callRecordMultiService;

    @Autowired
    private PhoneRecordRepository phoneRecordRepository;

    @Autowired
    private TaskRedisService taskRedisService;

    @RabbitHandler
    public void process(CallResultHandlerForPureAIWrapper callResultHandlerForPureAIWrapper) {
        LocalDateTime start = LocalDateTime.now();
        List<CallRecord> callRecordList = callResultHandlerForPureAIWrapper.getCallRecordList();
        List<PhoneRecord> phoneRecordList = callResultHandlerForPureAIWrapper.getPhoneRecordList();
        List<AiRedisTask> aiRedisTasks = callResultHandlerForPureAIWrapper.getAiRedisTasks();
        if (CollectionUtils.isNotEmpty(callRecordList)) {
            callRecordMultiService.saveAll(callRecordList);
        }
        LocalDateTime start2 = LocalDateTime.now();
        if (CollectionUtils.isNotEmpty(phoneRecordList)) {
            phoneRecordRepository.saveAll(phoneRecordList);
        }
        LocalDateTime start3 = LocalDateTime.now();
        if (CollectionUtils.isNotEmpty(aiRedisTasks)) {
            taskRedisService.updateTaskInRedisQueue(aiRedisTasks);
        }
        LocalDateTime end = LocalDateTime.now();
        log.info("批量保存{}条{}记录, call表耗时{}, phone表耗时:{}, taskRedis耗时:{},",
                callResultHandlerForPureAIWrapper.getCallRecordList().size(), "纯AI未呼通",
                Duration.between(start, start2).toMillis(),
                Duration.between(start2, start3).toMillis(),
                Duration.between(start3, end).toMillis());
    }
}
