package com.raipeng.aidatapush.service.mq.producer;


import com.raipeng.aidatapush.controller.wrapper.CallResultHandlerForPureAIWrapper;

import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.raipeng.aidatapush.config.RabbitConstants.CALL_RESULT_PUSH_ANTS_FOR_PURE_AI_EXCHANGE;
import static com.raipeng.aidatapush.config.RabbitConstants.CALL_RESULT_PUSH_ANTS_FOR_PURE_AI_ROUTING;


@Component
public class CallResultPushAntsForAIPureProducer {
    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void send(CallResultHandlerForPureAIWrapper callResultHandlerForPureAIWrapper) {
        this.rabbitTemplate.convertAndSend(CALL_RESULT_PUSH_ANTS_FOR_PURE_AI_EXCHANGE,
                CALL_RESULT_PUSH_ANTS_FOR_PURE_AI_ROUTING, callResultHandlerForPureAIWrapper);
    }
}
