package com.raipeng.aidatapush.service.mq.producer;

import com.raipeng.aidatapush.controller.wrapper.CallResultHandlerForPureAILandingPageWrapper;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.raipeng.aidatapush.config.RabbitConstants.RESULT_SAVE_FOR_CALLED_LANDING_PAGE_PURE_AI_EXCHANGE;
import static com.raipeng.aidatapush.config.RabbitConstants.RESULT_SAVE_FOR_CALLED_LANDING_PAGE_PURE_AI_ROUTING;

@Component
public class CallResultSaveForCalledPureAILandingPageProducer {
    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void send(CallResultHandlerForPureAILandingPageWrapper callResultHandlerForPureAILandingPageWrapper) {
        this.rabbitTemplate.convertAndSend(RESULT_SAVE_FOR_CALLED_LANDING_PAGE_PURE_AI_EXCHANGE,
                RESULT_SAVE_FOR_CALLED_LANDING_PAGE_PURE_AI_ROUTING, callResultHandlerForPureAILandingPageWrapper);
    }
}
