package com.raipeng.aidatapush.service.mq.producer.newCallback;

import com.raipeng.aidatacommon.model.CallRecordForManualDirect;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.raipeng.aidatapush.config.RabbitConstants.UN_CALL_DATA_PUSH_THIRD_FOR_HUMAN_EXCHANGE;
import static com.raipeng.aidatapush.config.RabbitConstants.UN_CALL_DATA_PUSH_THIRD_FOR_HUMAN_ROUTING;

@Component
public class UnCallDataPushThirdForHumanProducer {
    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void send(List<CallRecordForManualDirect> callRecordList) {
        this.rabbitTemplate.convertAndSend(UN_CALL_DATA_PUSH_THIRD_FOR_HUMAN_EXCHANGE,
                UN_CALL_DATA_PUSH_THIRD_FOR_HUMAN_ROUTING, callRecordList);
    }
}
