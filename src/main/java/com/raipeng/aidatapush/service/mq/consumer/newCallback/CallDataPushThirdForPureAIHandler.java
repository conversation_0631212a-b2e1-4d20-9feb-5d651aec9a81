package com.raipeng.aidatapush.service.mq.consumer.newCallback;

import com.raipeng.aidatacommon.model.record.CallRecord;
import com.raipeng.aidatapush.service.ConsumePushThirdDataService;

import lombok.extern.slf4j.Slf4j;

import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.raipeng.aidatapush.config.RabbitConstants.CALL_DATA_PUSH_THIRD_FOR_PURE_AI_QUEUE;


@Slf4j
@Component
@RabbitListener(queues = {CALL_DATA_PUSH_THIRD_FOR_PURE_AI_QUEUE}, concurrency = "10")
public class CallDataPushThirdForPureAIHandler {
    @Autowired
    private ConsumePushThirdDataService consumePushThirdDataService;


    @RabbitHandler
    public void handle(List<CallRecord> callRecords) {
        consumePushThirdDataService.consumePureAIPushData(callRecords, "纯AI呼通");
    }
}
