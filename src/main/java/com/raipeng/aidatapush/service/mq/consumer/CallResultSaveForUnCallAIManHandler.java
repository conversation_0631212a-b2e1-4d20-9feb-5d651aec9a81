package com.raipeng.aidatapush.service.mq.consumer;

import com.raipeng.aidatapush.controller.wrapper.CallResultHandlerForAIManWrapper;
import com.raipeng.aidatapush.service.HandleUnCallResultService;

import lombok.extern.slf4j.Slf4j;

import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.raipeng.aidatapush.config.RabbitConstants.RESULT_SAVE_FOR_UN_CALL_AI_MAN_QUEUE;

@Slf4j
@Component
@RabbitListener(queues = {RESULT_SAVE_FOR_UN_CALL_AI_MAN_QUEUE}, concurrency = "5")
public class CallResultSaveForUnCallAIManHandler {
    @Autowired
    private HandleUnCallResultService handleUnCallResultService;

    @RabbitHandler
    public void process(CallResultHandlerForAIManWrapper callResultHandlerForAIManWrapper) {
        handleUnCallResultService.saveUnCallResultForAIMan(callResultHandlerForAIManWrapper);
    }
}
