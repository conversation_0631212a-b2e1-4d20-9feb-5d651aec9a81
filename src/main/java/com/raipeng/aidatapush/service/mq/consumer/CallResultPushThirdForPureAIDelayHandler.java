package com.raipeng.aidatapush.service.mq.consumer;


import com.raipeng.aidatacommon.model.record.CallRecord;
import com.raipeng.aidatapush.controller.wrapper.CallResultHandlerForPureAIWrapper;
import com.raipeng.aidatapush.service.HandlerPushThirdService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.raipeng.aidatapush.config.RabbitConstants.DEAD_CALL_RESULT_PUSH_THIRD_FOR_PURE_AI_QUEUE;

@Slf4j
@Component
@RabbitListener(queues = {DEAD_CALL_RESULT_PUSH_THIRD_FOR_PURE_AI_QUEUE}, concurrency = "5")
public class CallResultPushThirdForPureAIDelayHandler {
    @Autowired
    private HandlerPushThirdService handlerPushThirdService;

    @RabbitHandler
    public void process(CallResultHandlerForPureAIWrapper callResultHandlerForPureAIWrapper) {
        List<CallRecord> pushThirdListDelay = callResultHandlerForPureAIWrapper.getPushThirdListDelay();
        Map<Long, String> taskGroupIdMap = callResultHandlerForPureAIWrapper.getTaskGroupIdMap();
        Map<String, List<CallRecord>> pushList = pushThirdListDelay.stream().collect(Collectors.groupingBy(a -> taskGroupIdMap.getOrDefault(Long.valueOf(a.getTaskId()), "")));
        for(Map.Entry<String, List<CallRecord>> entry : pushList.entrySet()){
            String key = entry.getKey();
            if(StringUtils.isEmpty(key)){
                log.error("Exception=>未查询到对应通话记录的GroupId！！！"+entry.getValue());
            }else{
                String[] s = key.split("_");
                handlerPushThirdService.pushResultToThirdForAI(entry.getValue(), s[1], s[2], true);
            }
        }
    }
}
