package com.raipeng.aidatapush.service.mq.producer;

import com.raipeng.common.model.dto.TaskStatusNoticeDTO;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.raipeng.aidatapush.config.RabbitConstants.TASK_STATUS_NOTICE_EXCHANGE;
import static com.raipeng.aidatapush.config.RabbitConstants.TASK_STATUS_NOTICE_ROUTING;


@Component
public class TaskStatusNoticeProducer {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void send(TaskStatusNoticeDTO taskStatusNoticeDTO) {
        this.rabbitTemplate.convertAndSend(TASK_STATUS_NOTICE_EXCHANGE, TASK_STATUS_NOTICE_ROUTING, taskStatusNoticeDTO);
    }
}
