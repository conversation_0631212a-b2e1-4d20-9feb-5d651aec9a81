package com.raipeng.aidatapush.service.mq.producer.newCallback;

import com.raipeng.aidatacommon.thirdrequests.entity.CallDataEntity;

import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.raipeng.aidatapush.config.RabbitConstants.PRE_X_CALL_DATA_PUSH_THIRD_FOR_HUMAN_EXCHANGE;
import static com.raipeng.aidatapush.config.RabbitConstants.PRE_X_CALL_DATA_PUSH_THIRD_FOR_HUMAN_ROUTING;

@Component
public class PreXCallDataPushThirdForHumanProducer {
    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void send(CallDataEntity entity) {
        this.rabbitTemplate.convertAndSend(PRE_X_CALL_DATA_PUSH_THIRD_FOR_HUMAN_EXCHANGE,
                PRE_X_CALL_DATA_PUSH_THIRD_FOR_HUMAN_ROUTING, entity);
    }
}
