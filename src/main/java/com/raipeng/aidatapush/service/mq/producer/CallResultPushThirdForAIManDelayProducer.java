package com.raipeng.aidatapush.service.mq.producer;


import com.raipeng.aidatapush.controller.wrapper.CallResultHandlerForAIManWrapper;

import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.raipeng.aidatapush.config.RabbitConstants.*;


@Component
public class CallResultPushThirdForAIManDelayProducer {
    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void send(CallResultHandlerForAIManWrapper callResultHandlerForAIManWrapper) {
        this.rabbitTemplate.convertAndSend(DELAY_CALL_RESULT_PUSH_THIRD_FOR_AI_MAN_EXCHANGE,
                DELAY_CALL_RESULT_PUSH_THIRD_FOR_AI_MAN_ROUTING, callResultHandlerForAIManWrapper);
    }
}
