package com.raipeng.aidatapush.service.mq.consumer;

import com.raipeng.aidatacommon.model.dto.CallRecordResult;
import com.raipeng.aidatapush.controller.wrapper.CallResultHandlerForHumanWrapper;
import com.raipeng.aidatapush.repository.CallRecordForManualDirectRepository;
import com.raipeng.aidatapush.service.CallRecordService;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

import static com.raipeng.aidatapush.config.RabbitConstants.RESULT_SAVE_FOR_CALLED_HUMAN_QUEUE;


@Slf4j
@Component
@RabbitListener(queues = {RESULT_SAVE_FOR_CALLED_HUMAN_QUEUE}, concurrency = "3")
public class CallResultSaveForCalledHumanHandler {
    @Autowired
    private CallRecordForManualDirectRepository callRecordForManualDirectRepository;

    @Autowired
    private CallRecordService callRecordService;

    @RabbitHandler
    public void process(CallResultHandlerForHumanWrapper callResultHandlerWrapper) {
        if (callResultHandlerWrapper.isIfCdr()){
            List<CallRecordResult> callRecordResultList = callResultHandlerWrapper.getCallRecordResultList();
            if(CollectionUtils.isNotEmpty(callRecordResultList)){
                for(CallRecordResult callRecordResult : callRecordResultList){
                    callRecordService.saveByCdrForHuman(callRecordResult);
                }
            }
        }else{
            List<CallRecordResult> callRecordResultList = callResultHandlerWrapper.getCallRecordResultList();
            if(CollectionUtils.isNotEmpty(callRecordResultList)){
                for(CallRecordResult callRecordResult : callRecordResultList){
                    callRecordForManualDirectRepository.saveByCalledResult(
                            callRecordResult.getWholeAudioFileUrl(),
                            callRecordResult.getUserFullAnswerContent(),
                            callRecordResult.getCallOutTime(),
                            callRecordResult.getContactTime(),
                            callRecordResult.getTalkTimeEnd(),
                            callRecordResult.getTalkTimeStart(),
                            callRecordResult.getSpeechCallId(),
                            callRecordResult.getLineGatewayNumbers(),
                            LocalDateTime.now()
                    );
                }
            }
        }
    }
}
