package com.raipeng.aidatapush.service.mq.consumer;


import com.raipeng.aidatapush.service.callresulthandler.impl.UnCallResultHandleForPureAIService;

import lombok.extern.slf4j.Slf4j;

import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.raipeng.aidatapush.config.RabbitConstants.PRE_X_CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_QUEUE;

@Slf4j
@Component
@RabbitListener(queues = {PRE_X_CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_QUEUE})
public class PreXUnCallForPureAIHandler {
    @Autowired
    private UnCallResultHandleForPureAIService unCallResultHandleForPureAIService;

    @RabbitHandler
    public void process(String callResultString) {
        unCallResultHandleForPureAIService.disPatchExceptPreXCallResult(callResultString);
    }
}
