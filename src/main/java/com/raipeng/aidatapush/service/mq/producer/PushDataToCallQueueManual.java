package com.raipeng.aidatapush.service.mq.producer;


import com.rabbitmq.client.Channel;

import lombok.extern.slf4j.Slf4j;

import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeoutException;

import static com.raipeng.aidatapush.config.RabbitConstants.*;

@Slf4j
@Component
public class PushDataToCallQueueManual {
    @Autowired
    private RabbitTemplate rabbitTemplate;

    private Channel channel;

    public void sendCallResult(String result) {
        try {
            channel.basicPublish(CALL_PHONE_RESULT_NOTICE_SPEECH_EXCHANGE,
                    CALL_PHONE_RESULT_NOTICE_SPEECH_ROUTING, null,
                    result.getBytes(StandardCharsets.UTF_8));
        } catch (IOException e) {
            log.error("Exception=>manualPush手动推送呼通数据失败{}", result);
        }
    }

    public void sendUnCallResult(String result) {
        try {
            channel.basicPublish(CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_EXCHANGE,
                    CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_ROUTING, null,
                    result.getBytes(StandardCharsets.UTF_8));
        } catch (IOException e) {
            log.error("Exception=>manualPush手动推送未呼通数据失败{}", result);
        }
    }

    public void startChannel() {
        channel = rabbitTemplate.getConnectionFactory().createConnection().createChannel(false);
        log.info("manualPush start channel success");
    }

    public void closeChannel() {
        try {
            channel.close();
            log.info("manualPush close channel success");
        } catch (IOException | TimeoutException e) {
            log.error("manualPush close channel error");
            throw new RuntimeException(e);
        }
    }

}
