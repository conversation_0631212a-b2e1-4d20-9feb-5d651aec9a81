package com.raipeng.aidatapush.service.mq.producer;

import com.raipeng.aidatapush.entity.AntsRequestLog;

import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.raipeng.aidatapush.config.RabbitConstants.*;


@Component
public class AntsRequestProducer {
    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void send(String antsRequestLog) {
        this.rabbitTemplate.convertAndSend(ANT_CALL_BACK_RESULT_EXCHANGE,
                ANT_CALL_BACK_RESULT_ROUTING, antsRequestLog);
    }
}
