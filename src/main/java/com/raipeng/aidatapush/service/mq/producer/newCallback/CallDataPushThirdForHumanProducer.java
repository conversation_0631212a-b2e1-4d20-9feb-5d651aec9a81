package com.raipeng.aidatapush.service.mq.producer.newCallback;

import com.raipeng.aidatacommon.model.CallRecordForManualDirect;

import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.raipeng.aidatapush.config.RabbitConstants.CALL_DATA_PUSH_THIRD_FOR_HUMAN_EXCHANGE;
import static com.raipeng.aidatapush.config.RabbitConstants.CALL_DATA_PUSH_THIRD_FOR_HUMAN_ROUTING;

@Component
public class CallDataPushThirdForHumanProducer {
    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void send(List<CallRecordForManualDirect> callRecordList) {
        this.rabbitTemplate.convertAndSend(CALL_DATA_PUSH_THIRD_FOR_HUMAN_EXCHANGE,
                CALL_DATA_PUSH_THIRD_FOR_HUMAN_ROUTING, callRecordList);
    }
}
