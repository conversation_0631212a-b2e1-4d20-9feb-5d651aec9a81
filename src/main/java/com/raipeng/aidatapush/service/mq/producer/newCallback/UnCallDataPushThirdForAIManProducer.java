package com.raipeng.aidatapush.service.mq.producer.newCallback;

import com.raipeng.aidatacommon.model.CallRecordForHumanMachine;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.raipeng.aidatapush.config.RabbitConstants.UN_CALL_DATA_PUSH_THIRD_FOR_AI_MAN_EXCHANGE;
import static com.raipeng.aidatapush.config.RabbitConstants.UN_CALL_DATA_PUSH_THIRD_FOR_AI_MAN_ROUTING;

@Component
public class UnCallDataPushThirdForAIManProducer {
    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void send(List<CallRecordForHumanMachine> callRecordList) {
        this.rabbitTemplate.convertAndSend(UN_CALL_DATA_PUSH_THIRD_FOR_AI_MAN_EXCHANGE,
                UN_CALL_DATA_PUSH_THIRD_FOR_AI_MAN_ROUTING, callRecordList);
    }
}
