package com.raipeng.aidatapush.service.mq.producer;

import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.raipeng.aidatapush.config.RabbitConstants.PRE_X_CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_DIRECT_CALL_EXCHANGE;
import static com.raipeng.aidatapush.config.RabbitConstants.PRE_X_CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_DIRECT_CALL_ROUTING;

@Component
public class PreXUnCallForHumanProducer {
    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void send(String xCallResult) {
        this.rabbitTemplate.convertAndSend(PRE_X_CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_DIRECT_CALL_EXCHANGE,
                PRE_X_CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_DIRECT_CALL_ROUTING, xCallResult);
    }
}
