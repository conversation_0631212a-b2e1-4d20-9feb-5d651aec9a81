package com.raipeng.aidatapush.service.mq.producer;


import com.raipeng.aidatapush.entity.AntsErrorLogWrapper;

import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.raipeng.aidatapush.config.RabbitConstants.ANT_ERROR_LOG_EXCHANGE;
import static com.raipeng.aidatapush.config.RabbitConstants.ANT_ERROR_LOG_ROUTING;

@Component
public class AntsErrorLogProducer {
    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void send(AntsErrorLogWrapper antsErrorLogWrapper) {
        this.rabbitTemplate.convertAndSend(ANT_ERROR_LOG_EXCHANGE,
                ANT_ERROR_LOG_ROUTING, antsErrorLogWrapper);
    }
}
