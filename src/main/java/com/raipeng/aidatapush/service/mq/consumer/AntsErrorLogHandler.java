package com.raipeng.aidatapush.service.mq.consumer;


import com.raipeng.aidatacommon.model.AntsErrorLog;
import com.raipeng.aidatapush.entity.AntsErrorLogWrapper;
import com.raipeng.aidatapush.repository.AntsErrorLogRepository;

import lombok.extern.slf4j.Slf4j;

import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

import static com.raipeng.aidatapush.config.RabbitConstants.ANT_ERROR_LOG_QUEUE;

@Slf4j
@Component
@RabbitListener(queues = {ANT_ERROR_LOG_QUEUE})
public class AntsErrorLogHandler {
    public static final Object LOCK = new Object();

    public static final List<AntsErrorLogWrapper> antsErrorLogWrappers = new ArrayList<>();

    @Value("${ants.error.log.consume.num:400}")
    private Integer antsErrorLogConsumeNum;

    @Autowired
    private AntsErrorLogRepository antsErrorLogRepository;

    @RabbitHandler
    public void process(AntsErrorLogWrapper antsErrorLogWrapper) {
        synchronized (LOCK) {
            antsErrorLogWrappers.add(antsErrorLogWrapper);
            if (antsErrorLogWrappers.size() >= antsErrorLogConsumeNum) {
                addRequestLog();
            }
        }
    }

    @Scheduled(cron = "* */2 * * * *")
    public void processAuto() {
        synchronized (LOCK) {
            if (antsErrorLogWrappers.size() > 0) {
                addRequestLog();
            }
        }
    }

    private void addRequestLog() {
        List<AntsErrorLog> antsErrorLogs = new ArrayList<>();
        for (AntsErrorLogWrapper wrapper : antsErrorLogWrappers) {
            AntsErrorLog antsErrorLog = new AntsErrorLog();
            antsErrorLog.setAccountId(wrapper.getAccountId());
            antsErrorLog.setCallRecordId(wrapper.getCallRecordId());
            antsErrorLog.setPhoneRecordId(wrapper.getPhoneRecordId());
            antsErrorLogs.add(antsErrorLog);
        }
        antsErrorLogRepository.saveAll(antsErrorLogs);
        antsErrorLogWrappers.clear();
    }
}
