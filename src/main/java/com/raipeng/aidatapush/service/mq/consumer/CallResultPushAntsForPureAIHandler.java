package com.raipeng.aidatapush.service.mq.consumer;


import com.raipeng.aidatacommon.model.record.CallRecord;
import com.raipeng.aidatapush.controller.wrapper.CallResultHandlerForPureAIWrapper;
import com.raipeng.aidatapush.service.HandlerPushThirdService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.raipeng.aidatapush.config.RabbitConstants.CALL_RESULT_PUSH_ANTS_FOR_PURE_AI_QUEUE;

@Slf4j
@Component
@RabbitListener(queues = {CALL_RESULT_PUSH_ANTS_FOR_PURE_AI_QUEUE}, concurrency = "3")
public class CallResultPushAntsForPureAIHandler {
    @Autowired
    private HandlerPushThirdService handlerPushThirdService;

    @RabbitHandler
    public void process(CallResultHandlerForPureAIWrapper callResultHandlerForPureAIWrapper) {
        List<CallRecord> pushAntsList = callResultHandlerForPureAIWrapper.getPushAntsList();
        Map<Long, String> taskGroupIdMap = callResultHandlerForPureAIWrapper.getTaskGroupIdMap();
        Map<String, List<CallRecord>> pushList = pushAntsList.stream().collect(Collectors.groupingBy(a -> taskGroupIdMap.getOrDefault(Long.valueOf(a.getTaskId()), "")));
        for(Map.Entry<String, List<CallRecord>> entry : pushList.entrySet()){
            String key = entry.getKey();
            if(StringUtils.isEmpty(key)){
                log.error("未查询到对应通话记录的GroupId！！！"+entry.getValue());
            }else{
                String[] s = key.split("_");
                handlerPushThirdService.pushResultToAntsForAI(entry.getValue(), s[2], false);
            }
        }
    }
}
