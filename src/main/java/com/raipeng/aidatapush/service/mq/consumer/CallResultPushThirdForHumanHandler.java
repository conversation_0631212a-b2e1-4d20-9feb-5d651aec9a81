package com.raipeng.aidatapush.service.mq.consumer;


import com.raipeng.aidatacommon.model.CallRecordForManualDirect;
import com.raipeng.aidatapush.controller.wrapper.CallResultHandlerForHumanWrapper;
import com.raipeng.aidatapush.service.HandlerPushThirdService;

import lombok.extern.slf4j.Slf4j;

import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.raipeng.aidatapush.config.RabbitConstants.CALL_RESULT_PUSH_THIRD_FOR_HUMAN_QUEUE;

@Slf4j
@Component
@RabbitListener(queues = {CALL_RESULT_PUSH_THIRD_FOR_HUMAN_QUEUE}, concurrency = "3")
public class CallResultPushThirdForHumanHandler {
    @Autowired
    private HandlerPushThirdService handlerPushThirdService;

    @RabbitHandler
    public void process(CallResultHandlerForHumanWrapper callResultHandlerWrapper) {
        List<CallRecordForManualDirect> pushThirdList = callResultHandlerWrapper.getPushThirdList();
        Map<String, List<CallRecordForManualDirect>> pushList = pushThirdList.stream().collect(Collectors.groupingBy(CallRecordForManualDirect::getGroupId));
        for(Map.Entry<String, List<CallRecordForManualDirect>> entry : pushList.entrySet()){
            String key = entry.getKey();
            if(StringUtils.isEmpty(key)){
                log.error("未查询到对应通话记录的GroupId！！！"+entry.getValue());
            }else{
                String[] s = key.split("_");
                handlerPushThirdService.pushResultToThirdForHuman(entry.getValue(), key, s[1], s[2]);
            }
        }
    }
}
