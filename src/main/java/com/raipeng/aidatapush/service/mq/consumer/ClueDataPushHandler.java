package com.raipeng.aidatapush.service.mq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.raipeng.aidatacommon.model.Admin;
import com.raipeng.aidatapush.controller.wrapper.ClueDataPushWrapper;
import com.raipeng.aidatapush.service.HandleCallDataPushThirdForAIManService;
import com.raipeng.aidatapush.service.HandlerPushThirdService;
import com.raipeng.aidatapush.service.RedisSecondCacheService;
import com.raipeng.aidatapush.service.mq.producer.newCallback.CallDataPushThirdForAIManProducer;
import com.raipeng.aidatapush.service.mq.producer.newCallback.CallSmsPushThirdForAIManProducer;
import com.raipeng.aidatapush.utils.CallDataHandleUtils;
import com.raipeng.aidatapush.utils.RaiYiEncryptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.raipeng.aidatapush.config.RabbitConstants.CLUE_DATA_PUSH_QUEUE;

@Slf4j
@Component
@RabbitListener(queues = {CLUE_DATA_PUSH_QUEUE}, concurrency = "3")
public class ClueDataPushHandler {

    @Autowired
    private HandlerPushThirdService handlerPushThirdService;

    @Autowired
    private HandleCallDataPushThirdForAIManService handleCallDataPushThirdForAIManService;

    @Autowired
    private CallSmsPushThirdForAIManProducer callSmsPushThirdForAIManProducer;

    @Autowired
    private CallDataPushThirdForAIManProducer callDataPushThirdForAIManProducer;

    @Autowired
    private RedisSecondCacheService redisSecondCacheService;

    @RabbitHandler
    public void process(String clueDataPushWrapper) {
        ClueDataPushWrapper wrapper = JSONObject.parseObject(clueDataPushWrapper, ClueDataPushWrapper.class);
        Admin admin = wrapper.getAdmin();
        String callSmsCallBackUrl = admin.getCallSmsCallBackUrl();
        String callDataCallBackUrl = admin.getCallDataCallBackUrl();
        if (callSmsCallBackUrl != null || callDataCallBackUrl != null) {
            try {
                String phone = wrapper.getRecord().getPhone();
                if (!admin.getIsForEncryptionPhones()) {
                    JSONObject decryptionResultBatch = RaiYiEncryptionUtil.getDecryptionResultBatch(phone);
                    if (decryptionResultBatch != null) {
                        String plainPhone = decryptionResultBatch.getString(phone);
                        wrapper.getRecord().setPlainPhone(plainPhone);
                    }
                } else {
                    wrapper.getRecord().setPlainPhone(wrapper.getRecord().getPhone());
                }
            } catch (Exception e) {
                log.error("[Exception]=>人机协同接通初次号码解密失败", e);
            }
            handleCallDataPushThirdForAIManService.pushClueDataToCarClue(admin, wrapper);
            callSmsPushThirdForAIManProducer.send(CallDataHandleUtils.parseCallSmsForAIManFromWrapper(wrapper));
            callDataPushThirdForAIManProducer.send(CallDataHandleUtils.parseCallDataForAIManFromWrapper(wrapper, redisSecondCacheService));
            return;
        }
        if (StringUtils.isNotBlank(admin.getAntAccessKey()) && StringUtils.isNotBlank(admin.getAntSecretKey())) {
            handlerPushThirdService.pushResultToAntsForAIMAN(wrapper,null, null, null);
            return;
        }
        handlerPushThirdService.pushManualIntentionToThird(wrapper);
    }
}
