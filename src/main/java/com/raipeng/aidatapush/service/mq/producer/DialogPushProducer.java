package com.raipeng.aidatapush.service.mq.producer;

import com.alibaba.fastjson.JSONObject;
import com.raipeng.common.util.JsonUtil;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.raipeng.aidatapush.config.RabbitConstants.*;

@Component
public class DialogPushProducer {
    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void send(JSONObject jsonObject) {
        this.rabbitTemplate.convertAndSend(DELAY_DIALOG_PUSH_EXCHANGE,
                DELAY_DIALOG_PUSH_ROUTING, jsonObject,
                message -> {
                    message.getMessageProperties().setExpiration(Long.toString(300000));
                    return message;
                });



    }
}
