package com.raipeng.aidatapush.service.mq.consumer;


import com.raipeng.aidatacommon.model.CallRecordForHumanMachine;
import com.raipeng.aidatapush.controller.wrapper.CallResultHandlerForAIManWrapper;
import com.raipeng.aidatapush.service.HandlerPushThirdService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.raipeng.aidatapush.config.RabbitConstants.DEAD_CALL_RESULT_PUSH_THIRD_FOR_AI_MAN_QUEUE;

@Slf4j
@Component
@RabbitListener(queues = {DEAD_CALL_RESULT_PUSH_THIRD_FOR_AI_MAN_QUEUE}, concurrency = "3")
public class CallResultPushThirdForAIManDelayHandler {
    @Autowired
    private HandlerPushThirdService handlerPushThirdService;

    @RabbitHandler
    public void process(CallResultHandlerForAIManWrapper callResultHandlerWrapper) {
        List<CallRecordForHumanMachine> pushThirdListDelay = callResultHandlerWrapper.getPushThirdListDelay();
        Map<Long, String> taskGroupIdMap = callResultHandlerWrapper.getTaskGroupIdMap();
        Map<String, List<CallRecordForHumanMachine>> pushList = pushThirdListDelay.stream().collect(Collectors.groupingBy(a -> taskGroupIdMap.getOrDefault(Long.valueOf(a.getTaskId()), "")));
        for(Map.Entry<String, List<CallRecordForHumanMachine>> entry : pushList.entrySet()){
            String key = entry.getKey();
            if(StringUtils.isEmpty(key)){
                log.error("Exception=>未查询到对应通话记录的GroupId！！！"+entry.getValue());
            }else{
                String[] s = key.split("_");
                handlerPushThirdService.pushResultToThirdForAIMan(entry.getValue(), s[1], s[2], true);
            }
        }
    }
}
