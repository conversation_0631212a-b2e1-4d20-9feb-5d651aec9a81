package com.raipeng.aidatapush.service.mq.consumer;


import com.raipeng.aidatapush.service.callresulthandler.impl.CallResultHandleForAIManService;

import lombok.extern.slf4j.Slf4j;

import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.raipeng.aidatapush.config.RabbitConstants.PRE_X_CALL_PHONE_RESULT_NOTICE_SPEECH_TAKE_OVER_LISTEN_IN_QUEUE;


@Slf4j
@Component
@RabbitListener(queues = {PRE_X_CALL_PHONE_RESULT_NOTICE_SPEECH_TAKE_OVER_LISTEN_IN_QUEUE})
public class PreXCallForAIManHandler {
    @Autowired
    private CallResultHandleForAIManService callResultHandleForAIManService;

    @RabbitHandler
    public void process(String callResultString) {
        callResultHandleForAIManService.disPatchExceptPreXCallResult(callResultString);
    }
}
