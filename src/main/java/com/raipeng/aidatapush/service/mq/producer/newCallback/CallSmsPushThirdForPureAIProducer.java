package com.raipeng.aidatapush.service.mq.producer.newCallback;

import com.raipeng.aidatacommon.model.record.CallRecord;
import com.raipeng.aidatapush.controller.wrapper.CallResultHandlerForPureAIWrapper;

import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


import java.util.List;

import static com.raipeng.aidatapush.config.RabbitConstants.CALL_SMS_PUSH_THIRD_FOR_PURE_AI_EXCHANGE;
import static com.raipeng.aidatapush.config.RabbitConstants.CALL_SMS_PUSH_THIRD_FOR_PURE_AI_ROUTING;

@Component
public class CallSmsPushThirdForPureAIProducer {
    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void send(List<CallRecord> records) {
        this.rabbitTemplate.convertAndSend(CALL_SMS_PUSH_THIRD_FOR_PURE_AI_EXCHANGE,
                CALL_SMS_PUSH_THIRD_FOR_PURE_AI_ROUTING, records);
    }
}
