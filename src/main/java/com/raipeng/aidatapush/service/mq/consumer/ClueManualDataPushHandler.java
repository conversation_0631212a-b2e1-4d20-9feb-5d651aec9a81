package com.raipeng.aidatapush.service.mq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.raipeng.aidatacommon.model.Admin;
import com.raipeng.aidatacommon.model.CallRecordForManualDirect;
import com.raipeng.aidatapush.controller.wrapper.ClueManualDataPushWrapper;
import com.raipeng.aidatapush.service.HandlerPushThirdService;
import com.raipeng.aidatapush.service.RedisSecondCacheService;
import com.raipeng.aidatapush.service.mq.producer.newCallback.CallDataPushThirdForHumanProducer;
import com.raipeng.aidatapush.utils.CallDataHandleUtils;

import lombok.extern.slf4j.Slf4j;

import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.raipeng.aidatapush.config.RabbitConstants.CLUE_MANUAL_DATA_PUSH_QUEUE;

@Slf4j
@Component
@RabbitListener(queues = {CLUE_MANUAL_DATA_PUSH_QUEUE}, concurrency = "3")
public class ClueManualDataPushHandler {
    @Autowired
    private HandlerPushThirdService handlerPushThirdService;

    @Autowired
    private CallDataPushThirdForHumanProducer callDataPushThirdForHumanProducer;

    @Autowired
    private RedisSecondCacheService redisSecondCacheService;


    @RabbitHandler
    public void process(String clueManualDataPushWrapper) {
        ClueManualDataPushWrapper wrapper = JSONObject.parseObject(clueManualDataPushWrapper, ClueManualDataPushWrapper.class);
        Admin admin = wrapper.getAdmin();
        if (admin.getCallDataCallBackUrl() != null || admin.getCallSmsCallBackUrl() != null) {
            log.info("产生人工直呼接通数据");
            List<CallRecordForManualDirect> callRecordForManualDirects = CallDataHandleUtils.parseCallDataForHumanFromWrapper(wrapper, redisSecondCacheService);
            callDataPushThirdForHumanProducer.send(callRecordForManualDirects);
            return;
        }
        handlerPushThirdService.pushClueManualDataToThird(wrapper);
    }
}
