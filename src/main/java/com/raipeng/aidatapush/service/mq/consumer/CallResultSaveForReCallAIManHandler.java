package com.raipeng.aidatapush.service.mq.consumer;


import com.raipeng.aidatapush.controller.wrapper.CallResultHandlerForAIManWrapper;
import com.raipeng.aidatapush.service.HandleUnCallResultService;

import lombok.extern.slf4j.Slf4j;

import org.redisson.api.RBatch;
import org.redisson.api.RScoredSortedSetAsync;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Map;

import static com.raipeng.aidatapush.config.RabbitConstants.RESULT_SAVE_FOR_RE_CALL_AI_MAN_QUEUE;

@Slf4j
@Component
@RabbitListener(queues = {RESULT_SAVE_FOR_RE_CALL_AI_MAN_QUEUE}, concurrency = "3")
public class CallResultSaveForReCallAIManHandler {
    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private HandleUnCallResultService handleUnCallResultService;

    @RabbitHandler
    public void process(CallResultHandlerForAIManWrapper callResultHandlerWrapper) {
        LocalDateTime start = LocalDateTime.now();

        handleUnCallResultService.saveUnCallResultForAIMan(callResultHandlerWrapper);
        Map<String, Map<String, Double>> saveRedisKeysMap = callResultHandlerWrapper.getSaveRedisKeysMap();
        RBatch batch = redissonClient.createBatch();
        for(String key : saveRedisKeysMap.keySet()){
            RScoredSortedSetAsync<String> scoredSortedSet = batch.getScoredSortedSet(key);
            Map<String, Double> values = saveRedisKeysMap.get(key);
            if(!values.isEmpty()){
                scoredSortedSet.addAllAsync(values);
            }
        }
        batch.execute();

        LocalDateTime end = LocalDateTime.now();
        log.info("批量处理{}条{}记录总耗时:{}ms", callResultHandlerWrapper.getCallRecordList().size(), "人机协同补呼", Duration.between(start, end).toMillis());
    }
}
