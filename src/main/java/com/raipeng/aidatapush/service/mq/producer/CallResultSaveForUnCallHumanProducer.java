package com.raipeng.aidatapush.service.mq.producer;

import com.raipeng.aidatapush.controller.wrapper.CallResultHandlerForHumanWrapper;

import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.raipeng.aidatapush.config.RabbitConstants.RESULT_SAVE_FOR_UN_CALL_HUMAN_EXCHANGE;
import static com.raipeng.aidatapush.config.RabbitConstants.RESULT_SAVE_FOR_UN_CALL_HUMAN_ROUTING;

@Component
public class CallResultSaveForUnCallHumanProducer {
    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void send(CallResultHandlerForHumanWrapper callResultHandlerForHumanWrapper) {
        this.rabbitTemplate.convertAndSend(RESULT_SAVE_FOR_UN_CALL_HUMAN_EXCHANGE,
                RESULT_SAVE_FOR_UN_CALL_HUMAN_ROUTING, callResultHandlerForHumanWrapper);
    }
}
