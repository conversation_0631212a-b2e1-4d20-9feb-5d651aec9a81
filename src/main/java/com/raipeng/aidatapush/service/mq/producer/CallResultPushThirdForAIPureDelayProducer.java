package com.raipeng.aidatapush.service.mq.producer;

import com.raipeng.aidatapush.controller.wrapper.CallResultHandlerForPureAIWrapper;

import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.raipeng.aidatapush.config.RabbitConstants.*;

@Component
public class CallResultPushThirdForAIPureDelayProducer {
    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void send(CallResultHandlerForPureAIWrapper callResultHandlerForPureAIWrapper) {
        this.rabbitTemplate.convertAndSend(DELAY_CALL_RESULT_PUSH_THIRD_FOR_PURE_AI_EXCHANGE,
                DELAY_CALL_RESULT_PUSH_THIRD_FOR_PURE_AI_ROUTING, callResultHandlerForPureAIWrapper);

    }
}
