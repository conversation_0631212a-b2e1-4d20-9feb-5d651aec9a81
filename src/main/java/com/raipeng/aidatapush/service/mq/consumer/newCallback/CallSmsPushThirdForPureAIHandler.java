package com.raipeng.aidatapush.service.mq.consumer.newCallback;

import com.raipeng.aidatacommon.thirdrequests.entity.CallDataEntity;
import com.raipeng.aidatacommon.model.record.CallRecord;
import com.raipeng.aidatapush.config.HotConfig;
import com.raipeng.aidatapush.service.HandleCallDataPushThirdForPureAIService;
import com.raipeng.aidatapush.utils.CallDataHandleUtils;

import lombok.extern.slf4j.Slf4j;

import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.raipeng.aidatapush.config.RabbitConstants.CALL_SMS_PUSH_THIRD_FOR_PURE_AI_QUEUE;


@Slf4j
@Component
@RabbitListener(queues = {CALL_SMS_PUSH_THIRD_FOR_PURE_AI_QUEUE}, concurrency = "10")
public class CallSmsPushThirdForPureAIHandler {
    @Autowired
    private HotConfig hotConfig;

    @Autowired
    private HandleCallDataPushThirdForPureAIService handleCallDataPushThirdForPureAIService;

    @RabbitHandler
    public void process(List<CallRecord> callRecords) {
        Map<String, List<CallRecord>> recordMap = callRecords.stream()
                .filter(callRecord -> callRecord.getGroupId() != null)
                .collect(Collectors.groupingBy(CallRecord::getGroupId));
        Set<Map.Entry<String, List<CallRecord>>> entries = recordMap.entrySet();
        for (Map.Entry<String, List<CallRecord>> entry : entries) {
            String groupId = entry.getKey();
            List<CallRecord> records = entry.getValue();
            CallDataEntity entity = CallDataEntity.builder()
                    .callRecordList(records)
                    .groupId(groupId)
                    .build();
            try {
                CallDataHandleUtils.PURE_AI_SMS_WARN_COUNT.compute(groupId, (key, value) -> {
                    if (value == null || value.get() <= hotConfig.getMaxPushFailSize()) {
                        handleCallDataPushThirdForPureAIService.pushCallSmsToThirdForAI(entity);
                    } else {
                        handleCallDataPushThirdForPureAIService.collectXCallSmsToThirdForAI(entity);
                        value.incrementAndGet();
                    }
                    return value;
                });
            } catch (Exception e) {
                log.warn("Exception=>纯AI短信初次回传失败提醒:{}", e.getMessage());
                CallDataHandleUtils.PURE_AI_SMS_WARN_COUNT.compute(groupId, (key, value) -> {
                    if (value == null) {
                        value = new AtomicInteger(0);
                    }
                    value.incrementAndGet();
                    return value;
                });
                handleCallDataPushThirdForPureAIService.collectPreXCallSmsToThirdForAI(entity);
            }
        }
    }
}
