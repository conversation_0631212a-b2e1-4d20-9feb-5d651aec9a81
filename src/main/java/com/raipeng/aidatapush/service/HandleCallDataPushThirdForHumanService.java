package com.raipeng.aidatapush.service;

import com.alibaba.fastjson.JSONObject;
import com.raipeng.aidatacommon.exception.CallFailedException;
import com.raipeng.aidatacommon.model.Admin;
import com.raipeng.aidatacommon.thirdrequests.entity.CallDataEntity;
import com.raipeng.aidatacommon.thirdrequests.enums.PushThirdType;
import com.raipeng.aidatapush.config.HotConfig;
import com.raipeng.aidatapush.repository.ClueImportHistoryRepository;
import com.raipeng.aidatapush.service.mq.consumer.newCallback.EnhancedCallDataPushService;
import com.raipeng.aidatapush.service.mq.producer.newCallback.PreXCallDataPushThirdForHumanProducer;
import com.raipeng.aidatapush.service.mq.producer.newCallback.XCallDataPushThirdForHumanProducer;
import com.raipeng.aidatapush.utils.CallDataHandleUtils;
import com.raipeng.aidatapush.utils.RaiYiEncryptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.raipeng.aidatapush.config.RabbitConstants.X_CALL_DATA_PUSH_THIRD_FOR_HUMAN_QUEUE;

@Slf4j
@Service
public class HandleCallDataPushThirdForHumanService {
    @Autowired
    private HotConfig hotConfig;

    @Autowired
    private AdminService adminService;

    @Autowired
    private RabbitAdmin rabbitAdmin;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private RestForThirdTemplateService restForThirdTemplateService;

    @Autowired
    private PreXCallDataPushThirdForHumanProducer preXCallDataPushThirdForHumanProducer;

    @Autowired
    private XCallDataPushThirdForHumanProducer xCallDataPushThirdForHumanProducer;

    @Autowired
    private ClueImportHistoryRepository clueImportHistoryRepository;

    @Autowired
    private DingDingHighPriorityService dingDingHighPriorityService;

    @Autowired
    private EnhancedCallDataPushService enhancedCallDataPushService;

    public void pushCallDataToThirdForHuman(CallDataEntity entity) {
        preHandleCallDataForDataEntity(entity);
        enhancedCallDataPushService.pushCallDataForHuman(entity);
    }

    public void collectPreXCallDataToThirdForHuman(CallDataEntity entity) {
        preXCallDataPushThirdForHumanProducer.send(entity);
    }

    public void collectXCallDataToThirdForHuman(CallDataEntity entity) {
        xCallDataPushThirdForHumanProducer.send(entity);
    }

    public void handleXCallDataForHuman() {
        CallDataHandleUtils.handleXQueue(rabbitTemplate,
                X_CALL_DATA_PUSH_THIRD_FOR_HUMAN_QUEUE,
                PushThirdType.CALL_DATA_FOR_HUMAN,
                CallDataHandleUtils.HUMAN_DATA_WARN_COUNT,
                this::pushCallDataToThirdForHuman,
                this::collectXCallDataToThirdForHuman,
                hotConfig.getMaxPushFailSize(),
                dingDingHighPriorityService);
    }

    public void handleXCallDataForHumanManual(String groupId) {
        CallDataHandleUtils.handleXQueueManual(rabbitTemplate,
                X_CALL_DATA_PUSH_THIRD_FOR_HUMAN_QUEUE,
                PushThirdType.CALL_DATA_FOR_HUMAN,
                CallDataHandleUtils.HUMAN_DATA_WARN_COUNT,
                this::pushCallDataToThirdForHuman,
                this::collectXCallDataToThirdForHuman,
                hotConfig.getMaxPushFailSize(),
                groupId,
                dingDingHighPriorityService
        );
    }

    public CallDataEntity monitorXCallDataForHuman() {
        return CallDataHandleUtils.monitorXQueue(rabbitTemplate,
                X_CALL_DATA_PUSH_THIRD_FOR_HUMAN_QUEUE,
                this::collectXCallDataToThirdForHuman
        );
    }

    public void checkXQueue() {
        CallDataHandleUtils.checkXQueue(rabbitAdmin, X_CALL_DATA_PUSH_THIRD_FOR_HUMAN_QUEUE, dingDingHighPriorityService);
    }

    private void preHandleCallDataForDataEntity(CallDataEntity entity) {
        entity.getCallRecordList().forEach(record -> {
            if (!"7".equals(record.getCallStatus())) {
                String batchID = clueImportHistoryRepository.findAllByAccountAndGroupId(entity.getGroupId(), record.getPhone());
                record.setBatchId(batchID);
            }
            if (record.getPlainPhone() == null) {
                String groupId = entity.getGroupId();
                Admin admin = adminService.findById(Long.parseLong(groupId.split("_")[2]));
                if (!admin.getIsForEncryptionPhones()) {
                    JSONObject decryptionResultBatch = RaiYiEncryptionUtil.getDecryptionResultBatch(record.getPhone());
                    if (decryptionResultBatch == null) {
                        log.error("[Exception]=>人工直呼,号码解密失败, recordId:{}", record.getRecordId());
                        throw new CallFailedException("人工直呼,号码解密失败，会推回错误队列");
                    }
                    String plainPhone = decryptionResultBatch.getString(record.getPhone());
                    record.setPlainPhone(plainPhone);
                }
            }
        });
    }
}
