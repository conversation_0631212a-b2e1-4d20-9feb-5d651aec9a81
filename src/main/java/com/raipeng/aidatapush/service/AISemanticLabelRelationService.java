package com.raipeng.aidatapush.service;

import com.raipeng.aidatacommon.model.script.AISemanticLabelRelation;
import com.raipeng.aidatapush.repository.AISemanticLabelRelationRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.raipeng.common.constant.AIRedisCacheKeyConstant.AI_SEMANTIC_LABEL_RELATION;

@Slf4j
@Service
public class AISemanticLabelRelationService {
    @Autowired
    private AISemanticLabelRelationRepository aiSemanticLabelRelationRepository;

    @Cacheable(value = AI_SEMANTIC_LABEL_RELATION)
    public Map<String, List<String>> findAllAISemanticLabelRelation() {
        List<AISemanticLabelRelation> all = aiSemanticLabelRelationRepository.findAll();
        Map<String, List<String>> hashMap = new HashMap<>();
        for (AISemanticLabelRelation aiSemanticLabelRelation : all) {
            List<String> longs = hashMap.getOrDefault(aiSemanticLabelRelation.getSemanticId().toString(), new ArrayList<>());
            longs.add(aiSemanticLabelRelation.getSemanticLabelId().toString());
            hashMap.put(aiSemanticLabelRelation.getSemanticId().toString(), longs);
        }
        return hashMap;
    }
}
