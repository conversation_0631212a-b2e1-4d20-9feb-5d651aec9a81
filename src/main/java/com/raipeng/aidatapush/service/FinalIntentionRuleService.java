package com.raipeng.aidatapush.service;


import com.raipeng.aidatacommon.model.CallRecordForHumanMachine;
import com.raipeng.aidatacommon.model.record.CallRecord;
import com.raipeng.aidatacommon.model.script.AdvancedRuleCondition;
import com.raipeng.aidatacommon.model.script.FinalIntentionRules;
import com.raipeng.aidatapush.repository.FinalIntentionRulesRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class FinalIntentionRuleService {
    @Autowired
    private FinalIntentionRulesRepository finalIntentionRulesRepository;

    @Autowired
    private AdvancedRuleConditionService advancedRuleConditionService;

    @Cacheable(value = "CACHE::FINAL_INTENTION_RULES_BY_SCRIPT_ID")
    public List<FinalIntentionRules> findAllByScriptId(Long id) {
        return finalIntentionRulesRepository.findAllByScriptId(id);
    }

    public boolean tagFinalIntentionList(CallRecord callRecord, List<FinalIntentionRules> finalIntentionRules,
                                         Map<String, List<AdvancedRuleCondition>> advancedRuleConditionList,
                                         Map<String, List<String>> allAISemanticLabelRelationMap) {
        //多个排除规则满足一个排除的就直接返回
        for (FinalIntentionRules finalIntentionRule : finalIntentionRules) {
            String excludeConditionUniqueIds = finalIntentionRule.getExcludeConditionUniqueIds();
            List<String> excludeConditionUniqueIdList = Arrays.asList(excludeConditionUniqueIds.split(","));
            boolean excludeIntention = advancedRuleConditionService.matchRuleCondition(callRecord, excludeConditionUniqueIdList,
                    advancedRuleConditionList, allAISemanticLabelRelationMap);
            if (excludeIntention) {
                return true;
            }
        }
        return false;
    }

    public boolean tagFinalIntentionList(CallRecordForHumanMachine callRecord, List<FinalIntentionRules> finalIntentionRules,
                                         Map<String, List<AdvancedRuleCondition>> advancedRuleConditionList,
                                         Map<String, List<String>> allAISemanticLabelRelationMap) {
        //多个排除规则满足一个排除的就直接返回
        for (FinalIntentionRules finalIntentionRule : finalIntentionRules) {
            String excludeConditionUniqueIds = finalIntentionRule.getExcludeConditionUniqueIds();
            List<String> excludeConditionUniqueIdList = Arrays.asList(excludeConditionUniqueIds.split(","));
            boolean excludeIntention = advancedRuleConditionService.matchRuleCondition(callRecord, excludeConditionUniqueIdList,
                    advancedRuleConditionList, allAISemanticLabelRelationMap);
            if (excludeIntention) {
                return true;
            }
        }
        return false;
    }

}
