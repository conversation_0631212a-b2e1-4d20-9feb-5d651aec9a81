package com.raipeng.aidatapush.service;

import com.raipeng.aidatapush.repository.RequestLogRepository;
import com.raipeng.aidatapush.utils.LogUtils;
import com.raipeng.aidatacommon.model.RequestLog;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;


/**
 * <AUTHOR>
 * @description: 请求日志记录
 * @date ：2020/8/3 11:52
 */
@Slf4j
@Service
public class RequestLogService {
    @Autowired
    private RequestLogRepository requestLogRepository;

    public void encryptAndSaveLog(RequestLog requestLog) {
        String requestContent = requestLog.getRequestContent();
        String responseContent = requestLog.getResponseContent();
        requestLog.setRequestContent(LogUtils.replacePhone(requestContent));
        requestLog.setResponseContent(LogUtils.replacePhone(responseContent));
        requestLogRepository.save(requestLog);
    }

    public void addLog(String requestMap, Map<String, ?> responseMap, String type, LocalDateTime startTime, LocalDateTime endTime){
        RequestLog requestLog = new RequestLog();
        requestLog.setRequestContent(requestMap);
        requestLog.setResponseContent(responseMap == null ? null : responseMap.toString());
        requestLog.setType(type);
        requestLog.setRequestTime(startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")));
        requestLog.setResponseTime(endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")));
        encryptAndSaveLog(requestLog);
    }

    public void addLog(String requestMap, String type, LocalDateTime startTime, LocalDateTime endTime){
        RequestLog requestLog = new RequestLog();
        requestLog.setRequestContent(requestMap);
        requestLog.setType(type);
        requestLog.setRequestTime(startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")));
        requestLog.setResponseTime(endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")));
        encryptAndSaveLog(requestLog);
    }
}
