package com.raipeng.aidatapush.service;

import com.raipeng.aidatacommon.model.CallSetting;
import com.raipeng.aidatapush.repository.CallSettingRepository;
import com.raipeng.common.util.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
@Service
public class CallSettingService {
    private final static List<String> INTENTION_CLASS_LIST = Arrays.asList("A", "B", "C", "D");

    @Autowired
    private CallSettingRepository callSettingRepository;

    public List<String> getDefaultIntentionClassList() {
        return INTENTION_CLASS_LIST;
    }

    public Map<String, List<String>> getGroupIdIntentionClassMap(List<String> groupIdList) {
        if (CollectionUtil.isEmpty(groupIdList)) {
            return new HashMap<>();
        }
        Map<String, List<String>> groupIdIntentionClassMap = new HashMap<>();
        //有可能有的 主账户 没有设置意向类型
        List<CallSetting> allByGroupIdList = callSettingRepository.findAllByGroupIdList(groupIdList);
        Map<String, String> collect = allByGroupIdList.stream().collect(Collectors.toMap(CallSetting::getGroupId, CallSetting::getIntentionClass));
        for (String groupId : groupIdList) {
            String intentionClass = collect.get(groupId);
            if (StringUtils.isBlank(intentionClass)) {
                groupIdIntentionClassMap.put(groupId, INTENTION_CLASS_LIST);
            } else {
                groupIdIntentionClassMap.put(groupId, Arrays.asList(intentionClass.split(",")));
            }
        }
        return groupIdIntentionClassMap;
    }
}
