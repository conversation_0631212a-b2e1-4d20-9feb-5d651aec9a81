package com.raipeng.aidatapush.service;


import com.raipeng.aidatapush.repository.SupplyLineRepository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.persistence.Tuple;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.raipeng.common.constant.AIRedisCacheKeyConstant.CACHE_FOR_CALL_BACK_SUPPLY_LINE;
import static com.raipeng.common.constant.AIRedisCacheKeyConstant.CACHE_FOR_SUPPLY_LINE_BELONG_MAP;

@Service
public class SupplyLineService {
    @Autowired
    private SupplyLineRepository supplyLineRepository;

    @Cacheable(value = CACHE_FOR_CALL_BACK_SUPPLY_LINE)
    public Set<String> getCallbackSupplyLineNumbers() {
        return supplyLineRepository.findNumbersForCallback();
    }

    @Cacheable(value = CACHE_FOR_SUPPLY_LINE_BELONG_MAP)
    public Map<String, String> getSupplyLineBelongMap() {
        List<Tuple> tuples = supplyLineRepository.findSupplyLineBelong();
        Map<String, String> map = new HashMap<>();
        for (Tuple tuple : tuples) {
            map.put(tuple.get(0, String.class), tuple.get(1, String.class));
        }
        return map;
    }
}
