package com.raipeng.aidatapush.service;

import com.raipeng.aidatacommon.model.AITenant;
import com.raipeng.aidatacommon.model.Admin;
import com.raipeng.aidatacommon.model.AntAdmin;
import com.raipeng.aidatacommon.thirdrequests.entity.AdminCallbackEntity;
import com.raipeng.aidatacommon.thirdrequests.enums.CallbackType;
import com.raipeng.aidatapush.repository.AITenantRepository;
import com.raipeng.aidatapush.repository.AdminRepository;
import com.raipeng.aidatapush.repository.AntAdminRepository;
import com.raipeng.common.util.JpaResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.persistence.Tuple;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.raipeng.common.constant.AIRedisCacheKeyConstant.*;


@Slf4j
@Service
public class AdminService {
    @Autowired
    private AdminRepository adminRepository;

    @Autowired
    private AntAdminRepository antAdminRepository;

    @Autowired
    private AITenantRepository aiTenantRepository;

    @Autowired
    private RedisSecondCacheService redisSecondCacheService;

    @Cacheable(value = CACHE_FOR_ADMIN)
    public Admin findById(Long id) {
        return adminRepository.findFirstById(id);
    }

    @Cacheable(value = CACHE_FOR_ANT_ADMIN)
    public AntAdmin findByAdminId(Long id) {
        return antAdminRepository.findByAdminId(id);
    }

    @Cacheable(value = CACHE_FOR_ADMIN_CALLBACK_ENTITY)
    public AdminCallbackEntity getCallbackEntityByGroupId(String groupId) {
        Tuple tuple = adminRepository.getCallbackEntityByGroupId(groupId);
        AdminCallbackEntity adminCallbackEntity = JpaResultUtils.processResult(tuple, AdminCallbackEntity.class);
        AITenant aiTenant = aiTenantRepository.getAITenantByGroupId(groupId);
        adminCallbackEntity.setExternal(aiTenant.getCallbackMode().isExternal());
        adminCallbackEntity.setCallbackMode(aiTenant.getCallbackMode());
        adminCallbackEntity.setExternalCallbackConfig(aiTenant.getExternalCallbackConfig() == null ? null : aiTenant.getExternalCallbackConfig().toString());
        return adminCallbackEntity;
    }

    public Map<String, CallbackType> getCallbackTypeMap() {
        return redisSecondCacheService.getCallbackTypeMapLocal();
    }

    @Cacheable(value = CACHE_FOR_CALLBACK_TYPE_MAP)
    public Map<String, CallbackType> getCallbackTypeFromRedis() {
        return getCallbackTypeFromPG();
    }

    public Map<String, CallbackType> getCallbackTypeFromPG() {
        List<Admin> admins = adminRepository.getTenantManagerAdmins();
        Map<String, CallbackType> callbackTypeMap = new HashMap<>();
        for (Admin admin : admins) {
            String groupId = admin.getGroupId();
            if (admin.getAntAccessKey() != null && admin.getAntSecretKey() != null) {
                callbackTypeMap.put(groupId, CallbackType.PUSH_ANTS);
            } else if (admin.getCallDataCallBackUrl() != null || admin.getCallSmsCallBackUrl() != null
                    || admin.getCallMCallBackUrl() != null || admin.getCallUpdateCallBackUrl() != null) {
                callbackTypeMap.put(groupId, CallbackType.PUSH_THIRD);
            } else {
                callbackTypeMap.put(groupId, CallbackType.OLD_PUSH);
            }
        }
        return callbackTypeMap;
    }
}
