package com.raipeng.aidatapush.service;

import com.alibaba.fastjson.JSONObject;
import com.raipeng.aidatapush.enums.DingDingMsgType;
import com.raipeng.aidatapush.utils.WxWorkRobotWarningUtils;
import com.raipeng.common.constant.CommonConstants;
import com.raipeng.common.model.request.HttpResult;
import com.raipeng.common.util.HttpClientUtils;
import com.raipeng.common.util.SysUtil;
import com.raipeng.common.util.dingding.wrapper.RequestWrapper;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


@Slf4j
@Service
public class DingDingHighPriorityService {
    private static final String URL_WITH_TOKEN = "https://oapi.dingtalk.com/robot/send?access_token=";

    @Value("${phones.high.priority:15895985540}")
    private String phones;

    @Value("${ding.ding.token.high.priority:48c1ac6364c2441ae84bb051313b8909111566aebb56dc178671c9932c614dde}")
    private String token;


    private static DingDingTokenService staticDingDingTokenService;

    @Autowired
    private DingDingTokenService dingDingTokenService;

    @PostConstruct
    private void init(){
        staticDingDingTokenService = dingDingTokenService;
    }


    public void sedDDMsg(DingDingMsgType dingDingMsgType, String... contents) {
        sendDingDingMessages(dingDingMsgType.getMessageType(), contents);
    }

    public void sedDDMsg(String phone, DingDingMsgType dingDingMsgType, String... contents) {
        sendDingDingMessages(dingDingMsgType.getMessageType(), Collections.singletonList(phone), contents);
    }

    private void sendDingDingMessages(String contentType, String... contents) {
        sendDingDingMessages(contentType, Arrays.asList(phones.split(";")), contents);
    }

    private void sendDingDingMessages(String contentType, List<String> phones, String... contents) {
        StringBuilder contentSb = new StringBuilder();
        for (String content : contents) {
            contentSb.append(content).append("\n");
        }
        contentSb.append("time: ")
                .append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        sendDingDingMessage(contentType, phones, contentSb.toString());
    }

    private void sendDingDingMessage(String contentType, List<String> phones, String content) {
        dingDingRobotMessagePushWithToken(
                new RequestWrapper("text",
                        "【" + contentType + "】" + content,
                        "【" + contentType + "】" + content,
                        null,
                        null,
                        false,
                        phones.stream().collect(Collectors.toMap(p -> p, p -> p, (k, v) -> k))),
                token);
    }

    private void dingDingRobotMessagePushWithToken(RequestWrapper wrapper, String token) {
        try {
            String url = URL_WITH_TOKEN + token;
            String content = createContent(wrapper);
            HttpResult httpResult = HttpClientUtils.doPostJson(url, content);

            if(httpResult.isSuccess()){
                String result = httpResult.getBody();
                if(result == null || !JSONObject.parseObject(result,JSONObject.class).getString("errcode").equals("0")){
                    String wxResult = WxWorkRobotWarningUtils.wxWorkPush("text", content, new ArrayList<>(), new ArrayList<>(), false, staticDingDingTokenService.getQiWeiToken());
                    log.error("Exception 钉钉发送消息失败！,已经推送给企微了,失败原因："+result+"企微的返回结果"+wxResult);
                }
            }

        } catch (Exception e) {
            log.error("push exception!", e);
        }
    }

    private String createContent(RequestWrapper wrapper) {
        String content;
        wrapper.setMessageType(SysUtil.isNotEmpty(wrapper.getMessageType()) ? wrapper.getMessageType() : "default");
        switch (wrapper.getMessageType()) {
            case CommonConstants.DING_DIGN_MESSAGE_TYPE_TEXT:
                String isAtAll = ",\"isAtAll\": false";
                String atMobiles = "";
                if (wrapper.isAtAll()) {
                    isAtAll = "\"isAtAll\": true";
                } else {
                    atMobiles = "\"atMobiles\": [\""+ StringUtils.join(wrapper.getAtMobiles().keySet(),"\",\"")+"\"]";
                }
                content = String.format(CommonConstants.DING_DIGN_MESSAGE_TEXT_USER_TEMPLATE, wrapper.getText(), atMobiles, isAtAll);
                break;
            case CommonConstants.DING_DIGN_MESSAGE_TYPE_LINK:
                content = String.format(CommonConstants.DING_DIGN_MESSAGE_LINK_TEMPLATE, wrapper.getText(), wrapper.getTitle(),
                        SysUtil.isNotEmpty(wrapper.getPicUrl()) ? wrapper.getPicUrl(): "", wrapper.getMessageUrl());
                break;
            default:
                content = String.format(CommonConstants.DING_DIGN_MESSAGE_TEXT_TEMPLATE,wrapper.getText());
                break;
        }
        return content;
    }
}
