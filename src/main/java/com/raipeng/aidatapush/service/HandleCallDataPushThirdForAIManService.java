package com.raipeng.aidatapush.service;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.raipeng.aidatacommon.exception.CallFailedException;
import com.raipeng.aidatacommon.model.Admin;
import com.raipeng.aidatacommon.thirdrequests.entity.AdminCallbackEntity;
import com.raipeng.aidatacommon.thirdrequests.entity.CallDataEntity;
import com.raipeng.aidatacommon.thirdrequests.enums.PushThirdType;
import com.raipeng.aidatapush.config.HotConfig;
import com.raipeng.aidatapush.controller.wrapper.ClueDataPushWrapper;
import com.raipeng.aidatapush.service.mq.consumer.newCallback.EnhancedCallDataPushService;
import com.raipeng.aidatapush.service.mq.producer.newCallback.PreXCallDataPushThirdForAIManProducer;
import com.raipeng.aidatapush.service.mq.producer.newCallback.PreXCallSmsPushThirdForAIManProducer;
import com.raipeng.aidatapush.service.mq.producer.newCallback.XCallDataPushThirdForAIManProducer;
import com.raipeng.aidatapush.service.mq.producer.newCallback.XCallSmsPushThirdForAIManProducer;
import com.raipeng.aidatapush.utils.CallDataHandleUtils;
import com.raipeng.aidatapush.utils.PushThirdUtil;
import com.raipeng.aidatapush.utils.RaiYiEncryptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static com.raipeng.aidatapush.config.RabbitConstants.X_CALL_DATA_PUSH_THIRD_FOR_AI_MAN_QUEUE;
import static com.raipeng.aidatapush.config.RabbitConstants.X_CALL_SMS_PUSH_THIRD_FOR_AI_MAN_QUEUE;
import static com.raipeng.aidatapush.service.HandlerPushThirdService.CLUE_PUSH_THIRD_MAP;

@Slf4j
@Service
public class HandleCallDataPushThirdForAIManService {
    @Autowired
    private HotConfig hotConfig;

    @Autowired
    private RabbitAdmin rabbitAdmin;

    @Autowired
    private AdminService adminService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private RestForThirdTemplateService restForThirdTemplateService;

    @Autowired
    private PreXCallDataPushThirdForAIManProducer preXCallDataPushThirdForAIManProducer;

    @Autowired
    private PreXCallSmsPushThirdForAIManProducer preXCallSmsPushThirdForAIManProducer;

    @Autowired
    private XCallDataPushThirdForAIManProducer xCallDataPushThirdForAIManProducer;

    @Autowired
    private XCallSmsPushThirdForAIManProducer xCallSmsPushThirdForAIManProducer;

    @Autowired
    private DingDingHighPriorityService dingDingHighPriorityService;

    @Autowired
    private EnhancedCallDataPushService enhancedCallDataPushService;

    public void pushCallDataToThirdForAIMan(CallDataEntity entity) {
        preHandleCallDataForDataEntity(entity);
        enhancedCallDataPushService.pushCallDataForAIMan(entity);
    }

    public void pushCallSmsToThirdForAIMan(CallDataEntity entity) {
        preHandleCallDataForDataEntity(entity);
        enhancedCallDataPushService.pushCallSmsForAIMan(entity);
    }

    /**
     * 汽车线索业务
     *
     * @param admin admin
     * @param wrapper wrapper
     */
    public void pushClueDataToCarClue(Admin admin, ClueDataPushWrapper wrapper) {
        AdminCallbackEntity callbackEntity = adminService.getCallbackEntityByGroupId(admin.getGroupId());
        String clueCallbackUrl = callbackEntity.getClueCallbackUrl();
        if (StringUtils.isNotBlank(clueCallbackUrl)) {
            int timeSlot = PushThirdUtil.getTimeSlot(hotConfig.getClueTimeMinuteInterval());
            String key = PushThirdUtil.getClueBackKey(callbackEntity.getAesKey(), callbackEntity.getSalt(), clueCallbackUrl);
            RMap<String, String> redisMap = redissonClient.getMap(CLUE_PUSH_THIRD_MAP + timeSlot);
            String clueDataPushWrapperJsonList = redisMap.computeIfAbsent(key, k -> JSONObject.toJSONString(new ArrayList<>()));
            List<ClueDataPushWrapper> clueDataPushWrapperList = JSONObject.parseObject(clueDataPushWrapperJsonList, new TypeReference<List<ClueDataPushWrapper>>(){});
            clueDataPushWrapperList.add(wrapper);
            redisMap.put(key, JSONObject.toJSONString(clueDataPushWrapperList));
        }
    }

    public void collectPreXCallDataToThirdForAIMan(CallDataEntity entity) {
        preXCallDataPushThirdForAIManProducer.send(entity);
    }

    public void collectPreXCallSmsToThirdForAIMan(CallDataEntity entity) {
        preXCallSmsPushThirdForAIManProducer.send(entity);
    }

    public void collectXCallDataToThirdForAIMan(CallDataEntity entity) {
        xCallDataPushThirdForAIManProducer.send(entity);
    }

    public void collectXCallSmsToThirdForAIMan(CallDataEntity entity) {
        xCallSmsPushThirdForAIManProducer.send(entity);
    }

    public void handleXCallDataForAIMan() {
        CallDataHandleUtils.handleXQueue(rabbitTemplate,
                X_CALL_DATA_PUSH_THIRD_FOR_AI_MAN_QUEUE,
                PushThirdType.CALL_DATA_FOR_AIMAN,
                CallDataHandleUtils.AIMAN_DATA_WARN_COUNT,
                this::pushCallDataToThirdForAIMan,
                this::collectXCallDataToThirdForAIMan,
                hotConfig.getMaxPushFailSize(),
                dingDingHighPriorityService);
    }

    public void handleXCallSmsForAIMan() {
        CallDataHandleUtils.handleXQueue(rabbitTemplate,
                X_CALL_SMS_PUSH_THIRD_FOR_AI_MAN_QUEUE,
                PushThirdType.CALL_SMS_FOR_AIMAN,
                CallDataHandleUtils.AIMAN_SMS_WARN_COUNT,
                this::pushCallSmsToThirdForAIMan,
                this::collectXCallSmsToThirdForAIMan,
                hotConfig.getMaxPushFailSize(),
                dingDingHighPriorityService);
    }

    public void handleXCallDataForAIManManual(String groupId) {
        CallDataHandleUtils.handleXQueueManual(rabbitTemplate,
                X_CALL_DATA_PUSH_THIRD_FOR_AI_MAN_QUEUE,
                PushThirdType.CALL_DATA_FOR_AIMAN,
                CallDataHandleUtils.AIMAN_DATA_WARN_COUNT,
                this::pushCallDataToThirdForAIMan,
                this::collectXCallDataToThirdForAIMan,
                hotConfig.getMaxPushFailSize(),
                groupId,
                dingDingHighPriorityService
        );
    }

    public void handleXCallSmsForAIManManual(String groupId) {
        CallDataHandleUtils.handleXQueueManual(rabbitTemplate,
                X_CALL_SMS_PUSH_THIRD_FOR_AI_MAN_QUEUE,
                PushThirdType.CALL_SMS_FOR_AIMAN,
                CallDataHandleUtils.AIMAN_SMS_WARN_COUNT,
                this::pushCallSmsToThirdForAIMan,
                this::collectXCallSmsToThirdForAIMan,
                hotConfig.getMaxPushFailSize(),
                groupId,
                dingDingHighPriorityService
        );
    }

    public CallDataEntity monitorXCallDataForAIMan() {
        return CallDataHandleUtils.monitorXQueue(rabbitTemplate,
                X_CALL_DATA_PUSH_THIRD_FOR_AI_MAN_QUEUE,
                this::collectXCallDataToThirdForAIMan
        );
    }

    public CallDataEntity monitorXCallSmsForAIMan() {
        return CallDataHandleUtils.monitorXQueue(rabbitTemplate,
                X_CALL_SMS_PUSH_THIRD_FOR_AI_MAN_QUEUE,
                this::collectXCallSmsToThirdForAIMan
        );
    }

    public void checkXQueue() {
        CallDataHandleUtils.checkXQueue(rabbitAdmin, X_CALL_DATA_PUSH_THIRD_FOR_AI_MAN_QUEUE, dingDingHighPriorityService);
        CallDataHandleUtils.checkXQueue(rabbitAdmin, X_CALL_SMS_PUSH_THIRD_FOR_AI_MAN_QUEUE, dingDingHighPriorityService);
    }

    private void preHandleCallDataForDataEntity(CallDataEntity entity) {
        entity.getCallRecordList().forEach(record -> {
            if (record.getPlainPhone() == null && record.getPhone() != null && record.getPhone().length() != 4) {
                String groupId = entity.getGroupId();
                Admin admin = adminService.findById(Long.parseLong(groupId.split("_")[2]));
                if (!admin.getIsForEncryptionPhones()) {
                    JSONObject decryptionResultBatch = RaiYiEncryptionUtil.getDecryptionResultBatch(record.getPhone());
                    if (decryptionResultBatch == null) {
                        log.error("[Exception]=>人机协同接通,号码解密失败, recordId:{}", record.getRecordId());
                        throw new CallFailedException("人机协同接通,号码解密失败，会推回错误队列");
                    }
                    String plainPhone = decryptionResultBatch.getString(record.getPhone());
                    record.setPlainPhone(plainPhone);
                }
            }
        });
    }
}
