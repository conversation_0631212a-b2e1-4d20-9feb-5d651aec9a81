package com.raipeng.aidatapush.service;


import com.raipeng.aidatacommon.entity.TaskRedisCountEntity;
import com.raipeng.aidatacommon.entity.AiRedisTask;

import lombok.extern.slf4j.Slf4j;

import org.redisson.api.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class TaskRedisService {

    private static final String REDIS_TASK_COUNT_QUEUE = "REDIS_TASK_COUNT_QUEUE";

    @Autowired
    @Qualifier("redissonSecondClient")
    private RedissonClient redissonSecondClient;

    @Autowired
    private BatchService batchService;

    @Autowired
    private AIOutboundTaskService aiOutboundTaskService;

    @Autowired
    private RedisSecondCacheService redisSecondCacheService;

    public List<AiRedisTask> getAiRedisTaskList(Set<Long> taskIds) {
        return redisSecondCacheService.getRedisTasksLocal(taskIds);
    }

    public void updateTaskInRedisQueue(List<AiRedisTask> aiRedisTasks) {
        RQueue<TaskRedisCountEntity> queue = redissonSecondClient.getQueue(REDIS_TASK_COUNT_QUEUE);
        List<TaskRedisCountEntity> entities = new ArrayList<>();
        for (AiRedisTask aiRedisTask : aiRedisTasks) {
            TaskRedisCountEntity entity = new TaskRedisCountEntity();
            entity.setId(aiRedisTask.getId());
            entity.setCalledPhoneNum(aiRedisTask.getCalledNumDiff()==null?0:aiRedisTask.getCalledNumDiff().get());
            entity.setCallingPhoneNum(aiRedisTask.getCallingNumDiff()==null?0:aiRedisTask.getCallingNumDiff().get());
            entity.setRecallingPhoneNum(aiRedisTask.getRecallingNumDiff()==null?0:aiRedisTask.getRecallingNumDiff().get());
            entity.setFinishedPhoneNum(aiRedisTask.getFinishedNumDiff()==null?0:aiRedisTask.getFinishedNumDiff().get());
            entity.setPutThroughPhoneNum(aiRedisTask.getPutThroughNumDiff()==null?0:aiRedisTask.getPutThroughNumDiff().get());
            entity.setPhoneIntentionNum(aiRedisTask.getPhoneIntentionNumDiff()==null?0:aiRedisTask.getPhoneIntentionNumDiff().get());
            entity.setAIntentionNum(aiRedisTask.getAIntentionNumDiff() == null ? 0 : aiRedisTask.getAIntentionNumDiff().get());
            entity.setCallRecordNum(aiRedisTask.getCallRecordNumDiff()==null?0:aiRedisTask.getCallRecordNumDiff().get());
            entity.setFeeMinute(aiRedisTask.getFeeMinuteDiff()==null?0:aiRedisTask.getFeeMinuteDiff().get());
            entities.add(entity);
        }
        queue.addAll(entities);
    }

    public void updateTasksInPG() {
        log.info("=======> start redis queue read all <=========");
        long start = System.currentTimeMillis();
        Map<Long, TaskRedisCountEntity> map = new HashMap<>();
        RQueue<TaskRedisCountEntity> queue = redissonSecondClient.getQueue(REDIS_TASK_COUNT_QUEUE);
        int size = queue.size();
        List<TaskRedisCountEntity> entities = queue.poll(size + 2);
        if (entities !=null && !entities.isEmpty()) {
            for (TaskRedisCountEntity taskRedisCountEntity : entities) {
                if (taskRedisCountEntity != null) {
                    map.compute(taskRedisCountEntity.getId(), (k, value) -> {
                        if (value == null) {
                            value = taskRedisCountEntity;
                        } else {
                            value.setCalledPhoneNum(value.getCalledPhoneNum() + taskRedisCountEntity.getCalledPhoneNum());
                            value.setCallingPhoneNum(value.getCallingPhoneNum() + taskRedisCountEntity.getCallingPhoneNum());
                            value.setRecallingPhoneNum(value.getRecallingPhoneNum() + taskRedisCountEntity.getRecallingPhoneNum());
                            value.setFinishedPhoneNum(value.getFinishedPhoneNum() + taskRedisCountEntity.getFinishedPhoneNum());
                            value.setPutThroughPhoneNum(value.getPutThroughPhoneNum() + taskRedisCountEntity.getPutThroughPhoneNum());
                            value.setCallRecordNum(value.getCallRecordNum() + taskRedisCountEntity.getCallRecordNum());
                            value.setPhoneIntentionNum(value.getPhoneIntentionNum() + taskRedisCountEntity.getPhoneIntentionNum());
                            value.setAIntentionNum(value.getAIntentionNum() + taskRedisCountEntity.getAIntentionNum());
                            value.setFeeMinute(value.getFeeMinute() +taskRedisCountEntity.getFeeMinute());
                        }
                        return value;
                    });
                }
            }
        }
        long start1 = System.currentTimeMillis();
        log.info("=======> end redis queue read all , consume time:{}<=========", start1 - start);
        List<TaskRedisCountEntity> taskRedisCountEntities = new ArrayList<>(map.values());

        if (!taskRedisCountEntities.isEmpty()) {
            batchUpdateTaskByUnnest(taskRedisCountEntities);
            long start2 = System.currentTimeMillis();
            log.info("=======> update tasks in postgres, size:{}, consume time:{}<=========", taskRedisCountEntities.size(), start2 - start1);
            for (TaskRedisCountEntity taskRedisCountEntity : taskRedisCountEntities) {
                aiOutboundTaskService.checkIfTaskNeedStop(taskRedisCountEntity.getId(), taskRedisCountEntity.getFinishedPhoneNum());
            }
            long start3 = System.currentTimeMillis();
            log.info("=======> stop tasks in postgres, size:{}, consume time:{}<=========", taskRedisCountEntities.size(), start3 - start2);
        }
    }

    public void batchUpdateTaskByUnnest(List<TaskRedisCountEntity> entities) {
        int batchSize = 5000;
        for (int i = 0; i < entities.size(); i += batchSize) {
            List<TaskRedisCountEntity> batch = entities.subList(i, Math.min(i + batchSize, entities.size()));
            int maxRetries = 5;
            int retryCount = 0;
            while (retryCount < maxRetries) {
                try {
                    batchService.batchUpdateTask(batch);
                    log.info("update task table success:{} count", retryCount);
                    break;
                } catch (Exception e) {
                    log.error("Exception=>update task table failed:{} count", retryCount);
                    retryCount ++;
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException ex) {
                        throw new RuntimeException(ex);
                    }
                }
            }
        }
    }
}
