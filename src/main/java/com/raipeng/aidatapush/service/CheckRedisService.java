package com.raipeng.aidatapush.service;


import com.raipeng.aidatacommon.model.Admin;

import com.raipeng.aidatacommon.model.TenantSecrets;
import com.raipeng.aidatapush.repository.AdminRepository;
import com.raipeng.aidatapush.repository.TenantSecretsRepository;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class CheckRedisService {
    @Autowired
    private AdminService adminService;

    @Autowired
    private TenantSecretsService tenantSecretsService;

    @Autowired
    private AdminRepository adminRepository;

    @Autowired
    private TenantSecretsRepository tenantSecretsRepository;

    public void checkAdminRedis() {
        List<Admin> admins = adminRepository.findAll();
        for (Admin admin : admins) {
            Admin user = adminService.findById(admin.getId());
            log.info("check user success:{}", user);
        }
    }

    public void checkTenantSecretsRedis() {
        List<TenantSecrets> tenantSecrets = tenantSecretsRepository.findAll();
        for (TenantSecrets tenantSecret : tenantSecrets) {
            TenantSecrets firstByTenantId = tenantSecretsService.findFirstByTenantId(tenantSecret.getTenantId());
            log.info("check tenantSecret success:{}", firstByTenantId);
        }
    }
}
