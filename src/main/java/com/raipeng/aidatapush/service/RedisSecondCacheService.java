package com.raipeng.aidatapush.service;


import com.alibaba.fastjson.JSONObject;
import com.raipeng.aidatacommon.entity.AiRedisTask;
import com.raipeng.aidatacommon.model.AIOutboundTask;
import com.raipeng.aidatacommon.thirdrequests.enums.CallbackType;
import com.raipeng.aidatapush.config.HotConfig;
import com.raipeng.aidatapush.repository.AIOutboundTaskRepository;

import lombok.extern.slf4j.Slf4j;

import org.redisson.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.raipeng.common.constant.AIRedisCacheKeyConstant.CACHE_FOR_CALLBACK_TYPE_MAP;
import static com.raipeng.common.constant.AIRedisCacheKeyConstant.CACHE_FOR_SUPPLY_LINE_BELONG_MAP;


@Slf4j
@Service
public class RedisSecondCacheService {
    private static final String CALLBACK_TYPE_MAP_LOCAL_CACHE = "CALLBACK_TYPE_MAP_LOCAL_CACHE";

    private static final String SUPPLY_LINE_BELONG_MAP_LOCAL_CACHE = "SUPPLY_LINE_BELONG_MAP_LOCAL_CACHE";

    private static final Map<String, Map<String, CallbackType>> localCallbackTypeCache = new ConcurrentHashMap<>();

    private static final Map<String, Map<String, String>> localSupplyLineBelongMapCache = new ConcurrentHashMap<>();

    private static final Map<Long, String> localAiRedisTaskCache = new ConcurrentHashMap<>();

    @Autowired
    private HotConfig hotConfig;

    @Autowired
    private AdminService adminService;

    @Autowired
    private AIOutboundTaskRepository aiOutboundTaskRepository;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private SupplyLineService supplyLineService;

    public Map<String, CallbackType> getCallbackTypeMapLocal() {
        Map<String, CallbackType> callbackTypeMap = localCallbackTypeCache.get(CALLBACK_TYPE_MAP_LOCAL_CACHE);
        if (callbackTypeMap != null) {
            return callbackTypeMap;
        } else {
            RBucket<Object> bucket = redissonClient.getBucket(CACHE_FOR_CALLBACK_TYPE_MAP);
            bucket.delete();
            return adminService.getCallbackTypeFromRedis();
        }
    }

    public Map<String, String> getSupplyLineBelongMapLocal() {
        Map<String, String> supplyLineBelongMap = localSupplyLineBelongMapCache.get(SUPPLY_LINE_BELONG_MAP_LOCAL_CACHE);
        if (supplyLineBelongMap != null) {
            return supplyLineBelongMap;
        } else {
            RBucket<Object> bucket = redissonClient.getBucket(CACHE_FOR_SUPPLY_LINE_BELONG_MAP);
            bucket.delete();
            return supplyLineService.getSupplyLineBelongMap();
        }
    }

    public List<AiRedisTask> getRedisTasksLocal(Set<Long> taskIds) {
        List<AiRedisTask> aiRedisTasks = new ArrayList<>();
        Set<Long> taskIdsNotInLocal = new HashSet<>();
        for (Long taskId : taskIds) {
            String stringTask = localAiRedisTaskCache.get(taskId);
            if (stringTask != null) {
                AiRedisTask aiRedisTask = JSONObject.parseObject(stringTask, AiRedisTask.class);
                aiRedisTasks.add(aiRedisTask);
            } else {
                taskIdsNotInLocal.add(taskId);
            }
        }
        if (!taskIdsNotInLocal.isEmpty()) {
            List<AIOutboundTask> tasks = aiOutboundTaskRepository.findAllByIdIn(taskIdsNotInLocal);
            for (AIOutboundTask task : tasks) {
                AiRedisTask aiRedisTaskRedis = getAiRedisTask(task);
                String stringTask = JSONObject.toJSONString(aiRedisTaskRedis);
                localAiRedisTaskCache.compute(task.getId(), (k, v) -> stringTask);
                aiRedisTasks.add(aiRedisTaskRedis);
            }
        }
        return aiRedisTasks;
    }

    @PostConstruct
    public void init() {
        log.info("----init callback type cache from local----");
        localCallbackTypeCache.compute(CALLBACK_TYPE_MAP_LOCAL_CACHE, (k, v) -> adminService.getCallbackTypeFromRedis());
        log.info("----init supply line belong cache from local----");
        localSupplyLineBelongMapCache.compute(SUPPLY_LINE_BELONG_MAP_LOCAL_CACHE, (k, v) -> supplyLineService.getSupplyLineBelongMap());
    }

    public void updateCache() {
        try {
            Map<String, CallbackType> callbackTypeFromRedis = adminService.getCallbackTypeFromRedis();
            localCallbackTypeCache.compute(CALLBACK_TYPE_MAP_LOCAL_CACHE, (k, v) -> callbackTypeFromRedis);
            log.info("----update callback type cache from local----");
        } catch (Exception e) {
            log.error("[Exception]=>更新本地callback type缓存失败", e.getCause());
        }

        try {
            Map<String, String> supplyLineBelongMap = supplyLineService.getSupplyLineBelongMap();
            localSupplyLineBelongMapCache.compute(SUPPLY_LINE_BELONG_MAP_LOCAL_CACHE, (k, v) -> supplyLineBelongMap);
            log.info("----update supply line belong cache from local----");
        } catch (Exception e) {
            log.error("[Exception]=>更新本地supplyLine belong 缓存失败", e.getCause());
        }
    }

    public void updateTaskCache() {
        try {
            RQueue<Long> queue = redissonClient.getQueue(hotConfig.getDataPushTaskQueueCode());
            int size = queue.size();
            if (size > 0) {
                List<Long> taskIds = queue.poll(size);
                if (!taskIds.isEmpty()) {
                    Set<Long> taskIdsNew = taskIds.stream().filter(Objects::nonNull).collect(Collectors.toSet());
                    List<AIOutboundTask> tasks = aiOutboundTaskRepository.findAllByIdIn(taskIdsNew);
                    for (AIOutboundTask task : tasks) {
                        AiRedisTask aiRedisTaskRedis = getAiRedisTask(task);
                        localAiRedisTaskCache.compute(task.getId(), (k, v) -> JSONObject.toJSONString(aiRedisTaskRedis));
                    }
                    log.info("----update task table cache from local----:{}", taskIdsNew);
                }
            }
            log.info("----update task table cache from local end----");
        } catch (Exception e) {
            log.error("[Exception]=>更新本地Task缓存失败", e.getCause());
        }
    }

    public void clearTaskCache() {
        localAiRedisTaskCache.clear();
    }

    private static AiRedisTask getAiRedisTask(AIOutboundTask task) {
        AiRedisTask aiRedisTaskRedis = new AiRedisTask();
        aiRedisTaskRedis.setId(task.getId());
        aiRedisTaskRedis.setGroupId(task.getGroupId());
        aiRedisTaskRedis.setProductId(task.getProductId());
        aiRedisTaskRedis.setIndustrySecondFieldId(task.getIndustrySecondFieldId());
        aiRedisTaskRedis.setAutoReCall(task.getAutoReCall());
        aiRedisTaskRedis.setFirstRecallTime(task.getFirstRecallTime());
        aiRedisTaskRedis.setSecondRecallTime(task.getSecondRecallTime());
        aiRedisTaskRedis.setHangUpSms(task.getHangUpSms());
        aiRedisTaskRedis.setHangUpExcluded(task.getHangUpExcluded());
        aiRedisTaskRedis.setTaskName(task.getTaskName());
        aiRedisTaskRedis.setProgramId(task.getProgramId());
        aiRedisTaskRedis.setSpeechCraftId(task.getSpeechCraftId());
        aiRedisTaskRedis.setScriptStringId(task.getScriptStringId());
        aiRedisTaskRedis.setSpeechCraftName(task.getSpeechCraftName());
        aiRedisTaskRedis.setTaskType(task.getTaskType());
        aiRedisTaskRedis.setTemplateId(task.getTemplateId());
        return aiRedisTaskRedis;
    }
}
