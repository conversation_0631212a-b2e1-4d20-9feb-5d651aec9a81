package com.raipeng.aidatapush.service;

import com.raipeng.aidatacommon.model.AIIntentionType;
import com.raipeng.aidatapush.repository.AIIntentionTypeRepository;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class AIIntentionTypeService {
    @Autowired
    private AIIntentionTypeRepository aiIntentionTypeRepository;

    @Cacheable(value = "CACHE::AI_INTENTION_TYPE_BY_SCRIPT_ID")
    public List<AIIntentionType> findAllByScriptId(Long id){
        return aiIntentionTypeRepository.findAllByScriptId(id);
    }
}
