package com.raipeng.aidatapush.service;

import com.alibaba.fastjson.JSONObject;
import com.raipeng.aidatacommon.model.CallOverRate;
import com.raipeng.aidatacommon.model.dto.RetryParameterDTO;
import com.raipeng.aidatacommon.utils.AESUtils;
import com.raipeng.aidatapush.repository.CallOverRateRepository;
import com.raipeng.aidatapush.utils.LogUtils;
import com.raipeng.aidatapush.utils.StringUtils;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
public class CallOverRateService {
    @Autowired
    private CallOverRateRepository repository;

    @Autowired
    private ApplicationContext applicationContext;

    public Long saveData(String name, String methodName, String infoKey, Class<?>[] parametersClass, Object[] args, String message) {
        List<RetryParameterDTO> parameterDTOList = new ArrayList<>();
        assembleParameterList(parameterDTOList, parametersClass, args);
        CallOverRate callOverRate = new CallOverRate();
        callOverRate.setRootClassName(name);
        callOverRate.setMethodName(methodName);
        callOverRate.setInfoKey(infoKey);
        callOverRate.setArgsEncryption(AESUtils.encryptByAES(JSONObject.toJSONString(parameterDTOList)));
        callOverRate.setArgsDesensitization(LogUtils.replacePhone(JSONObject.toJSONString(parameterDTOList)));
        callOverRate.setRetryResult(0);
        callOverRate.setMemo(message);
        repository.save(callOverRate);
        return callOverRate.getId();
    }

    private void assembleParameterList(List<RetryParameterDTO> parameterDTOList, Class<?>[] parametersClass, Object[] args) {
        int count = args.length;
        for (int i = 0; i < count; i++) {
            RetryParameterDTO dto = new RetryParameterDTO();
            dto.setParameterOrder(i);
            dto.setParameterClassName(parametersClass[i].getName());
            dto.setParameterValue(args[i]);
            parameterDTOList.add(dto);
        }
    }

    public void findOverRateDataAndDoRetry(String id) {
        List<CallOverRate> callOverRateList = new ArrayList<>();
        if (StringUtils.isNotEmpty(id)) {
            Long idL = null;
            try {
                idL = Long.valueOf(id);
            } catch (Exception e) {
                log.error("findOverRateDataAndDoRetry id = " + id + "  id 格式错误");
                return;
            }
            Optional<CallOverRate> callOverRate = repository.findById(idL);
            if (callOverRate.isPresent()) {
                callOverRateList.add(callOverRate.get());
            } else {
                log.error("findOverRateDataAndDoRetry id = " + id + "不存在");
                return;
            }
        } else {
            callOverRateList = repository.findAll();
            if (CollectionUtils.isEmpty(callOverRateList)) {
                log.info("findOverRateDataAndDoRetry,无需要重试数据，结束");
                return;
            }
        }
        log.info("findOverRateDataAndDoRetry,待重试数据 size = " + callOverRateList.size());
        for (CallOverRate callOverRate : callOverRateList) {
            doRetryUsingData(callOverRate);
        }
    }

    private void doRetryUsingData(CallOverRate callOverRate) {
        try {
            AutowiredAnnotationBeanPostProcessor postProcessor = applicationContext.getBean(AutowiredAnnotationBeanPostProcessor.class);
            Class<?> clazz = Thread.currentThread().getContextClassLoader().loadClass(callOverRate.getRootClassName());
            Object o = clazz.getConstructor().newInstance();
            postProcessor.processInjection(o);
            Method method = null;
            String paramString = callOverRate.getArgsEncryption();
            if (StringUtils.isNotEmpty(paramString)) {
                paramString = AESUtils.decryptByAES(paramString);
                List<RetryParameterDTO> parameterDTOList = JSONObject.parseArray(paramString, RetryParameterDTO.class);
                parameterDTOList = parameterDTOList.stream().sorted(Comparator.comparing(RetryParameterDTO::getParameterOrder)).collect(Collectors.toList());
                Class<?>[] cl = new Class[parameterDTOList.size()];
                Object[] args = new Object[parameterDTOList.size()];
                for (int i = 0; i < parameterDTOList.size(); i++) {
                    cl[i] = Thread.currentThread().getContextClassLoader().loadClass(parameterDTOList.get(i).getParameterClassName());
                    args[i] = JSONObject.parseObject(JSONObject.toJSONString(parameterDTOList.get(i).getParameterValue()), cl[i]);
                }

                method = clazz.getMethod(callOverRate.getMethodName(), cl);
                Type[] parameterTypes = method.getGenericParameterTypes();
                for (int i = 0; i < parameterTypes.length; i++) {
                    Type type = parameterTypes[i];
                    if (type instanceof ParameterizedType) {
                        ParameterizedType typeVariable = (ParameterizedType) type;
                        Type[] typeArgs = typeVariable.getActualTypeArguments();

                        for (Type typeArg : typeArgs) {
                            if (typeArg instanceof Class) {
                                Class typeArgClass = (Class) typeArg;
                                System.out.println(typeArgClass.getName());
                                Class<?> argClazz = Thread.currentThread().getContextClassLoader().loadClass(typeArgClass.getName());
                                args[i] = JSONObject.parseArray(JSONObject.toJSONString(parameterDTOList.get(i).getParameterValue()), argClazz);
                            }
                        }
                    }
                }

                try {
                    method.invoke(o, args);
                    repository.delete(callOverRate);
                    log.info("重试定时任务 \n  doRetryUsingData 方法执行成功，id = " + callOverRate.getId());
                } catch (Exception e) {
                    callOverRate.setMemo(e.getCause().getMessage());
                    repository.save(callOverRate);
                    log.error("重试定时任务 \n  doRetryUsingData 方法执行失败，id = " + callOverRate.getId(), e);
                    DingDingService.sendDingDingMsgRetryException("重试定时任务 \n "
                            + " doRetryUsingData 方法执行失败 \n"
                            + "id = " + callOverRate.getId() + "\n"
                            + "cause = " + e.getCause().getMessage() + "\n");
                }

            }
        } catch (Exception e) {
            log.error("重试定时任务 \n  doRetryUsingData 组装方法失败，id = " + callOverRate.getId(), e);
            DingDingService.sendDingDingMsgRetryException("重试定时任务 \n  doRetryUsingData 组装方法失败，id = " + callOverRate.getId());
        }
    }
}
