package com.raipeng.aidatapush.service.callresulthandler.impl;


import com.alibaba.fastjson.JSONObject;
import com.raipeng.aidatacommon.enums.CallStatusEnum;
import com.raipeng.aidatacommon.model.CallRecordForManualDirect;
import com.raipeng.aidatacommon.model.TenantProgramAdmin;
import com.raipeng.aidatacommon.model.dto.CallRecordResult;
import com.raipeng.aidatacommon.thirdrequests.enums.CallbackType;
import com.raipeng.aidatapush.config.HotConfig;
import com.raipeng.aidatapush.controller.wrapper.CallResultHandlerForHumanWrapper;
import com.raipeng.aidatapush.entity.SendMsgEntity;
import com.raipeng.aidatapush.enums.CallResultHandleType;
import com.raipeng.aidatapush.enums.DingDingMsgType;
import com.raipeng.aidatapush.repository.CallRecordForManualDirectRepository;
import com.raipeng.aidatapush.repository.TenantProgramAdminRepository;
import com.raipeng.aidatapush.service.AdminService;
import com.raipeng.aidatapush.service.CallResultUtilService;
import com.raipeng.aidatapush.service.DingDingHighPriorityService;
import com.raipeng.aidatapush.service.RedisSecondCacheService;
import com.raipeng.aidatapush.service.callresulthandler.ResultHandler;
import com.raipeng.aidatapush.service.mq.producer.CallResultPushThirdForHumanProducer;
import com.raipeng.aidatapush.service.mq.producer.CallResultSaveForUnCallHumanProducer;
import com.raipeng.aidatapush.service.mq.producer.PreXUnCallForHumanProducer;
import com.raipeng.aidatapush.service.mq.producer.newCallback.UnCallDataPushThirdForHumanProducer;
import com.raipeng.aidatapush.utils.CallResultHandleUtil;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.raipeng.aidatapush.config.RabbitConstants.*;

@Slf4j
@Service
public class UnCallResultHandleForHumanService implements ResultHandler {
    @Autowired
    private CallResultUtilService callResultUtilService;

    @Autowired
    private CallResultPushThirdForHumanProducer callResultPushThirdForHumanProducer;

    @Autowired
    private CallResultSaveForUnCallHumanProducer callResultSaveForUnCallHumanProducer;

    @Autowired
    private CallRecordForManualDirectRepository callRecordForManualDirectRepository;

    @Autowired
    private TenantProgramAdminRepository tenantProgramAdminRepository;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private HotConfig hotConfig;

    @Autowired
    private DingDingHighPriorityService dingDingHighPriorityService;

    @Autowired
    private PreXUnCallForHumanProducer preXUnCallForHumanProducer;

    @Autowired
    private UnCallDataPushThirdForHumanProducer unCallDataPushThirdForHumanProducer;

    @Autowired
    private AdminService adminService;

    @Autowired
    private RedisSecondCacheService redisSecondCacheService;

    @Override
    public void disPatchExceptCallResultSingle() {
        CallResultHandleUtil.handleXSingle(
                X_CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_DIRECT_CALL_QUEUE,
                rabbitTemplate,
                this,
                CallResultHandleType.HUMAN_UN_CALL,
                hotConfig.getTimeoutSeconds());
    }

    @Override
    public void disPatchExceptCallResult() {
        CallResultHandleUtil.handleX(
                X_CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_DIRECT_CALL_QUEUE,
                rabbitTemplate,
                this,
                CallResultHandleType.HUMAN_UN_CALL,
                hotConfig.getTimeoutSeconds());
    }

    @Override
    public void disPatchExceptPreXCallResult(String callResultsString) {
        CallResultHandleUtil.handlePreXSingle(
                callResultsString,
                this,
                CallResultHandleType.HUMAN_UN_CALL,
                hotConfig.getTimeoutSeconds());
    }

    @Override
    public void collectXCallResult(String callResultsString) {
        CallResultHandleUtil.pushCallResultBack(rabbitTemplate,
                callResultsString,
                X_CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_DIRECT_CALL_QUEUE,
                X_CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_DIRECT_CALL_EXCHANGE,
                X_CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_DIRECT_CALL_ROUTING,
                CallResultHandleType.HUMAN_UN_CALL);
        dingDingHighPriorityService.sedDDMsg(DingDingMsgType.WARN, "人工直呼未呼通记录处理错误，请及时关注");
    }

    @Override
    public void collectPreXCallResult(String callResultsString) {
        preXUnCallForHumanProducer.send(callResultsString);
    }

    @Override
    public SendMsgEntity dispatchCallResult(List<CallRecordResult> callRecordResultList) {
        int size = callRecordResultList.size();
        long m1 = System.currentTimeMillis();

        // 1. 检查有没有未解密的电话号码
        callResultUtilService.checkIfNeedDecryption(callRecordResultList);

        // 2. 根据recordIds进行数据库查询操作
        List<String> recordIds = callRecordResultList.stream()
                .map(CallRecordResult::getSpeechCallId)
                .collect(Collectors.toList());
        List<CallRecordForManualDirect> callRecordList = callRecordForManualDirectRepository.findAllByRecordIdIn(recordIds);
        Map<String, CallRecordForManualDirect> callRecordMap = callRecordList.stream()
                .collect(Collectors.toMap(CallRecordForManualDirect::getRecordId, Function.identity(), (a, b) -> a));

        long m2 = System.currentTimeMillis();

        Map<String, CallbackType> callbackTypeMap = adminService.getCallbackTypeMap();
        // 3. 创建传输容器
        List<CallRecordResult> errorCallRecordResults = new ArrayList<>();
        List<CallRecordForManualDirect> unCallDataPushThirdList = new ArrayList<>();
        List<CallRecordForManualDirect> needPushThirdList = new ArrayList<>();

        // 4. 单条数据操作
        for (CallRecordResult callRecordResult : callRecordResultList) {
            CallRecordForManualDirect callRecord = callRecordMap.get(callRecordResult.getSpeechCallId());
            if (callRecord == null) {
                log.error("Exception:{}, 人工直呼callRecordResult:{}", "找不到通话记录", callRecordResult);
                errorCallRecordResults.add(callRecordResult);
                continue;
            }
            TenantProgramAdmin firstByGroupId = tenantProgramAdminRepository.findFirstByGroupId(callRecord.getGroupId());
            if(firstByGroupId != null){
                callRecordResult.setProductId(firstByGroupId.getProductId());
                callRecordResult.setIndustrySecondFieldId(firstByGroupId.getSecondIndustryId());
            }
            updateCallRecord(callRecordResult, callRecord, needPushThirdList, unCallDataPushThirdList, callbackTypeMap);
        }

        long m3 = System.currentTimeMillis();
        log.info("[人工直呼未呼通消费{}条]=>call表耗时:{}, 数据处理耗时:{}", size, m2-m1, m3-m2);

        // 5. 返回容器数据
        return SendMsgEntity.builder().msgForHumanUnCall(
                SendMsgEntity.MsgForHumanUnCall.builder()
                .callRecordResultList(callRecordResultList)
                .callRecordList(callRecordList)
                .errorCallRecordResults(errorCallRecordResults)
                .needPushThirdList(needPushThirdList)
                .unCallDataForPushThird(unCallDataPushThirdList)
                .build()).build();
    }

    @Override
    public void sendMsgToMq(SendMsgEntity entity) {
        SendMsgEntity.MsgForHumanUnCall msg = entity.getMsgForHumanUnCall();
        List<CallRecordResult> callRecordResultList = msg.getCallRecordResultList();
        List<CallRecordForManualDirect> callRecordList = msg.getCallRecordList();
        List<CallRecordResult> errorCallRecordResults = msg.getErrorCallRecordResults();
        List<CallRecordForManualDirect> needPushThirdList = msg.getNeedPushThirdList();
        List<CallRecordForManualDirect> unCallDataForPushThird = msg.getUnCallDataForPushThird();

        callResultUtilService.saveUnCallHistoryToRedis(callRecordResultList);

        CallResultHandlerForHumanWrapper callResultHandlerForHumanWrapper = new CallResultHandlerForHumanWrapper();
        CallResultHandlerForHumanWrapper wrapperForUnCall = new CallResultHandlerForHumanWrapper();

        if (CollectionUtils.isNotEmpty(needPushThirdList)) {
            callResultHandlerForHumanWrapper.setPushThirdList(needPushThirdList);
            callResultPushThirdForHumanProducer.send(callResultHandlerForHumanWrapper);
        }
        if (CollectionUtils.isNotEmpty(unCallDataForPushThird)) {
            unCallDataPushThirdForHumanProducer.send(unCallDataForPushThird);
        }

        if (CollectionUtils.isNotEmpty(callRecordList)) {
            wrapperForUnCall.setCallRecordList(callRecordList);
            wrapperForUnCall.setCallRecordResultList(callRecordResultList);
            callResultSaveForUnCallHumanProducer.send(wrapperForUnCall);
        }

        if(CollectionUtils.isNotEmpty(errorCallRecordResults)) {
            String jsonString = JSONObject.toJSONString(errorCallRecordResults);
            collectXCallResult(jsonString);
        }
    }

    private void updateCallRecord(CallRecordResult callRecordResult,
                                  CallRecordForManualDirect callRecord,
                                  List<CallRecordForManualDirect> needPushThirdList,
                                  List<CallRecordForManualDirect> unCallDataPushThirdList,
                                  Map<String, CallbackType> callbackTypeMap) {
        callRecord.setCallDuration(callRecordResult.getCallDuration());
        callRecord.setCallDurationSec(callRecordResult.getCallDurationSec());
        callRecord.setCallStatus(callRecordResult.getCallStatus().toString());
        callRecord.setCallStatusStr(CallStatusEnum.codeOf(callRecordResult.getCallStatus()).getField());
        callRecord.setErrorCode(callRecordResult.getErrorCode());
        callRecord.setCause(callRecordResult.getCause());
        callRecord.setCallId(callRecordResult.getCallId());
        callRecord.setLineId(callRecordResult.getLineId());
        callRecord.setLineCode(callRecordResult.getLineCode());
        callRecord.setMerchantLineCode(callRecordResult.getMerchantLineCode());
        callRecord.setMerchantLineId(callRecordResult.getMerchantLineId());
        callRecord.setFsIp(callRecordResult.getFsIp());
        callRecord.setAiCallIp(callRecordResult.getAiCallIp());
        callRecord.setCallOutTime(callRecordResult.getCallOutTime());
        callRecord.setWaitmsec(callRecordResult.getWaitmsec());
        callRecord.setPlainPhone(callRecordResult.getPlainPhone());
        callRecord.setSipCallId(callRecordResult.getSipCallId());
        callRecord.setSupplyLineBelong(redisSecondCacheService.getSupplyLineBelongMapLocal().get(callRecord.getLineId()));

        CallbackType callbackType = callbackTypeMap.get(callRecord.getGroupId());
        switch (callbackType) {
            case PUSH_THIRD:
                unCallDataPushThirdList.add(callRecord);
                break;
            case PUSH_ANTS:
                log.info("人工直呼没有蚂蚁任务");
                break;
            case OLD_PUSH:
                needPushThirdList.add(callRecord);
            default:
                log.info("混入了未知回传类型");
                break;
        }

    }
}
