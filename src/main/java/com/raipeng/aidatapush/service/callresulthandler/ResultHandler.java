package com.raipeng.aidatapush.service.callresulthandler;

import com.raipeng.aidatacommon.model.dto.CallRecordResult;
import com.raipeng.aidatapush.entity.SendMsgEntity;

import java.util.List;

public interface ResultHandler {
    void disPatchExceptCallResultSingle();

    void disPatchExceptCallResult();

    void disPatchExceptPreXCallResult(String callResultsString);

    void collectXCallResult(String callResultsString);

    void collectPreXCallResult(String callResultString);

    SendMsgEntity dispatchCallResult(List<CallRecordResult> callRecordResultList);

    void sendMsgToMq(SendMsgEntity entity);
}
