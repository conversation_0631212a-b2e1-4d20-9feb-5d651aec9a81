package com.raipeng.aidatapush.service.callresulthandler.impl;

import com.raipeng.aidatacommon.model.dto.CallRecordResult;
import com.raipeng.aidatapush.config.HotConfig;
import com.raipeng.aidatapush.controller.wrapper.CallResultHandlerForPureAILandingPageWrapper;
import com.raipeng.aidatapush.entity.SendMsgEntity;
import com.raipeng.aidatapush.enums.CallResultHandleType;
import com.raipeng.aidatapush.enums.DingDingMsgType;
import com.raipeng.aidatapush.service.callresulthandler.ResultHandler;
import com.raipeng.aidatapush.service.mq.producer.PreXCallForLandingPagePureAIProducer;
import com.raipeng.aidatapush.service.mq.producer.CallResultSaveForCalledPureAILandingPageProducer;
import com.raipeng.aidatapush.service.DingDingHighPriorityService;
import com.raipeng.aidatapush.service.CallResultUtilService;
import com.raipeng.aidatapush.utils.CallResultHandleUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.raipeng.aidatapush.config.RabbitConstants.*;

@Slf4j
@Service
public class CallResultHandleForLandingPagePureAIService implements ResultHandler {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private HotConfig hotConfig;

    @Autowired
    private DingDingHighPriorityService dingDingHighPriorityService;

    @Autowired
    private PreXCallForLandingPagePureAIProducer preXCallForLandingPagePureAIProducer;

    @Autowired
    private CallResultSaveForCalledPureAILandingPageProducer callResultSaveForCalledPureAILandingPageProducer;

    @Autowired
    private CallResultUtilService callResultUtilService;

    @Override
    public void disPatchExceptCallResultSingle() {
        CallResultHandleUtil.handleXSingle(
                X_CALL_PHONE_RESULT_NOTICE_SPEECH_LANDING_PAGE_QUEUE,
                rabbitTemplate,
                this,
                CallResultHandleType.LANDING_PAGE_PURE_AI,
                hotConfig.getTimeoutSeconds());
    }

    @Override
    public void disPatchExceptCallResult() {
        CallResultHandleUtil.handleX(
                X_CALL_PHONE_RESULT_NOTICE_SPEECH_LANDING_PAGE_QUEUE,
                rabbitTemplate,
                this,
                CallResultHandleType.LANDING_PAGE_PURE_AI,
                hotConfig.getTimeoutSeconds());
    }

    @Override
    public void disPatchExceptPreXCallResult(String callResultsString) {
        CallResultHandleUtil.handlePreXSingle(
                callResultsString,
                this,
                CallResultHandleType.LANDING_PAGE_PURE_AI,
                hotConfig.getTimeoutSeconds());
    }

    @Override
    public void collectXCallResult(String callResultsString) {
        CallResultHandleUtil.pushCallResultBack(rabbitTemplate,
                callResultsString,
                X_CALL_PHONE_RESULT_NOTICE_SPEECH_LANDING_PAGE_QUEUE,
                X_CALL_PHONE_RESULT_NOTICE_SPEECH_LANDING_PAGE_EXCHANGE,
                X_CALL_PHONE_RESULT_NOTICE_SPEECH_LANDING_PAGE_ROUTING,
                CallResultHandleType.LANDING_PAGE_PURE_AI);
        dingDingHighPriorityService.sedDDMsg(DingDingMsgType.WARN, "落地页纯AI记录处理错误，请及时关注");
    }

    @Override
    public void collectPreXCallResult(String callResultsString) {
        preXCallForLandingPagePureAIProducer.send(callResultsString);
    }

    @Override
    public SendMsgEntity dispatchCallResult(List<CallRecordResult> callRecordResultList) {
        // TODO: 实现具体的业务逻辑，仿照 CallResultHandleForPureAIService 的实现
        // 这里暂时返回一个基本的 SendMsgEntity，具体实现由人工完成
        log.info("开始处理落地页纯AI呼通记录，共{}条", callRecordResultList.size());

        // 1. 检测是否需要解密
        callResultUtilService.checkIfNeedDecryption(callRecordResultList);

        CallResultHandlerForPureAILandingPageWrapper cdrWrapper = new CallResultHandlerForPureAILandingPageWrapper();
        cdrWrapper.setCallRecordResultList(callRecordResultList);

        return SendMsgEntity.builder().msgForLandingPagePureAICall(
                SendMsgEntity.MsgForLandingPagePureAICall.builder()
                        .cdrWrapper(cdrWrapper)
                        .build()).build();
    }

    @Override
    public void sendMsgToMq(SendMsgEntity entity) {
        SendMsgEntity.MsgForLandingPagePureAICall msg = entity.getMsgForLandingPagePureAICall();
        if (msg != null) {
            CallResultHandlerForPureAILandingPageWrapper cdrWrapper = msg.getCdrWrapper();
            callResultSaveForCalledPureAILandingPageProducer.send(cdrWrapper);
            log.info("落地页纯AI记录已发送到保存队列");
        }
    }
}
