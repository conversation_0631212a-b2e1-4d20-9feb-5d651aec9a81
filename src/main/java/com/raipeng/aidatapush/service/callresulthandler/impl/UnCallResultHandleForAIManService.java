package com.raipeng.aidatapush.service.callresulthandler.impl;


import com.alibaba.fastjson.JSONObject;
import com.raipeng.aidatacommon.enums.CallStatusEnum;
import com.raipeng.aidatacommon.model.CallRecordForHumanMachine;
import com.raipeng.aidatacommon.model.PhoneRecord;
import com.raipeng.aidatacommon.model.dto.CallRecordResult;
import com.raipeng.aidatacommon.thirdrequests.enums.CallbackType;
import com.raipeng.aidatapush.config.HotConfig;
import com.raipeng.aidatapush.controller.wrapper.CallResultHandlerForAIManWrapper;
import com.raipeng.aidatapush.entity.SendMsgEntity;
import com.raipeng.aidatapush.enums.CallResultHandleType;
import com.raipeng.aidatapush.enums.DingDingMsgType;
import com.raipeng.aidatapush.repository.CallRecordForHumanMachineRepository;
import com.raipeng.aidatapush.repository.PhoneRecordRepository;
import com.raipeng.aidatapush.service.*;
import com.raipeng.aidatapush.service.callresulthandler.ResultHandler;
import com.raipeng.aidatapush.service.mq.producer.*;
import com.raipeng.aidatapush.service.mq.producer.newCallback.UnCallDataPushThirdForAIManProducer;
import com.raipeng.aidatapush.utils.CallResultHandleUtil;
import com.raipeng.aidatacommon.entity.AiRedisTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.raipeng.aidatapush.config.RabbitConstants.*;

@Slf4j
@Service
public class UnCallResultHandleForAIManService implements ResultHandler {
    @Autowired
    private TaskRedisService taskRedisService;

    @Autowired
    private CallRecordForHumanMachineRepository callRecordForHumanMachineRepository;

    @Autowired
    private PhoneRecordRepository phoneRecordRepository;

    @Autowired
    private CallResultPushThirdForAIManProducer callResultPushThirdForAIManProducer;

    @Autowired
    private CallResultPushAntsForAIManProducer callResultPushAntsForAIManProducer;

    @Autowired
    private CallResultSaveForReCallAIManProducer callResultSaveForReCallAIManProducer;

    @Autowired
    private CallResultSaveForUnCallAIManProducer callResultSaveForUnCallAIManProducer;

    @Autowired
    private CallResultUtilService callResultUtilService;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private HotConfig hotConfig;

    @Autowired
    private DingDingHighPriorityService dingDingHighPriorityService;

    @Autowired
    private PreXUnCallForAIManProducer preXUnCallForAIManProducer;

    @Autowired
    private UnCallDataPushThirdForAIManProducer unCallDataPushThirdForAIManProducer;

    @Autowired
    private AdminService adminService;

    @Autowired
    private RedisSecondCacheService redisSecondCacheService;

    @Override
    public void disPatchExceptCallResultSingle() {
        CallResultHandleUtil.handleXSingle(
                X_CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_TAKE_OVER_LISTEN_IN_QUEUE,
                rabbitTemplate,
                this,
                CallResultHandleType.AI_MAN_UN_CALL,
                hotConfig.getTimeoutSeconds());
    }

    @Override
    public void disPatchExceptCallResult() {
        CallResultHandleUtil.handleX(
                X_CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_TAKE_OVER_LISTEN_IN_QUEUE,
                rabbitTemplate,
                this,
                CallResultHandleType.AI_MAN_UN_CALL,
                hotConfig.getTimeoutSeconds());
    }

    @Override
    public void disPatchExceptPreXCallResult(String callResultsString) {
        CallResultHandleUtil.handlePreXSingle(
                callResultsString,
                this,
                CallResultHandleType.AI_MAN_UN_CALL,
                hotConfig.getTimeoutSeconds());
    }

    @Override
    public void collectXCallResult(String callResultsString) {
        CallResultHandleUtil.pushCallResultBack(rabbitTemplate,
                callResultsString,
                X_CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_TAKE_OVER_LISTEN_IN_QUEUE,
                X_CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_TAKE_OVER_LISTEN_IN_EXCHANGE,
                X_CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_TAKE_OVER_LISTEN_IN_ROUTING,
                CallResultHandleType.AI_MAN_UN_CALL);
        dingDingHighPriorityService.sedDDMsg(DingDingMsgType.WARN, "人机协同未呼通记录处理错误，请及时关注");
    }

    @Override
    public void collectPreXCallResult(String callResultsString) {
        preXUnCallForAIManProducer.send(callResultsString);
    }

    @Override
    public SendMsgEntity dispatchCallResult(List<CallRecordResult> callRecordResultList) {
        int size = callRecordResultList.size();
        long m1 = System.currentTimeMillis();
        // 1. 检查有没有未解密的电话号码
        callResultUtilService.checkIfNeedDecryption(callRecordResultList);

        // 2. 根据recordIds进行数据库查询操作
        List<String> recordIds = callRecordResultList.stream()
                .map(CallRecordResult::getSpeechCallId)
                .collect(Collectors.toList());
        List<CallRecordForHumanMachine> callRecordList = callRecordForHumanMachineRepository.findAllByRecordIdIn(recordIds);
        Map<String, CallRecordForHumanMachine> callRecordMap = callRecordList.stream()
                .collect(Collectors.toMap(CallRecordForHumanMachine::getRecordId, Function.identity(), (a, b) -> a));

        long m2 = System.currentTimeMillis();
        List<String> phoneRecordIds = callRecordList.stream()
                .map(CallRecordForHumanMachine::getPhoneRecordId)
                .collect(Collectors.toList());
        List<PhoneRecord> phoneRecordList = phoneRecordRepository.findAllByRecordIdIn(phoneRecordIds);
        Map<String, PhoneRecord> phoneRecordMap = phoneRecordList.stream()
                .collect(Collectors.toMap(PhoneRecord::getRecordId, Function.identity(), (a, b) -> a));

        long m3 = System.currentTimeMillis();
        Set<Long> taskIds = phoneRecordList.stream()
                .map(task -> Long.valueOf(task.getTaskId()))
                .collect(Collectors.toSet());

        List<AiRedisTask> aiRedisTaskList = taskRedisService.getAiRedisTaskList(taskIds);
        Map<Long, String> taskGroupIdMap = aiRedisTaskList.stream().collect(
                Collectors.toMap(AiRedisTask::getId, AiRedisTask::getGroupId, (a, b) -> a));
        Map<Long, AiRedisTask> aiOutboundTaskMap = aiRedisTaskList.stream()
                .collect(Collectors.toMap(AiRedisTask::getId, Function.identity(), (a, b) -> a));

        aiRedisTaskList.forEach(task -> callResultUtilService.initUnCallTaskStatistic(task));
        long m4 = System.currentTimeMillis();
        Map<String, CallbackType> callbackTypeMap = adminService.getCallbackTypeMap();
        long m5 = System.currentTimeMillis();
        // 3. 创建传输容器
        List<CallRecordForHumanMachine> needRetryRecordList = new ArrayList<>();
        List<CallRecordForHumanMachine> needReCallRecordList = new ArrayList<>();
        List<CallRecordForHumanMachine> needPushThirdList = new ArrayList<>();
        List<CallRecordForHumanMachine> needPushAntsList = new ArrayList<>();
        List<CallRecordForHumanMachine> unCallDataPushThirdList = new ArrayList<>();
        Set<String> needReCheckSpeechCallIds = new HashSet<>();
        Set<String> unCallSpeechCallIds = new HashSet<>();
        Map<String, Map<String, Double>> saveRedisKeysMap = new HashMap<>();
        List<CallRecordResult> errorCallRecordResults = new ArrayList<>();

        // 4. 单条数据操作
        for (CallRecordResult callRecordResult : callRecordResultList) {
            String speechCallId = callRecordResult.getSpeechCallId();
            CallRecordForHumanMachine callRecord = callRecordMap.get(callRecordResult.getSpeechCallId());
            if (callRecord == null) {
                log.error("Exception:{}, 人机协同callRecordResult:{}", "找不到通话记录", callRecordResult);
                errorCallRecordResults.add(callRecordResult);
                continue;
            }

            PhoneRecord phoneRecord = phoneRecordMap.get(callRecord.getPhoneRecordId());
            AiRedisTask aiRedisTask = aiOutboundTaskMap.get(Long.parseLong(callRecord.getTaskId()));
            if(phoneRecord == null || aiRedisTask == null){
                log.error("Exception:{}, 人机协同callRecordResult:{}", "名单或者任务记录", callRecordResult);
                errorCallRecordResults.add(callRecordResult);
                continue;
            }
            callRecordResult.setProductId(aiRedisTask.getProductId());
            callRecordResult.setIndustrySecondFieldId(aiRedisTask.getIndustrySecondFieldId());

            // 4.1 处理小于200ms需要重试的情况
            if ("1".equals(callRecordResult.getIsNewRecord())) {
                needRetryRecordList.add(createNewRecordInCdr(callRecordResult, callRecord));
                needReCheckSpeechCallIds.add(speechCallId);
                // 4.2 处理未呼通通话记录和补呼情况
            } else {
                unCallSpeechCallIds.add(speechCallId);
                handleUnCallBatch(
                        callRecordResult,
                        callRecord,
                        phoneRecord,
                        aiRedisTask,
                        saveRedisKeysMap,
                        needReCallRecordList,
                        needPushThirdList,
                        needPushAntsList,
                        unCallDataPushThirdList,
                        callbackTypeMap);
            }
        }

        // 5. 剔除会覆盖呼通的通话记录
        for (String needReCheckSpeechCallId : needReCheckSpeechCallIds) {
            if (!unCallSpeechCallIds.contains(needReCheckSpeechCallId)) {
                CallRecordForHumanMachine callRecord = callRecordMap.get(needReCheckSpeechCallId);
                if (callRecord == null) {
                    log.error("Exception:{}, 人机协同 callRecordResult:{}", "找不到通话记录", needReCheckSpeechCallId);
                    continue;
                }
                PhoneRecord phoneRecord = phoneRecordMap.get(callRecord.getPhoneRecordId());
                if(phoneRecord == null){
                    log.error("Exception:{}, 人机协同 callRecordResult:{}", "找不到名单记录", needReCheckSpeechCallId);
                    continue;
                }
                callRecordList.remove(callRecord);
                phoneRecordList.remove(phoneRecord);
            }
        }

        long m6 = System.currentTimeMillis();
        log.info("[人机协同未呼通消费{}条]=>" +
                        "call表耗时:{}, " +
                        "phone表耗时:{}, " +
                        "task表耗时:{}, " +
                        "账号类型查询耗时:{}, " +
                        "数据处理耗时:{}",
                size, m2-m1, m3-m2, m4-m3, m5-m4, m6-m5);

        // 6. 返回容器数据
        return SendMsgEntity.builder().msgForAIManUnCall(
                SendMsgEntity.MsgForAIManUnCall.builder()
                .taskGroupIdMap(taskGroupIdMap)
                .needPushThirdList(needPushThirdList)
                .needPushAntsList(needPushAntsList)
                .needRetryRecordList(needRetryRecordList)
                .needReCallRecordList(needReCallRecordList)
                .saveRedisKeysMap(saveRedisKeysMap)
                .callRecordList(callRecordList)
                .phoneRecordList(phoneRecordList)
                .aiRedisTaskList(aiRedisTaskList)
                .callRecordResultList(callRecordResultList)
                .errorCallRecordResults(errorCallRecordResults)
                .unCallDataPushThirdList(unCallDataPushThirdList)
                .build()).build();
    }

    @Override
    public void sendMsgToMq(SendMsgEntity entity) {
        SendMsgEntity.MsgForAIManUnCall msg = entity.getMsgForAIManUnCall();
        Map<Long, String> taskGroupIdMap = msg.getTaskGroupIdMap();
        List<CallRecordForHumanMachine> needPushThirdList = msg.getNeedPushThirdList();
        List<CallRecordForHumanMachine> needPushAntsList = msg.getNeedPushAntsList();
        List<CallRecordForHumanMachine> needRetryRecordList = msg.getNeedRetryRecordList();
        List<CallRecordForHumanMachine> needReCallRecordList = msg.getNeedReCallRecordList();
        List<CallRecordForHumanMachine> callRecordList = msg.getCallRecordList();
        List<PhoneRecord> phoneRecordList = msg.getPhoneRecordList();
        List<AiRedisTask> aiRedisTasks = msg.getAiRedisTaskList();
        Map<String, Map<String, Double>> saveRedisKeysMap = msg.getSaveRedisKeysMap();
        List<CallRecordResult> callRecordResultList = msg.getCallRecordResultList();
        List<CallRecordResult> errorCallRecordResults = msg.getErrorCallRecordResults();
        List<CallRecordForHumanMachine> unCallDataPushThirdList = msg.getUnCallDataPushThirdList();

        callResultUtilService.saveUnCallHistoryToRedis(callRecordResultList);


        if (CollectionUtils.isNotEmpty(needPushThirdList)) {
            CallResultHandlerForAIManWrapper callResultHandlerWrapper = new CallResultHandlerForAIManWrapper();
            callResultHandlerWrapper.setTaskGroupIdMap(taskGroupIdMap);
            callResultHandlerWrapper.setPushThirdList(needPushThirdList);
            callResultPushThirdForAIManProducer.send(callResultHandlerWrapper);
        }
        if (CollectionUtils.isNotEmpty(needPushAntsList)) {
            CallResultHandlerForAIManWrapper callResultHandlerWrapper = new CallResultHandlerForAIManWrapper();
            callResultHandlerWrapper.setTaskGroupIdMap(taskGroupIdMap);
            callResultHandlerWrapper.setPushAntList(needPushAntsList);
            callResultPushAntsForAIManProducer.send(callResultHandlerWrapper);
        }
        if (CollectionUtils.isNotEmpty(unCallDataPushThirdList)) {
            unCallDataPushThirdForAIManProducer.send(unCallDataPushThirdList);
        }

        CallResultHandlerForAIManWrapper wrapperForUnCall = new CallResultHandlerForAIManWrapper();
        CallResultHandlerForAIManWrapper wrapperForReCall = new CallResultHandlerForAIManWrapper();

        if(CollectionUtils.isNotEmpty(needRetryRecordList)){
            callRecordList.addAll(needRetryRecordList);
        }
        wrapperForUnCall.setCallRecordList(callRecordList);
        if(CollectionUtils.isEmpty(needReCallRecordList)){
            wrapperForUnCall.setPhoneRecordList(phoneRecordList);
            wrapperForUnCall.setAiRedisTaskList(aiRedisTasks);
        } else {
            wrapperForReCall.setCallRecordList(needReCallRecordList);
            wrapperForReCall.setPhoneRecordList(phoneRecordList);
            wrapperForReCall.setSaveRedisKeysMap(saveRedisKeysMap);
            wrapperForReCall.setAiRedisTaskList(aiRedisTasks);
            callResultSaveForReCallAIManProducer.send(wrapperForReCall);
        }

        callResultSaveForUnCallAIManProducer.send(wrapperForUnCall);

        if(CollectionUtils.isNotEmpty(errorCallRecordResults)) {
            String jsonString = JSONObject.toJSONString(errorCallRecordResults);
            collectXCallResult(jsonString);
        }
    }

    private void handleUnCallBatch(CallRecordResult callRecordResult,
                                   CallRecordForHumanMachine callRecord,
                                   PhoneRecord phoneRecord,
                                   AiRedisTask aiRedisTask,
                                   Map<String, Map<String, Double>> saveRedisKeysMap,
                                   List<CallRecordForHumanMachine> needReCallRecordList,
                                   List<CallRecordForHumanMachine> needPushThirdList,
                                   List<CallRecordForHumanMachine> needPushAntsList,
                                   List<CallRecordForHumanMachine> unCallDataPushThirdList,
                                   Map<String, CallbackType> callbackTypeMap) {
        phoneRecord.setCalledNum(1 + phoneRecord.getCalledNum());
        CallRecordForHumanMachine callRecordNew = addPhoneToReCall(callRecordResult, phoneRecord, callRecord,
                aiRedisTask, saveRedisKeysMap);
        if(callRecordNew == null){
            if(phoneRecord.getIfRecalling() == 0){
                AtomicInteger callingNumDiff = aiRedisTask.getCallingNumDiff();
                callingNumDiff.addAndGet(-1);
            }else{
                AtomicInteger recallingNumDiff = aiRedisTask.getRecallingNumDiff();
                recallingNumDiff.addAndGet(-1);
            }
        }else{
            if(callRecordNew.getIfRecall() == 1){
                AtomicInteger recallingNumDiff = aiRedisTask.getRecallingNumDiff();
                recallingNumDiff.addAndGet(1);
                AtomicInteger callingNumDiff = aiRedisTask.getCallingNumDiff();
                callingNumDiff.addAndGet(-1);
            }
            needReCallRecordList.add(callRecordNew);
        }
        updateAiOutboundTaskByCdr(aiRedisTask, phoneRecord.getCalledNum()==1?1:0,"呼叫完成".equals(phoneRecord.getCallStatus())?1:0);
        updateCallRecordByCdr(callRecordResult, callRecord, aiRedisTask);
        callRecord.setCalledNum(phoneRecord.getCalledNum());
        Integer ifTest = callRecord.getIfTest();

        if (ifTest == null || ifTest != 1) {
            CallbackType callbackType = callbackTypeMap.get(aiRedisTask.getGroupId());
            switch (callbackType) {
                case PUSH_THIRD:
                    unCallDataPushThirdList.add(callRecord);
                    break;
                case PUSH_ANTS:
//                    log.info("人机协同没有蚂蚁任务");
                    needPushAntsList.add(callRecord);
                    break;
                case OLD_PUSH:
                    needPushThirdList.add(callRecord);
                    break;
                default:
                    log.info("混入了未知回传类型");
                    break;
            }
        }
    }

    private CallRecordForHumanMachine addPhoneToReCall(CallRecordResult callRecordResult,
                                                       PhoneRecord phoneRecord,
                                                       CallRecordForHumanMachine callRecord,
                                                       AiRedisTask aiRedisTask,
                                                       Map<String, Map<String, Double>> saveRedisKeysMap) {
        String taskId = callRecord.getTaskId();
        String recallTaskKey = taskId + "_recall";
        Integer autoReCall = aiRedisTask.getAutoReCall();
        if (autoReCall != null && autoReCall.equals(1)) {
            String recordId = createNewRecordId(callRecordResult.getSpeechCallId());
            CallRecordForHumanMachine newCallRecord = createNewCallRecord(callRecord.getPhone(), phoneRecord);
            newCallRecord.setMerchantLineId(callRecord.getMerchantLineId());
            newCallRecord.setMerchantLineCode(callRecord.getMerchantLineCode());
            newCallRecord.setTenantCode(callRecord.getTenantCode());
            newCallRecord.setTenantName(callRecord.getTenantName());
            newCallRecord.setAccount(callRecord.getAccount());
            newCallRecord.setIfTest(callRecord.getIfTest());
            newCallRecord.setTaskName(callRecord.getTaskName());
            newCallRecord.setRecordId(recordId);

            LocalDateTime nowTime = LocalDateTime.now();
            LocalDateTime targetTime;
            if(phoneRecord.getIfRecalling() == 0 && callRecord.getIfRecall() == 0 ){
                targetTime = nowTime.plusMinutes(aiRedisTask.getFirstRecallTime());
                newCallRecord.setIfRecall(1);
            }else if(phoneRecord.getIfRecalling() == 1 && callRecord.getIfRecall() == 1 && aiRedisTask.getSecondRecallTime() != null){
                targetTime  = nowTime.plusMinutes(aiRedisTask.getSecondRecallTime());
                newCallRecord.setIfRecall(2);
            } else {
                targetTime = null;
            }
            phoneRecord.setIfRecalling(1);
            if (targetTime != null) {
                if(!saveRedisKeysMap.containsKey(recallTaskKey)){
                    saveRedisKeysMap.put(recallTaskKey, new HashMap<>());
                }
                Map<String, Double> redisValueMaps = saveRedisKeysMap.get(recallTaskKey);
                String key = callRecord.getProvince()+","
                        +callRecord.getProvinceCode()+","
                        +callRecord.getCity()+","
                        +callRecord.getCityCode()+","
                        +callRecord.getOperator()+","
                        +targetTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                String plainPhone = callRecordResult.getPlainPhone();
                String redisValueMapKey = (plainPhone==null?callRecord.getPhone():plainPhone)+","
                        +newCallRecord.getRecordId()+","
                        +callRecord.getPhone()+","
                        +key;
                double redisValueMapValue = (double)(targetTime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli());
                redisValueMaps.put(redisValueMapKey, redisValueMapValue);
                newCallRecord.setRedisKey(key);
                phoneRecord.setLatestRecordId(newCallRecord.getRecordId());
                phoneRecord.setAddTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                return newCallRecord;
            } else {
                phoneRecord.setCallStatus("呼叫完成");
                return null;
            }
        } else {
            phoneRecord.setCallStatus("呼叫完成");
            return null;
        }
    }

    private void updateAiOutboundTaskByCdr(AiRedisTask aiRedisTask, Integer calledNum, Integer finishedNum){
        AtomicInteger finishedNumDiff = aiRedisTask.getFinishedNumDiff();
        AtomicInteger calledNumDiff = aiRedisTask.getCalledNumDiff();
        finishedNumDiff.addAndGet(finishedNum);
        calledNumDiff.addAndGet(calledNum);
        aiRedisTask.getCallRecordNumDiff().addAndGet(1);
    }

    private void updateCallRecordByCdr(CallRecordResult callRecordResult, CallRecordForHumanMachine callRecord, AiRedisTask aiRedisTask){
        callRecord.setCallDuration(callRecordResult.getCallDuration());
        callRecord.setCallDurationSec(callRecordResult.getCallDurationSec());
        callRecord.setCallStatus(callRecordResult.getCallStatus().toString());
        callRecord.setCallStatusStr(CallStatusEnum.codeOf(callRecordResult.getCallStatus()).getField());
        callRecord.setErrorCode(callRecordResult.getErrorCode());
        callRecord.setCause(callRecordResult.getCause());
        callRecord.setCallId(callRecordResult.getCallId());
        callRecord.setLineId(callRecordResult.getLineId());
        callRecord.setLineCode(callRecordResult.getLineCode());
        callRecord.setMerchantLineCode(callRecordResult.getMerchantLineCode());
        callRecord.setMerchantLineId(callRecordResult.getMerchantLineId());
        callRecord.setFsIp(callRecordResult.getFsIp());
        callRecord.setAiCallIp(callRecordResult.getAiCallIp());
        callRecord.setCallOutTime(callRecordResult.getCallOutTime());
        callRecord.setWhoHangup((callRecordResult.getCallStatus() != null && callRecordResult.getCallStatus() == 7 && callRecordResult.getCallDurationSec() <= 1) ? 1 : callRecordResult.getWhoHangup());
        callRecord.setScriptStringId(callRecordResult.getScriptId());
        callRecord.setSpeechCraftId(callRecordResult.getScriptLongId());
        callRecord.setWaitmsec(callRecordResult.getWaitmsec());
        callRecord.setPlainPhone(callRecordResult.getPlainPhone());
        callRecord.setSipCallId(callRecordResult.getSipCallId());
        callRecord.setGroupId(aiRedisTask.getGroupId());
        callRecord.setTemplateId(aiRedisTask.getTemplateId());
        callRecord.setSupplyLineBelong(redisSecondCacheService.getSupplyLineBelongMapLocal().get(callRecord.getLineId()));
    }

    private CallRecordForHumanMachine createNewRecordInCdr(CallRecordResult callRecordResult, CallRecordForHumanMachine callRecord){
        CallRecordForHumanMachine callRecordNew = new CallRecordForHumanMachine();
        BeanUtils.copyProperties(callRecord, callRecordNew);
        callRecordNew.setId(null);
        callRecordNew.setTaskName(callRecordResult.getTaskName());
        callRecordNew.setTaskId(callRecordResult.getTaskId());
        callRecordNew.setRecordId(createNewRecordId(callRecordResult.getSpeechCallId()));
        callRecordNew.setCallDuration(callRecordResult.getCallDuration());
        callRecordNew.setCallDurationSec(callRecordResult.getCallDurationSec());
        callRecordNew.setCallStatus(callRecordResult.getCallStatus().toString());
        callRecordNew.setCallStatusStr(CallStatusEnum.codeOf(callRecordResult.getCallStatus()).getField());
        callRecordNew.setErrorCode(callRecordResult.getErrorCode());
        callRecordNew.setCause(callRecordResult.getCause());
        callRecordNew.setCallId(callRecordResult.getCallId());
        callRecordNew.setLineId(callRecordResult.getLineId());
        callRecordNew.setLineCode(callRecordResult.getLineCode());
        callRecordNew.setMerchantLineCode(callRecordResult.getMerchantLineCode());
        callRecordNew.setMerchantLineId(callRecordResult.getMerchantLineId());
        callRecordNew.setFsIp(callRecordResult.getFsIp());
        callRecordNew.setAiCallIp(callRecordResult.getAiCallIp());
        callRecordNew.setCallOutTime(callRecordResult.getCallOutTime());
        callRecordNew.setWhoHangup((callRecordResult.getCallStatus()!=null&&callRecordResult.getCallStatus()==7&&callRecordResult.getCallDurationSec()<=1)?1:callRecordResult.getWhoHangup());
        callRecordNew.setScriptStringId(callRecordResult.getScriptId());
        callRecordNew.setSpeechCraftId(callRecordResult.getScriptLongId());
        callRecordNew.setWaitmsec(callRecordResult.getWaitmsec());
        callRecordNew.setTaskName(callRecordResult.getTaskName());
        callRecordNew.setTaskId(callRecordResult.getTaskId());
        callRecordNew.setIntentionClass("其他");
        callRecordNew.setIfFastRecall(1);
        callRecordNew.setSipCallId(callRecordResult.getSipCallId());
        return callRecordNew;
    }

    private CallRecordForHumanMachine createNewCallRecord(String phone, PhoneRecord phoneRecord){
        CallRecordForHumanMachine callRecord = new CallRecordForHumanMachine();
        callRecord.setPhone(phone);
        callRecord.setCallStatusStr("待呼叫");
        callRecord.setIfRecall(0);
        callRecord.setSayCount(0);
        callRecord.setIfSendSms("否");
        callRecord.setCycleCount(0);
        callRecord.setCallDuration(0);
        callRecord.setCallDurationSec(0);
        callRecord.setIntentionClass("其他");
        callRecord.setCallOutTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        if(phoneRecord != null){
            callRecord.setTaskId(phoneRecord.getTaskId());
            callRecord.setPhoneRecordId(phoneRecord.getRecordId());
            callRecord.setCity(phoneRecord.getCity());
            callRecord.setCityCode(phoneRecord.getCityCode());
            callRecord.setProvince(phoneRecord.getProvince());
            callRecord.setProvinceCode(phoneRecord.getProvinceCode());
            callRecord.setOperator(phoneRecord.getOperator());
            callRecord.setSpeechCraftId(phoneRecord.getScriptLongId());
            callRecord.setScriptStringId(phoneRecord.getScriptStringId());
        }
        return callRecord;
    }

    private String createNewRecordId(String oldRecordId) {
        return oldRecordId.split("_")[0] + "_" +UUID.randomUUID();
    }
}
