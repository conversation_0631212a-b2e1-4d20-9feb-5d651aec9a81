package com.raipeng.aidatapush.service.callresulthandler.impl;


import com.alibaba.fastjson.JSONObject;
import com.raipeng.aidatacommon.enums.CallStatusEnum;
import com.raipeng.aidatacommon.model.*;
import com.raipeng.aidatacommon.model.dto.CallRecordResult;
import com.raipeng.aidatapush.config.HotConfig;
import com.raipeng.aidatapush.controller.wrapper.CallResultHandlerForHumanWrapper;
import com.raipeng.aidatapush.entity.SendMsgEntity;
import com.raipeng.aidatapush.enums.CallResultHandleType;
import com.raipeng.aidatapush.enums.DingDingMsgType;
import com.raipeng.aidatapush.repository.CallRecordForManualDirectRepository;
import com.raipeng.aidatapush.repository.TenantProgramAdminRepository;
import com.raipeng.aidatapush.service.AIOutboundTaskService;
import com.raipeng.aidatapush.service.CallResultUtilService;
import com.raipeng.aidatapush.service.DingDingHighPriorityService;
import com.raipeng.aidatapush.service.RedisSecondCacheService;
import com.raipeng.aidatapush.service.callresulthandler.ResultHandler;
import com.raipeng.aidatapush.service.mq.producer.CallResultSaveForCalledHumanProducer;
import com.raipeng.aidatapush.service.mq.producer.PreXCallForHumanProducer;
import com.raipeng.aidatapush.utils.CallResultHandleUtil;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.raipeng.aidatapush.config.RabbitConstants.*;


@Slf4j
@Service
public class CallResultHandleForHumanService implements ResultHandler {
    @Autowired
    private CallResultUtilService callResultUtilService;

    @Autowired
    private AIOutboundTaskService aiOutboundTaskService;

    @Autowired
    private CallResultSaveForCalledHumanProducer callResultSaveForCalledHumanProducer;

    @Autowired
    private CallRecordForManualDirectRepository callRecordForManualDirectRepository;

    @Autowired
    private TenantProgramAdminRepository tenantProgramAdminRepository;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private HotConfig hotConfig;

    @Autowired
    private DingDingHighPriorityService dingDingHighPriorityService;

    @Autowired
    private PreXCallForHumanProducer preXCallForHumanProducer;

    @Autowired
    private RedisSecondCacheService redisSecondCacheService;

    @Override
    public void disPatchExceptCallResultSingle() {
        CallResultHandleUtil.handleXSingle(
                X_CALL_PHONE_RESULT_NOTICE_SPEECH_DIRECT_CALL_QUEUE,
                rabbitTemplate,
                this,
                CallResultHandleType.HUMAN_CALL,
                hotConfig.getTimeoutSeconds());
    }

    @Override
    public void disPatchExceptCallResult() {
        CallResultHandleUtil.handleX(
                X_CALL_PHONE_RESULT_NOTICE_SPEECH_DIRECT_CALL_QUEUE,
                rabbitTemplate,
                this,
                CallResultHandleType.HUMAN_CALL,
                hotConfig.getTimeoutSeconds());
    }

    @Override
    public void disPatchExceptPreXCallResult(String callResultsString) {
        CallResultHandleUtil.handlePreXSingle(
                callResultsString,
                this,
                CallResultHandleType.HUMAN_CALL,
                hotConfig.getTimeoutSeconds());
    }

    @Override
    public void collectXCallResult(String callResultsString) {
        CallResultHandleUtil.pushCallResultBack(rabbitTemplate,
                callResultsString,
                X_CALL_PHONE_RESULT_NOTICE_SPEECH_DIRECT_CALL_QUEUE,
                X_CALL_PHONE_RESULT_NOTICE_SPEECH_DIRECT_CALL_EXCHANGE,
                X_CALL_PHONE_RESULT_NOTICE_SPEECH_DIRECT_CALL_ROUTING,
                CallResultHandleType.HUMAN_CALL);
        dingDingHighPriorityService.sedDDMsg(DingDingMsgType.WARN, "人工直呼呼通记录处理错误，请及时关注");
    }

    @Override
    public void collectPreXCallResult(String callResultsString) {
        preXCallForHumanProducer.send(callResultsString);
    }

    @Override
    public SendMsgEntity dispatchCallResult(List<CallRecordResult> callRecordResultList){
        int size = callRecordResultList.size();
        long m1 = System.currentTimeMillis();

        // 1. 检测是否需要解密
        callResultUtilService.checkIfNeedDecryption(callRecordResultList);

        // 2. 根据recordIds进行数据库查询操作
        List<String> recordIds = callRecordResultList.stream()
                .map(CallRecordResult::getSpeechCallId)
                .collect(Collectors.toList());
        List<CallRecordForManualDirect> callRecordList = callRecordForManualDirectRepository.findAllByRecordIdIn(recordIds);
        Map<String, CallRecordForManualDirect> callRecordMap = callRecordList.stream()
                .collect(Collectors.toMap(CallRecordForManualDirect::getRecordId, Function.identity(), (a, b) -> a));

        long m2 = System.currentTimeMillis();

        // 3. 创建传输容器
        CallResultHandlerForHumanWrapper cdrWrapper = new CallResultHandlerForHumanWrapper();
        CallResultHandlerForHumanWrapper noCdrWrapper = new CallResultHandlerForHumanWrapper();
        List<CallRecordResult> saveHistoryList = new ArrayList<>();
        List<CallRecordResult> errorCallRecordResults = new ArrayList<>();
        cdrWrapper.setIfCdr(true);
        noCdrWrapper.setIfCdr(false);

        // 4. 单条数据操作
        for(CallRecordResult callRecordResult : callRecordResultList){
            CallRecordForManualDirect callRecord = callRecordMap.get(callRecordResult.getSpeechCallId());
            if (callRecord == null) {
                log.error("Exception:{}, 人工直呼callRecordResult:{}", "找不到通话记录", callRecordResult);
                errorCallRecordResults.add(callRecordResult);
                continue;
            }
            TenantProgramAdmin firstByGroupId = tenantProgramAdminRepository.findFirstByGroupId(callRecord.getGroupId());
            if(firstByGroupId != null){
                callRecordResult.setProductId(firstByGroupId.getProductId());
                callRecordResult.setIndustrySecondFieldId(firstByGroupId.getSecondIndustryId());
            }

            if(callRecordResult.getIsCdr() == 1){
                cdrWrapper.getCallRecordResultList().add(callRecordResult);
                saveHistoryList.add(callRecordResult);
            }
            if(callRecordResult.getIsCdr() == 0){
                handleCalledNoCdr(callRecord, callRecordResult);
                noCdrWrapper.getCallRecordResultList().add(callRecordResult);
            }
        }

        long m3 = System.currentTimeMillis();
        log.info("[人工直呼呼通消费{}条]=>call表耗时:{}, 数据处理耗时:{}", size, m2-m1, m3-m2);

        // 5. 返回容器数据
        return SendMsgEntity.builder().msgForHumanCall(
                SendMsgEntity.MsgForHumanCall.builder()
                .saveHistoryList(saveHistoryList)
                .cdrWrapper(cdrWrapper)
                .noCdrWrapper(noCdrWrapper)
                .errorCallRecordResults(errorCallRecordResults)
                .build()).build();
    }

    @Override
    public void sendMsgToMq(SendMsgEntity entity) {
        SendMsgEntity.MsgForHumanCall msg = entity.getMsgForHumanCall();
        List<CallRecordResult> saveHistoryList = msg.getSaveHistoryList();
        CallResultHandlerForHumanWrapper cdrWrapper = msg.getCdrWrapper();
        CallResultHandlerForHumanWrapper noCdrWrapper = msg.getNoCdrWrapper();
        List<CallRecordResult> errorCallRecordResults = msg.getErrorCallRecordResults();

        callResultUtilService.saveCalledHistoryToRedis(saveHistoryList);

        callResultSaveForCalledHumanProducer.send(cdrWrapper);
        callResultSaveForCalledHumanProducer.send(noCdrWrapper);
        if(CollectionUtils.isNotEmpty(errorCallRecordResults)) {
            String jsonString = JSONObject.toJSONString(errorCallRecordResults);
            collectXCallResult(jsonString);
        }
    }

    public void handleCalledNoCdr(CallRecordForManualDirect callRecord, CallRecordResult callRecordResult){
        callRecordResult.setWholeAudioFileUrl(aiOutboundTaskService.replaceUrl(callRecordResult.getWholeAudioFileUrl()));
        callRecord.setCallDurationSec(callRecordResult.getCallDurationSec());
        callRecord.setWholeAudioFileUrl(callRecordResult.getWholeAudioFileUrl());
        callRecord.setCallDuration(callRecordResult.getCallDuration());
        callRecord.setCallStatus(callRecordResult.getCallStatus().toString());
        callRecord.setWhoHangup(callRecordResult.getWhoHangup());
        callRecord.setUserFullAnswerContent(callRecordResult.getUserFullAnswerContent());
        callRecord.setErrorCode(callRecordResult.getErrorCode());
        callRecord.setCause(callRecordResult.getCause());
        callRecord.setCallOutTime(callRecordResult.getCallOutTime());
        callRecord.setContactTime(callRecordResult.getContactTime());
        callRecord.setTalkTimeEnd(callRecordResult.getTalkTimeEnd());
        callRecord.setTalkTimeStart(callRecordResult.getTalkTimeStart());
        callRecord.setCallId(callRecordResult.getCallId());
        callRecord.setPlainPhone(callRecordResult.getPlainPhone());
        callRecord.setCallStatusStr(callRecordResult.getCallStatus() == null ? "未知" : CallStatusEnum.codeOf(callRecordResult.getCallStatus()).getField());
        callRecord.setSupplyLineBelong(redisSecondCacheService.getSupplyLineBelongMapLocal().get(callRecordResult.getLineId()));
    }
}
