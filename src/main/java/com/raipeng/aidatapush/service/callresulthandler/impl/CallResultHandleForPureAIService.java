package com.raipeng.aidatapush.service.callresulthandler.impl;


import com.alibaba.fastjson.JSONObject;
import com.raipeng.aidatacommon.enums.CallStatusEnum;
import com.raipeng.aidatacommon.model.PhoneRecord;
import com.raipeng.aidatacommon.model.dto.CallRecordResult;
import com.raipeng.aidatacommon.model.record.CallRecord;
import com.raipeng.aidatacommon.thirdrequests.enums.CallbackType;
import com.raipeng.aidatapush.config.HotConfig;
import com.raipeng.aidatapush.controller.wrapper.CallResultHandlerForPureAIWrapper;
import com.raipeng.aidatapush.entity.SendMsgEntity;
import com.raipeng.aidatapush.enums.CallResultHandleType;
import com.raipeng.aidatapush.enums.DingDingMsgType;
import com.raipeng.aidatapush.repository.PhoneRecordRepository;
import com.raipeng.aidatapush.service.*;
import com.raipeng.aidatapush.service.callresulthandler.ResultHandler;
import com.raipeng.aidatapush.service.mq.producer.*;
import com.raipeng.aidatapush.service.mq.producer.newCallback.CallDataPushThirdForPureAIProducer;
import com.raipeng.aidatapush.service.mq.producer.newCallback.CallSmsPushThirdForPureAIProducer;
import com.raipeng.aidatapush.utils.CallDataHandleUtils;
import com.raipeng.aidatapush.utils.CallResultHandleUtil;
import com.raipeng.common.constant.CommonConstants;
import com.raipeng.aidatacommon.entity.AiRedisTask;
import com.raipeng.common.entity.smsentity.SmsDataPushWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.raipeng.aidatapush.config.RabbitConstants.*;

@Slf4j
@Service
public class CallResultHandleForPureAIService implements ResultHandler {

    @Autowired
    private CallRecordMultiService callRecordMultiService;

    @Autowired
    private PhoneRecordRepository phoneRecordRepository;

    @Autowired
    private AIOutboundTaskService aiOutboundTaskService;

    @Autowired
    private TaskRedisService taskRedisService;

    @Autowired
    private CallResultSaveForCalledPureAIProducer callResultSaveForCalledPureAIProducer;

    @Autowired
    private CallResultPushThirdForAIPureProducer callResultPushThirdForAIPureProducer;

    @Autowired
    private CallResultPushThirdForAIPureDelayProducer callResultPushThirdForAIPureDelayProducer;

    @Autowired
    private CallResultPushAntsForAIPureProducer callResultPushAntsForAIPureProducer;

    @Autowired
    private CallResultPushAntsForAIPureDelayProducer callResultPushAntsForAIPureDelayProducer;

    @Autowired
    private CallResultUtilService callResultUtilService;

    @Autowired
    private CallSettingService callSettingService;

    @Autowired
    private SmsService smsService;

    @Autowired
    private AdminService adminService;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private HotConfig hotConfig;

    @Autowired
    private DingDingHighPriorityService dingDingHighPriorityService;

    @Autowired
    private PreXCallForPureAIProducer preXCallForPureAIProducer;

    @Autowired
    private CallDataPushThirdForPureAIProducer callDataPushThirdForPureAIProducer;

    @Autowired
    private CallSmsPushThirdForPureAIProducer callSmsPushThirdForPureAIProducer;

    @Autowired
    private RedisSecondCacheService redisSecondCacheService;

    @Override
    public void disPatchExceptCallResultSingle() {
        CallResultHandleUtil.handleXSingle(X_CALL_PHONE_RESULT_NOTICE_SPEECH_QUEUE,
                rabbitTemplate,
                this,
                CallResultHandleType.PURE_AI_CALL,
                hotConfig.getTimeoutSeconds());
    }

    @Override
    public void disPatchExceptCallResult() {
        CallResultHandleUtil.handleX(X_CALL_PHONE_RESULT_NOTICE_SPEECH_QUEUE,
                rabbitTemplate,
                this,
                CallResultHandleType.PURE_AI_CALL,
                hotConfig.getTimeoutSeconds());
    }

    @Override
    public void disPatchExceptPreXCallResult(String callResultsString) {
        CallResultHandleUtil.handlePreXSingle(
                callResultsString,
                this,
                CallResultHandleType.PURE_AI_CALL,
                hotConfig.getTimeoutSeconds());
    }

    @Override
    public void collectXCallResult(String callResultsString) {
        CallResultHandleUtil.pushCallResultBack(rabbitTemplate,
                callResultsString,
                X_CALL_PHONE_RESULT_NOTICE_SPEECH_QUEUE,
                X_CALL_PHONE_RESULT_NOTICE_SPEECH_EXCHANGE,
                X_CALL_PHONE_RESULT_NOTICE_SPEECH_ROUTING,
                CallResultHandleType.PURE_AI_CALL);
        dingDingHighPriorityService.sedDDMsg(DingDingMsgType.WARN, "纯AI呼通记录处理错误，请及时关注");
    }

    @Override
    public void collectPreXCallResult(String callResultsString) {
        preXCallForPureAIProducer.send(callResultsString);
    }

    @Override
    public SendMsgEntity dispatchCallResult(List<CallRecordResult> callRecordResultList){
        int size = callRecordResultList.size();
        long m1 = System.currentTimeMillis();
        // 1. 检测是否需要解密
        callResultUtilService.checkIfNeedDecryption(callRecordResultList);

        // 2. 根据recordIds进行数据库查询操作
        List<String> recordIds = callRecordResultList.stream()
                .map(CallRecordResult::getSpeechCallId)
                .collect(Collectors.toList());
        List<CallRecord> callRecordList = callRecordMultiService.findAllByRecordIdIn(recordIds);
        Map<String, CallRecord> callRecordMap = callRecordList.stream()
                .collect(Collectors.toMap(CallRecord::getRecordId, Function.identity()));

        long m2 = System.currentTimeMillis();
        List<String> phoneRecordIds = callRecordList.stream()
                .map(CallRecord::getPhoneRecordId).collect(Collectors.toList());
        List<PhoneRecord> phoneRecordList = phoneRecordRepository.findAllByRecordIdIn(phoneRecordIds);
        Map<String, PhoneRecord> phoneRecordMap = phoneRecordList.stream()
                .collect(Collectors.toMap(PhoneRecord::getRecordId, Function.identity()));

        long m3 = System.currentTimeMillis();
        Set<Long> taskIds = phoneRecordList.stream()
                .map(task -> Long.valueOf(task.getTaskId()))
                .collect(Collectors.toSet());

        List<AiRedisTask> aiRedisTaskList = taskRedisService.getAiRedisTaskList(taskIds);
        Map<Long, AiRedisTask> aiRedisTaskMap = aiRedisTaskList.stream()
                .collect(Collectors.toMap(AiRedisTask::getId, Function.identity()));
        Map<Long, String> taskGroupIdMap = aiRedisTaskList.stream()
                .collect(Collectors.toMap(AiRedisTask::getId, AiRedisTask::getGroupId, (a, b) -> a));
        List<String> groupIdList = aiRedisTaskList.stream().map(AiRedisTask::getGroupId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());

        Map<String, List<String>> groupIdIntentionClassMap = callSettingService.getGroupIdIntentionClassMap(groupIdList);

        long m4 = System.currentTimeMillis();
        Map<String, CallbackType> callbackTypeMap = adminService.getCallbackTypeMap();
        long m5 = System.currentTimeMillis();
        aiRedisTaskList.forEach(task -> callResultUtilService.initCalledTaskStatistic(task));

        // 3. 创建传输容器
        CallResultHandlerForPureAIWrapper cdrWrapper = new CallResultHandlerForPureAIWrapper();
        CallResultHandlerForPureAIWrapper noCdrWrapper = new CallResultHandlerForPureAIWrapper();

        cdrWrapper.setIfCdr(true);
        cdrWrapper.setAiRedisTasks(aiRedisTaskList);
        noCdrWrapper.setIfCdr(false);

        List<CallRecordResult> saveHistoryList = new ArrayList<>();
        List<CallRecordResult> errorCallRecordResults = new ArrayList<>();
        List<SmsDataPushWrapper> smsData = new ArrayList<>();


        List<CallRecord> needPushThirdList = new ArrayList<>();
        List<CallRecord> needPushThirdListDelay = new ArrayList<>();
        List<CallRecord> needPushAntsList = new ArrayList<>();
        List<CallRecord> needPushAntsListDelay = new ArrayList<>();
        List<CallRecord> callExtraForPushThird = new ArrayList<>();
        List<CallRecord> callSmsForPushThird = new ArrayList<>();


        //cdr和noCdr呼叫次数计数，判断是否重复计算
        Map<String, Boolean> callNumReMap = new HashMap<>();

        // 4. 单条数据操作
        for(CallRecordResult callRecordResult : callRecordResultList){
            CallRecord callRecord = callRecordMap.get(callRecordResult.getSpeechCallId());
            if (callRecord == null) {
                log.error("Exception:{}, 纯AI callRecordResult:{}", "找不到通话记录", callRecordResult);
                errorCallRecordResults.add(callRecordResult);
                continue;
            }
            PhoneRecord phoneRecord = phoneRecordMap.get(callRecord.getPhoneRecordId());
            if (phoneRecord == null) {
                log.error("Exception:{}, 纯AI callRecordResult:{}", "找不到名单记录", callRecordResult);
                errorCallRecordResults.add(callRecordResult);
                continue;
            }
            AiRedisTask aiRedisTask = aiRedisTaskMap.get(Long.valueOf(phoneRecord.getTaskId()));
            if(aiRedisTask == null){
                log.error("Exception:{}, 纯AI callRecordResult:{}", "找不到任务记录", callRecordResult);
                errorCallRecordResults.add(callRecordResult);
                continue;
            }
            callRecordResult.setProductId(aiRedisTask.getProductId());
            callRecordResult.setIndustrySecondFieldId(aiRedisTask.getIndustrySecondFieldId());

            if(callRecordResult.getIsCdr() == 1){
                handleCalledCdr(
                        callRecord,
                        phoneRecord,
                        aiRedisTask,
                        callRecordResult,
                        callbackTypeMap,
                        callExtraForPushThird,
                        callNumReMap);
                cdrWrapper.getCallRecordResultList().add(callRecordResult);
                cdrWrapper.getPhoneRecordList().add(phoneRecord);
                saveHistoryList.add(callRecordResult);
            }

            if(callRecordResult.getIsCdr() == 0){
                handleCalledNoCdr(
                        callRecord,
                        phoneRecord,
                        aiRedisTask,
                        callRecordResult,
                        needPushThirdList,
                        needPushThirdListDelay,
                        needPushAntsList,
                        needPushAntsListDelay,
                        callbackTypeMap,
                        smsData,
                        groupIdIntentionClassMap,
                        callSmsForPushThird,
                        callNumReMap);
                noCdrWrapper.getCallRecordResultList().add(callRecordResult);
                noCdrWrapper.getPhoneRecordList().add(phoneRecord);
            }
        }

        long m6 = System.currentTimeMillis();
        log.info("[纯AI呼通消费{}条]=>" +
                    "call表耗时:{}, " +
                    "phone表耗时:{}, " +
                    "task表耗时:{}, " +
                    "账号类型查询耗时:{}, " +
                    "数据处理耗时:{}",
                    size, m2-m1, m3-m2, m4-m3, m5-m4, m6-m5);
        // 5. 返回容器数据
        return SendMsgEntity.builder().msgForPureAICall(
                SendMsgEntity.MsgForPureAICall.builder()
                .saveHistoryList(saveHistoryList)
                .cdrWrapper(cdrWrapper)
                .noCdrWrapper(noCdrWrapper)
                .needPushThirdList(needPushThirdList)
                .needPushThirdListDelay(needPushThirdListDelay)
                .needPushAntsListDelay(needPushAntsListDelay)
                .needPushAntsList(needPushAntsList)
                .taskGroupIdMap(taskGroupIdMap)
                .errorCallRecordResults(errorCallRecordResults)
                .needPushSmsList(smsData)
                .callSmsForPushThird(callSmsForPushThird)
                .callExtraForPushThird(callExtraForPushThird)
                .build()).build();
    }

    @Override
    public void sendMsgToMq(SendMsgEntity entity) {
        SendMsgEntity.MsgForPureAICall msg = entity.getMsgForPureAICall();
        List<CallRecordResult> saveHistoryList = msg.getSaveHistoryList();
        CallResultHandlerForPureAIWrapper cdrWrapper = msg.getCdrWrapper();
        CallResultHandlerForPureAIWrapper noCdrWrapper = msg.getNoCdrWrapper();
        List<CallRecord> needPushAntsList = msg.getNeedPushAntsList();
        List<CallRecord> needPushAntsListDelay = msg.getNeedPushAntsListDelay();
        List<CallRecord> needPushThirdList = msg.getNeedPushThirdList();
        List<CallRecord> needPushThirdListDelay = msg.getNeedPushThirdListDelay();
        List<SmsDataPushWrapper> needPushSmsList = msg.getNeedPushSmsList();
        Map<Long, String> taskGroupIdMap = msg.getTaskGroupIdMap();
        List<CallRecordResult> errorCallRecordResults = msg.getErrorCallRecordResults();
        List<CallRecord> callExtraForPushThird = msg.getCallExtraForPushThird();
        List<CallRecord> callSmsForPushThird = msg.getCallSmsForPushThird();
        List<CallRecord> callDataForPushThird = new ArrayList<>();

        callResultUtilService.saveCalledHistoryToRedis(saveHistoryList);

        callResultSaveForCalledPureAIProducer.send(cdrWrapper);
        callResultSaveForCalledPureAIProducer.send(noCdrWrapper);

        if(CollectionUtils.isNotEmpty(needPushThirdList)){
            CallResultHandlerForPureAIWrapper callResultHandlerForPureAIWrapper = new CallResultHandlerForPureAIWrapper();
            callResultHandlerForPureAIWrapper.setPushThirdList(needPushThirdList);
            callResultHandlerForPureAIWrapper.setTaskGroupIdMap(taskGroupIdMap);
            callResultPushThirdForAIPureProducer.send(callResultHandlerForPureAIWrapper);
        }
        if(CollectionUtils.isNotEmpty(needPushThirdListDelay)){
            CallResultHandlerForPureAIWrapper callResultHandlerForPureAIWrapper = new CallResultHandlerForPureAIWrapper();
            callResultHandlerForPureAIWrapper.setPushThirdListDelay(needPushThirdListDelay);
            callResultHandlerForPureAIWrapper.setTaskGroupIdMap(taskGroupIdMap);
            callResultPushThirdForAIPureDelayProducer.send(callResultHandlerForPureAIWrapper);
        }
        if (CollectionUtils.isNotEmpty(needPushAntsList)) {
            CallResultHandlerForPureAIWrapper callResultHandlerForPureAIWrapper = new CallResultHandlerForPureAIWrapper();
            callResultHandlerForPureAIWrapper.setPushAntsList(needPushAntsList);
            callResultHandlerForPureAIWrapper.setTaskGroupIdMap(taskGroupIdMap);
            callResultPushAntsForAIPureProducer.send(callResultHandlerForPureAIWrapper);
        }
        if (CollectionUtils.isNotEmpty(needPushAntsListDelay)) {
            CallResultHandlerForPureAIWrapper callResultHandlerForPureAIWrapper = new CallResultHandlerForPureAIWrapper();
            callResultHandlerForPureAIWrapper.setPushAntsListDelay(needPushAntsListDelay);
            callResultHandlerForPureAIWrapper.setTaskGroupIdMap(taskGroupIdMap);
            callResultPushAntsForAIPureDelayProducer.send(callResultHandlerForPureAIWrapper);

        }
        if(CollectionUtils.isNotEmpty(errorCallRecordResults)) {
            String jsonString = JSONObject.toJSONString(errorCallRecordResults);
            collectXCallResult(jsonString);
        }

        if (CollectionUtils.isNotEmpty(needPushSmsList)) {
            smsService.sendHangUpSms(needPushSmsList);
        }

        if (CollectionUtils.isNotEmpty(callSmsForPushThird)) {
            callSmsPushThirdForPureAIProducer.send(
                    callSmsForPushThird
                            .stream()
                            .filter(record ->
                                    (record.getIntentionLabels() == null
                                            || !record.getIntentionLabels().contains(CommonConstants.SPECIAL_SEND_MESSAGE_TAG)))
                            .collect(Collectors.toList())
            );
        }

        CallDataHandleUtils.cacheDataFromSmsForPureAI(callSmsForPushThird, callExtraForPushThird, callDataForPushThird);
        if (CollectionUtils.isNotEmpty(callDataForPushThird)) {
            callDataPushThirdForPureAIProducer.send(callDataForPushThird);
        }
        log.info("纯AI缓存中还有数据条目:{}", CallDataHandleUtils.PURE_AI_DATA_CACHE.size());
    }

    public void handleCalledCdr(CallRecord callRecord,
                                PhoneRecord phoneRecord,
                                AiRedisTask aiRedisTask,
                                CallRecordResult callRecordResult,
                                Map<String, CallbackType> callbackTypeMap,
                                List<CallRecord> callExtraForPushThird,
                                Map<String, Boolean> callNumReMap){
        callNumReMap.putIfAbsent(callRecord.getRecordId(), Boolean.TRUE);
        phoneRecord.setCalledNum(phoneRecord.getCalledNum() + 1);
        phoneRecord.setCallStatus("呼叫完成");
        aiRedisTask.getFinishedNumDiff().addAndGet(1);
        int callDurationMin = 0;
        if(callRecordResult.getCallDurationSec() != null){
            if(callRecordResult.getCallDurationSec() == 0){
                callDurationMin = 1;
            }else{
                callDurationMin = BigDecimal.valueOf(callRecordResult.getCallDurationSec())
                        .divide(BigDecimal.valueOf(60), 0, RoundingMode.CEILING).intValue();
            }
        }
        aiRedisTask.getFeeMinuteDiff().addAndGet(callDurationMin);
        if(phoneRecord.getIfRecalling() == 1){
            aiRedisTask.getRecallingNumDiff().addAndGet(-1);
        }else{
            aiRedisTask.getCallingNumDiff().addAndGet(-1);
        }
        aiRedisTask.getCalledNumDiff().addAndGet(phoneRecord.getCalledNum() == 1 ? 1 : 0);
        aiRedisTask.getCallRecordNumDiff().addAndGet(1);
        Integer ifTest = callRecord.getIfTest();
        CallbackType callbackType = callbackTypeMap.get(aiRedisTask.getGroupId());
        if ((ifTest == null || ifTest != 1) && CallbackType.PUSH_THIRD.equals(callbackType)) {
            callRecord.setCallDuration(callRecordResult.getCallDuration());
            callRecord.setSipCallId(callRecordResult.getSipCallId());
            callRecord.setGroupId(aiRedisTask.getGroupId());
            callExtraForPushThird.add(callRecord);
        }
    }

    public void handleCalledNoCdr(CallRecord callRecord,
                                  PhoneRecord phoneRecord,
                                  AiRedisTask aiRedisTask,
                                  CallRecordResult callRecordResult,
                                  List<CallRecord> needPushThirdList,
                                  List<CallRecord> needPushThirdListDelay,
                                  List<CallRecord> needPushAntsList,
                                  List<CallRecord> needPushAntsListDelay,
                                  Map<String, CallbackType> callbackTypeMap,
                                  List<SmsDataPushWrapper> smsDataList,
                                  Map<String, List<String>> groupIdIntentionClassMap,
                                  List<CallRecord> callSmsForPushThird,
                                  Map<String, Boolean> callNumReMap){
        callRecordResult.setWholeAudioFileUrl(aiOutboundTaskService.replaceUrl(callRecordResult.getWholeAudioFileUrl()));
        callRecord.setCallDurationSec(callRecordResult.getCallDurationSec());
        callRecord.setWholeAudioFileUrl(callRecordResult.getWholeAudioFileUrl());
        callRecord.setCycleCount(callRecordResult.getCycleCount());
        callRecord.setSayCount(callRecordResult.getSayCount());
        callRecord.setCallStatus(callRecordResult.getCallStatus().toString());
        callRecord.setWhoHangup(callRecordResult.getWhoHangup());
        callRecord.setUserFullAnswerContent(callRecordResult.getUserFullAnswerContent());
        callRecord.setIntentionClass(callRecordResult.getIntentionClass());
        callRecord.setIntentionLabelIds(callRecordResult.getIntentionLabelIds());
        callRecord.setIntentionLabels(callRecordResult.getIntentionLabels());
        callRecord.setErrorCode(callRecordResult.getErrorCode());
        callRecord.setCause(callRecordResult.getCause());
        callRecord.setCallOutTime(callRecordResult.getCallOutTime());
        callRecord.setContactTime(callRecordResult.getContactTime());
        callRecord.setTalkTimeEnd(callRecordResult.getTalkTimeEnd());
        callRecord.setTalkTimeStart(callRecordResult.getTalkTimeStart());
        callRecord.setHitAnswerIds(callRecordResult.getHitAnswerIds());
        callRecord.setScriptStringId(callRecordResult.getScriptId());
        callRecord.setSpeechCraftId(callRecordResult.getScriptLongId());
        callRecord.setCallId(callRecordResult.getCallId());
        callRecord.setCallStatusStr(callRecordResult.getCallStatus() == null ? "未知" : CallStatusEnum.codeOf(callRecordResult.getCallStatus()).getField());
        callRecord.setHitSemanticIds(callRecordResult.getHitSemanticIds());
        callRecord.setMerchantLineCode(callRecordResult.getMerchantLineCode());
        callRecord.setCorpusIds(callRecordResult.getCorpusIds());
        callRecord.setLineId(callRecordResult.getLineId());
        callRecord.setExtraInfo(callRecordResult.getExtraInfo());
        callRecord.setGroupId(aiRedisTask.getGroupId());
        callRecord.setPlainPhone(callRecordResult.getPlainPhone());
        callRecord.setDialogContentsCache(callRecordResult.getDialogContentsCache());
        callRecord.setTemplateId(aiRedisTask.getTemplateId());
        callRecord.setSupplyLineBelong(redisSecondCacheService.getSupplyLineBelongMapLocal().get(callRecord.getLineId()));
        if (phoneRecord.getCalledNum() == 0) {
            callRecord.setCalledNum(1);
        } else {
            if (LocalDateTime.now().plusSeconds(30).isAfter(phoneRecord.getUpdateTime()) && !callNumReMap.containsKey(callRecord.getRecordId())) {
                callRecord.setCalledNum(phoneRecord.getCalledNum() + 1);
            } else {
                callRecord.setCalledNum(phoneRecord.getCalledNum());
            }
        }
        callResultUtilService.tagCallRecord(callRecord);

        if(StringUtils.isNotBlank(callRecord.getCallStatus()) && callRecord.getCallStatus().contains("7")){
            Integer ifTest = callRecord.getIfTest();
            if (ifTest == null || ifTest != 1) {
                CallbackType callbackType = callbackTypeMap.get(aiRedisTask.getGroupId());
                String intentionLabels = callRecordResult.getIntentionLabels();
                switch (callbackType) {
                    case PUSH_THIRD:
                        callSmsForPushThird.add(callRecord);
                        break;
                    case PUSH_ANTS:
                        if (StringUtils.isNotEmpty(intentionLabels) && intentionLabels.contains(CommonConstants.SPECIAL_SEND_MESSAGE_TAG)) {
                            needPushAntsListDelay.add(callRecord);
                        } else {
                            needPushAntsList.add(callRecord);
                        }
                        break;
                    case OLD_PUSH:
                        if (StringUtils.isNotEmpty(intentionLabels) && intentionLabels.contains(CommonConstants.SPECIAL_SEND_MESSAGE_TAG)) {
                            needPushThirdListDelay.add(callRecord);
                        } else {
                            needPushThirdList.add(callRecord);
                        }
                        break;
                    default:
                        log.info("混入了未知回传类型");
                        break;
                }
            }
            // 根据任务配置和标签意向，判断是否需要推送短信
            smsService.sendMessage(aiRedisTask, callRecordResult,
                    callRecord.getRecordId(),
                    callRecord.getIntentionLabels(),
                    callRecord.getIntentionClass(),
                    callRecord.getAccount(),
                    callRecord.getCity(),
                    callRecord.getOperator(),
                    callRecord.getCityCode(),
                    callRecord.getProvince(),
                    callRecord.getProvinceCode(),
                    phoneRecord,smsDataList);
            List<String> intentionClassList = callSettingService.getDefaultIntentionClassList();
            if (StringUtils.isNotBlank(aiRedisTask.getGroupId())) {
                List<String> groupIdIntentionClass = groupIdIntentionClassMap.get(aiRedisTask.getGroupId());
                if (CollectionUtils.isNotEmpty(groupIdIntentionClass)) {
                    intentionClassList = groupIdIntentionClass;
                }
            }
            if(StringUtils.isBlank(phoneRecord.getLatestIntentionClass()) || !intentionClassList.contains(phoneRecord.getLatestIntentionClass())){
                if(intentionClassList.contains(callRecord.getIntentionClass())){
                    aiRedisTask.getPhoneIntentionNumDiff().addAndGet(1);
                }
            }else{
                if(!intentionClassList.contains(callRecord.getIntentionClass())){
                    aiRedisTask.getPhoneIntentionNumDiff().addAndGet(-1);
                }
            }
            if(StringUtils.isBlank(phoneRecord.getLatestIntentionClass()) || !"A".equals(phoneRecord.getLatestIntentionClass())){
                if("A".contains(callRecord.getIntentionClass())){
                    aiRedisTask.getAIntentionNumDiff().addAndGet(1);
                }
            }else{
                if(!"A".contains(callRecord.getIntentionClass())){
                    aiRedisTask.getAIntentionNumDiff().addAndGet(-1);
                }
            }

            aiRedisTask.getPutThroughNumDiff().addAndGet(phoneRecord.getPutThroughNum() == 0 ? 1 : 0);
            phoneRecord.setPutThroughNum(phoneRecord.getPutThroughNum() + 1);
        }
        callRecordResult.setIntentionLabelIds(callRecord.getIntentionLabelIds());
        callRecordResult.setIntentionClass(callRecord.getIntentionClass());
        callRecordResult.setIntentionLabels(callRecord.getIntentionLabels());
        callRecordResult.setHitAdvanceIds(callRecord.getHitAdvanceIds());
        callRecordResult.setIfSendSms(callRecord.getIfSendSms());
        phoneRecord.setLatestIntentionClass(callRecordResult.getIntentionClass());
    }


}
