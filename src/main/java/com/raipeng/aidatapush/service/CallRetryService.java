package com.raipeng.aidatapush.service;

import com.alibaba.fastjson.JSONObject;
import com.github.rholder.retry.*;
import com.raipeng.aidatacommon.enums.retry.WaitStrategyType;
import com.raipeng.aidatacommon.exception.CallFailedException;
import com.raipeng.aidatacommon.model.CallRetry;
import com.raipeng.aidatacommon.model.dto.RetryParameterDTO;
import com.raipeng.aidatacommon.utils.AESUtils;
import com.raipeng.aidatapush.repository.CallRetryRepository;
import com.raipeng.aidatapush.utils.LogUtils;
import com.raipeng.aidatapush.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@RefreshScope
@Slf4j
public class CallRetryService {
    @Autowired
    private CallRetryRepository repository;
    @Autowired
    private ApplicationContext applicationContext;


    public Long saveData(String retryNumber, String name, String methodName, String infoKey, Class<?>[] parametersClass, Object[] args, int i, String message) {

        List<RetryParameterDTO> parameterDTOList = new ArrayList<>();
        assembleParameterList(parameterDTOList, parametersClass, args);
        CallRetry retryModel = new CallRetry();
        retryModel.setRetryNumber(retryNumber);
        retryModel.setRootClassName(name);
        retryModel.setMethodName(methodName);
        retryModel.setInfoKey(infoKey);
        retryModel.setArgsEncryption(AESUtils.encryptByAES(JSONObject.toJSONString(parameterDTOList)));
        retryModel.setArgsDesensitization(LogUtils.replacePhone(JSONObject.toJSONString(parameterDTOList)));
        retryModel.setRetryResult(0);
        retryModel.setCurrentRetryCount(0);
        retryModel.setMaxRetryCount(Math.min(i, 99));
        retryModel.setMemo(message);
        repository.save(retryModel);
        return retryModel.getId();
    }

    private void assembleParameterList(List<RetryParameterDTO> parameterDTOList, Class<?>[] parametersClass, Object[] args) {
        int count = args.length;
        for (int i = 0; i < count; i++) {
            RetryParameterDTO dto = new RetryParameterDTO();
            dto.setParameterOrder(i);
            dto.setParameterClassName(parametersClass[i].getName());
            dto.setParameterValue(args[i]);
            parameterDTOList.add(dto);
        }
    }


    public Retryer<Object> buildRetryer(com.raipeng.aidatapush.annotation.CallRetry retryAnno, String retryNumber, CallFailedException ex, String methodName) {
        return RetryerBuilder.<Object>newBuilder()
                //根据异常重试
                //        .retryIfException()
                .retryIfExceptionOfType(CallFailedException.class)
                .withBlockStrategy(BlockStrategies.threadSleepStrategy())
                //根据结果重试
                //     .retryIfResult(result -> !Objects.equals(result, 0))
                //重试策略
                .withWaitStrategy(findStrategies(retryAnno))
                //停止策略
                .withStopStrategy(StopStrategies.stopAfterAttempt(retryAnno.retryTimes()))
                //监听重试进度
                .withRetryListener(new RetryListener() {
                    @Override
                    public <V> void onRetry(Attempt<V> attempt) {
                        String info = "方法重试\n"
                                + "编号 : " + retryNumber + "\n"
                                + "方法名称 : " + methodName + "\n"
                                + "infoKey : " + ex.getInfoKey() + "\n"
                                + "重试原因 : " + ex.getExtraMessage() + "\n"
                                + "距初次尝试的间隔时间为:" + attempt.getDelaySinceFirstAttempt() + " ms\n"
                                + "重试次数: " + (attempt.getAttemptNumber()) + " / " + retryAnno.retryTimes() + "\n"
                                + "重试过程是否有异常:" + attempt.hasException() + "\n"
                                + (attempt.hasException() ? "异常的原因:" + attempt.getExceptionCause().toString() + "\n" : "")
                                + "重试结果为:" + (attempt.hasResult() ? "重试已成功" : "重试未成功");
                        log.info(info);
                        DingDingService.sendDingDingMsgRetryException(info);
                    }
                }).build();
    }

    private WaitStrategy findStrategies(com.raipeng.aidatapush.annotation.CallRetry retryAnno) {
        /**
         * 重试间隔单位
         */
        TimeUnit waitTimeUnit = retryAnno.waitTimeUnit();

        /**
         * 重试间隔
         */
        int retryInterval = retryAnno.retryInterval();

        /**
         * 重试策略
         */
        WaitStrategyType waitStrategy = retryAnno.waitStrategy();

        switch (waitStrategy) {
            case FIXED_WAIT_STRATEGY:
                return WaitStrategies.fixedWait(retryInterval, waitTimeUnit);
            case RANDOM_WAIT_STRATEGY:
                return WaitStrategies.randomWait(retryInterval, waitTimeUnit);
            case INCREMENTING_WAIT_STRATEGY:
                return WaitStrategies.incrementingWait(retryInterval, waitTimeUnit, retryInterval, waitTimeUnit);
            case FIBONACCI_WAIT_STRATEGY:
                WaitStrategies.fibonacciWait(retryInterval, waitTimeUnit);
        }

        return WaitStrategies.fibonacciWait();

    }


    public Object doRetry(ProceedingJoinPoint joinPoint, Retryer<Object> retryer) throws ExecutionException, RetryException {
        return retryer.call(() -> {
            try {
                return joinPoint.proceed();
            } catch (CallFailedException e) {
                throw e;
            } catch (Throwable e) {
                throw new RuntimeException(e);
            }
        });
    }



    public void findFailedDataAndDoRetry(String id) {
        List<CallRetry> callRetryList = new ArrayList<>();
        if (StringUtils.isNotEmpty(id)) {
            Long idl = null;
            try {
                idl = Long.valueOf(id);
            } catch (Exception e) {
                log.error("findFailedDataAndDoRetry id = " + id + "  id 格式错误");
                return;
            }
            Optional<CallRetry> callRetry = repository.findById(idl);
            if (callRetry.isPresent()) {
                callRetryList.add(callRetry.get());
            } else {
                log.error("findFailedDataAndDoRetry id = " + id + "不存在");
                return;
            }
        } else {
            callRetryList = repository.findAllFailedAndNotBeMaxData();
            if (CollectionUtils.isEmpty(callRetryList)) {
                log.info("findFailedDataAndDoRetry,无需要重试数据，结束");
                return;
            }
        }
        log.info("findFailedDataAndDoRetry,待重试数据 size = " + callRetryList.size());
        for (CallRetry callRetry : callRetryList) {
            doRetryUsingData(callRetry);
        }

    }

    public void doRetryUsingData(CallRetry callRetry) {
        try {
            AutowiredAnnotationBeanPostProcessor postProcessor = applicationContext.getBean(AutowiredAnnotationBeanPostProcessor.class);
            Class<?> clazz = Thread.currentThread().getContextClassLoader().loadClass(callRetry.getRootClassName());
            Object o = clazz.getConstructor().newInstance();
            postProcessor.processInjection(o);
            Method method = null;
            String paramString = callRetry.getArgsEncryption();
            if (StringUtils.isNotEmpty(paramString)) {
                paramString = AESUtils.decryptByAES(paramString);
                List<RetryParameterDTO> parameterDTOList = JSONObject.parseArray(paramString, RetryParameterDTO.class);
                parameterDTOList = parameterDTOList.stream().sorted(Comparator.comparing(RetryParameterDTO::getParameterOrder)).collect(Collectors.toList());
                Class<?>[] cl = new Class[parameterDTOList.size()];
                Object[] args = new Object[parameterDTOList.size()];
                for (int i = 0; i < parameterDTOList.size(); i++) {
                    cl[i] = Thread.currentThread().getContextClassLoader().loadClass(parameterDTOList.get(i).getParameterClassName());
                    args[i] = JSONObject.parseObject(JSONObject.toJSONString(parameterDTOList.get(i).getParameterValue()), cl[i]);
                }

                method = clazz.getMethod(callRetry.getMethodName(), cl);
                //兼容list<E> 泛型
                Type[] parameterTypes = method.getGenericParameterTypes();
                for (int i = 0; i < parameterTypes.length; i++) {
                    Type type = parameterTypes[i];
                    if (type instanceof ParameterizedType) {
                        ParameterizedType typeVariable = (ParameterizedType) type;
                        Type[] typeArgs = typeVariable.getActualTypeArguments();

                        for (Type typeArg : typeArgs) {
                            if (typeArg instanceof Class) {
                                Class typeArgClass = (Class) typeArg;
                                System.out.println(typeArgClass.getName());
                                Class<?> argClazz = Thread.currentThread().getContextClassLoader().loadClass(typeArgClass.getName());
                                args[i] = JSONObject.parseArray(JSONObject.toJSONString(parameterDTOList.get(i).getParameterValue()), argClazz);
                            }
                        }
                    }
                }

                try {
                    method.invoke(o, args);
                    //   callRetry.setRetryResult(1);
                    repository.delete(callRetry);
                    log.info("重试定时任务 \n  doRetryUsingData 方法执行成功，id = " + callRetry.getId());
                } catch (Exception e) {
                    callRetry.setMemo(e.getCause().getMessage());
                    callRetry.setCurrentRetryCount(callRetry.getCurrentRetryCount() + 1);
                    repository.save(callRetry);
                    log.error("重试定时任务 \n  doRetryUsingData 方法执行失败，id = " + callRetry.getId(), e);
                    DingDingService.sendDingDingMsgRetryException("重试定时任务 \n "
                            + " doRetryUsingData 方法执行失败 \n"
                            + "id = " + callRetry.getId() + "\n"
                            + "cause = " + e.getCause().getMessage() + "\n"
                            + (Objects.equals(callRetry.getCurrentRetryCount(), callRetry.getMaxRetryCount()) ? "已达到最大重试次数" : ""));
                    //  throw e;
                }

            }


        } catch (Exception e) {
            log.error("重试定时任务 \n  doRetryUsingData 组装方法失败，id = " + callRetry.getId(), e);
            DingDingService.sendDingDingMsgRetryException("重试定时任务 \n  doRetryUsingData 组装方法失败，id = " + callRetry.getId());
         //   throw new RuntimeException(e);
        }


    }
}
