package com.raipeng.aidatapush.service;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.raipeng.aidatacommon.enums.CallStatusEnum;
import com.raipeng.aidatacommon.model.*;
import com.raipeng.aidatacommon.model.dto.CallRecordResult;
import com.raipeng.aidatacommon.model.record.CallRecord;
import com.raipeng.aidatacommon.model.script.AdvancedRuleCondition;
import com.raipeng.aidatacommon.model.script.FinalIntentionRules;
import com.raipeng.aidatapush.entity.MatchRuleInfo;
import com.raipeng.aidatapush.entity.ScriptAdvanceRuleEntity;
import com.raipeng.aidatapush.utils.RaiYiEncryptionUtil;
import com.raipeng.aidatacommon.entity.AiRedisTask;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBatch;
import org.redisson.api.RListAsync;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.raipeng.aidatacommon.constants.Constants.PHONE_HISTORY_NEW;

@Service
public class CallResultUtilService {
    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    RabbitTemplate rabbitTemplate;

    @Autowired
    private GlobalConfigService globalConfigService;

    @Autowired
    private AIIntentionTypeService aiIntentionTypeService;

    @Autowired
    private AdvanceRuleService advanceRuleService;

    @Autowired
    private AISemanticLabelRelationService aiSemanticLabelRelationService;

    @Autowired
    private FinalIntentionRuleService finalIntentionRuleService;

    public void saveCalledHistoryToRedis(List<CallRecordResult> callRecordResultList) {
        savePhoneHistoryToRedisBatch(callRecordResultList);
    }

    public void saveUnCallHistoryToRedis(List<CallRecordResult> callRecordResultList) {
        List<CallRecordResult> callRecordResults = callRecordResultList
                .stream()
                .filter(callRecordResult -> CallStatusEnum.ZERO.getValue().equals(callRecordResult.getCallStatus()))
                .collect(Collectors.toList());
        savePhoneHistoryToRedisBatch(callRecordResults);
    }

    public void checkIfNeedDecryption(List<CallRecordResult> callRecordResultList) {
        Set<String> phones = new HashSet<>();
        List<CallRecordResult> needDecryptionList = new ArrayList<>();
        for (CallRecordResult callRecordResult : callRecordResultList) {
            String plainPhone = callRecordResult.getPlainPhone();
            String phone = callRecordResult.getPhone();
            // 没有原号码，并且加密号码不是4位的未加密话术训练号码=>需要调用解密接口
            if (plainPhone == null && phone != null && phone.length() != 4) {
                phones.add(callRecordResult.getPhone());
                needDecryptionList.add(callRecordResult);
            }
        }
        if (phones.size() != 0) {
            JSONObject decryptionResultBatch = RaiYiEncryptionUtil.getDecryptionResultBatch(String.join(",", phones));
            needDecryptionList.forEach(callRecordResult -> {
                callRecordResult.setPlainPhone(decryptionResultBatch.getString(callRecordResult.getPhone()));
            });
        }
    }

    public void initCalledTaskStatistic(AiRedisTask task) {
        task.setCalledNumDiff(new AtomicInteger(0));
        task.setFinishedNumDiff(new AtomicInteger(0));
        task.setCallingNumDiff(new AtomicInteger(0));
        task.setRecallingNumDiff(new AtomicInteger(0));
        task.setPutThroughNumDiff(new AtomicInteger(0));
        task.setFeeMinuteDiff(new AtomicInteger(0));
        task.setCallRecordNumDiff(new AtomicInteger(0));
        task.setPhoneIntentionNumDiff(new AtomicInteger(0));
        task.setAIntentionNumDiff(new AtomicInteger(0));
    }

    public void initUnCallTaskStatistic(AiRedisTask task) {
        task.setFinishedNumDiff(new AtomicInteger(0));
        task.setCalledNumDiff(new AtomicInteger(0));
        task.setRecallingNumDiff(new AtomicInteger(0));
        task.setCallingNumDiff(new AtomicInteger(0));
        task.setPutThroughNumDiff(new AtomicInteger(0));
        task.setFeeMinuteDiff(new AtomicInteger(0));
        task.setCallRecordNumDiff(new AtomicInteger(0));
    }

    public void tagCallRecord(CallRecord callRecord) {
        GlobalConfig globalConfig1 = globalConfigService.getFirstByKey(callRecord.getSpeechCraftId() + "_FORBID_TAG_IN_PROCESS");
        GlobalConfig globalConfig2 = globalConfigService.getFirstByKey(callRecord.getSpeechCraftId() + "_ORDER_FIRST");
        //aiIntentionTypeList已经按优先级排序了
        List<AIIntentionType> aiIntentionTypeList = aiIntentionTypeService.findAllByScriptId(callRecord.getSpeechCraftId());
        Map<String, Integer> aiIntentionTypeMap = new HashMap<>();
        Map<String, String> aiIntentionTypeDescriptionMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(aiIntentionTypeList)) {
            aiIntentionTypeMap = aiIntentionTypeList.stream().collect(Collectors.toMap(AIIntentionType::getIntentionType, AIIntentionType::getSequence));
            aiIntentionTypeDescriptionMap = aiIntentionTypeList.stream().collect(Collectors.toMap(AIIntentionType::getIntentionType, AIIntentionType::getIntentionName));
        }
        boolean isForbidProcess = (globalConfig1 != null && "1".equals(globalConfig1.getValue()));
        boolean isOrderFirst = (globalConfig2 != null && "1".equals(globalConfig2.getValue()));
        ScriptAdvanceRuleEntity allAdvanceRuleByScriptId = advanceRuleService.findAllAdvanceRuleByScriptId(callRecord.getSpeechCraftId());
        List<AdvancedRules> advancedRulesList = allAdvanceRuleByScriptId.getAdvancedRulesList();
        Map<String, List<AdvancedRuleCondition>> advancedRuleConditionList = allAdvanceRuleByScriptId.getAdvancedRuleConditionMap();
        List<FinalIntentionRules> finalIntentionRulesList = allAdvanceRuleByScriptId.getFinalIntentionRulesList();

        Map<String, List<String>> allAISemanticLabelRelationMap = aiSemanticLabelRelationService.findAllAISemanticLabelRelation();
        //高级规则校验 ps校验中需要的参数是什么
        List<MatchRuleInfo> matchRuleInfo = advanceRuleService.tagCallRecord(callRecord, isOrderFirst, advancedRulesList,
                advancedRuleConditionList, allAISemanticLabelRelationMap);
        //没有命中高级规则
        if (CollectionUtils.isEmpty(matchRuleInfo)) {
            if (isForbidProcess) {
                callRecord.setIntentionClass("其他");
                return;
            }
        }
        // 高级规则中命中标签和标签id与流程中合并
        List<String> intentionTagIdList = matchRuleInfo.stream().map(MatchRuleInfo::getIntentionTagId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<String> intentionTagNameList = matchRuleInfo.stream().map(MatchRuleInfo::getIntentionTagName).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        callRecord.setIntentionLabelIds(advanceRuleService.combineTags(String.join(",", intentionTagIdList), callRecord.getIntentionLabelIds()));
        callRecord.setIntentionLabels(advanceRuleService.combineTags(String.join(",", intentionTagNameList), callRecord.getIntentionLabels()));

        //进行最终意向统计
        String finalIntentionType = "其他";
        Set<String> intentionLevelNameSet = matchRuleInfo.stream().map(MatchRuleInfo::getIntentionLevelName).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        // 产品设计 8.2版本中流程中意向现在是多个而不是一个
        if (!isForbidProcess && StringUtils.isNotBlank(callRecord.getIntentionClass())) {
            //不屏蔽流程中意向，并且流程中意向不为空 并且流程中意向不为其他
            intentionLevelNameSet.addAll(Arrays.asList(callRecord.getIntentionClass().split(",")));
        }
        //避免出现"其他"这个意向等级
        intentionLevelNameSet.remove("其他");
        List<String> orderAiIntentionTypeList = new ArrayList<>(intentionLevelNameSet);
        Map<String, Integer> finalAiIntentionTypeMap = aiIntentionTypeMap;
        // 按值从小到大排序
        orderAiIntentionTypeList.sort(Comparator.comparingInt(
                key -> finalAiIntentionTypeMap.getOrDefault(key, Integer.MAX_VALUE)
        ));
        if (CollectionUtils.isEmpty(orderAiIntentionTypeList)) {
            //命中的高级规则没有意向等级
            callRecord.setIntentionClass(finalIntentionType);
            return;
        }

        if (CollectionUtils.isEmpty(finalIntentionRulesList)) {
            finalIntentionType = orderAiIntentionTypeList.get(0);
        } else {
            //最终意向排除的规则可能存在一个等级对应多个排除规则的
            Map<String, List<FinalIntentionRules>> finalIntentionGroupByLevelMap = finalIntentionRulesList.stream().collect(Collectors.groupingBy(FinalIntentionRules::getIntentionLevelName));
            for (String intentionType : orderAiIntentionTypeList) {
                List<FinalIntentionRules> finalIntentionRules = finalIntentionGroupByLevelMap.get(intentionType);
                //命中的意向没有对应的排除规则，直接返回该意向作为最终意向
                if (CollectionUtils.isEmpty(finalIntentionRules)) {
                    finalIntentionType = intentionType;
                    break;
                }
                //满足最终意向中的排除条件时，当前意向等级被排除
                boolean excludeIntention = finalIntentionRuleService.tagFinalIntentionList(callRecord, finalIntentionRules, advancedRuleConditionList, allAISemanticLabelRelationMap);
                if (!excludeIntention) {
                    finalIntentionType = intentionType;
                    break;
                }
            }
        }
        callRecord.setIntentionClass(finalIntentionType);
        callRecord.setIntentionClassDescription(aiIntentionTypeDescriptionMap.get(finalIntentionType));
    }

    public void tagCallRecord(CallRecordForHumanMachine callRecord) {
        GlobalConfig globalConfig1 = globalConfigService.getFirstByKey(callRecord.getSpeechCraftId() + "_FORBID_TAG_IN_PROCESS");
        GlobalConfig globalConfig2 = globalConfigService.getFirstByKey(callRecord.getSpeechCraftId() + "_ORDER_FIRST");
        //aiIntentionTypeList已经按优先级排序了
        List<AIIntentionType> aiIntentionTypeList = aiIntentionTypeService.findAllByScriptId(callRecord.getSpeechCraftId());
        Map<String, Integer> aiIntentionTypeMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(aiIntentionTypeList)) {
            aiIntentionTypeMap = aiIntentionTypeList.stream().collect(Collectors.toMap(AIIntentionType::getIntentionType, AIIntentionType::getSequence));
        }
        boolean isForbidProcess = (globalConfig1 != null && "1".equals(globalConfig1.getValue()));
        boolean isOrderFirst = (globalConfig2 != null && "1".equals(globalConfig2.getValue()));
        ScriptAdvanceRuleEntity allAdvanceRuleByScriptId = advanceRuleService.findAllAdvanceRuleByScriptId(callRecord.getSpeechCraftId());
        List<AdvancedRules> advancedRulesList = allAdvanceRuleByScriptId.getAdvancedRulesList();
        Map<String, List<AdvancedRuleCondition>> advancedRuleConditionList = allAdvanceRuleByScriptId.getAdvancedRuleConditionMap();
        List<FinalIntentionRules> finalIntentionRulesList = allAdvanceRuleByScriptId.getFinalIntentionRulesList();
        Map<String, List<String>> allAISemanticLabelRelationMap = aiSemanticLabelRelationService.findAllAISemanticLabelRelation();
        //高级规则校验 ps校验中需要的参数是什么
        List<MatchRuleInfo> matchRuleInfo = advanceRuleService.matchRuleCondition(callRecord, isOrderFirst, advancedRulesList, advancedRuleConditionList,
                allAISemanticLabelRelationMap);
        //没有命中高级规则
        if (CollectionUtils.isEmpty(matchRuleInfo)) {
            if (isForbidProcess) {
                callRecord.setIntentionClass("其他");
                return;
            }
        }
        // 高级规则中命中标签和标签id与流程中合并
        List<String> intentionTagIdList = matchRuleInfo.stream().map(MatchRuleInfo::getIntentionTagId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<String> intentionTagNameList = matchRuleInfo.stream().map(MatchRuleInfo::getIntentionTagName).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        callRecord.setIntentionLabelIds(advanceRuleService.combineTags(String.join(",", intentionTagIdList), callRecord.getIntentionLabelIds()));
        callRecord.setIntentionLabels(advanceRuleService.combineTags(String.join(",", intentionTagNameList), callRecord.getIntentionLabels()));

        //进行最终意向统计
        String finalIntentionType = "其他";
        Set<String> intentionLevelNameSet = matchRuleInfo.stream().map(MatchRuleInfo::getIntentionLevelName).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        // 8.2版本中流程中意向现在是多个
        if (!isForbidProcess && StringUtils.isNotBlank(callRecord.getIntentionClass()) && !"其他".equals(callRecord.getIntentionClass())) {
            //不屏蔽流程中意向，并且流程中意向不为空 并且流程中意向不为其他
            intentionLevelNameSet.addAll(Arrays.asList(callRecord.getIntentionClass().split(",")));
        }
        List<String> orderAiIntentionTypeList = new ArrayList<>(intentionLevelNameSet);
        Map<String, Integer> finalAiIntentionTypeMap = aiIntentionTypeMap;
        // 按值从小到大排序
        orderAiIntentionTypeList.sort(Comparator.comparingInt(
                key -> finalAiIntentionTypeMap.getOrDefault(key, Integer.MAX_VALUE)
        ));
        if (CollectionUtils.isEmpty(orderAiIntentionTypeList)) {
            //命中的高级规则没有意向等级
            callRecord.setIntentionClass(finalIntentionType);
            return;
        }

        if (CollectionUtils.isEmpty(finalIntentionRulesList)) {
            finalIntentionType = orderAiIntentionTypeList.get(0);
        } else {
            //最终意向排除的规则可能存在一个等级对应多个排除规则的
            Map<String, List<FinalIntentionRules>> finalIntentionGroupByLevelMap = finalIntentionRulesList.stream().collect(Collectors.groupingBy(FinalIntentionRules::getIntentionLevelName));
            for (String intentionType : orderAiIntentionTypeList) {
                List<FinalIntentionRules> finalIntentionRules = finalIntentionGroupByLevelMap.get(intentionType);
                if (CollectionUtils.isEmpty(finalIntentionRules)) {
                    finalIntentionType = intentionType;
                    break;
                }
                //满足最终意向中的排除条件时，当前意向等级被排除
                boolean excludeIntention = finalIntentionRuleService.tagFinalIntentionList(callRecord, finalIntentionRules, advancedRuleConditionList, allAISemanticLabelRelationMap);
                if (!excludeIntention) {
                    finalIntentionType = intentionType;
                    break;
                }
            }
        }
        callRecord.setIntentionClass(finalIntentionType);
    }

    private void savePhoneHistoryToRedisBatch(List<CallRecordResult> callRecordResultList) {
        RBatch batch = redissonClient.createBatch();
        for (CallRecordResult callRecordResult : callRecordResultList) {
            if (callRecordResult.getCallOutTime() != null) {
                // 1. 存储供应线路-电话信息
                savePhoneHistoryToRedis(callRecordResult.getPhone(),
                        callRecordResult.getCallOutTime(),
                        callRecordResult.getCallStatus().toString(),
                        callRecordResult.getLineId(),
                        callRecordResult.getProductId(),
                        callRecordResult.getIndustrySecondFieldId(),
                        batch);

                // 1. 存储网关-电话信息
                String lineGatewayNumbers = callRecordResult.getLineGatewayNumbers();
                if (lineGatewayNumbers != null) {
                    ArrayList<String> lineGatewayNumberList = JSONObject.parseObject(lineGatewayNumbers, new TypeReference<ArrayList<String>>() {
                    });
                    for (String lineGatewayNumber : lineGatewayNumberList) {
                        savePhoneHistoryToRedis(callRecordResult.getPhone(),
                                callRecordResult.getCallOutTime(),
                                callRecordResult.getCallStatus().toString(),
                                lineGatewayNumber,
                                "",
                                "",
                                batch);
                    }
                }
            }
        }
        batch.execute();
    }

    private void savePhoneHistoryToRedis(String phone, String calloutTime, String callStatus, String lineId,
                                         String productId, String industryId, RBatch batch) {
        String key = PHONE_HISTORY_NEW + phone;
        RListAsync<Object> list = batch.getList(key);
        if (StringUtils.isEmpty(productId) || StringUtils.isEmpty(industryId)) {
            list.addAsync(lineId + "," + callStatus + "," + calloutTime);
        } else {
            list.addAsync(lineId + "," + callStatus + "," + calloutTime + "," + industryId + "," + productId);
        }
    }
}
