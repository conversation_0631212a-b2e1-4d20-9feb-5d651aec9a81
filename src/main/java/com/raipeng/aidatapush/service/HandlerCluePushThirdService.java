package com.raipeng.aidatapush.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.raipeng.aidatacommon.enums.retry.WaitStrategyType;
import com.raipeng.aidatacommon.exception.CallFailedException;
import com.raipeng.aidatacommon.model.CallRecordForHumanMachine;
import com.raipeng.aidatacommon.model.clue.Clue;
import com.raipeng.aidatacommon.model.clue.FormCollection;
import com.raipeng.aidatacommon.model.clue.FormRecord;
import com.raipeng.aidatapush.annotation.CallRetry;
import com.raipeng.aidatapush.controller.wrapper.ClueDataPushWrapper;
import com.raipeng.aidatapush.repository.ClueRepository;
import com.raipeng.aidatapush.repository.FormCollectionRepository;
import com.raipeng.aidatapush.repository.FormRecordRepository;
import com.raipeng.common.enums.FollowUpStatus;
import com.raipeng.common.model.BaseEntity;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.raipeng.aidatacommon.utils.AESUtils.AESEncode;

@Slf4j
@Service
public class HandlerCluePushThirdService {
    @Autowired
    private ClueRepository clueRepository;

    @Autowired
    private FormRecordRepository formRecordRepository;

    @Autowired
    private FormCollectionRepository formCollectionRepository;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private RequestLogService requestLogService;

    @CallRetry(retryTimes = 5, waitStrategy = WaitStrategyType.INCREMENTING_WAIT_STRATEGY)
    public void handleClueDataPushWrappers(String aesKey, String salt, String url, List<ClueDataPushWrapper> clueDataPushWrapperList) {
        Map<Long, Clue> clueMap = clueRepository.findAllById(
                        clueDataPushWrapperList.stream()
                                .map(ClueDataPushWrapper::getClueId)
                                .collect(Collectors.toList())).stream()
                .filter(clue -> FollowUpStatus.SUCCESS.equals(clue.getLatestFollowUpStatus()) && clue.getExamineStatus() != null)
                .collect(Collectors.toMap(Clue::getId, Function.identity()));
        if (clueMap.size() == 0) {
            return;
        }
        Set<Long> clueIds = clueMap.keySet();

        List<FormRecord> formRecords = formRecordRepository.findFormRecordsByClueIdIn(clueIds);
        Map<Long, List<FormRecord>> clueIdFormRecordMap = formRecords.stream().collect(Collectors.groupingBy(FormRecord::getClueId));
        Map<Long, String> collectionIdNameMap = formCollectionRepository.findAllById(
                        formRecords.stream()
                                .map(FormRecord::getFormCollectionId)
                                .collect(Collectors.toList())).stream()
                .filter(formCollection -> formCollection.getCollectionStatus()==1)
                .collect(Collectors.toMap(BaseEntity::getId, FormCollection::getCollectionItemName));

        JSONArray paramArray = new JSONArray();
        for (ClueDataPushWrapper wrapper : clueDataPushWrapperList) {
            Long clueId = wrapper.getClueId();
            if (clueIds.contains(clueId)) {
                JSONObject params = new JSONObject();
                JSONObject forms = new JSONObject();
                List<FormRecord> formRecordList = clueIdFormRecordMap.get(clueId);
                for (FormRecord formRecord : formRecordList) {
                    String formKey = collectionIdNameMap.get(formRecord.getFormCollectionId());
                    if (formKey != null) {
                        forms.put(formKey, formRecord.getContent());
                    }
                }
                CallRecordForHumanMachine record = wrapper.getRecord();
                params.put("id", record.getId());
                params.put("callOutTime", record.getCallOutTime());
                params.put("callStatus", record.getCallStatus());
                params.put("contactTime", record.getContactTime());
                params.put("customerMobile", record.getPhone());
                params.put("intentionClass", wrapper.getIntentionClass());
                params.put("talkTime", record.getCallDurationSec());
                params.put("talkTimeEnd", record.getTalkTimeEnd());
                params.put("talkTimeStart", record.getTalkTimeStart());
                params.put("taskId", record.getTaskId());
                params.put("interactNum", record.getCycleCount());
                params.put("intentionLabels", wrapper.getIntentionLabels());
                params.put("voiceUrl", record.getWholeAudioFileUrl());
                params.put("examineStatus", clueMap.get(clueId).getExamineStatus());
                params.put("formRecord", forms);
                paramArray.add(params);
            }
        }

        Map<String, Object> map = new HashMap<>();
        map.put("size", clueIds.size());
        map.put("text", AESEncode(aesKey, paramArray.toJSONString()));

        map = new TreeMap<>(map);
        map.put("sign", DigestUtils.md5Hex(JSON.toJSONString(map, SerializerFeature.SortField) + salt));

        Map<String, Object> response = null;
        LocalDateTime startTime = LocalDateTime.now();
        LocalDateTime endTime = LocalDateTime.now();
        try {
            response = restTemplate.postForObject(url, map, Map.class);
            endTime = LocalDateTime.now();
        } catch (Exception ex){
            log.error("Exception： 手机号推送汽车平台报错！！");
            throw new CallFailedException(ex, "手机号推送汽车平台报错", "clue_record_push");
        }finally {
            requestLogService.addLog(paramArray.toJSONString() + "[" + url + "]", response, "clue_record_push", startTime, endTime);
        }
    }
}
