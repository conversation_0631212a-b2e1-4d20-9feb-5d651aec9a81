package com.raipeng.aidatapush.service;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.raipeng.aidatacommon.model.record.CallRecord;
import com.raipeng.aidatapush.repositorymulti.CallRecordRepository;
import com.raipeng.aidatapush.utils.ThreadManagerUtil;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;



@Slf4j
@Service
public class CallRecordMultiService {
    @Autowired
    private CallRecordRepository callRecordRepository;

    public List<CallRecord> findAllByRecordIdIn(List<String> recordIds) {
        List<CallRecord> records = Collections.synchronizedList(new ArrayList<>());
        Map<String, List<String>> recordIdMap = new HashMap<>();
        for (String recordId : recordIds) {
            String prefix = recordId.split("_")[0];
            recordIdMap.compute(prefix, (k, v) -> {
                if (v == null) {
                    v = new ArrayList<>();
                }
                v.add(recordId);
                return v;
            });
        }
        List<Future<?>> futures = new ArrayList<>();

        recordIdMap.forEach((dataSourceName, v) -> {
            if (CollectionUtils.isNotEmpty(v)) {
                Future<?> submit = ThreadManagerUtil.executeSqlPool.submit(() -> {
                    DynamicDataSourceContextHolder.push(dataSourceName);
                    try {
                        records.addAll(callRecordRepository.findAllByRecordIdIn(v));
                    } finally {
                        DynamicDataSourceContextHolder.poll(); // 清理防止内存泄漏
                    }
                });
                futures.add(submit);
            }
        });

        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("Exception:查询通话记录失败", e);
                throw new RuntimeException(e);
            }
        }
        return records;
    }

    public void saveAll(List<CallRecord> callRecordList) {
        Map<String, List<CallRecord>> callRecordMap = new HashMap<>();
        for (CallRecord callRecord : callRecordList) {
            String recordId = callRecord.getRecordId();
            String prefix = recordId.split("_")[0];
            callRecordMap.compute(prefix, (k, v) -> {
                if (v == null) {
                    v = new ArrayList<>();
                }
                v.add(callRecord);
                return v;
            });
        }

        List<Future<?>> futures = new ArrayList<>();
        callRecordMap.forEach((dataSourceName, v) -> {
            if (CollectionUtils.isNotEmpty(v)) {
                Future<?> submitA = ThreadManagerUtil.executeSqlPool.submit(() -> {
                    DynamicDataSourceContextHolder.push(dataSourceName);
                    try {
                        callRecordRepository.saveAll(v);
                    } finally {
                        DynamicDataSourceContextHolder.poll(); // 清理防止内存泄漏
                    }
                });
                futures.add(submitA);
            }
        });

        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("Exception:查询通话记录失败", e);
                throw new RuntimeException(e);
            }
        }
    }

    public void saveByCdr(Integer callDurationSec, Integer callDuration, String callStatus, String callStatusStr,
                          String errorCode, String cause, String callId, String lineId, String lineCode,
                          String merchantLineId, String merchantLineCode, String recordId, String fsIp, String aiCallIp,
                          String callOutTime, Integer whoHangUp, String scriptStringId, Long speechCraftId,
                          Integer waitmsec, String sipCallId, LocalDateTime updateTime) {
        DynamicDataSourceContextHolder.push(recordId.split("_")[0]);
        try {
            callRecordRepository.saveByCdr(callDurationSec, callDuration, callStatus, callStatusStr, errorCode,
                    cause, callId, lineId, lineCode, merchantLineId, merchantLineCode, recordId, fsIp, aiCallIp,
                    callOutTime, whoHangUp, scriptStringId, speechCraftId, waitmsec, sipCallId, updateTime);
        } finally {
            DynamicDataSourceContextHolder.poll();
        }
    }

    public void saveByCalledResult(String wholeAudioFileUrl, Integer cycleCount, Integer sayCount,
                                   String userFullAnswerContent, String intentionClass, String intentionLabelIds,
                                   String intentionLabels, String callOutTime, String contactTime, String talkTimeEnd,
                                   String talkTimeStart, String hitAnswerIds, String scriptStringId, Long speechCraftId,
                                   String recordId, String hitAdvanceIds, String corpusIds, String hitSemanticIds, String extraInfo,
                                   String lineGatewayNumbers, LocalDateTime updateTime) {
        DynamicDataSourceContextHolder.push(recordId.split("_")[0]);
        try {
            callRecordRepository.saveByCalledResult(wholeAudioFileUrl, cycleCount, sayCount, userFullAnswerContent,
                    intentionClass, intentionLabelIds, intentionLabels, callOutTime, contactTime, talkTimeEnd,
                    talkTimeStart, hitAnswerIds, scriptStringId, speechCraftId, recordId, hitAdvanceIds,
                    corpusIds, hitSemanticIds, extraInfo, lineGatewayNumbers, updateTime);
        } finally {
            DynamicDataSourceContextHolder.poll();
        }
    }
}
