package com.raipeng.aidatapush.service;

import com.rabbitmq.client.Channel;

import lombok.extern.slf4j.Slf4j;

import org.springframework.amqp.rabbit.core.RabbitTemplate;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeoutException;

/**
 * rabbitMq channel 管理服务
 */

@Slf4j
public class ChannelManagerService {
    public static Map<String, Channel> channelMap = new ConcurrentHashMap<>();

    public static Channel getChannel(String queueName, RabbitTemplate rabbitTemplate) {
        return channelMap.compute(queueName, (key, value) -> {
            if (value == null) {
                value = rabbitTemplate.getConnectionFactory().createConnection().createChannel(false);
            }
            return value;
        });
    }

    public static void closeChannelSingle(String queueName) {
        Channel channel = channelMap.get(queueName);
        try {
            channel.close();
            channelMap.remove(queueName);
            log.info("Exception=>队列{}停止消费", queueName);
        } catch (IOException | TimeoutException e) {
            throw new RuntimeException(e);
        }
    }

    public static void destroyChannel() {
        System.out.println("===>关闭channel开始");
        for (Channel channel : channelMap.values()) {
            try {
                channel.close();
            } catch (IOException | TimeoutException e) {
                throw new RuntimeException(e);
            }
        }
        channelMap.clear();
        System.out.println("===>关闭channel结束");
    }
}
