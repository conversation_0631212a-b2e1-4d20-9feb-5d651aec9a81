package com.raipeng.aidatapush.service;


import com.alibaba.fastjson.JSONObject;
import com.raipeng.aidatacommon.model.RequestLogForPushThird;
import com.raipeng.aidatacommon.model.inter.CallDataForPushThird;
import com.raipeng.aidatacommon.thirdrequests.entity.AdminCallbackEntity;
import com.raipeng.aidatacommon.thirdrequests.enums.PushThirdType;
import com.raipeng.aidatacommon.thirdrequests.inter.RestForThirdTemplate;
import com.raipeng.aidatapush.repository.RequestLogForPushThirdRepository;
import com.raipeng.common.util.LogUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@Service
public class RestForThirdTemplateService implements RestForThirdTemplate {
    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private AdminService adminService;

    @Autowired
    private SupplyLineService supplyLineService;

    @Autowired
    private RequestLogForPushThirdRepository requestLogForPushThirdRepository;

    @Override
    public <T> T postForObject(String url, Object request, Class<T> responseType, Object... uriVariables) {
        return restTemplate.postForObject(url, request, responseType, uriVariables);
    }

    @Override
    public AdminCallbackEntity findAdminCallbackEntity(String groupId) {
        return adminService.getCallbackEntityByGroupId(groupId);
    }

    @Override
    public Set<String> getCallbackSupplyLineNumbers() {
        return supplyLineService.getCallbackSupplyLineNumbers();
    }

    @Override
    public void finalHandle(String requests, Map<Object, Object> response, String pushUrl, String groupId,
                            PushThirdType pushType, LocalDateTime startTime, LocalDateTime endTime,
                            List<? extends CallDataForPushThird> callRecordList) {
        RequestLogForPushThird requestLogForPushThird = new RequestLogForPushThird();
        requestLogForPushThird.setPushType(pushType.getDescription());
        requestLogForPushThird.setPushUrl(pushUrl);
        requestLogForPushThird.setGroupId(groupId);
        requestLogForPushThird.setRequestContent(LogUtils.replacePhone(requests));
        requestLogForPushThird.setResponseContent(JSONObject.toJSONString(response));
        requestLogForPushThird.setRequestTime(startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        requestLogForPushThird.setResponseTime(endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        requestLogForPushThirdRepository.save(requestLogForPushThird);
    }
}
