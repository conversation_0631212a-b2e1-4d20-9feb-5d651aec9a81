package com.raipeng.aidatapush.service;

import com.raipeng.aidatacommon.model.GlobalConfig;
import com.raipeng.aidatapush.repository.GlobalConfigRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import static com.raipeng.aidatacommon.constants.Constants.CONFIG_ACCOUNT;

@Service
@RefreshScope
@Slf4j
public class GlobalConfigService {
    @Autowired
    private GlobalConfigRepository globalConfigRepository;

    @Cacheable(value = "CACHE::GLOBAL_CONFIG_BY_KEY")
    public GlobalConfig getFirstByKey(String key){
        return globalConfigRepository.getFirstByKey(key);
    }
}
