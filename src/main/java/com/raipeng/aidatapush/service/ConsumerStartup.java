package com.raipeng.aidatapush.service;

import com.raipeng.aidatapush.service.mq.handler.*;
import com.raipeng.aidatapush.utils.ThreadManagerUtil;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 消费启动器
 */
@Slf4j
@RefreshScope
@Service
public class ConsumerStartup {
    ExecutorService executorService1 = Executors.newFixedThreadPool(1);
    ExecutorService executorService2 = Executors.newFixedThreadPool(1);
    ExecutorService executorService3 = Executors.newFixedThreadPool(1);

    ExecutorService executorService4 = Executors.newFixedThreadPool(4);
    ExecutorService executorService5 = Executors.newFixedThreadPool(1);
    ExecutorService executorService6 = Executors.newFixedThreadPool(1);

    ExecutorService executorService7 = Executors.newFixedThreadPool(1);

    @Autowired
    private ResultFromAICallPureAICalledHandler resultFromAICallPureAICalledHandler;

    @Autowired
    private ResultFromAICallAIManCalledHandler resultFromAICallAIManCalledHandler;

    @Autowired
    private ResultFromAICallHumanCalledHandler resultFromAICallHumanCalledHandler;

    @Autowired
    private ResultFromAICallPureAIUnCallHandlerSingle resultFromAICallPureAIUnCallHandlerSingle;

    @Autowired
    private ResultFromAICallAIManUnCallHandlerSingle resultFromAICallAIManUnCallHandlerSingle;

    @Autowired
    private ResultFromAICallHumanUnCallHandlerSingle resultFromAICallHumanUnCallHandlerSingle;

    @Autowired
    private ResultFromLandingPagePureAICallHandler resultFromLandingPagePureAICallHandler;

    @PostConstruct
    public void startup(){
        executorService1.submit(()->{
            try {
                resultFromAICallPureAICalledHandler.handle();
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        executorService2.submit(()->{
            try {
                resultFromAICallAIManCalledHandler.handle();
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        executorService3.submit(()->{
            try {
                resultFromAICallHumanCalledHandler.handle();
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        for (int i = 0; i <3; i++) {
            executorService4.submit(()->{
                try {
                    resultFromAICallPureAIUnCallHandlerSingle.handle();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
        }
        executorService5.submit(()->{
            try {
                resultFromAICallAIManUnCallHandlerSingle.handle();
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        executorService6.submit(()->{
            try {
                resultFromAICallHumanUnCallHandlerSingle.handle();
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        executorService7.submit(()->{
            try {
                resultFromLandingPagePureAICallHandler.handle();
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        //注册一个钩子
        Runtime.getRuntime().addShutdownHook(new Thread(()->{
            try{
                ChannelManagerService.destroyChannel();
            }catch (Exception e){
                e.printStackTrace();
            }
            ThreadManagerUtil.shutdownCallExecutorGracefully();
        }));
    }
}
