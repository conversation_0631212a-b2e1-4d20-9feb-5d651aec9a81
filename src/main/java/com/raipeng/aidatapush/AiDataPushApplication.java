package com.raipeng.aidatapush;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@EnableScheduling
@EnableCaching
@SpringBootApplication
@EnableFeignClients
@ServletComponentScan(basePackages = "com.raipeng.aidatapush.listener")
@EnableDiscoveryClient
@EntityScan("com.raipeng.aidatacommon.model")
@EnableTransactionManagement
@EnableJpaRepositories(basePackages = {
        "com.raipeng.aidatapush.repositorymulti",
        "com.raipeng.aidatapush.repository"
})
public class AiDataPushApplication {
    public static void main(String[] args) {
        SpringApplication.run(AiDataPushApplication.class, args);
    }
}
