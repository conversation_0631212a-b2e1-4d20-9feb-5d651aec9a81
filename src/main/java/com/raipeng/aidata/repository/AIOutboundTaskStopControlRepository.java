package com.raipeng.aidata.repository;

import com.raipeng.aidatacommon.model.AIOutboundTaskStopControl;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface AIOutboundTaskStopControlRepository extends JpaRepository<AIOutboundTaskStopControl, Long> {

    @Query(value = "select * from t_ai_outbound_task_stop_control t \n" +
            " where t.create_time >= :startTime\n" +
            " and t.create_time < :endTime", nativeQuery = true)
    List<AIOutboundTaskStopControl> findTaskListByTimeAndGroupId(LocalDateTime startTime, LocalDateTime endTime);


    @Query(value = "select * from t_ai_outbound_task_stop_control t \n" +
            " where t.group_id = cast(:groupId as text) and stop_type = 'ACCOUNT' limit 1", nativeQuery = true)
    AIOutboundTaskStopControl findStopControlByGroupId(String groupId);


    @Query(value = "select * from t_ai_outbound_task_stop_control t \n" +
            " where t.task_id = :taskId\n" +
            " and t.group_id = cast(:groupId as text) and stop_type = 'TASK' limit 1", nativeQuery = true)
    AIOutboundTaskStopControl findStopControlByTaskIdAndGroupId(Long taskId, String groupId);

    AIOutboundTaskStopControl findByTaskId(Long taskId);
}
