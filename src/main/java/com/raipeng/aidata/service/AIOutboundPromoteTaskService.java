package com.raipeng.aidata.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.raipeng.aidata.annotation.RedisLock;
import com.raipeng.aidata.controller.wrapper.MobileBelongResultWrapper;
import com.raipeng.aidata.repository.*;
import com.raipeng.aidata.utils.RaiYiEncryptionUtil;
import com.raipeng.aidatacommon.model.*;
import com.raipeng.aidatacommon.model.dto.AIOutboundPhonesSync;
import com.raipeng.aidatacommon.model.record.CallRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RQueue;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;


@Service
@Slf4j
public class AIOutboundPromoteTaskService {
    @Autowired
    private AIOutboundTaskRepository aiOutboundTaskRepository;

    @Autowired
    private AiTaskWriteService aiTaskWriteService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private PhoneRecordService phoneRecordService;

    @Autowired
    private CallRecordService callRecordService;

    @Autowired
    private AITenantRepository aiTenantRepository;

    @Autowired
    private MobileBelongCacheService mobileBelongCacheService;

    @Autowired
    private AdminRepository adminRepository;

    @Autowired
    private AIOutboundTaskTemplateRepository aiOutboundTaskTemplateRepository;


    @Autowired
    private BatchService batchService;

    @Autowired
    private BatchService1 batchService1;

    private String REDIS_TASK_TMP = "REDIS_TASK_TMP::";

    @RedisLock(paramName = "taskName")
    public synchronized String createTask(Long templateId, String taskName){
        AIOutboundTask aiOutboundTask = new AIOutboundTask();
        RBucket<Object> taskIdBucket = redissonClient.getBucket(REDIS_TASK_TMP + taskName + "_id");
        String taskIdStr = (String)taskIdBucket.get();
        if(StringUtils.isNotBlank(taskIdStr)){
            return taskIdStr;
        }
        AIOutboundTaskTemplate aiOutboundTaskTemplate = aiOutboundTaskTemplateRepository.findById(templateId).get();
        RAtomicLong max_task_id = redissonClient.getAtomicLong("MAX_TASK_ID");
        BeanUtils.copyProperties(aiOutboundTaskTemplate, aiOutboundTask);
        long taskId = max_task_id.addAndGet(1);
        aiOutboundTask.setId(taskId);
        aiOutboundTask.setTaskName(taskName);
        aiOutboundTask.setCallStatus("待执行");
        aiOutboundTask.setAiAnswerNum(200);//默认200并发，促活应该够了
        aiOutboundTask.setPhoneNum(0);
        aiOutboundTask.setCalledPhoneNum(0);
        aiOutboundTask.setPutThroughPhoneNum(0);
        aiOutboundTask.setFinishedPhoneNum(0);
        aiOutboundTask.setPhoneIntentionNum(0);
        aiOutboundTask.setAClassNum(0);
        aiOutboundTask.setCallRecordNum(0);
        aiOutboundTask.setGroupId(aiOutboundTaskTemplate.getGroupId());
        aiOutboundTask.setTenantName(aiOutboundTaskTemplate.getTenantName());
        aiOutboundTask.setFeeMinute(0);
        aiOutboundTask.setIsAutoStop(0);//未止损
        aiOutboundTask.setTemplateId(String.valueOf(templateId));
        aiOutboundTask.setTenantBlackList(aiOutboundTaskTemplate.getTenantBlackList());
        taskIdBucket.set(String.valueOf(taskId));
        aiTaskWriteService.initTask(aiOutboundTask);//直接在数据库中创建任务
        return String.valueOf(taskId);
    }


    public String syncPhones(AIOutboundPhonesSync aiOutboundPhonesSync){
        LocalDateTime aTime = LocalDateTime.now();
        Optional<AIOutboundTask> optional = aiOutboundTaskRepository.findById(aiOutboundPhonesSync.getTaskId());
        if(optional.isPresent()){
            AIOutboundTask aiOutboundTask = optional.get();
            String text = aiOutboundPhonesSync.getText();
            JSONArray phoneList = JSON.parseArray(text);
            List<String> phones = new ArrayList<>();
            List<PhoneRecord> phoneRecordSaveList = new ArrayList<>();
            List<CallRecord> callRecordSaveList = new ArrayList<>();
            List<String> decryptedPhoneList = new ArrayList<>();
            for (int i = 0; i < phoneList.size(); i++) {
                JSONObject jsonObject = phoneList.getJSONObject(i);
                String mobile = (String)jsonObject.get("mobile");
                decryptedPhoneList.add(mobile);
            }
            AITenant aiTenant = null;
            Admin admin = null;
            if(StringUtils.isNotBlank(aiOutboundTask.getGroupId())){
                Optional<AITenant> aiTenantOptional = aiTenantRepository.findById(Long.valueOf(aiOutboundTask.getGroupId().split("_")[1]));
                aiTenant = aiTenantOptional.orElse(null);
                admin = adminRepository.findFirstById(Long.valueOf(aiOutboundTask.getGroupId().split("_")[2]));
            }
            JSONObject encryptionResultBatch = RaiYiEncryptionUtil.getEncryptionResultBatch(String.join(",", decryptedPhoneList));
            log.info("任务"+aiOutboundPhonesSync.getTaskId()+","+aiOutboundPhonesSync.getSign()+"加密手机号耗时耗时："+ ChronoUnit.MILLIS.between(aTime, LocalDateTime.now()));
            aTime = LocalDateTime.now();
            Map<String, MobileBelongResultWrapper> mobileBelongResultMap = mobileBelongCacheService.getMobileBelongResultMap(decryptedPhoneList.stream().map(a -> "MOBILE_BELONG:" + a.substring(0,Math.min(a.length(), 7))).collect(Collectors.toList()));
            for (int i = 0; i < phoneList.size(); i++) {
                JSONObject jsonObject = phoneList.getJSONObject(i);
                String mobile = (String)jsonObject.get("mobile");
                PhoneRecord phoneRecord = phoneRecordService.createNewPhoneRecord(mobile, encryptionResultBatch == null ? mobile :encryptionResultBatch.getString(mobile), aiOutboundTask.getId().toString(),aiOutboundTask.getTaskName(), mobileBelongResultMap);
                String recordId = UUID.randomUUID().toString();
                recordId = "PT1_" + recordId;
                phoneRecord.setRecordId(recordId);
                phoneRecord.setLatestRecordId(recordId);
                phoneRecord.setScriptStringId(aiOutboundTask.getScriptStringId());
                phoneRecord.setScriptName(aiOutboundTask.getSpeechCraftName());
                phoneRecord.setVersion(aiOutboundTask.getVersion());
                phoneRecord.setScriptLongId(aiOutboundTask.getSpeechCraftId());
                phoneRecord.setTenantName(aiTenant == null ? null : aiTenant.getTenantName());
                CallRecord callRecord = callRecordService.createNewCallRecord(encryptionResultBatch == null ? mobile : encryptionResultBatch.getString(mobile), phoneRecord);
                callRecord.setRecordId(recordId);
                callRecord.setPhoneRecordId(recordId);
                callRecord.setScriptStringId(aiOutboundTask.getScriptStringId());
                callRecord.setSpeechCraftId(aiOutboundTask.getSpeechCraftId());
                callRecord.setTenantName(aiTenant == null ? null : aiTenant.getTenantName());
                callRecord.setTenantCode(aiTenant == null ? null : aiTenant.getTenantNo());
                callRecord.setAccount(admin == null ? null : admin.getAccount());
                callRecord.setTaskName(aiOutboundTask.getTaskName());
                callRecord.setTaskId(aiOutboundTask.getId().toString());
                callRecord.setRedisKey(phoneRecord.getProvince() + ","+phoneRecord.getProvinceCode() +"," + phoneRecord.getCity()+ "," + phoneRecord.getCityCode() + "," + phoneRecord.getOperator());
                callRecord.setIfTest(aiOutboundPhonesSync.getIfTest());
                //String batchId = (String)jsonObject.get("batchId");
                //callRecord.setHitAnswerIds(batchId);
                phones.add(mobile + "," +recordId + "," +phoneRecord.getPhone()+","+ callRecord.getRedisKey());
                phoneRecordSaveList.add(phoneRecord);
                callRecordSaveList.add(callRecord);
            }

            log.info("任务"+aiOutboundPhonesSync.getTaskId()+","+aiOutboundPhonesSync.getSign()+"查询手机号所属地耗时："+ ChronoUnit.MILLIS.between(aTime, LocalDateTime.now()));
            aTime = LocalDateTime.now();
            //异步改成同步
            //phoneRecordSaveProducer.send(phoneRecordWrapper);
            batchService.batchInsert(phoneRecordSaveList);
            //callRecordSaveProducer.send(callRecordWrapper);
            // ai促活recordId前缀设置为PT1 直接保存到 1套数据库
            batchService1.batchInsert(callRecordSaveList);
            log.info("任务"+aiOutboundPhonesSync.getTaskId()+","+aiOutboundPhonesSync.getSign()+"保存名单表,通话记录表耗时："+ ChronoUnit.MILLIS.between(aTime, LocalDateTime.now()));
            aTime = LocalDateTime.now();
            log.info("任务"+aiOutboundPhonesSync.getTaskId()+","+aiOutboundPhonesSync.getSign()+"更新redis中任务耗时："+ ChronoUnit.MILLIS.between(aTime, LocalDateTime.now()));
            aTime = LocalDateTime.now();
            RQueue<Object> queue1 = redissonClient.getQueue(aiOutboundPhonesSync.getTaskId() + "");
            queue1.addAll(phones);
            log.info("任务"+aiOutboundPhonesSync.getTaskId()+","+aiOutboundPhonesSync.getSign()+"同步redis耗时："+ ChronoUnit.MILLIS.between(aTime, LocalDateTime.now()));

            aiTaskWriteService.updatePhoneNum(aiOutboundTask.getId(),phones.size(),0,phones.size(),0,0,0,0, 0);
            return "同步成功";
        }else{
            return "同步失败，没有创建任务";
        }

    }

    @Autowired
    private TenantLineRepository tenantLineRepository;
    public void startTask(String taskId,String tenantLineId){
        Optional<AIOutboundTask> optional = aiOutboundTaskRepository.findById(Long.valueOf(taskId));
        optional.ifPresent(new Consumer<AIOutboundTask>() {
            @Override
            public void accept(AIOutboundTask aiOutboundTask) {
                Optional<TenantLine> lineOptional = tenantLineRepository.findById(Long.valueOf(tenantLineId));
                if(aiOutboundTask.getTaskName().contains("ai-促活")&&lineOptional.isPresent()){  //只启动ai促活任务
                    TenantLine tenantLine = lineOptional.get();
                    log.info("===>更新任务状态为进行中");
                    aiTaskWriteService.updateCallStatus("进行中",Long.valueOf(tenantLineId),tenantLine.getLineNumber(),tenantLine.getLineName(),Long.valueOf(taskId));
                }
            }
        });
    }
}
