package com.raipeng.aidata.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Maps;
import com.raipeng.aidata.annotation.RedisLock;
import com.raipeng.aidata.controller.wrapper.ImportPhoneRecordWrapper;
import com.raipeng.aidata.controller.wrapper.MobileBelongResultWrapper;
import com.raipeng.aidata.controller.wrapper.RecordSaveWrapper;
import com.raipeng.aidata.entity.AntChainTaskDTO;
import com.raipeng.aidata.repository.*;
import com.raipeng.aidata.service.mq.*;
import com.raipeng.aidata.utils.HttpClientUtils;
import com.raipeng.aidata.utils.HttpResult;
import com.raipeng.aidata.utils.RaiYiEncryptionUtil;
import com.raipeng.aidata.utils.TenantLineUtils;
import com.raipeng.aidatacommon.constants.Constants;
import com.raipeng.aidatacommon.entity.TaskStopTriggerPojo;
import com.raipeng.aidatacommon.entity.VariableSmsPojo;
import com.raipeng.aidatacommon.entity.VariableSmsValuePojo;
import com.raipeng.aidatacommon.model.*;
import com.raipeng.aidatacommon.model.dto.*;
import com.raipeng.aidatacommon.model.record.CallRecord;
import com.raipeng.common.constant.VariableConstants;
import com.raipeng.common.enums.AIOutboundTaskType;
import com.raipeng.common.enums.ImportType;
import com.raipeng.common.model.dto.TaskStatusNoticeDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.raipeng.aidatacommon.utils.AESUtils.AESEncode;

@Service
@RefreshScope
@Slf4j
public class AIOutboundTaskService {
    @Value("${push.called.record.to.insurance.url:http://localhost:8090/dt-ai/sync}")
    private String pushCalledRecordToInsuranceUrl;

    @Value("${push.called.record.to.finance.url:http://localhost:8090/dt-ai/sync}")
    private String pushCalledRecordToFinanceUrl;

    @Value("${push.called.cost.data.sync.url:http://localhost:8090/dt-ai/cost-data-sync}")
    private String pushCalledCostDataSyncUrl;

    @Value("${push.called.cost.data.sync.finance.url:http://localhost:8090/dt-ai/cost-data-sync}")
    private String pushCalledCostDataSyncFinanceUrl;

    @Value("${push.called.record.to.insurance.accounts:baotai,baotai01}")
    private String pushCalledRecordToInsuranceAccounts;

    @Value("${push.called.record.to.finance.accounts:baotai,baotai01}")
    private String pushCalledRecordToFinanceAccounts;

    @Value("${push.ai.promote.record.to.insurance.url:http://localhost:8090/ai-promote/bz-record-back}")
    private String pushAiPromoteRecordToInsuranceUrl;

    @Value("${ai.data.distribute.call:Y}")
    private String distributeCall;


    @Value("${ai.data.master.call:Y}")
    private String masterCall;

    private static final String REDIS_TASK_TMP = "REDIS_TASK_TMP::";

    @Autowired
    private CallRecordForAIManualService callRecordForAIManualService;

    @Autowired
    private AIOutboundTaskRepository aiOutboundTaskRepository;

    @Autowired
    private AiTaskWriteService aiTaskWriteService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private CallRecordMultiService callRecordMultiService;

    @Autowired
    private PhoneRecordRepository phoneRecordRepository;

    @Autowired
    private PhoneRecordService phoneRecordService;

    @Autowired
    private CallRecordService callRecordService;

    @Autowired
    private AITenantRepository aiTenantRepository;

    @Autowired
    private MobileBelongCacheService mobileBelongCacheService;

    @Autowired
    private AdminRepository adminRepository;

    @Autowired
    private PhoneRecordSaveProducer phoneRecordSaveProducer;

    @Autowired
    private CallRecordSaveProducer callRecordSaveProducer;

    @Autowired
    private CallRecordSaveProducer1 callRecordSaveProducer1;

    @Autowired
    private CallRecordSaveProducer2 callRecordSaveProducer2;

    @Autowired
    private CallRecordSaveProducer3 callRecordSaveProducer3;

    @Autowired
    private TenantLineUtils tenantLineUtils;

    @Autowired
    private GlobalConfigRepository globalConfigRepository;

    @Autowired
    private RequestLogService requestLogService;

    @Autowired
    private TenantSecretsService tenantSecretsService;

    @Autowired
    private AdminService adminService;

    @Autowired
    private AIOutboundTaskTemplateRepository aiOutboundTaskTemplateRepository;

    @Autowired
    private  TenantProgramAdminRepository tenantProgramAdminRepository;


    @Autowired
    private ImportPhoneRecordProducer importPhoneRecordProducer;

    @Autowired
    private ForbiddenWordService forbiddenWordService;

    @Autowired
    private TaskStatusNoticeProducer taskStatusNoticeProducer;

    @Autowired
    private TaskStopControlSaveProducer taskStopControlSaveProducer;

    public String getTaskUuidByNameFromRedis(String name) {
        return (String) redissonClient.getBucket("REDIS_TASK_TMP::" + name + "_id").get();
    }

    public String getTaskTemplateIdByTaskIdFromRedis(String taskId) {
        return (String) redissonClient.getBucket(REDIS_TASK_TMP + taskId + "_templateId").get();
    }

    public String syncPhonesToRedis(AIOutboundPhonesSync aiOutboundPhonesSync, String taskId) {
        String taskType = (String) redissonClient.getBucket(REDIS_TASK_TMP + taskId + "_type").get();
        if (StringUtils.isBlank(taskType) || (!taskType.equals("AI_AUTO") && !taskType.equals("AI_MANUAL"))) {
            return "未找到任务类型";
        }
        if (taskType.equals("AI_AUTO")) {
            return syncPhonesToRedisV2(aiOutboundPhonesSync, taskId);
        } else {
            return syncPhonesToRedisForAIManual(aiOutboundPhonesSync, taskId);
        }
    }

    public String syncPhonesToRedisForAIManual(AIOutboundPhonesSync aiOutboundPhonesSync, String taskId) {
        LocalDateTime aTime = LocalDateTime.now();
        RBucket<Object> bucket = redissonClient.getBucket(REDIS_TASK_TMP + aiOutboundPhonesSync.getTaskIdStr() + "_body");
        String taskBody = (String)bucket.get();
        RAtomicLong atomicLong = redissonClient.getAtomicLong(REDIS_TASK_TMP + aiOutboundPhonesSync.getTaskIdStr() + "_phoneNum");
        if(StringUtils.isBlank(taskBody)){
            log.info("syncPhonesToRedisForAIManual {} {}", taskId, atomicLong);
            return "未找到任务Id,任务可能已被操作";
        }
        JSONObject aiOutboundTask = JSONObject.parseObject(taskBody);
        String text = aiOutboundPhonesSync.getText();
        JSONArray phoneList = JSON.parseArray(text);
        List<String> phones = new ArrayList<>();
        List<PhoneRecord> phoneRecordSaveList = new ArrayList<>();
        List<CallRecordForHumanMachine> callRecordSaveList = new ArrayList<>();
        List<String> decryptedPhoneList = new ArrayList<>();
        Set<String> decryptedPhoneSet = new HashSet<>();
        List<ImportPhoneFail> importFailRecords = new ArrayList<>();

        JSONArray variableSmsTask = aiOutboundTask.getJSONArray("variableSms");
        List<VariableSmsPojo> filteredVariableList = null;
        if(variableSmsTask != null) {
            List<VariableSmsPojo> variableSmsList = variableSmsTask.toJavaList(VariableSmsPojo.class);
            filteredVariableList = variableSmsList.stream()
                    .filter(variable -> !(VariableConstants.SYS_ISSUE_DATE.equals(variable.getVariableName())
                            || VariableConstants.SYS_CITY.equals(variable.getVariableName())
                            || VariableConstants.SYS_LAST_FOUR_DIGITS.equals(variable.getVariableName())))
                    .collect(Collectors.toList());
        }

        Map<String,List<VariableSmsValuePojo>> varValueMap = new HashMap<>();
        for (int i = 0; i < phoneList.size(); i++) {
            JSONObject jsonObject = phoneList.getJSONObject(i);
            String mobile = (String)jsonObject.get("mobile");
            if(mobile.length() > 16){
                log.info("人机协同存在号码大于16位任务" + taskId + " mobile " + mobile );
                return "人机协同存在大于16位号码";
            }
            if(filteredVariableList != null && !filteredVariableList.isEmpty()){
                List<VariableSmsValuePojo> varValueList = new ArrayList<>();
                for(VariableSmsPojo var : filteredVariableList){
                    String varValue = jsonObject.getString(var.getVariableName());
                    if(StringUtils.isNotBlank(varValue)){
                        if (StringUtils.isNotBlank(forbiddenWordService.ifContainsForbiddenWord(varValue))) {
                            ImportPhoneFail importPhoneFail = new ImportPhoneFail();
                            importPhoneFail.setFailReason("变量包含违禁词" + var.getVariableName());
                            jsonObject.remove("mobile");
                            importPhoneFail.setText(jsonObject.toJSONString());
                            importFailRecords.add(importPhoneFail);
                            log.info("短信变量{} 变量包含违禁词 任务{} 号码 {}", var.getVariableName(), taskId, mobile);
                            varValueList = null;
                            break;
                        }else {
                            VariableSmsValuePojo variableSmsValuePojo = new VariableSmsValuePojo();
                            variableSmsValuePojo.setVariableName(var.getVariableName());
                            variableSmsValuePojo.setVariableValue(varValue);
                            varValueList.add(variableSmsValuePojo);
                        }
                    }else{
                        ImportPhoneFail importPhoneFail = new ImportPhoneFail();
                        importPhoneFail.setFailReason("变量未找到" + var.getVariableName());
                        jsonObject.remove("mobile");
                        importPhoneFail.setText(jsonObject.toJSONString());
                        importFailRecords.add(importPhoneFail);
                        log.info("短信变量{} 不存在 任务{} 号码 {}", var.getVariableName(), taskId, mobile);
//                        return "短信变量" + var.getVariableName() + " 不存在 任务" + taskId + " 号码 " + mobile ;
                        varValueList = null;
                        break;
                    }
                }
                if(varValueList != null){
                    varValueMap.put(mobile, varValueList);
                    decryptedPhoneSet.add(mobile);
                }
            }else{
                decryptedPhoneSet.add(mobile);
            }
        }
        AITenant aiTenant = null;
        Admin admin = null;
        if(StringUtils.isNotBlank(aiOutboundTask.getString("groupId"))){
            Optional<AITenant> aiTenantOptional = aiTenantRepository.findById(Long.valueOf(aiOutboundTask.getString("groupId").split("_")[1]));
            aiTenant = aiTenantOptional.orElse(null);
            admin = adminRepository.findFirstById(Long.valueOf(aiOutboundTask.getString("groupId").split("_")[2]));
        }
        decryptedPhoneList = new ArrayList<>(decryptedPhoneSet);
        if(phoneList.size() != decryptedPhoneList.size()){
            log.info("人机任务{} 可能存在重复号码 {}, {}", taskId, phoneList.size(), decryptedPhoneList.size());
        }
        JSONObject encryptionResultBatch = RaiYiEncryptionUtil.getEncryptionResultBatch(String.join(",", decryptedPhoneList));
        log.info("任务"+aiOutboundPhonesSync.getTaskId()+","+aiOutboundPhonesSync.getSign()+"加密手机号耗时耗时："+ ChronoUnit.MILLIS.between(aTime, LocalDateTime.now()));
        aTime = LocalDateTime.now();
        Map<String, MobileBelongResultWrapper> mobileBelongResultMap = mobileBelongCacheService.getMobileBelongResultMap(decryptedPhoneList.stream().map(a -> "MOBILE_BELONG:" + a.substring(0,Math.min(a.length(), 7))).collect(Collectors.toList()));
        for (int i = 0; i < decryptedPhoneList.size(); i++) {
            String mobile = decryptedPhoneList.get(i);
            PhoneRecord phoneRecord = phoneRecordService.createNewPhoneRecord(mobile, encryptionResultBatch == null ? mobile :encryptionResultBatch.getString(mobile), aiOutboundTask.getLong("id").toString(),aiOutboundTask.getString("taskName"), mobileBelongResultMap);
            String recordId = UUID.randomUUID().toString();
            phoneRecord.setRecordId(recordId);
            phoneRecord.setLatestRecordId(recordId);
            phoneRecord.setScriptStringId(aiOutboundTask.getString("scriptStringId"));
            phoneRecord.setScriptName(aiOutboundTask.getString("scriptName"));
            phoneRecord.setVersion(aiOutboundTask.getInteger("version"));
            phoneRecord.setScriptLongId(aiOutboundTask.getLong("scriptLongId"));
            phoneRecord.setTenantName(aiTenant == null ? null : aiTenant.getTenantName());
            phoneRecord.setVariableSmsValue(varValueMap.get(mobile));
            CallRecordForHumanMachine callRecordForHumanMachine = callRecordForAIManualService.createNewCallRecord(encryptionResultBatch == null ? mobile : encryptionResultBatch.getString(mobile), phoneRecord);
            callRecordForHumanMachine.setRecordId(recordId);
            callRecordForHumanMachine.setPhoneRecordId(recordId);
            callRecordForHumanMachine.setScriptStringId(aiOutboundTask.getString("scriptStringId"));
            callRecordForHumanMachine.setSpeechCraftId(aiOutboundTask.getLong("speechCraftId"));
            callRecordForHumanMachine.setTenantName(aiTenant == null ? null : aiTenant.getTenantName());
            callRecordForHumanMachine.setTenantCode(aiTenant == null ? null : aiTenant.getTenantNo());
            callRecordForHumanMachine.setAccount(admin == null ? null : admin.getAccount());
            callRecordForHumanMachine.setTaskName(aiOutboundTask.getString("taskName"));
            callRecordForHumanMachine.setTaskId(aiOutboundTask.getLong("id").toString());
            callRecordForHumanMachine.setRedisKey(phoneRecord.getProvince() + ","+phoneRecord.getProvinceCode() +"," + phoneRecord.getCity()+ "," + phoneRecord.getCityCode() + "," + phoneRecord.getOperator());
            callRecordForHumanMachine.setIfTest(aiOutboundPhonesSync.getIfTest());
            if(distributeCall.equals("Y")) {
                int hasCode = Math.abs(recordId.hashCode());
                String distributedRecordId = "";
                if (hasCode % 3 == 0) {
                    distributedRecordId = "PT1_" + recordId;
                    phoneRecord.setRecordId(distributedRecordId);
                    phoneRecord.setLatestRecordId(distributedRecordId);
                    callRecordForHumanMachine.setRecordId(distributedRecordId);
                    callRecordForHumanMachine.setPhoneRecordId(distributedRecordId);
                } else if (hasCode % 3 == 1) {
                    distributedRecordId = "PT2_" + recordId;
                    phoneRecord.setRecordId(distributedRecordId);
                    phoneRecord.setLatestRecordId(distributedRecordId);
                    callRecordForHumanMachine.setRecordId(distributedRecordId);
                    callRecordForHumanMachine.setPhoneRecordId(distributedRecordId);
                } else {
                    distributedRecordId = "PT3_" + recordId;
                    phoneRecord.setRecordId(distributedRecordId);
                    phoneRecord.setLatestRecordId(distributedRecordId);
                    callRecordForHumanMachine.setRecordId(distributedRecordId);
                    callRecordForHumanMachine.setPhoneRecordId(distributedRecordId);
                }
                phones.add(mobile + "," +distributedRecordId + "," +phoneRecord.getPhone()+","+ callRecordForHumanMachine.getRedisKey());
            }else {
                phones.add(mobile + "," + recordId + "," + phoneRecord.getPhone() + "," + callRecordForHumanMachine.getRedisKey());
            }
            phoneRecordSaveList.add(phoneRecord);
            callRecordSaveList.add(callRecordForHumanMachine);
        }
        RAtomicLong totalPhoneNumR = redissonClient.getAtomicLong(REDIS_TASK_TMP + "totalPhoneNum");
        totalPhoneNumR.addAndGet(decryptedPhoneList.size());
        log.info("任务"+aiOutboundPhonesSync.getTaskId()+","+aiOutboundPhonesSync.getSign()+"查询手机号所属地耗时："+ ChronoUnit.MILLIS.between(aTime, LocalDateTime.now()));
        aTime = LocalDateTime.now();
        RecordSaveWrapper phoneRecordWrapper = new RecordSaveWrapper();
        phoneRecordWrapper.setPhoneRecordList(phoneRecordSaveList);
        phoneRecordWrapper.setIsForAI(false);
        phoneRecordWrapper.setTaskId(taskId);
        phoneRecordSaveProducer.send(phoneRecordWrapper);
        RecordSaveWrapper callRecordWrapper = new RecordSaveWrapper();
        callRecordWrapper.setCallRecordForHumanMachineList(callRecordSaveList);
        callRecordWrapper.setIsForAI(false);
        callRecordWrapper.setTaskId(taskId);
        callRecordSaveProducer.send(callRecordWrapper);
//        log.info("任务"+aiOutboundPhonesSync.getTaskId()+","+aiOutboundPhonesSync.getSign()+"保存名单表,通话记录表耗时："+ ChronoUnit.MILLIS.between(aTime, LocalDateTime.now()));
//        aTime = LocalDateTime.now();
        atomicLong.addAndGet(decryptedPhoneList.size());

//        log.info("任务"+aiOutboundPhonesSync.getTaskId()+","+aiOutboundPhonesSync.getSign()+"更新redis中任务耗时："+ ChronoUnit.MILLIS.between(aTime, LocalDateTime.now()));
//        aTime = LocalDateTime.now();
        RQueue<Object> queue1 = redissonClient.getQueue(aiOutboundPhonesSync.getTaskId() + "");
        queue1.addAll(phones);
        log.info("任务"+aiOutboundPhonesSync.getTaskId()+","+aiOutboundPhonesSync.getSign()+"同步redis耗时："+ ChronoUnit.MILLIS.between(aTime, LocalDateTime.now()));

        ImportPhoneRecordWrapper importPhoneRecordWrapper = new ImportPhoneRecordWrapper();
        ImportPhoneRecord importPhoneRecord = new ImportPhoneRecord();
        importPhoneRecord.setImportType(ImportType.API);
        int validCount = decryptedPhoneList.size();
        int invalidCount = importFailRecords.size();
        importPhoneRecord.setImportSuccessCount(validCount);
        importPhoneRecord.setImportFailCount(invalidCount);
        importPhoneRecord.setTaskType(AIOutboundTaskType.AI_MANUAL);
        importPhoneRecord.setGroupId(aiOutboundTask.getString("groupId"));
        importPhoneRecord.setTaskId(taskId);
        importPhoneRecord.setTaskName(aiOutboundTask.getString("taskName"));
        importPhoneRecordWrapper.setImportPhoneRecord(importPhoneRecord);
        importPhoneRecordWrapper.setImportFailRecords(importFailRecords);
        importPhoneRecordProducer.send(importPhoneRecordWrapper);
        return "同步成功";
    }

    public String syncPhonesToRedisV2(AIOutboundPhonesSync aiOutboundPhonesSync, String taskId){
        LocalDateTime aTime = LocalDateTime.now();
        RBucket<Object> bucket = redissonClient.getBucket(REDIS_TASK_TMP + aiOutboundPhonesSync.getTaskIdStr() + "_body");
        String taskBody = (String)bucket.get();
        RAtomicLong atomicLong = redissonClient.getAtomicLong(REDIS_TASK_TMP + aiOutboundPhonesSync.getTaskIdStr() + "_phoneNum");
        if(StringUtils.isBlank(taskBody)){
            log.info("syncPhonesToRedisV2 {} {}", taskId, atomicLong);
            return "未找到任务Id,任务可能已被操作";
        }
        JSONObject aiOutboundTask = JSONObject.parseObject(taskBody);
        String text = aiOutboundPhonesSync.getText();
        //String decryptText = AESUtils.AESDecode(aesSecret, text);
        JSONArray phoneList = JSON.parseArray(text);
        List<String> phones = new ArrayList<>();
        List<PhoneRecord> phoneRecordSaveList = new ArrayList<>();
        List<CallRecord> callRecordSaveList = new ArrayList<>();
        List<String> decryptedPhoneList = new ArrayList<>();
        Set<String> decryptedPhoneSet = new HashSet<>();
        List<CallRecord> callRecordSave1 = new ArrayList<>();
        List<CallRecord> callRecordSave2 = new ArrayList<>();
        List<CallRecord> callRecordSave3 = new ArrayList<>();
        List<ImportPhoneFail> importFailRecords = new ArrayList<>();

        JSONArray variableSmsTask = aiOutboundTask.getJSONArray("variableSms");
        List<VariableSmsPojo> filteredVariableList = null;
        if(variableSmsTask != null) {
            List<VariableSmsPojo> variableSmsList = variableSmsTask.toJavaList(VariableSmsPojo.class);
            filteredVariableList = variableSmsList.stream()
                    .filter(variable -> !(VariableConstants.SYS_ISSUE_DATE.equals(variable.getVariableName())
                            || VariableConstants.SYS_CITY.equals(variable.getVariableName())
                            || VariableConstants.SYS_LAST_FOUR_DIGITS.equals(variable.getVariableName())))
                    .collect(Collectors.toList());
            log.info("需要变量 {}", filteredVariableList);
        }
        Map<String,List<VariableSmsValuePojo>> varValueMap = new HashMap<>();
        for (int i = 0; i < phoneList.size(); i++) {
            JSONObject jsonObject = phoneList.getJSONObject(i);
            String mobile = (String)jsonObject.get("mobile");
            if(mobile.length() > 16){
                log.info("存在号码大于16任务" + taskId + " mobile " + mobile );
                return "存在大于16位号码";
            }
            if (filteredVariableList != null && !filteredVariableList.isEmpty()) {
                List<VariableSmsValuePojo> varValueList = new ArrayList<>();
                for (VariableSmsPojo var : filteredVariableList) {
                    String varValue = jsonObject.getString(var.getVariableName());
                    if (StringUtils.isNotBlank(varValue)) {
                        if (StringUtils.isNotBlank(forbiddenWordService.ifContainsForbiddenWord(varValue))) {
                            ImportPhoneFail importPhoneFail = new ImportPhoneFail();
                            importPhoneFail.setFailReason("变量包含违禁词" + var.getVariableName());
                            jsonObject.remove("mobile");
                            importPhoneFail.setText(jsonObject.toJSONString());
                            importFailRecords.add(importPhoneFail);
                            log.info("短信变量{} 变量包含违禁词 任务{} 号码 {}", var.getVariableName(), taskId, mobile);
                            varValueList = null;
                            break;
                        } else {
                            VariableSmsValuePojo variableSmsValuePojo = new VariableSmsValuePojo();
                            variableSmsValuePojo.setVariableName(var.getVariableName());
                            variableSmsValuePojo.setVariableValue(varValue);
                            varValueList.add(variableSmsValuePojo);
                        }
                    } else {
                        ImportPhoneFail importPhoneFail = new ImportPhoneFail();
                        importPhoneFail.setFailReason("变量未找到" + var.getVariableName());
                        jsonObject.remove("mobile");
                        importPhoneFail.setText(jsonObject.toJSONString());
                        importFailRecords.add(importPhoneFail);
                        log.info("短信变量{} 不存在 任务{} 号码 {}", var.getVariableName(), taskId, mobile);
                        varValueList = null;
                        break;
                    }
                }
                if(varValueList != null){
                    varValueMap.put(mobile, varValueList);
                    decryptedPhoneSet.add(mobile);
                }
            }else{
                decryptedPhoneSet.add(mobile);
            }

        }
        AITenant aiTenant = null;
        Admin admin = null;
        if(StringUtils.isNotBlank(aiOutboundTask.getString("groupId"))){
            Optional<AITenant> aiTenantOptional = aiTenantRepository.findById(Long.valueOf(aiOutboundTask.getString("groupId").split("_")[1]));
            aiTenant = aiTenantOptional.orElse(null);
            admin = adminRepository.findFirstById(Long.valueOf(aiOutboundTask.getString("groupId").split("_")[2]));
        }
        decryptedPhoneList = new ArrayList<>(decryptedPhoneSet);
        if(phoneList.size() != decryptedPhoneList.size()){
            log.info("纯AI任务{} 可能存在重复号码 {}, {}", taskId, phoneList.size(), decryptedPhoneList.size());
        }
        JSONObject encryptionResultBatch = RaiYiEncryptionUtil.getEncryptionResultBatch(String.join(",", decryptedPhoneList));
        //JSONObject encryptionResultBatch = null;
        log.info("任务"+aiOutboundPhonesSync.getTaskId()+","+aiOutboundPhonesSync.getSign()+"加密手机号耗时耗时："+ ChronoUnit.MILLIS.between(aTime, LocalDateTime.now()));
        aTime = LocalDateTime.now();
        Map<String, MobileBelongResultWrapper> mobileBelongResultMap = mobileBelongCacheService.getMobileBelongResultMap(decryptedPhoneList.stream().map(a -> "MOBILE_BELONG:" + a.substring(0,Math.min(a.length(), 7))).collect(Collectors.toList()));
        for (int i = 0; i < decryptedPhoneList.size(); i++) {
            String mobile = decryptedPhoneList.get(i);
            PhoneRecord phoneRecord = phoneRecordService.createNewPhoneRecord(mobile, encryptionResultBatch == null ? mobile :encryptionResultBatch.getString(mobile), aiOutboundTask.getLong("id").toString(),aiOutboundTask.getString("taskName"), mobileBelongResultMap);
            String recordId = UUID.randomUUID().toString();
            phoneRecord.setRecordId(recordId);
            phoneRecord.setLatestRecordId(recordId);
            phoneRecord.setScriptStringId(aiOutboundTask.getString("scriptStringId"));
            phoneRecord.setScriptName(aiOutboundTask.getString("scriptName"));
            phoneRecord.setVersion(aiOutboundTask.getInteger("version"));
            phoneRecord.setScriptLongId(aiOutboundTask.getLong("scriptLongId"));
            phoneRecord.setTenantName(aiTenant == null ? null : aiTenant.getTenantName());
            phoneRecord.setVariableSmsValue(varValueMap.get(mobile));
            CallRecord callRecord = callRecordService.createNewCallRecord(encryptionResultBatch == null ? mobile : encryptionResultBatch.getString(mobile), phoneRecord);
            callRecord.setRecordId(recordId);
            callRecord.setPhoneRecordId(recordId);
            callRecord.setScriptStringId(aiOutboundTask.getString("scriptStringId"));
            callRecord.setSpeechCraftId(aiOutboundTask.getLong("speechCraftId"));
            callRecord.setTenantName(aiTenant == null ? null : aiTenant.getTenantName());
            callRecord.setTenantCode(aiTenant == null ? null : aiTenant.getTenantNo());
            callRecord.setAccount(admin == null ? null : admin.getAccount());
            callRecord.setTaskName(aiOutboundTask.getString("taskName"));
            callRecord.setTaskId(aiOutboundTask.getLong("id").toString());
            callRecord.setRedisKey(phoneRecord.getProvince() + ","+phoneRecord.getProvinceCode() +"," + phoneRecord.getCity()+ "," + phoneRecord.getCityCode() + "," + phoneRecord.getOperator());
            callRecord.setIfTest(aiOutboundPhonesSync.getIfTest());

            if(distributeCall.equals("Y")) {
                int hasCode = Math.abs(recordId.hashCode());
                String distributedRecordId = "";
                if (hasCode % 3 == 0) {
                    distributedRecordId = "PT1_" + recordId;
                    phoneRecord.setRecordId(distributedRecordId);
                    phoneRecord.setLatestRecordId(distributedRecordId);
                    callRecord.setRecordId(distributedRecordId);
                    callRecord.setPhoneRecordId(distributedRecordId);
                    callRecordSave1.add(callRecord);
                } else if (hasCode % 3 == 1) {
                    distributedRecordId = "PT2_" + recordId;
                    phoneRecord.setRecordId(distributedRecordId);
                    phoneRecord.setLatestRecordId(distributedRecordId);
                    callRecord.setRecordId(distributedRecordId);
                    callRecord.setPhoneRecordId(distributedRecordId);
                    callRecordSave2.add(callRecord);
                } else {
                    distributedRecordId = "PT3_" + recordId;
                    phoneRecord.setRecordId(distributedRecordId);
                    phoneRecord.setLatestRecordId(distributedRecordId);
                    callRecord.setRecordId(distributedRecordId);
                    callRecord.setPhoneRecordId(distributedRecordId);
                    callRecordSave3.add(callRecord);
                }
                phones.add(mobile + "," +distributedRecordId + "," +phoneRecord.getPhone()+","+ callRecord.getRedisKey());
            }else {
                phones.add(mobile + "," + recordId + "," + phoneRecord.getPhone() + "," + callRecord.getRedisKey());
            }
            phoneRecordSaveList.add(phoneRecord);
            callRecordSaveList.add(callRecord);
        }
        RAtomicLong totalPhoneNumR = redissonClient.getAtomicLong(REDIS_TASK_TMP + "totalPhoneNum");
        totalPhoneNumR.addAndGet(decryptedPhoneList.size());
        log.info("任务"+aiOutboundPhonesSync.getTaskId()+","+aiOutboundPhonesSync.getSign()+"查询手机号所属地耗时："+ ChronoUnit.MILLIS.between(aTime, LocalDateTime.now()));
        aTime = LocalDateTime.now();
        RecordSaveWrapper phoneRecordWrapper = new RecordSaveWrapper();
        phoneRecordWrapper.setPhoneRecordList(phoneRecordSaveList);
        phoneRecordWrapper.setTaskId(taskId);
        phoneRecordWrapper.setIsForAI(true);
        phoneRecordSaveProducer.send(phoneRecordWrapper);
        RecordSaveWrapper callRecordWrapper = new RecordSaveWrapper();
        callRecordWrapper.setCallRecordList(callRecordSaveList);
        callRecordWrapper.setIsForAI(true);
        if(masterCall.equals("Y")){
            callRecordSaveProducer.send(callRecordWrapper);
        }
        if(distributeCall.equals("Y")) {
            if(!callRecordSave1.isEmpty()){
                RecordSaveWrapper callRecordWrapper1 = new RecordSaveWrapper();
                callRecordWrapper1.setCallRecordList(callRecordSave1);
                callRecordWrapper1.setTaskId(taskId);
                callRecordSaveProducer1.send(callRecordWrapper1);
            }
            if(!callRecordSave2.isEmpty()){
                RecordSaveWrapper callRecordWrapper2 = new RecordSaveWrapper();
                callRecordWrapper2.setCallRecordList(callRecordSave2);
                callRecordWrapper2.setTaskId(taskId);
                callRecordSaveProducer2.send(callRecordWrapper2);
            }
            if(!callRecordSave3.isEmpty()){
                RecordSaveWrapper callRecordWrapper3 = new RecordSaveWrapper();
                callRecordWrapper3.setCallRecordList(callRecordSave3);
                callRecordWrapper3.setTaskId(taskId);
                callRecordSaveProducer3.send(callRecordWrapper3);
            }
        }
//        log.info("任务"+aiOutboundPhonesSync.getTaskId()+","+aiOutboundPhonesSync.getSign()+"保存名单表,通话记录表耗时："+ ChronoUnit.MILLIS.between(aTime, LocalDateTime.now()));
//        aTime = LocalDateTime.now();
        atomicLong.addAndGet(decryptedPhoneList.size());

//        log.info("任务"+aiOutboundPhonesSync.getTaskId()+","+aiOutboundPhonesSync.getSign()+"更新redis中任务耗时："+ ChronoUnit.MILLIS.between(aTime, LocalDateTime.now()));
//        aTime = LocalDateTime.now();
        RQueue<Object> queue1 = redissonClient.getQueue(aiOutboundPhonesSync.getTaskId() + "");
        queue1.addAll(phones);
        log.info("任务"+aiOutboundPhonesSync.getTaskId()+","+aiOutboundPhonesSync.getSign()+"同步redis耗时："+ ChronoUnit.MILLIS.between(aTime, LocalDateTime.now()));

        ImportPhoneRecordWrapper importPhoneRecordWrapper = new ImportPhoneRecordWrapper();
        ImportPhoneRecord importPhoneRecord = new ImportPhoneRecord();
        importPhoneRecord.setImportType(ImportType.API);
        int validCount = decryptedPhoneList.size();
        int invalidCount = importFailRecords.size();
        importPhoneRecord.setImportSuccessCount(validCount);
        importPhoneRecord.setImportFailCount(invalidCount);
        importPhoneRecord.setTaskType(AIOutboundTaskType.AI_AUTO);
        importPhoneRecord.setGroupId(aiOutboundTask.getString("groupId"));
        importPhoneRecord.setTaskId(taskId);
        importPhoneRecord.setTaskName(aiOutboundTask.getString("taskName"));
        importPhoneRecordWrapper.setImportPhoneRecord(importPhoneRecord);
        importPhoneRecordWrapper.setImportFailRecords(importFailRecords);
        importPhoneRecordProducer.send(importPhoneRecordWrapper);
        return "同步成功";
    }

    @RedisLock(paramName = "taskName")
    public synchronized Long createTask(Long id, String taskName){
        AIOutboundTask aiOutboundTask = new AIOutboundTask();
        if(StringUtils.isNotBlank(taskName)){
            AIOutboundTask ifExistTask = aiOutboundTaskRepository.findFirstByTaskNameAndCreateTimeBetween(taskName, LocalDate.now().atStartOfDay(), LocalDate.now().atStartOfDay().plusDays(1L));
            if(ifExistTask!=null){
                return ifExistTask.getId();
            }
        }
        AIOutboundTaskTemplate aiOutboundTaskTemplate = aiOutboundTaskTemplateRepository.findById(id).get();
        BeanUtils.copyProperties(aiOutboundTaskTemplate, aiOutboundTask);
        RAtomicLong max_task_id = redissonClient.getAtomicLong("MAX_TASK_ID");
        long taskId = max_task_id.addAndGet(1);
        aiOutboundTask.setId(taskId);
        if(StringUtils.isNotBlank(taskName)){
            aiOutboundTask.setTaskName(taskName);
        }else{
            taskName = aiOutboundTaskTemplate.getTaskName();
            Integer taskIncrId = aiOutboundTaskTemplate.getTaskIncrId();
            taskIncrId = taskIncrId == null ? 1 : taskIncrId + 1;
            taskName = taskName + "_" + taskIncrId;
            aiOutboundTaskTemplate.setTaskIncrId(taskIncrId);
            aiOutboundTask.setTaskName(taskName);
        }
        aiOutboundTask.setCallStatus("待执行");
        aiOutboundTask.setAiAnswerNum(1);
        aiOutboundTask.setPhoneNum(0);
        aiOutboundTask.setCalledPhoneNum(0);
        aiOutboundTask.setPutThroughPhoneNum(0);
        aiOutboundTask.setFinishedPhoneNum(0);
        aiOutboundTask.setPhoneIntentionNum(0);
        aiOutboundTask.setAClassNum(0);
        aiOutboundTask.setCallRecordNum(0);
        aiOutboundTask.setGroupId(aiOutboundTaskTemplate.getGroupId());
        aiOutboundTask.setTenantName(aiOutboundTaskTemplate.getTenantName());
        aiOutboundTask.setFeeMinute(0);
        aiOutboundTask.setIsAutoStop(0);
        aiTaskWriteService.initTask(aiOutboundTask);
        aiOutboundTaskTemplateRepository.save(aiOutboundTaskTemplate);
        return aiOutboundTask.getId();
    }

    @RedisLock(paramName = "taskName")
    public synchronized String createTaskInRedis(Long id, String taskName){
        AIOutboundTask aiOutboundTask = new AIOutboundTask();
        String uuId = taskName + "_" + id;
        RBucket<Object> taskIdBucket = redissonClient.getBucket(REDIS_TASK_TMP + uuId + "_id");

        String taskIdStr = (String)taskIdBucket.get();
        if(StringUtils.isNotBlank(taskIdStr)){
            return taskIdStr;
        }
        RSet<String> taskNameKeyList = redissonClient.getSet(REDIS_TASK_TMP + "taskNameKeyList");
        taskNameKeyList.add(REDIS_TASK_TMP + uuId + "_id");

        AIOutboundTaskTemplate aiOutboundTaskTemplate = aiOutboundTaskTemplateRepository.findById(id).get();
        RAtomicLong max_task_id = redissonClient.getAtomicLong("MAX_TASK_ID");
        BeanUtils.copyProperties(aiOutboundTaskTemplate, aiOutboundTask);
        long taskId = max_task_id.addAndGet(1);
        aiOutboundTask.setId(taskId);
        String groupId = aiOutboundTaskTemplate.getGroupId();
        TenantProgramAdmin tenantProgramAdmin = tenantProgramAdminRepository.findFirstByGroupId(groupId);
        if(tenantProgramAdmin != null){
            aiOutboundTask.setProductId(tenantProgramAdmin.getProductId());
            aiOutboundTask.setIndustrySecondFieldId(tenantProgramAdmin.getSecondIndustryId());
            aiOutboundTask.setProgramId(tenantProgramAdmin.getId().toString());
        }
        if(StringUtils.isNotBlank(taskName)){
            aiOutboundTask.setTaskName(taskName);
        }else{
            taskName = aiOutboundTaskTemplate.getTaskName();
            Integer taskIncrId = aiOutboundTaskTemplate.getTaskIncrId();
            taskIncrId = taskIncrId == null ? 1 : taskIncrId + 1;
            taskName = taskName + "_" + taskIncrId;
            aiOutboundTaskTemplate.setTaskIncrId(taskIncrId);
            aiOutboundTask.setTaskName(taskName);
        }

        setRestrictLocation(aiOutboundTask, aiOutboundTaskTemplate);
        if(aiOutboundTaskTemplate.getScriptSms() != null){
            aiOutboundTask.setScriptSms(aiOutboundTaskTemplate.getScriptSms());
        }
        if(aiOutboundTaskTemplate.getHangUpSms() != null){
            aiOutboundTask.setHangUpSms(aiOutboundTaskTemplate.getHangUpSms());
        }
        if(aiOutboundTaskTemplate.getVariableSms() != null){
            aiOutboundTask.setVariableSms(aiOutboundTaskTemplate.getVariableSms());
        }

        aiOutboundTask.setCallStatus("待执行");
        aiOutboundTask.setAiAnswerNum(1);
        aiOutboundTask.setPhoneNum(0);
        aiOutboundTask.setCalledPhoneNum(0);
        aiOutboundTask.setPutThroughPhoneNum(0);
        aiOutboundTask.setFinishedPhoneNum(0);
        aiOutboundTask.setPhoneIntentionNum(0);
        aiOutboundTask.setAClassNum(0);

        aiOutboundTask.setCallRecordNum(0);
        aiOutboundTask.setGroupId(aiOutboundTaskTemplate.getGroupId());
        aiOutboundTask.setTenantName(aiOutboundTaskTemplate.getTenantName());
        aiOutboundTask.setFeeMinute(0);
        aiOutboundTask.setIsAutoStop(0);//未止损
        aiOutboundTask.setTemplateId(String.valueOf(id));
        aiOutboundTask.setTenantBlackList(aiOutboundTaskTemplate.getTenantBlackList());
        String taskBody = JSONObject.toJSONString(aiOutboundTask);

        taskIdBucket.set(String.valueOf(taskId));
        redissonClient.getBucket(REDIS_TASK_TMP + taskId + "_body").set(taskBody);
        RAtomicLong phoneNum = redissonClient.getAtomicLong(REDIS_TASK_TMP + taskId + "_phoneNum");
        taskNameKeyList.add(REDIS_TASK_TMP + taskId + "_body");
        taskNameKeyList.add(REDIS_TASK_TMP + taskId + "_phoneNum");
        phoneNum.set(0);
        redissonClient.getBucket(REDIS_TASK_TMP + taskId + "_type").set(aiOutboundTaskTemplate.getTaskType().toString());
        redissonClient.getBucket(REDIS_TASK_TMP + taskId + "_templateId").set(id.toString());
        if(aiOutboundTaskTemplate.getTaskStopRule() != null){
            AIOutboundTaskStopControl aiOutboundTaskStopControl = new AIOutboundTaskStopControl();
            aiOutboundTaskStopControl.setTaskId(taskId);
            aiOutboundTaskStopControl.setTaskName(taskName);
            TaskStopTriggerPojo taskStopRule = aiOutboundTaskTemplate.getTaskStopRule();
            aiOutboundTaskStopControl.setCalledPhoneNumTrigger(taskStopRule.getCalledPhoneNumTrigger());
            aiOutboundTaskStopControl.setPutThroughPhoneNumTrigger(taskStopRule.getPutThroughPhoneNumTrigger());
            aiOutboundTaskStopControl.setFinishedPhoneRateTrigger(taskStopRule.getFinishedPhoneRateTrigger() == null ? null : BigDecimal.valueOf(taskStopRule.getFinishedPhoneRateTrigger()));
            aiOutboundTaskStopControl.setReachRateTrigger(taskStopRule.getReachRateTrigger() == null ? null : BigDecimal.valueOf(taskStopRule.getReachRateTrigger()));
            aiOutboundTaskStopControl.setAClassNumTrigger(taskStopRule.getAClassNumTrigger());
            aiOutboundTaskStopControl.setStopTimeTrigger(taskStopRule.getStopTimeTrigger());
            aiOutboundTaskStopControl.setStopType("TASK");
            aiOutboundTaskStopControl.setGroupId(aiOutboundTaskTemplate.getGroupId());
            aiOutboundTaskStopControl.setStopControlStatus(taskStopRule.getStopControlStatus());
            taskStopControlSaveProducer.send(aiOutboundTaskStopControl);
        }
        return String.valueOf(taskId);
    }

    /**
     * 定时30分钟执行一次-只推送ABCD类记录
     */
    public void pushABCDRecord(LocalDateTime endTime,String types){
        GlobalConfig config = globalConfigRepository.getFirstByKey(Constants.PUSH_ABCD_RECORD);
        LocalDateTime startTime = LocalDateTime.parse(config.getValue(),DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        try{
            Long start = 0L;
            Long end = 0L;
            do{
                start = System.currentTimeMillis();
                List<CallRecord> records = callRecordMultiService.findByCallOutTimeBetween(startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),startTime.plusMinutes(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                        Arrays.asList(types.split(",")),Arrays.asList(pushCalledRecordToInsuranceAccounts.split(",")));
                List<PushCalledRecordToInsuranceDto> pushDto = Lists.newArrayList();
                if(CollectionUtils.isNotEmpty(records)){
                    for (CallRecord record : records) {
                        if(StringUtils.isNotEmpty(record.getLineCode())&&
                                StringUtils.isNotEmpty(record.getProvince())&&
                                StringUtils.isNotEmpty(record.getCity())
                        ){
                            PushCalledRecordToInsuranceDto dto = new PushCalledRecordToInsuranceDto();
                            BeanUtils.copyProperties(record,dto);
                            dto.setMerchantCode(record.getMerchantLineCode());
                            dto.setDate(record.getCreateTime().toLocalDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                            pushDto.add(dto);
                        }
                    }
                }
                if(pushDto.size()>0){
                    HttpResult httpResult = HttpClientUtils.doPostJson(pushCalledRecordToInsuranceUrl,JSON.toJSONString(pushDto),1000);
                    if(!httpResult.isSuccess()){
                        httpResult = HttpClientUtils.doPostJson(pushCalledRecordToInsuranceUrl,JSON.toJSONString(pushDto),1000);
                        if(!httpResult.isSuccess()){
                            httpResult = HttpClientUtils.doPostJson(pushCalledRecordToInsuranceUrl,JSON.toJSONString(pushDto),1000);
                        }else{
                            DingDingService.dingDingSendMsgException("推送意向数据给insurance出错1","时间："+startTime);
                        }
                    }
                }

                config.setValue(startTime.plusMinutes(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                end = System.currentTimeMillis();
                log.info("===>同步有效ABCD记录,耗时=" + (end-start));
                startTime = startTime.plusMinutes(1);

            }while(startTime.compareTo(endTime)<=0);
        }catch (Exception e){
            e.printStackTrace();
            DingDingService.dingDingSendMsgException("推送意向数据给insurance出错2","时间："+startTime);
        }finally {
            globalConfigRepository.save(config);
        }
    }

    public void pushABCDRecordFinance(LocalDateTime endTime,String types){
        GlobalConfig config = globalConfigRepository.getFirstByKey(Constants.PUSH_ABCD_RECORD_FINANCE);
        LocalDateTime startTime = LocalDateTime.parse(config.getValue(),DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        try{
            Long start = 0L;
            Long end = 0L;
            do{
                start = System.currentTimeMillis();
                List<CallRecord> records = callRecordMultiService.findByCallOutTimeBetween(startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),startTime.plusMinutes(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                        Arrays.asList(types.split(",")),Arrays.asList(pushCalledRecordToFinanceAccounts.split(",")));
                List<PushCalledRecordToInsuranceDto> pushDto = Lists.newArrayList();
                if(CollectionUtils.isNotEmpty(records)){
                    for (CallRecord record : records) {
                        if(StringUtils.isNotEmpty(record.getLineCode())&&
                                StringUtils.isNotEmpty(record.getProvince())&&
                                StringUtils.isNotEmpty(record.getCity())
                        ){
                            PushCalledRecordToInsuranceDto dto = new PushCalledRecordToInsuranceDto();
                            BeanUtils.copyProperties(record,dto);
                            dto.setMerchantCode(record.getMerchantLineCode());
                            dto.setDate(record.getCreateTime().toLocalDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                            pushDto.add(dto);
                        }
                    }
                }
                if(pushDto.size()>0){
                    HttpResult httpResult = HttpClientUtils.doPostJson(pushCalledRecordToFinanceUrl,JSON.toJSONString(pushDto),1000);
                    if(!httpResult.isSuccess()){
                        httpResult = HttpClientUtils.doPostJson(pushCalledRecordToFinanceUrl,JSON.toJSONString(pushDto),1000);
                        if(!httpResult.isSuccess()){
                            httpResult = HttpClientUtils.doPostJson(pushCalledRecordToFinanceUrl,JSON.toJSONString(pushDto),1000);
                        }else{
                            DingDingService.dingDingSendMsgException("推送意向数据给Finance出错1","时间："+startTime);
                        }
                    }
                }

                config.setValue(startTime.plusMinutes(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                end = System.currentTimeMillis();
                log.info("===>同步有效ABCD记录,耗时=" + (end-start));
                startTime = startTime.plusMinutes(1);

            }while(startTime.compareTo(endTime)<=0);
        }catch (Exception e){
            e.printStackTrace();
            DingDingService.dingDingSendMsgException("推送意向数据给Finance出错2","时间："+startTime);
        }finally {
            globalConfigRepository.save(config);
        }
    }

    @Data
    public static class PushData{
        private List<PushAiPromoteRecordToInsuranceDto> list;
    }

    public void pushAiPromoteRecord(LocalDateTime endTime){
        GlobalConfig config = globalConfigRepository.getFirstByKey(Constants.PUSH_AI_PROMOTE_RECORD);
        LocalDateTime startTime = LocalDateTime.parse(config.getValue(),DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        try{
            Long start = 0L;
            Long end = 0L;
            do{
                start = System.currentTimeMillis();
                List<CallRecord> records = callRecordMultiService
                        .findByCallOutTimeBetweenAndTaskName(startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),startTime.plusMinutes(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),"ai-促活");
                List<PushAiPromoteRecordToInsuranceDto> pushDto = Lists.newArrayList();
                if(CollectionUtils.isNotEmpty(records)){
                    for (CallRecord record : records) {
                        PushAiPromoteRecordToInsuranceDto dto = new PushAiPromoteRecordToInsuranceDto();
                        BeanUtils.copyProperties(record,dto);
                        dto.setUuid(record.getCallId());
                        dto.setProjectname(record.getTaskName());
                        dto.setAutocalltime(record.getCallOutTime());
                        dto.setBillsec(record.getCallDurationSec());
                        pushDto.add(dto);
                    }
                }
                if(pushDto.size()>0){
                    HttpResult httpResult = HttpClientUtils.doPostJson(pushAiPromoteRecordToInsuranceUrl,JSON.toJSONString(pushDto),1000);
                    if(!httpResult.isSuccess()){
                        httpResult = HttpClientUtils.doPostJson(pushAiPromoteRecordToInsuranceUrl,JSON.toJSONString(pushDto),1000);
                        if(!httpResult.isSuccess()){
                            httpResult = HttpClientUtils.doPostJson(pushAiPromoteRecordToInsuranceUrl,JSON.toJSONString(pushDto),1000);
                        }else{
                            DingDingService.dingDingSendMsgException("推送意向数据给insurance出错1","时间："+startTime);
                        }
                    }
                }

                config.setValue(startTime.plusMinutes(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                end = System.currentTimeMillis();
                log.info("===>同步AI-促活到保险侧,耗时=" + (end-start));
                startTime = startTime.plusMinutes(1);

            }while(startTime.compareTo(endTime)<=0);
        }catch (Exception e){
            e.printStackTrace();
            DingDingService.dingDingSendMsgException("推送意向数据给insurance出错2","时间："+startTime);
        }finally {
            globalConfigRepository.save(config);
        }
    }


    public void closeTask(){
        List<AIOutboundTask> list = aiOutboundTaskRepository.findTask(LocalDate.now());
        if(CollectionUtils.isNotEmpty(list)){
            for (AIOutboundTask ele : list) {
                ele.setCallStatus("已停止");
                aiTaskWriteService.closeTaskSave(ele);
            }
        }
    }

    /**
     * 成本数据同步-统计近30分钟的数据
     */
    public void costDataSync(LocalDate callOutTime,LocalDate callOutTime1){
        List<PushCostDataDto> dtoList = callRecordMultiService.findCostData(callOutTime, callOutTime1, Arrays.asList(pushCalledRecordToInsuranceAccounts.split(",")));
        List<PushCostDataDto> tempList = Lists.newArrayList();
        if(dtoList.size()>0){
            for (PushCostDataDto ele : dtoList) {
                tempList.add(ele);
                if(tempList.size()>=1000){
                    send(tempList);
                    tempList.clear();
                }
            }
            if(tempList.size()>0){
                send(tempList);
                tempList.clear();
            }
        }

    }

    public void costDataSyncFinance(LocalDate callOutTime,LocalDate callOutTime1){
        List<PushCostDataDto> dtoList = callRecordMultiService.findCostData(callOutTime,callOutTime1,Arrays.asList(pushCalledRecordToFinanceAccounts.split(",")));
        List<PushCostDataDto> tempList = Lists.newArrayList();
        if(dtoList.size()>0){
            for (PushCostDataDto ele : dtoList) {
                tempList.add(ele);
                if(tempList.size()>=1000){
                    sendFinance(tempList);
                    tempList.clear();
                }
            }
            if(tempList.size()>0){
                sendFinance(tempList);
                tempList.clear();
            }
        }

    }
    private void send(List<PushCostDataDto> tempList) {
        HttpResult httpResult = HttpClientUtils.doPostJson(pushCalledCostDataSyncUrl,JSON.toJSONString(tempList),1000);
        if(!httpResult.isSuccess()){
            httpResult = HttpClientUtils.doPostJson(pushCalledCostDataSyncUrl,JSON.toJSONString(tempList),1000);
            if(!httpResult.isSuccess()){
                httpResult = HttpClientUtils.doPostJson(pushCalledCostDataSyncUrl,JSON.toJSONString(tempList),1000);
            }else{
                DingDingService.dingDingSendMsgException("推送意向数据给insurance出错2-成本统计数据","时间："+ tempList);
            }
        }
    }

    private void sendFinance(List<PushCostDataDto> tempList) {
        HttpResult httpResult = HttpClientUtils.doPostJson(pushCalledCostDataSyncFinanceUrl,JSON.toJSONString(tempList),1000);
        if(!httpResult.isSuccess()){
            httpResult = HttpClientUtils.doPostJson(pushCalledCostDataSyncFinanceUrl,JSON.toJSONString(tempList),1000);
            if(!httpResult.isSuccess()){
                httpResult = HttpClientUtils.doPostJson(pushCalledCostDataSyncFinanceUrl,JSON.toJSONString(tempList),1000);
            }else{
                DingDingService.dingDingSendMsgException("推送意向数据给Finance出错2-成本统计数据","时间："+ tempList);
            }
        }
    }


    public boolean ifInWorkingTime(AIOutboundTask aiOutboundTask){
        aiOutboundTask.setStartWorkTimeList(Arrays.stream(aiOutboundTask.getStartWorkTimes().split(",")).collect(Collectors.toList()));
        aiOutboundTask.setEndWorkTimeList(Arrays.stream(aiOutboundTask.getEndWorkTimes().split(",")).collect(Collectors.toList()));
        List<String> startWorkTimeList = aiOutboundTask.getStartWorkTimeList();
        List<String> endWorkTimeList = aiOutboundTask.getEndWorkTimeList();
        if(startWorkTimeList.size() != endWorkTimeList.size()){
            return false;
        }
        LocalDateTime now = LocalDateTime.now();
        String preFix = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))+" ";
        String subFix = ":00";
        for (int i = 0; i < startWorkTimeList.size(); i++) {
            LocalDateTime sTime = LocalDateTime.parse(preFix+startWorkTimeList.get(i)+subFix, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            LocalDateTime eTime = LocalDateTime.parse(preFix+endWorkTimeList.get(i)+subFix, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            if(now.compareTo(sTime)>=0 && now.compareTo(eTime)<=0){
                return true;
            }
        }
        return false;
    }



    public AIOutboundTask stopTask(AIOutboundQueryDto aiOutboundQueryDto, boolean ifForce){
        AIOutboundTask aiOutboundTask = aiOutboundTaskRepository.findById(aiOutboundQueryDto.getTaskId()).get();
        if("进行中".equals(aiOutboundTask.getCallStatus())){
            if(ifForce){
                aiOutboundTask.setCallStatus("已停止");
            }else{
                Integer totalNum = phoneRecordRepository.findPhoneNums(0, 0, null, aiOutboundQueryDto.getTaskId().toString());
                Integer finishedNum = phoneRecordRepository.findPhoneNums(0, 0, "呼叫完成", aiOutboundQueryDto.getTaskId().toString());
                if(finishedNum >= totalNum){
                    aiOutboundTask.setCallStatus("已停止");
                }else {
                    aiOutboundTask.setCallStatus("未完成");
                }
            }
            if(aiOutboundTask.getLineId() != null){
                boolean ifSuccess = tenantLineUtils.updateRemainConcurrentNumWithCAS(aiOutboundTask.getLineId(), aiOutboundTask.getAiAnswerNum());
            }
            aiOutboundTask.setTaskEndTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }else{
            if(ifForce){
                aiOutboundTask.setCallStatus("已停止");
            }
        }
        aiTaskWriteService.updateStatus(aiOutboundTask.getId(), aiOutboundTask.getCallStatus(), aiOutboundTask.getTaskEndTime());
        sendTaskStatus(String.valueOf(aiOutboundTask.getId()),aiOutboundTask.getTaskName(),aiOutboundTask.getCallStatus(),aiOutboundTask.getGroupId(),"AiData.stopTask");
        return aiOutboundTask;
    }

    public void updateResultToThird(List<CallRecord> callRecordList,String tenantId, String accountId){
        TenantSecrets tenantSecrets = tenantSecretsService.findFirstByTenantId(tenantId);
        Admin admin = adminService.findById(Long.valueOf(accountId));
        if(tenantSecrets == null){
            DingDingService.dingDingSendMsgException("手机号回调8848报错:商户配置不存在");
            log.error("手机号推送8848失败：商户配置不存在！！");
            return;
        }
        if (admin == null) {
            DingDingService.dingDingSendMsgException("手机号回调8848报错:账户不存在");
            log.error("手机号推送8848失败：账户不存在！！");
            return;
        }
        LocalDateTime s = LocalDateTime.now();
        RestTemplate restTemplate = new RestTemplate();
        Map<String, Object> map = new HashMap<>();
        map.put("size",callRecordList.size());
        JSONArray textList = new JSONArray();
        StringBuilder mobileBuilder = new StringBuilder();
        for(CallRecord callRecord : callRecordList){
            mobileBuilder.append(callRecord.getPhone()).append(",");
        }

        if (admin.getIsForEncryptionPhones() != null && admin.getIsForEncryptionPhones()) {
            for(CallRecord callRecord : callRecordList){
                JSONObject callRecordMap = new JSONObject();
                callRecordMap.put("taskId", callRecord.getTaskId());
                callRecordMap.put("talkTime", getCalculateSeconds(callRecord.getCallDuration()));
                callRecordMap.put("customerMobile", callRecord.getPhone());
                callRecordMap.put("interactNum", callRecord.getCycleCount());
                callRecordMap.put("hangupParty", callRecord.getWhoHangup() == null ? null : callRecord.getWhoHangup()+1);
                callRecordMap.put("keypress", callRecord.getIntentionLabels());
                callRecordMap.put("ifUpdate", 1);
                textList.add(callRecordMap);
            }
        } else {
            //解密
            JSONObject jsonObject = RaiYiEncryptionUtil.getDecryptionResultBatch(mobileBuilder.toString());
            if(jsonObject == null){
                jsonObject = RaiYiEncryptionUtil.getDecryptionResultBatch(mobileBuilder.toString());
                if(jsonObject == null){
                    jsonObject = RaiYiEncryptionUtil.getDecryptionResultBatch(mobileBuilder.toString());
                    if(jsonObject == null){
                        DingDingService.dingDingSendMsgException("推送通话记录给8848解密出现异常");
                    }
                }
            }
            for(CallRecord callRecord : callRecordList){
                JSONObject callRecordMap = new JSONObject();
                //手机号加解密修改
                String decryptionResult = null;
                if(jsonObject != null && jsonObject.containsKey(callRecord.getPhone())){
                    decryptionResult = jsonObject.getString(callRecord.getPhone());
                    if(decryptionResult.length()<7){
                        continue;
                    }
                }
                callRecordMap.put("taskId", callRecord.getTaskId());
                callRecordMap.put("talkTime", getCalculateSeconds(callRecord.getCallDuration()));
                callRecordMap.put("customerMobile", decryptionResult == null ? callRecord.getPhone() : decryptionResult);
                callRecordMap.put("interactNum", callRecord.getCycleCount());
                callRecordMap.put("hangupParty", callRecord.getWhoHangup() == null ? null : callRecord.getWhoHangup()+1);
                callRecordMap.put("keypress", callRecord.getIntentionLabels());
                callRecordMap.put("ifUpdate", 1);
                textList.add(callRecordMap);
            }
        }

        LocalDateTime e = LocalDateTime.now();
        log.info("批量推送加解密"+textList.size()+"条需要的时间："+Duration.between(s, e).toMillis()+"ms");
        if(CollectionUtils.isEmpty(textList)){
            return;
        }
        map.put("text", AESEncode(tenantSecrets.getAesKey(), textList.toJSONString()));
        map = new TreeMap<>(map);
        String jsonStr = JSON.toJSONString(map, SerializerFeature.SortField);
        jsonStr += tenantSecrets.getSalt();
        String sign = DigestUtils.md5Hex(jsonStr);
        map.put("sign", sign);
        Map<String, Object> res = null;
        LocalDateTime startTime = LocalDateTime.now();
        LocalDateTime endTime = LocalDateTime.now();
        try {
            startTime = LocalDateTime.now();
            res = restTemplate.postForObject(admin.getCallbackUrl(), map, Map.class);
            endTime = LocalDateTime.now();
        } catch (Exception ex){
            log.error("手机号推送8848报错！！");
            DingDingService.dingDingSendMsgException("手机号回调8848报错");
            ex.printStackTrace();
        }finally {
            requestLogService.addLog(textList.toJSONString() + "[" + admin.getCallbackUrl() + "]", res, "call_record_push", startTime, endTime);
        }
    }

    public void taskStopCallPhoneToRedis(List<String> list){
        List<String> first = Lists.newArrayList();
        List<String> reCall = Lists.newArrayList();
        for (String s : list) {
            if(s.contains("first"))
                first.add(s.replace(",firstCall",""));
            else
                reCall.add(s.replace(",reCall",""));
        }

        Map<String,List<String>> map = first.stream().collect(Collectors.groupingBy(p->p.split(",")[0]));
        map.forEach((k,v)->{
            RQueue<String> rQueue = redissonClient.getQueue(k);
            List<String> tar = v.stream().map(p->p.substring(p.indexOf(",")+1)).collect(Collectors.toList());
            rQueue.addAll(tar);
        });

        RBatch batch = redissonClient.createBatch();
        Map<String,List<String>> map1 = reCall.stream().collect(Collectors.groupingBy(p->p.split(",")[0]));
        map1.forEach((k,v)->{
            Map<String, Double> redisValueMaps = Maps.newHashMap();
            for (String s : v) {
                LocalDateTime targetTime  = LocalDateTime.parse(s.split(",")[9],DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                redisValueMaps.put(s.substring(s.indexOf(",")+1), (double) (targetTime.toInstant(ZoneOffset.ofHours(9)).toEpochMilli()));
            }
            RScoredSortedSetAsync<String> scoredSortedSet = batch.getScoredSortedSet(k+"_recall");
            if (!redisValueMaps.isEmpty()) {
                scoredSortedSet.addAllAsync(redisValueMaps);
            }
        });
        batch.execute();

    }

    public AIOutboundTaskTemplate checkValidTemplate(Long templateId){
        Optional<AIOutboundTaskTemplate> aiOutboundTaskTemplate = aiOutboundTaskTemplateRepository.findById(templateId);
        return aiOutboundTaskTemplate.orElse(null);
    }

    public List<AntChainTaskDTO> getAntChainAiOutboundTaskList(String appKey, String templateId, String lastCallTime) {
        Admin admin = adminRepository.findAdminByAccessKey(appKey);
        List<AIOutboundTaskTemplate> aiOutboundTaskTemplates = new ArrayList<>();
        if (StringUtils.isEmpty(templateId)) {
            List<AIOutboundTaskTemplate> db = aiOutboundTaskTemplateRepository.findAllByGroupId(admin.getGroupId());
            aiOutboundTaskTemplates.addAll(db);
        } else {
            AIOutboundTaskTemplate aiOutboundTaskTemplate = aiOutboundTaskTemplateRepository.findFirstByIdAndGroupId(Long.valueOf(templateId), admin.getGroupId());
            if (null != aiOutboundTaskTemplate) {
                aiOutboundTaskTemplates.add(aiOutboundTaskTemplate);
            }
        }
        List<AntChainTaskDTO> antChainTaskDTOList = new ArrayList<>();
        for (AIOutboundTaskTemplate aiOutboundTaskTemplate : aiOutboundTaskTemplates) {
            AntChainTaskDTO antChainTaskDTO = new AntChainTaskDTO();
            antChainTaskDTO.setTaskId(aiOutboundTaskTemplate.getId().toString());
            antChainTaskDTO.setTaskName(aiOutboundTaskTemplate.getTaskName());
            antChainTaskDTO.setStatus(2);
            antChainTaskDTOList.add(antChainTaskDTO);
        }
        return antChainTaskDTOList;
    }

    public List<AntChainTaskDTO> getAntChainAiOutboundTaskList(String appKey, List<String> templateIdList) {
        Admin admin = adminRepository.findAdminByAccessKey(appKey);
        List<AntChainTaskDTO> antChainTaskDTOList = new ArrayList<>();
        List<AIOutboundTaskTemplate> aiOutboundTaskTemplates = new ArrayList<>();
        if (CollectionUtils.isEmpty(templateIdList)) {
            List<AIOutboundTaskTemplate> db = aiOutboundTaskTemplateRepository.findAllByGroupId(admin.getGroupId());
            aiOutboundTaskTemplates.addAll(db);
        } else {
            List<Long> idList = templateIdList.stream().map(Long::parseLong).collect(Collectors.toList());
            List<AIOutboundTaskTemplate> db = aiOutboundTaskTemplateRepository.findAllInIdListAndGroupId(idList, admin.getGroupId());
            aiOutboundTaskTemplates.addAll(db);
        }

        for (AIOutboundTaskTemplate aiOutboundTaskTemplate : aiOutboundTaskTemplates) {
            AntChainTaskDTO antChainTaskDTO = new AntChainTaskDTO();
            antChainTaskDTO.setTaskId(aiOutboundTaskTemplate.getId().toString());
            antChainTaskDTO.setTaskName(aiOutboundTaskTemplate.getTaskName());
            antChainTaskDTO.setStatus(2);
            antChainTaskDTOList.add(antChainTaskDTO);
        }
        return antChainTaskDTOList;
    }

    private static Integer getCalculateSeconds(Integer callDuration) {
        if (callDuration == null) {
            return 0;
        } else {
            if (callDuration % 1000 < 100) {
                return callDuration/1000;
            } else {
                return (int)Math.ceil(((double)callDuration)/1000);
            }
        }
    }

    private void setRestrictLocation(AIOutboundTask aiOutboundTask, AIOutboundTaskTemplate aiOutboundTaskTemplate){
        if(aiOutboundTaskTemplate.getAllRestrictCity() != null){
            aiOutboundTask.setAllRestrictCity(aiOutboundTaskTemplate.getAllRestrictCity());
        }
        if(aiOutboundTaskTemplate.getAllRestrictProvince() != null) {
            aiOutboundTask.setAllRestrictProvince(aiOutboundTaskTemplate.getAllRestrictProvince());
        }
        if(aiOutboundTaskTemplate.getYdRestrictCity() != null){
            aiOutboundTask.setYdRestrictCity(aiOutboundTaskTemplate.getYdRestrictCity());
        }
        if(aiOutboundTaskTemplate.getYdRestrictProvince() != null){
            aiOutboundTask.setYdRestrictProvince(aiOutboundTaskTemplate.getYdRestrictProvince());
        }
        if(aiOutboundTaskTemplate.getLtRestrictCity() != null) {
            aiOutboundTask.setLtRestrictCity(aiOutboundTaskTemplate.getLtRestrictCity());
        }
        if(aiOutboundTaskTemplate.getLtRestrictProvince() != null) {
            aiOutboundTask.setLtRestrictProvince(aiOutboundTaskTemplate.getLtRestrictProvince());
        }
        if(aiOutboundTaskTemplate.getDxRestrictCity() != null) {
            aiOutboundTask.setDxRestrictCity(aiOutboundTaskTemplate.getDxRestrictCity());
        }
        if(aiOutboundTaskTemplate.getDxRestrictProvince() != null) {
            aiOutboundTask.setDxRestrictProvince(aiOutboundTaskTemplate.getDxRestrictProvince());
        }
        if(aiOutboundTaskTemplate.getUnknownRestrictCity() != null) {
            aiOutboundTask.setUnknownRestrictCity(aiOutboundTaskTemplate.getUnknownRestrictCity());
        }
        if(aiOutboundTaskTemplate.getUnknownRestrictProvince() != null) {
            aiOutboundTask.setUnknownRestrictProvince(aiOutboundTaskTemplate.getUnknownRestrictProvince());
        }
    }

    public void sendTaskStatus(String taskId, String taskName, String taskStatus, String groupId, String location ){
        TaskStatusNoticeDTO taskNotice = new TaskStatusNoticeDTO();
        taskNotice.setTaskId(taskId);
        taskNotice.setTaskName(taskName);
        taskNotice.setTaskStatus(taskStatus);
        taskNotice.setGroupId(groupId);
        taskNotice.setLocation(location);
        taskStatusNoticeProducer.send(taskNotice);
    }
    
    public List<String> getTemplateIdByAccount(String account){
        Admin adminByAccount = adminRepository.findAdminByAccount(account);
        List<String> res = new ArrayList<>();
        if(adminByAccount != null){
            String groupId = adminByAccount.getGroupId();
            List<AIOutboundTaskTemplate> allByGroupId = aiOutboundTaskTemplateRepository.findAllByGroupId(groupId);
            return allByGroupId.stream().map(template -> String.valueOf(template.getId())).collect(Collectors.toList());
        }
        return res;
    }
}
