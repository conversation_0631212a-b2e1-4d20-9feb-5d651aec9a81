package com.raipeng.aidata.service.mq;

import com.raipeng.aidata.config.Constants;
import com.raipeng.aidatacommon.model.AIOutboundTaskStopControl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class TaskStopControlSaveProducer {
    @Autowired
    @Qualifier("mainRabbitTemplate")
    private RabbitTemplate rabbitTemplate;


    public void send(AIOutboundTaskStopControl aiOutboundTaskStopControl) {
        this.rabbitTemplate.convertAndSend(Constants.TASK_STOP_CONTROL_SAVE_EXCHANGE,
                Constants.TASK_STOP_CONTROL_SAVE_ROUTING, aiOutboundTaskStopControl);
    }
}
