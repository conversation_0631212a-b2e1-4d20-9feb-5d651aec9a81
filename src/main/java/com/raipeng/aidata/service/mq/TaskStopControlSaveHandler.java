package com.raipeng.aidata.service.mq;


import com.raipeng.aidata.config.Constants;
import com.raipeng.aidata.repository.AIOutboundTaskStopControlRepository;
import com.raipeng.aidatacommon.model.AIOutboundTaskStopControl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class TaskStopControlSaveHandler {

    @Autowired
    private AIOutboundTaskStopControlRepository aiOutboundTaskStopControlRepository;


    @RabbitListener(queues = {Constants.TASK_STOP_CONTROL_SAVE_QUEUE})
    public void handle(AIOutboundTaskStopControl taskStopControl) {
        try {
            long now = System.currentTimeMillis();
            AIOutboundTaskStopControl byTaskId = aiOutboundTaskStopControlRepository.findByTaskId(taskStopControl.getTaskId());
            if (byTaskId == null) {
                aiOutboundTaskStopControlRepository.save(taskStopControl);
            }
            log.info("自动暂停任务:{} {},耗时:{}", taskStopControl.getTaskId(), taskStopControl.getTaskName(), System.currentTimeMillis() - now);
        } catch (Exception e) {
            log.info("Exception 保存自动暂停任务失败: {}", e.toString());
        }
    }
}

