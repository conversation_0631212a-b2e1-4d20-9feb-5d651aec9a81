package com.raipeng.aidata.controller;

import com.alibaba.fastjson.JSONObject;
import com.raipeng.aidata.controller.response.Response;
import com.raipeng.aidata.entity.*;
import com.raipeng.aidata.repository.AIOutboundTaskRepository;
import com.raipeng.aidata.service.*;
import com.raipeng.aidata.utils.IpAddressUtil;
import com.raipeng.aidatacommon.model.AIOutboundTask;
import com.raipeng.aidatacommon.model.Admin;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;

import static com.raipeng.aidatacommon.enums.CommonConstants.REDIS_TASK_TMP;

@RestController
@RequestMapping("/api/task")
@Api(value = "/api/task", tags = {"第三方任务接口"})
@Slf4j
public class ApiController {

    @Autowired
    private AIOutboundTaskService aiOutboundTaskService;

    @Autowired
    private ApiAIOutboundTemplateService apiAiOutboundTemplateService;

    @Autowired
    private AdminService adminService;

    @Autowired
    private ApiAIOutboundTaskService apiAIOutboundTaskService;

    @Autowired
    private ApiCommonService apiCommonService;

    @Autowired
    private ApiTaskService apiTaskService;


    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private AIOutboundTaskRepository aiOutboundTaskRepository;

    @ApiOperation(value = "generateTask")
    @PostMapping(value = "/generateTask")
    public Response<Map<String, String>> generateTask(@RequestBody ApiTaskRequest params, HttpServletRequest request) {
        Response<Map<String, String>> response = new Response<>();
        String ip = IpAddressUtil.getIpAddress(request);
        Response<Map<String, String>> validRes = apiCommonService.validateRequest(params, request, response, ip);
        if (validRes != null) {
            return validRes;
        }
        LocalTime now = LocalTime.now();
        LocalTime fiveAM = LocalTime.of(5, 0);
        if (now.isBefore(fiveAM)) {
            response.setResponseFail();
            response.setMsg("数据导入不能早于5点");
            log.info("Exception 第三方数据导入不能早于5点 {}", ip);
            return response;
        }
        Long templateId = params.getTemplateId();
        String taskName = params.getTaskName();
        if (templateId == null) {
            response.setResponseFail();
            response.setMsg("模板ID缺失");
            log.info("Exception 第三方模板ID缺失 {}", ip);
            return response;
        }
        if (StringUtils.isEmpty(taskName)) {
            response.setResponseFail();
            response.setMsg("任务名缺失");
            log.info("Exception 第三方任务名 {}", ip);
            return response;
        }
        Map<String, String> res = new HashMap<>();
        String templateGroupId = apiTaskService.checkApiTemplate(templateId);
        if (StringUtils.isEmpty(templateGroupId)) {
            response.setResponseFail();
            response.setMsg("模板ID已停用或不存在");
            log.info("Exception 第三方模板ID {} 已停用或不存在 {}", templateId, ip);
            return response;
        }
        String[] groupSplit = templateGroupId.split("_");
        if (!groupSplit[1].equals(params.getTenantId())) {
            response.setResponseFail();
            response.setMsg("模板ID不存在");
            log.info("Exception 第三方非法模板ID {} 不存在 {}", templateId, ip);
            return response;
        }
        String taskId = aiOutboundTaskService.createTaskInRedis(templateId, taskName);
        res.put("taskId", taskId);
        response.setResponseSuccess();
        response.setData(res);
        return response;
    }

    @ApiOperation(value = "loadPhones")
    @PostMapping(value = "/loadPhones")
    public Response<String> loadPhones(@RequestBody ApiTaskRequest params, HttpServletRequest request) {
        long startTime0 = System.currentTimeMillis();
        Response<String> response = new Response<>();
        String ip = IpAddressUtil.getIpAddress(request);
        Response<String> validRes = apiCommonService.validateRequest(params, request, response, ip);
        if (validRes != null) {
            return validRes;
        }
        String taskName = params.getTaskName();
        Long templateId = params.getTemplateId();
        Long taskId = params.getTaskId();
        if (StringUtils.isBlank(taskName)) {
            response.setResponseFail();
            response.setMsg("任务名为空");
            log.info("Exception 第三方任务名为空 {}", ip);
            return response;
        }
        if (templateId == null) {
            response.setResponseFail();
            response.setMsg("模板ID为空");
            log.info("Exception 第三方模板ID {}", ip);
            return response;
        }
        if (taskId == null) {
            response.setResponseFail();
            response.setMsg("任务ID为空");
            log.info("Exception 第三方任务ID为空 {}", ip);
            return response;
        }
        String taskIdVal = aiOutboundTaskService.getTaskUuidByNameFromRedis(taskName + "_" + templateId);
        if (StringUtils.isBlank(taskIdVal)) {
            response.setResponseFail();
            response.setMsg("第三方任务不存在或已被操作");
            log.info("第三方任务不存在或已被操作,任务名 {}, 模板 {}, taskId {} ,IP {}", taskName, templateId, taskId, ip);
            return response;
        }
        if (!taskIdVal.equals(taskId.toString())) {
            response.setResponseFail();
            response.setMsg("任务名和任务ID不匹配");
            log.info("Exception 第三方任务名和任务ID不匹配,任务名 {}, 模板 {}, taskId {} ,IP {}", taskName, templateId, taskId, ip);
            return response;
        }
        List<ApiCustomer> customers = params.getCustomers();
        if (customers == null || customers.isEmpty()) {
            response.setResponseFail();
            response.setMsg("客户列表为空");
            log.info("Exception 第三方任务ID {} 客户列表为空 IP{}", taskId, ip);
            return response;
        }
        if (customers.size() > 5000) {
            response.setResponseFail();
            response.setMsg("单次客户列表超过5000");
            log.info("Exception 第三方任务ID {} 客户列表大小 {} IP {}", taskId, customers.size(), ip);
            return response;
        }
        long startTime1 = System.currentTimeMillis();
        try {
            String loadResponse = apiAIOutboundTaskService.apiSyncPhonesToRedis(params, String.valueOf(taskId));
            response.setData(loadResponse);
            if ("同步成功".equals(loadResponse)) {
                response.setResponseSuccess();
            } else {
                response.setResponseFail();
            }
        } catch (Exception e) {
            e.printStackTrace();
            response.setResponseFail();
        }
        long endTime = System.currentTimeMillis();
        log.info("API任务 {} 任务名 {} 总耗时：{} 导入耗时:{} IP{}", taskId, taskName, endTime - startTime0, endTime - startTime1, ip);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "loadExcelPhones")
    @PostMapping(value = "/loadExcelPhones")
    public Response<String> loadExcelPhones(@RequestParam Long tenantId, @RequestParam Long taskId, @RequestParam String taskName,
                                            @RequestParam Long templateId, @RequestParam(value = "importType", required = false) String importType,
                                            @RequestParam MultipartFile excelFile, HttpServletRequest request) {
//        log.info(params.toString());
        Response<String> response = new Response<>();
        ApiTaskRequest params = new ApiTaskRequest();
        params.setTenantId(tenantId.toString());
        params.setTaskId(taskId);
        params.setTaskName(taskName);
        params.setTemplateId(templateId);
        params.setImportType(importType);
        String ip = IpAddressUtil.getIpAddress(request);
        Response<String> validRes = apiCommonService.validateRequest(params, request, response, ip);
        if (validRes != null) {
            return validRes;
        }

        if (!apiTaskService.isValidFileSize(excelFile)) {
            response.setResponseFail();
            response.setMsg("导入文件为空 或 文件超过50MB");
            log.info("Exception Excel导入文件为空 或 文件超过50MB {}", ip);
            return response;
        }
        if (StringUtils.isBlank(taskName)) {
            response.setResponseFail();
            response.setMsg("任务名为空");
            log.info("Exception Excel第三方任务名为空 {}", ip);
            return response;
        }
        if (templateId == null) {
            response.setResponseFail();
            response.setMsg("模板ID为空");
            log.info("Exception Excel第三方Excel模板ID {}", ip);
            return response;
        }
        if (taskId == null) {
            response.setResponseFail();
            response.setMsg("任务ID为空");
            log.info("Exception Excel第三方任务ID为空 {}", ip);
            return response;
        }
        String taskIdVal = aiOutboundTaskService.getTaskUuidByNameFromRedis(taskName + "_" + templateId);
        if (StringUtils.isBlank(taskIdVal)) {
            response.setResponseFail();
            response.setMsg("第三方任务不存在或已被操作");
            log.info("Excel第三方任务不存在或已被操作,任务名 {}, 模板 {}, taskId {} ,IP {}", taskName, templateId, taskId, ip);
            return response;
        }
        if (!taskIdVal.equals(taskId.toString())) {
            response.setResponseFail();
            response.setMsg("任务名和任务ID不匹配");
            log.info("Exception Excel第三方任务名和任务ID不匹配,任务名 {}, 模板 {}, taskId {} ,IP {}", taskName, templateId, taskId, ip);
            return response;
        }
        List<ApiCustomer> customers = apiTaskService.getExcelCustomers(params, excelFile);
        if (customers == null || customers.isEmpty()) {
            response.setResponseFail();
            response.setMsg("有效客户列表为空");
            log.info("Exception 第三方任务ID {} 有效客户列表为空 {}", taskId, ip);
            return response;
        }

        LocalDateTime startTime = LocalDateTime.now();
        params.setCustomers(customers);
        try {
            String loadResponse = apiAIOutboundTaskService.apiSyncPhonesToRedis(params, String.valueOf(taskId));
            response.setData(loadResponse);
            if ("同步成功".equals(loadResponse)) {
                response.setResponseSuccess();
            } else {
                response.setResponseFail();
            }
        } catch (Exception e) {
            e.printStackTrace();
            response.setResponseFail();
        }
        LocalDateTime endTime = LocalDateTime.now();
        log.info("API任务 {} 任务名 {} 总耗时：{} IP{}", taskId, taskName, ChronoUnit.MILLIS.between(startTime, endTime), ip);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "停止任务")
    @PostMapping("/stopTask")
    public Response<List<String>> stopTask(@RequestBody ApiTaskRequest params, HttpServletRequest request) {
        Response<List<String>> response = new Response<>();
        String ip = IpAddressUtil.getIpAddress(request);
        Response<List<String>> validRes = apiCommonService.validateRequest(params, request, response, ip);
        if (validRes != null) {
            return validRes;
        }
        List<Long> taskIds = params.getTaskIds();
        if (taskIds == null || taskIds.isEmpty()) {
            response.setResponseFail();
            response.setMsg("任务ID缺失");
            log.info("Exception 任务IDs缺失 {}", ip);
            return response;
        }
        response.setData(apiTaskService.stopTasks(params));
        response.setResponseSuccess();
        return response;
    }

    @PostMapping("/importClues")
    public Response<Map<String, String>> importClues(@RequestBody ApiClueRequest params, HttpServletRequest request) {
        Response<Map<String, String>> response = new Response<>();
        String ip = IpAddressUtil.getIpAddress(request);
        Response<Map<String, String>> validRes = apiCommonService.validateRequest(params, request, response, ip);
        if (validRes != null) {
            return validRes;
        }
        String batchID = UUID.randomUUID().toString().replace("-", "");
        int size = apiTaskService.importPhonesOfClue(params, batchID);
        HashMap<String, String> stringStringHashMap = new HashMap<>();
        stringStringHashMap.put("size", String.valueOf(size));
        stringStringHashMap.put("batchID", batchID);
        response.setData(stringStringHashMap);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "findAIOutboundTaskTemplate")
    @PostMapping(value = "/findAIOutboundTaskTemplate")
    public Response<List<AIOutboundTaskTemplateQueryDto>> findList(@RequestBody ApiTaskTemplateRequest params,
                                                                   HttpServletRequest request) {
        Response<List<AIOutboundTaskTemplateQueryDto>> response = new Response<>();
        String ip = IpAddressUtil.getIpAddress(request);
        log.info("三方接口请求日志: ip: {} , 参数: {}", ip, params);
        Response<List<AIOutboundTaskTemplateQueryDto>> validRes = apiCommonService.validateRequest(params, request, response, ip);
        if (validRes != null) {
            return validRes;
        }
        //账号校验
        Response<List<AIOutboundTaskTemplateQueryDto>> res = new Response<>();
        if (StringUtils.isBlank(params.getAccount())) {
            res.setMsg("account为空");
            res.setResponseFail();
            return res;
        }
        Admin admin = adminService.findAdminByAccount(params.getAccount());
        if (null == admin || StringUtils.isBlank(admin.getGroupId())) {
            res.setMsg("account异常");
            res.setResponseFail();
            return res;
        } else {
            String groupId = admin.getGroupId();
            String tenant = groupId.split("_")[1];
            if (!tenant.equals(params.getTenantId())) {
                res.setMsg("商户下没有该账号");
                res.setResponseFail();
                return res;
            }
        }
        List<AIOutboundTaskTemplateQueryDto> data = apiAiOutboundTemplateService.findList(admin.getGroupId());
        res.setData(data);
        res.setResponseSuccess();
        return res;
    }

    @PostMapping("/verifyTaskStatus")
    public Response<Void> verifyTaskStatus(@RequestBody Map<String, String> params, HttpServletRequest request) {
        log.info("源码：" + params.toString());
        Response<Void> response = new Response<>();
        log.info(getRecord(params, "hVQ1mP49l1JofZ9RJ0NbxjKcEQhb06xx"));
        return response;
    }

    public static String getRecord(Map<String, String> receivedMap, String appKey) {
        String text = (String) receivedMap.get("text");
        return AESDecode(appKey, text);
    }

    public static String AESDecode(String appKey, String content) {
        try {
            KeyGenerator keygen = KeyGenerator.getInstance("AES");
            SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
            secureRandom.setSeed(appKey.getBytes());
            keygen.init(128, secureRandom);
            SecretKey original_key = keygen.generateKey();
            byte[] raw = original_key.getEncoded();
            SecretKey key = new SecretKeySpec(raw, "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(2, key);
            byte[] byte_encode = Base64.getDecoder().decode(content);
            byte[] byte_AES = cipher.doFinal(byte_encode);
            return new String(byte_AES);
        } catch (Exception e) {
            return null;
        }
    }

    @ApiOperation(value = "syncPermanentTaskPhones")
    @PostMapping(value = "/syncPermanentTaskPhones")
    public Response<String> syncPermanentTaskPhones(@RequestBody ApiTaskRequest params, HttpServletRequest request) {
        long startTime = System.currentTimeMillis();
        Response<String> response = new Response<>();
        String ip = IpAddressUtil.getIpAddress(request);
        List<ApiCustomer> customers = params.getCustomers();

        Response<String> validRes = apiCommonService.validateRequest(params, request, response, ip);
        if (validRes != null) {
            return validRes;
        }
        LocalTime now = LocalTime.now();
        LocalTime fiveAM = LocalTime.of(5, 0);
        if (now.isBefore(fiveAM)) {
            response.setResponseFail();
            response.setMsg("数据导入不能早于5点");
            log.info("Exception 第三方续呼任务数据导入不能早于5点 {}", ip);
            return response;
        }
        Long taskId = params.getTaskId();
        if (taskId == null) {
            response.setResponseFail();
            response.setMsg("任务ID为空");
            log.info("Exception 第三方续呼任务ID为空 {}", ip);
            return response;
        }
        if (customers == null || customers.isEmpty()) {
            response.setResponseFail();
            response.setMsg("客户列表为空");
            log.info("Exception 第三方续呼任务ID {} 客户列表为空 IP{}", taskId, ip);
            return response;
        }

        if (customers.size() > 5000) {
            response.setResponseFail();
            response.setMsg("单次客户列表超过5000");
            log.info("Exception 第三方续呼任务ID {} 客户列表大小 {} IP {}", taskId, customers.size(), ip);
            return response;
        }


        RBucket<Object> bucket = redissonClient.getBucket(REDIS_TASK_TMP + taskId + "_permTask");
        String taskBody = "";
        if (bucket.isExists()) {
            taskBody = (String) bucket.get();
        } else {
            Optional<AIOutboundTask> byId = aiOutboundTaskRepository.findById(taskId);
            if (byId.isPresent()) {
                AIOutboundTask aiOutboundTask = byId.get();
                taskBody = JSONObject.toJSONString(aiOutboundTask);
                redissonClient.getBucket(REDIS_TASK_TMP + taskId + "_permTask").set(taskBody);
            } else {
                response.setResponseFail();
                response.setMsg("任务ID 不存在 " + taskId);
                log.info("Exception 第三方续呼 任务ID 不存在 {}", taskId);
                return response;
            }
        }

        JSONObject task = JSONObject.parseObject(taskBody);
        String taskName = task.getString("taskName");
        if (StringUtils.isEmpty(taskName)) {
            response.setResponseFail();
            response.setMsg("任务名为空");
            log.info("第三方续呼任务名 {}", taskBody);
            return response;
        }


        String groupId = task.getString("groupId");
        if (StringUtils.isEmpty(groupId)) {
            response.setResponseFail();
            response.setMsg("无效任务");
            log.info("第三方续呼 groupId 为空 {}", taskBody);
            return response;
        }

        String account = params.getAccount();
        if (StringUtils.isNotEmpty(account)) {
            Admin admin = adminService.getApiAdminCache(account);
            if(admin == null){
                response.setResponseFail();
                response.setMsg("任务和账号不匹配 " + taskId + " " + account);
                log.info("第三方续呼任务未找到  taskId {} task {} admin {}",  taskId, taskBody, account);
                return response;
            }
            if (!groupId.equals(admin.getGroupId())) {
                response.setResponseFail();
                response.setMsg("任务和账号不匹配" + taskId + " " + account);
                log.info("第三方续呼任务和账号不匹配 taskId {} task {} admin {}",  taskId, taskBody, account);
                return response;
            }
        }

        String nextDayCall = task.getString("nextDayCall");
        if (StringUtils.isEmpty(nextDayCall) || !"1".equals(nextDayCall)) {
            response.setResponseFail();
            response.setMsg("任务类型不匹配");
            log.info("第三方续呼任务类型为普通任务 {}", taskBody);
            return response;
        }

        long startTime1 = System.currentTimeMillis();
        try {
            String loadResponse = apiAIOutboundTaskService.apiSyncPermanentTaskPhonesToRedis(params, String.valueOf(taskId), task);

            if ("同步成功".equals(loadResponse)) {
                response.setResponseSuccess();
                response.setData(String.valueOf(taskId));
            } else {
                response.setResponseFail();
            }
        } catch (Exception e) {
            e.printStackTrace();
            response.setResponseFail();
        }
        long endTime = System.currentTimeMillis();
        log.info("第三方续呼任务 Customer size: {} 任务总耗时：{} 导入耗时：{}", customers.size(), startTime - endTime, startTime1 - endTime);
        return response;
    }

    @ApiOperation(value = "续呼任务查询任务列表接⼝")
    @PostMapping(value = "/findPermanentTaskList")
    public Response<List<ApiTaskResponse>> findPermanentTaskList(@RequestBody ApiTaskRequest params, HttpServletRequest request) {
        Response<List<ApiTaskResponse>> response = new Response<>();
        String ip = IpAddressUtil.getIpAddress(request);

        Response<List<ApiTaskResponse>> validRes = apiCommonService.validateRequest(params, request, response, ip);
        if (validRes != null) {
            return validRes;
        }
        String tenantId = params.getTenantId();
        if (StringUtils.isEmpty(tenantId)) {
            response.setResponseFail();
            response.setMsg("商户ID为空");
            log.info("第三方续呼任务商户ID为空 {} {}", params, ip);
            return response;
        }

        List<ApiTaskResponse> res = apiAIOutboundTaskService.findPermanentTaskList(params.getTenantId(), params.getAccount(),params.getTaskId());
        response.setResponseSuccess();
        response.setData(res);
        return response;
    }

}



