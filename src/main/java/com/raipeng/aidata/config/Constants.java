package com.raipeng.aidata.config;

public class Constants {
    public static final String CALL_PHONE_RESULT_NOTICE_EXCHANGE = "call_phone_result_notice_speech_exchange";
    public static final String CALL_PHONE_RESULT_NOTICE_ROUTING = "call_phone_result_notice_speech_routing";
    public static final String CALL_PHONE_RESULT_NOTICE_QUEUE = "call_phone_result_notice_speech_queue";


    public static final String CALL_PHONE_EXCHANGE = "call_phone_exchange";
    public static final String CALL_PHONE_ROUTING = "call_phone_routing";
    public static final String CALL_PHONE_QUEUE = "call_phone_queue";

    public static final String PHONE_RECORD_SAVE_EXCHANGE = "phone_record_save_exchange";
    public static final String PHONE_RECORD_SAVE_ROUTING = "phone_record_save_routing";
    public static final String PHONE_RECORD_SAVE_QUEUE = "phone_record_save_queue";
    public static final String CALL_RECORD_SAVE_EXCHANGE = "call_record_save_exchange";
    public static final String CALL_RECORD_SAVE_1_EXCHANGE = "call_record_save_1_exchange";
    public static final String CALL_RECORD_SAVE_2_EXCHANGE = "call_record_save_2_exchange";
    public static final String CALL_RECORD_SAVE_3_EXCHANGE = "call_record_save_3_exchange";
    public static final String CALL_RECORD_SAVE_ROUTING = "call_record_save_routing";
    public static final String CALL_RECORD_SAVE_1_ROUTING = "call_record_save_1_routing";
    public static final String CALL_RECORD_SAVE_2_ROUTING = "call_record_save_2_routing";
    public static final String CALL_RECORD_SAVE_3_ROUTING = "call_record_save_3_routing";
    public static final String CALL_RECORD_SAVE_QUEUE = "call_record_save_queue";
    public static final String CALL_RECORD_SAVE_1_QUEUE = "call_record_save_1_queue";
    public static final String CALL_RECORD_SAVE_2_QUEUE = "call_record_save_2_queue";
    public static final String CALL_RECORD_SAVE_3_QUEUE = "call_record_save_3_queue";
    public static final String TRAIN_PHONE_EXCHANGE = "train_phone_dialog_exchange";
    public static final String TRAIN_PHONE_ROUTING = "train_phone_dialog_routing";
    public static final String TRAIN_PHONE_QUEUE = "train_phone_dialog_queue";

    public static final String CALL_PHONE_RESULT_NOTICE_SPEECH_EXCHANGE = "call_phone_result_notice_speech_exchange";
    public static final String CALL_PHONE_RESULT_NOTICE_SPEECH_ROUTING = "call_phone_result_notice_speech_routing";
    public static final String CALL_PHONE_RESULT_NOTICE_SPEECH_QUEUE = "call_phone_result_notice_speech_queue";

    public static final String CLUE_SAVE_EXCHANGE = "CLUE_SAVE_EXCHANGE";
    public static final String CLUE_SAVE_ROUTING = "CLUE_SAVE_ROUTING";
    public static final String CLUE_SAVE_QUEUE = "CLUE_SAVE_QUEUE";

    public static final String UPDATE_SHIELD_PHONES_EXCHANGE = "UPDATE_SHIELD_PHONES_EXCHANGE";
    public static final String UPDATE_SHIELD_PHONES_ROUTING = "UPDATE_SHIELD_PHONES_ROUTING";
    public static final String UPDATE_SHIELD_PHONES_QUEUE = "UPDATE_SHIELD_PHONES_QUEUE";

    public static final String FAST_CALL_PHONE_EXCHANGE = "fast_call_phone_exchange";
    public static final String FAST_CALL_PHONE_ROUTING = "fast_call_phone_routing";
    public static final String FAST_CALL_PHONE_QUEUE = "fast_call_phone_queue";

    public static final String CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_EXCHANGE = "call_phone_result_notice_speech_uncalled_exchange";
    public static final String CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_ROUTING = "call_phone_result_notice_speech_uncalled_routing";
    public static final String CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_QUEUE = "call_phone_result_notice_speech_uncalled_queue";

    public static final String ANT_REQUEST_EXCHANGE = "ant_request_exchange";
    public static final String ANT_REQUEST_ROUTING = "ant_request_routing";
    public static final String ANT_REQUEST_QUEUE = "ant_request_queue";

    public static final String ANT_RECORD_EXCHANGE = "ant_record_exchange";
    public static final String ANT_RECORD_ROUTING = "ant_record_routing";
    public static final String ANT_RECORD_QUEUE = "ant_record_queue";

    public static final String ANT_DIGITAL_REQUEST_EXCHANGE = "ant_digital_request_exchange";
    public static final String ANT_DIGITAL_REQUEST_ROUTING = "ant_digital_request_routing";
    public static final String ANT_DIGITAL_REQUEST_QUEUE = "ant_digital_request_queue";

    public static final String ANT_DIGITAL_RECORD_EXCHANGE = "ant_digital_record_exchange";
    public static final String ANT_DIGITAL_RECORD_ROUTING = "ant_digital_record_routing";
    public static final String ANT_DIGITAL_RECORD_QUEUE = "ant_digital_record_queue";

    public static final String ANT_DIGITAL_SECRET_RECORD_EXCHANGE = "ant_digital_secret_record_exchange";
    public static final String ANT_DIGITAL_SECRET_RECORD_ROUTING = "ant_digital_secret_record_routing";
    public static final String ANT_DIGITAL_SECRET_RECORD_QUEUE = "ant_digital_secret_record_queue";

    public static final String ANT_DIGITAL_SECRET_REQUEST_EXCHANGE = "ant_digital_secret_request_exchange";
    public static final String ANT_DIGITAL_SECRET_REQUEST_ROUTING = "ant_digital_secret_request_routing";
    public static final String ANT_DIGITAL_SECRET_REQUEST_QUEUE = "ant_digital_secret_request_queue";

    public static final String CLUE_IMPORT_REQUEST_EXCHANGE = "clue_import_request_exchange";
    public static final String CLUE_IMPORT_REQUEST_ROUTING = "clue_import_request_routing";
    public static final String CLUE_IMPORT_REQUEST_QUEUE = "clue_import_request_queue";


    public static final String IMPORT_PHONE_RECORD_EXCHANGE = "import_phone_record_exchange";
    public static final String IMPORT_PHONE_RECORD_ROUTING = "import_phone_record_routing";
    public static final String IMPORT_PHONE_RECORD_QUEUE = "import_phone_record_queue";


    public static final String VOLCANO_DATA_DOWNLOAD_EXCHANGE = "volcano_data_download_exchange";
    public static final String VOLCANO_DATA_DOWNLOAD_ROUTING = "volcano_data_download_routing";
    public static final String VOLCANO_DATA_DOWNLOAD_QUEUE = "volcano_data_download_queue";

    public static final String TASK_STATUS_NOTICE_EXCHANGE = "task_status_notice_exchange";
    public static final String TASK_STATUS_NOTICE_ROUTING = "task_status_notice_routing";
    public static final String TASK_STATUS_NOTICE_QUEUE = "task_status_notice_queue";

    public static final String TASK_STOP_CONTROL_SAVE_EXCHANGE = "task_stop_control_save_exchange";
    public static final String TASK_STOP_CONTROL_SAVE_ROUTING = "task_stop_control_save_routing";
    public static final String TASK_STOP_CONTROL_SAVE_QUEUE = "task_stop_control_save_queue";
}
