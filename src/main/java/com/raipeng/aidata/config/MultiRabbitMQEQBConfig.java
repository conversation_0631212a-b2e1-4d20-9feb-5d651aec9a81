package com.raipeng.aidata.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Slf4j
@Configuration
public class MultiRabbitMQEQBConfig {


    //创建Exchange 绑定Queue

    @Resource(name = "mainRabbitAdmin")
    private RabbitAdmin mainRabbitAdmin;

    @Resource(name = "pt1RabbitAdmin")
    private RabbitAdmin pt1RabbitAdmin;

    @Resource(name = "pt2RabbitAdmin")
    private RabbitAdmin pt2RabbitAdmin;

    @Resource(name = "pt3RabbitAdmin")
    private RabbitAdmin pt3RabbitAdmin;


    /**
     * 推送外呼手机号
     */
    public DirectExchange defaultExchangeAIOutboundPhones() {
        return new DirectExchange(Constants.CALL_PHONE_EXCHANGE);
    }

    public Queue queueAIOutboundPhones() {
        //队列持久
        return new Queue(Constants.CALL_PHONE_QUEUE, true);
    }

    public Binding bindingAIOutboundPhones() {
        return BindingBuilder.bind(queueAIOutboundPhones()).to(defaultExchangeAIOutboundPhones()).with(Constants.CALL_PHONE_ROUTING);
    }


    /**
     *
     */
    public DirectExchange defaultExchangeCallPhoneResult() {
        return new DirectExchange(Constants.CALL_PHONE_RESULT_NOTICE_EXCHANGE);
    }

    public Queue queueCallPhoneResult() {
        //队列持久
        return new Queue(Constants.CALL_PHONE_RESULT_NOTICE_QUEUE, true);
    }

    public Binding bindingCallPhoneResult() {
        return BindingBuilder.bind(queueCallPhoneResult()).to(defaultExchangeCallPhoneResult()).with(Constants.CALL_PHONE_RESULT_NOTICE_ROUTING);
    }


    public DirectExchange defaultExchangeCallRecordSaveResult() {
        return new DirectExchange(Constants.CALL_RECORD_SAVE_EXCHANGE);
    }

    public Queue queueCallRecordSaveResult() {
        //队列持久
        return new Queue(Constants.CALL_RECORD_SAVE_QUEUE, true);
    }

    public Binding bindingCallRecordSaveResult() {
        return BindingBuilder.bind(queueCallRecordSaveResult()).to(defaultExchangeCallRecordSaveResult()).with(Constants.CALL_RECORD_SAVE_ROUTING);
    }

    /**
     * CALL_RECORD_SAVE_1
     */
    public DirectExchange defaultExchangeCallRecordSave1Result() {
        return new DirectExchange(Constants.CALL_RECORD_SAVE_1_EXCHANGE);
    }

    public Queue queueCallRecordSave1Result() {
        return new Queue(Constants.CALL_RECORD_SAVE_1_QUEUE, true);
    }

    public Binding bindingCallRecordSave1Result() {
        return BindingBuilder.bind(queueCallRecordSave1Result()).to(defaultExchangeCallRecordSave1Result()).with(Constants.CALL_RECORD_SAVE_1_ROUTING);
    }

    /**
     * CALL_RECORD_SAVE_2
     */
    public DirectExchange defaultExchangeCallRecordSave2Result() {
        return new DirectExchange(Constants.CALL_RECORD_SAVE_2_EXCHANGE);
    }

    public Queue queueCallRecordSave2Result() {
        return new Queue(Constants.CALL_RECORD_SAVE_2_QUEUE, true);
    }

    public Binding bindingCallRecordSave2Result() {
        return BindingBuilder.bind(queueCallRecordSave2Result()).to(defaultExchangeCallRecordSave2Result()).with(Constants.CALL_RECORD_SAVE_2_ROUTING);
    }

    /**
     * CALL_RECORD_SAVE_3
     */
    public DirectExchange defaultExchangeCallRecordSave3Result() {
        return new DirectExchange(Constants.CALL_RECORD_SAVE_3_EXCHANGE);
    }

    public Queue queueCallRecordSave3Result() {
        return new Queue(Constants.CALL_RECORD_SAVE_3_QUEUE, true);
    }

    public Binding bindingCallRecordSave3Result() {
        return BindingBuilder.bind(queueCallRecordSave3Result()).to(defaultExchangeCallRecordSave3Result()).with(Constants.CALL_RECORD_SAVE_3_ROUTING);
    }


    public DirectExchange defaultExchangePhoneRecordSaveResult() {
        return new DirectExchange(Constants.PHONE_RECORD_SAVE_EXCHANGE);
    }

    public Queue queuePhoneRecordSaveResult() {
        //队列持久
        return new Queue(Constants.PHONE_RECORD_SAVE_QUEUE);
    }

    public Binding bindingPhoneRecordSaveResult() {
        return BindingBuilder.bind(queuePhoneRecordSaveResult()).to(defaultExchangePhoneRecordSaveResult()).with(Constants.PHONE_RECORD_SAVE_ROUTING);
    }

    //
    public DirectExchange defaultExchangeTrainPhones() {
        return new DirectExchange(Constants.TRAIN_PHONE_EXCHANGE);
    }

    public Queue queueTrainPhones() {
        //队列持久
        return new Queue(Constants.TRAIN_PHONE_QUEUE, true);
    }

    public Binding bindingTrainPhones() {
        return BindingBuilder.bind(queueTrainPhones()).to(defaultExchangeTrainPhones()).with(Constants.TRAIN_PHONE_ROUTING);
    }

    //
    public DirectExchange defaultExchangeUncalledPhones() {
        return new DirectExchange(Constants.CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_EXCHANGE);
    }

    public Queue queueUncalledPhones() {
        //队列持久
        return new Queue(Constants.CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_QUEUE, true);
    }

    public Binding bindingUncalledPhones() {
        return BindingBuilder.bind(queueUncalledPhones()).to(defaultExchangeUncalledPhones()).with(Constants.CALL_PHONE_RESULT_NOTICE_SPEECH_UNCALLED_ROUTING);
    }

    //
    public DirectExchange callRecordResultNoticeSpeechExchange() {
        return new DirectExchange(Constants.CALL_PHONE_RESULT_NOTICE_SPEECH_EXCHANGE);
    }

    public Queue callRecordResultNoticeSpeechQueue() {
        return new Queue(Constants.CALL_PHONE_RESULT_NOTICE_SPEECH_QUEUE, true);
    }

    public Binding callRecordResultNoticeSpeechBinding() {
        return BindingBuilder.bind(callRecordResultNoticeSpeechQueue()).to(callRecordResultNoticeSpeechExchange()).with(Constants.CALL_PHONE_RESULT_NOTICE_SPEECH_ROUTING);
    }

    //
    public DirectExchange vipPhonesExchange() {
        return new DirectExchange(Constants.FAST_CALL_PHONE_EXCHANGE);
    }

    public Queue vipPhonesQueue() {
        //队列持久
        return new Queue(Constants.FAST_CALL_PHONE_QUEUE, true);
    }

    public Binding vipPhonesBinding() {
        return BindingBuilder.bind(vipPhonesQueue()).to(vipPhonesExchange()).with(Constants.FAST_CALL_PHONE_ROUTING);
    }

    //
    public DirectExchange defaultExchangeClueSave() {
        return new DirectExchange(Constants.CLUE_SAVE_EXCHANGE);
    }

    public Queue queueClueSave() {
        //队列持久
        return new Queue(Constants.CLUE_SAVE_QUEUE, true);
    }

    public Binding bindingClueSave() {
        return BindingBuilder.bind(queueClueSave()).to(defaultExchangeClueSave()).with(Constants.CLUE_SAVE_ROUTING);
    }

    public DirectExchange updateShieldPhonesExchange() {
        return new DirectExchange(Constants.UPDATE_SHIELD_PHONES_EXCHANGE);
    }

    public Queue updateShieldPhonesQueue() {
        //队列持久
        return new Queue(Constants.UPDATE_SHIELD_PHONES_QUEUE, true);
    }

    public Binding updateShieldPhoneBing() {
        return BindingBuilder.bind(updateShieldPhonesQueue()).to(updateShieldPhonesExchange()).with(Constants.UPDATE_SHIELD_PHONES_ROUTING);
    }

    public DirectExchange antRequestExchange() {
        return new DirectExchange(Constants.ANT_REQUEST_EXCHANGE);
    }

    public Queue antRequestQueue() {
        return new Queue(Constants.ANT_REQUEST_QUEUE, true);
    }

    public Binding antRequestBinding() {
        return BindingBuilder.bind(antRequestQueue()).to(antRequestExchange()).with(Constants.ANT_REQUEST_ROUTING);
    }

    public DirectExchange antRecordExchange() {
        return new DirectExchange(Constants.ANT_RECORD_EXCHANGE);
    }

    public Queue antRecordQueue() {
        return new Queue(Constants.ANT_RECORD_QUEUE, true);
    }

    public Binding antRecordBinding() {
        return BindingBuilder.bind(antRecordQueue()).to(antRecordExchange()).with(Constants.ANT_RECORD_ROUTING);
    }

    public DirectExchange clueImportRequestExchange() {
        return new DirectExchange(Constants.CLUE_IMPORT_REQUEST_EXCHANGE);
    }

    public Queue clueImportRequestQueue() {
        return new Queue(Constants.CLUE_IMPORT_REQUEST_QUEUE, true);
    }

    public Binding clueImportRequestBinding() {
        return BindingBuilder.bind(clueImportRequestQueue()).to(clueImportRequestExchange()).with(Constants.CLUE_IMPORT_REQUEST_ROUTING);
    }

    public DirectExchange importPhoneRecordExchange() {
        return new DirectExchange(Constants.IMPORT_PHONE_RECORD_EXCHANGE);
    }

    public Queue importPhoneRecordQueue() {
        return new Queue(Constants.IMPORT_PHONE_RECORD_QUEUE, true);
    }

    public Binding importPhoneRecordBinding() {
        return BindingBuilder.bind(importPhoneRecordQueue()).to(importPhoneRecordExchange()).with(Constants.IMPORT_PHONE_RECORD_ROUTING);
    }

    public DirectExchange volcanoDataDownloadExchange() {
        return new DirectExchange(Constants.VOLCANO_DATA_DOWNLOAD_EXCHANGE);
    }

    public Queue volcanoDataDownloadQueue() {
        return new Queue(Constants.VOLCANO_DATA_DOWNLOAD_QUEUE, true);
    }

    public Binding volcanoDataDownloadBinding() {
        return BindingBuilder.bind(volcanoDataDownloadQueue()).to(volcanoDataDownloadExchange()).with(Constants.VOLCANO_DATA_DOWNLOAD_ROUTING);
    }

    public DirectExchange antDigitalRequestExchange() {
        return new DirectExchange(Constants.ANT_DIGITAL_REQUEST_EXCHANGE);
    }

    public Queue antDigitalRequestQueue() {
        return new Queue(Constants.ANT_DIGITAL_REQUEST_QUEUE, true);
    }

    public Binding antDigitalRequestBinding() {
        return BindingBuilder.bind(antDigitalRequestQueue()).to(antDigitalRequestExchange()).with(Constants.ANT_DIGITAL_REQUEST_ROUTING);
    }

    public DirectExchange antDigitalRecordExchange() {
        return new DirectExchange(Constants.ANT_DIGITAL_RECORD_EXCHANGE);
    }

    public Queue antDigitalRecordQueue() {
        return new Queue(Constants.ANT_DIGITAL_RECORD_QUEUE, true);
    }

    public Binding antDigitalRecordBinding() {
        return BindingBuilder.bind(antDigitalRecordQueue()).to(antDigitalRecordExchange()).with(Constants.ANT_DIGITAL_RECORD_ROUTING);
    }


    public DirectExchange antDigitalSecretRequestExchange() {
        return new DirectExchange(Constants.ANT_DIGITAL_SECRET_REQUEST_EXCHANGE);
    }

    public Queue antDigitalSecretRequestQueue() {
        return new Queue(Constants.ANT_DIGITAL_SECRET_REQUEST_QUEUE, true);
    }

    public Binding antDigitalSecretRequestBinding() {
        return BindingBuilder.bind(antDigitalSecretRequestQueue()).to(antDigitalSecretRequestExchange()).with(Constants.ANT_DIGITAL_SECRET_REQUEST_ROUTING);
    }

    public DirectExchange antDigitalSecretRecordExchange() {
        return new DirectExchange(Constants.ANT_DIGITAL_SECRET_RECORD_EXCHANGE);
    }

    public Queue antDigitalSecretRecordQueue() {
        return new Queue(Constants.ANT_DIGITAL_SECRET_RECORD_QUEUE, true);
    }

    public Binding antDigitalSecretRecordBinding() {
        return BindingBuilder.bind(antDigitalSecretRecordQueue()).to(antDigitalSecretRecordExchange()).with(Constants.ANT_DIGITAL_SECRET_RECORD_ROUTING);
    }


    public DirectExchange taskStopControlSaveExchange() {
        return new DirectExchange(Constants.TASK_STOP_CONTROL_SAVE_EXCHANGE);
    }

    public Queue taskStopControlSaveQueue() {
        return new Queue(Constants.TASK_STOP_CONTROL_SAVE_QUEUE, true);
    }

    public Binding taskStopControlSaveBinding() {
        return BindingBuilder.bind(taskStopControlSaveQueue()).to(taskStopControlSaveExchange()).with(Constants.TASK_STOP_CONTROL_SAVE_ROUTING);
    }

    //分库使用的

    @PostConstruct
    public void initEQB() {
        mainInitMq(mainRabbitAdmin);
        ptInitMq(pt1RabbitAdmin);
        ptInitMq(pt2RabbitAdmin);
        ptInitMq(pt3RabbitAdmin);
    }

    private void mainInitMq(RabbitAdmin mainRabbitAdmin) {
//
        mainRabbitAdmin.declareExchange(defaultExchangeAIOutboundPhones());
        mainRabbitAdmin.declareQueue(queueAIOutboundPhones());
        mainRabbitAdmin.declareBinding(bindingAIOutboundPhones());
        //
        mainRabbitAdmin.declareExchange(defaultExchangeCallPhoneResult());
        mainRabbitAdmin.declareQueue(queueCallPhoneResult());
        mainRabbitAdmin.declareBinding(bindingCallPhoneResult());

        //
        mainRabbitAdmin.declareExchange(defaultExchangeCallRecordSaveResult());
        mainRabbitAdmin.declareQueue(queueCallRecordSaveResult());
        mainRabbitAdmin.declareBinding(bindingCallRecordSaveResult());

        //
        mainRabbitAdmin.declareExchange(defaultExchangeCallRecordSave1Result());
        mainRabbitAdmin.declareQueue(queueCallRecordSave1Result());
        mainRabbitAdmin.declareBinding(bindingCallRecordSave1Result());

        mainRabbitAdmin.declareExchange(defaultExchangeCallRecordSave2Result());
        mainRabbitAdmin.declareQueue(queueCallRecordSave2Result());
        mainRabbitAdmin.declareBinding(bindingCallRecordSave2Result());

        //对话记录
        mainRabbitAdmin.declareExchange(defaultExchangeCallRecordSave3Result());
        mainRabbitAdmin.declareQueue(queueCallRecordSave3Result());
        mainRabbitAdmin.declareBinding(bindingCallRecordSave3Result());

        mainRabbitAdmin.declareExchange(defaultExchangePhoneRecordSaveResult());
        mainRabbitAdmin.declareQueue(queuePhoneRecordSaveResult());
        mainRabbitAdmin.declareBinding(bindingPhoneRecordSaveResult());

        mainRabbitAdmin.declareExchange(defaultExchangeTrainPhones());
        mainRabbitAdmin.declareQueue(queueTrainPhones());
        mainRabbitAdmin.declareBinding(bindingTrainPhones());

        mainRabbitAdmin.declareExchange(defaultExchangeUncalledPhones());
        mainRabbitAdmin.declareQueue(queueUncalledPhones());
        mainRabbitAdmin.declareBinding(bindingUncalledPhones());

        mainRabbitAdmin.declareExchange(callRecordResultNoticeSpeechExchange());
        mainRabbitAdmin.declareQueue(callRecordResultNoticeSpeechQueue());
        mainRabbitAdmin.declareBinding(callRecordResultNoticeSpeechBinding());

        mainRabbitAdmin.declareExchange(vipPhonesExchange());
        mainRabbitAdmin.declareQueue(vipPhonesQueue());
        mainRabbitAdmin.declareBinding(vipPhonesBinding());

        mainRabbitAdmin.declareExchange(defaultExchangeClueSave());
        mainRabbitAdmin.declareQueue(queueClueSave());
        mainRabbitAdmin.declareBinding(bindingClueSave());

        mainRabbitAdmin.declareExchange(updateShieldPhonesExchange());
        mainRabbitAdmin.declareQueue(updateShieldPhonesQueue());
        mainRabbitAdmin.declareBinding(updateShieldPhoneBing());

        mainRabbitAdmin.declareExchange(antRequestExchange());
        mainRabbitAdmin.declareQueue(antRequestQueue());
        mainRabbitAdmin.declareBinding(antRequestBinding());

        mainRabbitAdmin.declareExchange(antRecordExchange());
        mainRabbitAdmin.declareQueue(antRecordQueue());
        mainRabbitAdmin.declareBinding(antRecordBinding());

        mainRabbitAdmin.declareExchange(clueImportRequestExchange());
        mainRabbitAdmin.declareQueue(clueImportRequestQueue());
        mainRabbitAdmin.declareBinding(clueImportRequestBinding());

        mainRabbitAdmin.declareExchange(importPhoneRecordExchange());
        mainRabbitAdmin.declareQueue(importPhoneRecordQueue());
        mainRabbitAdmin.declareBinding(importPhoneRecordBinding());

        mainRabbitAdmin.declareExchange(volcanoDataDownloadExchange());
        mainRabbitAdmin.declareQueue(volcanoDataDownloadQueue());
        mainRabbitAdmin.declareBinding(volcanoDataDownloadBinding());


        mainRabbitAdmin.declareExchange(antDigitalRequestExchange());
        mainRabbitAdmin.declareQueue(antDigitalRequestQueue());
        mainRabbitAdmin.declareBinding(antDigitalRequestBinding());

        mainRabbitAdmin.declareExchange(antDigitalRecordExchange());
        mainRabbitAdmin.declareQueue(antDigitalRecordQueue());
        mainRabbitAdmin.declareBinding(antDigitalRecordBinding());

        mainRabbitAdmin.declareExchange(antDigitalSecretRequestExchange());
        mainRabbitAdmin.declareQueue(antDigitalSecretRequestQueue());
        mainRabbitAdmin.declareBinding(antDigitalSecretRequestBinding());

        mainRabbitAdmin.declareExchange(antDigitalSecretRecordExchange());
        mainRabbitAdmin.declareQueue(antDigitalSecretRecordQueue());
        mainRabbitAdmin.declareBinding(antDigitalSecretRecordBinding());


        mainRabbitAdmin.declareExchange(taskStopControlSaveExchange());
        mainRabbitAdmin.declareQueue(taskStopControlSaveQueue());
        mainRabbitAdmin.declareBinding(taskStopControlSaveBinding());
    }

    private void ptInitMq(RabbitAdmin ptRabbitAdmin) {
        //
        ptRabbitAdmin.declareExchange(defaultExchangeAIOutboundPhones());
        ptRabbitAdmin.declareQueue(queueAIOutboundPhones());
        ptRabbitAdmin.declareBinding(bindingAIOutboundPhones());

    }


}
