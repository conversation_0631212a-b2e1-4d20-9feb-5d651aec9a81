package com.raipeng.aiClickHouse.repository;

import com.raipeng.aiClickHouse.model.AIOutboundTaskPermanent;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface AIOutboundTaskPermanentRepository extends JpaRepository<AIOutboundTaskPermanent, Long> {

    @Query(value = "select distinct task_id from t_ai_outbound_task_permanent t \n" +
            " where t.create_time >= :startTime\n", nativeQuery = true)
    List<Long> findTaskListByTime(LocalDateTime startTime);
}
