package com.raipeng.aiClickHouse.repository;


import com.raipeng.aiClickHouse.model.ImportPhoneRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

public interface ImportPhoneRecordRepository extends JpaRepository<ImportPhoneRecord, Long> {


    @Transactional
    @Modifying
    @Query(value = "delete from t_import_phone_record   \n" +
            "where create_time < :createTime ", nativeQuery = true)
    void deleteByCreateTime(LocalDateTime createTime);
}
