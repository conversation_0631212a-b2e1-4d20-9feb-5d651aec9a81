package com.raipeng.aiClickHouse.repository;

import com.raipeng.aiClickHouse.model.record.CallRecordForHumanMachineHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Tuple;
import java.util.List;

public interface CallRecordForHumanMachineHistoryRepository extends JpaRepository<CallRecordForHumanMachineHistory, String> {


    @Query(value = "select * from call_record_for_human_machine_history t \n" +
            "where t.call_out_time >= :startTime \n" +
            "and t.call_out_time < :endTime \n"
            , nativeQuery = true)
    List<CallRecordForHumanMachineHistory> findByCallOutTimeRecord(String startTime, String endTime);


    @Query(value = "select count(*) from call_record_for_human_machine_history r where r.call_out_time >= :startTime\n" +
            "and r.call_out_time < :endTime", nativeQuery = true)
    Integer findCallRecordNums(String startTime, String endTime);


    @Query(value = "select distinct t.task_id from call_record_for_human_machine_history t \n" +
            "where t.call_out_time >= :startTime \n" +
            "and t.call_out_time < :endTime \n", nativeQuery = true)
    List<String> findTaskIdListByTime(String startTime, String endTime);

    @Query(value = "select * from call_record_for_human_machine_history t \n" +
            "where t.task_id in :taskIdList \n", nativeQuery = true)
    List<CallRecordForHumanMachineHistory> findCallRecordByTaskIdList(List<String> taskIdList);


    @Query(value = "select * from call_record_for_human_machine_history t \n" +
            "where t.task_id = :taskId \n" +
            "and t.call_out_time >= :startTime \n" +
            "and t.call_out_time < :endTime", nativeQuery = true)
    List<CallRecordForHumanMachineHistory> findCallRecordByTaskIdAndCallOutTime(String taskId, String startTime, String endTime);

    @Query(value = "select * from call_record_for_human_machine t \n" +
            "where t.task_id = :taskId \n", nativeQuery = true)
    List<CallRecordForHumanMachineHistory> findCallRecordByTaskId(String taskId);

    @Query(value = "select count(*) from call_record_for_human_machine_history r", nativeQuery = true)
    Integer findAllCallRecordNums();

    @Query(value = "select count(*) from call_record_for_human_machine_history where call_out_time is null", nativeQuery = true)
    Integer findAllCallOutTimeNullRecordNums();

    @Query(value = "select * from call_record_for_human_machine_history t \n" +
            "where t.id in :callIdList", nativeQuery = true)
    List<CallRecordForHumanMachineHistory> findCallRecordListByIdList(List<String> callIdList);

    @Query(value = "select t.task_id as taskId, count(*) as recordNum from call_record_for_human_machine_history t \n" +
            "where  \n" +
            "t.call_out_time >= :startTime \n" +
            "and t.call_out_time < :endTime \n" +
            " group by t.task_id", nativeQuery = true)
    List<Tuple> findCallRecordNumGroupByTaskId(String startTime, String endTime);

    @Query(value = "select * from call_record_for_human_machine_history t \n" +
            "where t.call_out_time >= :startTime \n" +
            "and t.call_out_time < :endTime \n" +
            "limit :limitNum OFFSET :offset \n", nativeQuery = true)
    List<CallRecordForHumanMachineHistory> findCallRecordByTime(String startTime, String endTime, Integer limitNum, Integer offset);

    @Modifying
    @Transactional
    @Query(value = "delete from call_record_for_human_machine_history t\n" +
            "where t.task_id = :taskId and  t.call_out_time >= :startTime and  t.call_out_time < :endTime \n", nativeQuery = true)
    int deleteByTaskIdAndTime(String taskId, String startTime, String endTime);

    @Modifying
    @Transactional
    @Query(value = "delete from call_record_for_human_machine_history t\n" +
            "where t.task_id = :taskId ", nativeQuery = true)
    int deleteByTaskId(String taskId);

    @Query(value = "select distinct task_id from call_record_for_human_machine_history t \n" +
            "where  t.call_out_time >= :startTime \n" +
            "and t.call_out_time < :endTime \n", nativeQuery = true)
    List<String> findDistinctTaskByCallOutTime(String startTime, String endTime);


}
