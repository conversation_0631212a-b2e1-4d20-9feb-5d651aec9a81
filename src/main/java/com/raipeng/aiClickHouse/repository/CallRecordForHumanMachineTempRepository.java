package com.raipeng.aiClickHouse.repository;

import com.raipeng.aiClickHouse.model.CallRecordForHumanMachineTemp;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;


public interface CallRecordForHumanMachineTempRepository extends JpaRepository<CallRecordForHumanMachineTemp, String> {
    @Modifying
    @Transactional
    @Query(value = "insert into call_record_for_human_machine_temp( \n" +
            "id,\n" +
            "form_id,\n" +
            "account,\n" +
            "ai_call_ip,\n" +
            "call_duration,\n" +
            "call_duration_sec,\n" +
            "call_id,\n" +
            "call_out_time,\n" +
            "call_seat_id,\n" +
            "call_status,\n" +
            "call_status_str,\n" +
            "call_team_id,\n" +
            "cause,\n" +
            "city,\n" +
            "city_code,\n" +
            "clue_follow_up_log_id,\n" +
            "clue_id,\n" +
            "contact_time,\n" +
            "corpus_ids,\n" +
            "create_time,\n" +
            "cycle_count,\n" +
            "end_answer_time,\n" +
            "end_monitor_time,\n" +
            "end_pop_win_time,\n" +
            "error_code,\n" +
            "fs_ip,\n" +
            "hit_advance_ids,\n" +
            "hit_answer_ids,\n" +
            "hit_semantic_ids,\n" +
            "if_fast_recall,\n" +
            "if_recall,\n" +
            "if_send_sms,\n" +
            "if_test,\n" +
            "intention_class,\n" +
            "intention_label_ids,\n" +
            "intention_labels,\n" +
            "is_convert_to_clue,\n" +
            "is_posting_out_of_time,\n" +
            "is_trans_to_call_seat,\n" +
            "line_code,\n" +
            "line_gateway_numbers,\n" +
            "line_id,\n" +
            "manual_intention_class,\n" +
            "manual_intention_label_ids,\n" +
            "manual_intention_labels,\n" +
            "merchant_line_code,\n" +
            "merchant_line_id,\n" +
            "mis_call_seat_ids,\n" +
            "no_reception_reason,\n" +
            "operator,\n" +
            "phone,\n" +
            "phone_record_id,\n" +
            "posting_duration,\n" +
            "province,\n" +
            "province_code,\n" +
            "record_id,\n" +
            "redis_key,\n" +
            "say_count,\n" +
            "script_string_id,\n" +
            "speech_craft_id,\n" +
            "start_answer_time,\n" +
            "start_monitor_time,\n" +
            "start_pop_win_time,\n" +
            "talk_time_end,\n" +
            "talk_time_start,\n" +
            "task_id,\n" +
            "task_name,\n" +
            "tenant_code,\n" +
            "tenant_name,\n" +
            "update_time,\n" +
            "user_full_answer_content,\n" +
            "waitmsec,\n" +
            "who_hangup,\n" +
            "whole_audio_file_url,\n" +
            "status,\n" +
            "merchant_id,\n" +
            "data_clear_phone\n" +
            ")\n" +
            "SELECT\n" +
            "id,\n" +
            "form_id,\n" +
            "account,\n" +
            "ai_call_ip,\n" +
            "call_duration,\n" +
            "call_duration_sec,\n" +
            "call_id,\n" +
            "call_out_time,\n" +
            "call_seat_id,\n" +
            "call_status,\n" +
            "call_status_str,\n" +
            "call_team_id,\n" +
            "cause,\n" +
            "city,\n" +
            "city_code,\n" +
            "clue_follow_up_log_id,\n" +
            "clue_id,\n" +
            "contact_time,\n" +
            "corpus_ids,\n" +
            "create_time,\n" +
            "cycle_count,\n" +
            "end_answer_time,\n" +
            "end_monitor_time,\n" +
            "end_pop_win_time,\n" +
            "error_code,\n" +
            "fs_ip,\n" +
            "hit_advance_ids,\n" +
            "hit_answer_ids,\n" +
            "hit_semantic_ids,\n" +
            "if_fast_recall,\n" +
            "if_recall,\n" +
            "if_send_sms,\n" +
            "if_test,\n" +
            "intention_class,\n" +
            "intention_label_ids,\n" +
            "intention_labels,\n" +
            "is_convert_to_clue,\n" +
            "is_posting_out_of_time,\n" +
            "is_trans_to_call_seat,\n" +
            "line_code,\n" +
            "line_gateway_numbers,\n" +
            "line_id,\n" +
            "manual_intention_class,\n" +
            "manual_intention_label_ids,\n" +
            "manual_intention_labels,\n" +
            "merchant_line_code,\n" +
            "merchant_line_id,\n" +
            "mis_call_seat_ids,\n" +
            "no_reception_reason,\n" +
            "operator,\n" +
            "phone,\n" +
            "phone_record_id,\n" +
            "posting_duration,\n" +
            "province,\n" +
            "province_code,\n" +
            "record_id,\n" +
            "redis_key,\n" +
            "say_count,\n" +
            "script_string_id,\n" +
            "speech_craft_id,\n" +
            "start_answer_time,\n" +
            "start_monitor_time,\n" +
            "start_pop_win_time,\n" +
            "talk_time_end,\n" +
            "talk_time_start,\n" +
            "task_id,\n" +
            "task_name,\n" +
            "tenant_code,\n" +
            "tenant_name,\n" +
            "update_time,\n" +
            "user_full_answer_content,\n" +
            "waitmsec,\n" +
            "who_hangup,\n" +
            "whole_audio_file_url,\n" +
            "status,\n" +
            "merchant_id,\n" +
            "phone\n" +
            "FROM call_record_for_human_machine where call_out_time >= :startTime and call_out_time < :endTime", nativeQuery = true)
    int insertIntoOtherTable(String startTime, String endTime);
}
