package com.raipeng.aiClickHouse.repository;


import com.raipeng.aiClickHouse.model.CallRecordForManualDirect;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface CallRecordForManualDirectRepository extends JpaRepository<CallRecordForManualDirect, Long> {
    @Query(value = "select count(*) from call_record_for_manual_direct r where r.call_out_time >= :startTime\n" +
            "and r.call_out_time < :endTime", nativeQuery = true)
    Integer findCallRecordNums(String startTime, String endTime);


}
