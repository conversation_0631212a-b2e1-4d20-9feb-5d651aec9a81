package com.raipeng.aiClickHouse.repository;


import com.raipeng.aiClickHouse.model.SmsRecordHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Tuple;
import java.util.List;

public interface SmsRecordHistoryRepository extends JpaRepository<SmsRecordHistory, Long> {

    @Query(value = "select * from t_sms_record_history t \n" +
            "where t.trigger_time >= :startTime \n" +
            "and t.trigger_time < :endTime limit 1 \n"
            , nativeQuery = true)
    SmsRecordHistory findOneByTriggerTimeRecord(String startTime, String endTime);


    @Modifying
    @Transactional
    @Query(value = "delete from t_sms_record_history t where\n" +
            " t.trigger_time >= :startTime \n" +
            "and t.trigger_time < :endTime limit 1 \n"
            , nativeQuery = true)
    int deleteByTriggerTimeRecord(String startTime, String endTime);

    @Modifying
    @Transactional
    @Query(value = "delete from t_sms_record_history t where\n" +
            " t.trigger_time >= :startTime \n" +
            "and t.trigger_time < :endTime \n" +
            "and t.group_id = :groupId \n"
            , nativeQuery = true)
    int deleteByTriggerTimeRecordByGroupId(String startTime, String endTime, String groupId);

    @Query(value = "select t.group_id as groupId, count(*) as recordNum from t_sms_record_history t \n" +
            "where  \n" +
            "t.trigger_time >= :startTime \n" +
            "and t.trigger_time < :endTime \n" +
            " group by t.group_id", nativeQuery = true)
    List<Tuple> findCountByTimeGroupByGroupId(String startTime, String endTime);

    @Query(value = "select count(*) from t_sms_record_history t where  t.trigger_time >= :startTime and t.trigger_time < :endTime", nativeQuery = true)
    Integer findCountByTime(String startTime, String endTime);

    @Query(value = "select count(*) from t_sms_record_history t where  t.trigger_time >= :startTime and t.trigger_time < :endTime and group_id = :groupId", nativeQuery = true)
    Integer findCountByTimeAndGroupId(String startTime, String endTime, String groupId);

    @Query(value = "select * from t_sms_record_history t \n" +
            "where t.trigger_time >= :startTime \n" +
            "and t.trigger_time < :endTime \n" +
            "limit :limitNum  \n", nativeQuery = true)
    List<SmsRecordHistory> findCallRecordByTime(String startTime, String endTime, int limitNum);

}
