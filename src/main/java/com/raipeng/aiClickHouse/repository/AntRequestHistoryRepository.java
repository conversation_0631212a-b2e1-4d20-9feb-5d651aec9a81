package com.raipeng.aiClickHouse.repository;



import com.raipeng.aiClickHouse.model.AntRequestHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

public interface AntRequestHistoryRepository extends JpaRepository<AntRequestHistory, Long> {
    @Transactional
    @Modifying
    @Query(value = "delete from ant_request_history   \n" +
            "where create_time < :createTime ", nativeQuery = true)
    void deleteByCreateTime(LocalDateTime createTime);
}
