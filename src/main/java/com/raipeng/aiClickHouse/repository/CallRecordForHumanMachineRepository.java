package com.raipeng.aiClickHouse.repository;

import com.raipeng.aiClickHouse.model.record.CallRecordForHumanMachine;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Tuple;
import java.util.List;

public interface CallRecordForHumanMachineRepository extends JpaRepository<CallRecordForHumanMachine, String> {

    @Query(value = "select * from call_record_for_human_machine " +
            "where update_time >= cast(:startTime AS timestamp) and update_time < cast(:endTime AS timestamp) and call_status is not null", nativeQuery = true)
    List<CallRecordForHumanMachine> findCallRecordWithPeriod(String startTime, String endTime);

    @Query(value = "select * from call_record_for_human_machine t \n" +
            "where t.call_out_time >= :startTime \n" +
            "and t.call_out_time < :endTime \n"
            , nativeQuery = true)
    List<CallRecordForHumanMachine> findByCallOutTimeRecord(String startTime, String endTime);


    @Query(value = "select * from call_record_for_human_machine " +
            "where update_time >= cast(:startTime AS timestamp) and update_time < cast(:endTime AS timestamp)", nativeQuery = true)
    List<CallRecordForHumanMachine> findByUpdateTimeRecord(String startTime, String endTime);

    @Query(value = "select * from call_record_for_human_machine t \n" +
            "where t.call_out_time >= :startTime \n" +
            "and t.call_out_time < :endTime limit 5000\n"
            , nativeQuery = true)
    List<CallRecordForHumanMachine> findByCallOutTimeRecord5000(String startTime, String endTime);


    @Query(value = "select count(*) from call_record_for_human_machine r where r.call_out_time >= :startTime\n" +
            "and r.call_out_time < :endTime", nativeQuery = true)
    Integer findCallRecordNums(String startTime, String endTime);


    @Query(value = "select * from call_record_for_human_machine t \n" +
            "where t.record_id = :recordId limit 1\n"
            , nativeQuery = true)
    CallRecordForHumanMachine findCallRecordNumsByRecordId(String recordId);

    @Query(value = "select distinct t.task_id from call_record_for_human_machine t \n" +
            "where t.call_out_time >= :startTime \n" +
            "and t.call_out_time < :endTime \n", nativeQuery = true)
    List<String> findTaskIdListByTime(String startTime, String endTime);

    @Query(value = "select * from call_record_for_human_machine t \n" +
            "where t.task_id in :taskIdList \n", nativeQuery = true)
    List<CallRecordForHumanMachine> findCallRecordByTaskIdList(List<String> taskIdList);


    @Query(value = "select * from call_record_for_human_machine t \n" +
            "where t.task_id = :taskId \n" +
            "and t.call_out_time >= :startTime \n" +
            "and t.call_out_time < :endTime", nativeQuery = true)
    List<CallRecordForHumanMachine> findCallRecordByTaskIdAndCallOutTime(String taskId, String startTime, String endTime);

    @Query(value = "select * from call_record_for_human_machine t \n" +
            "where t.task_id = :taskId \n", nativeQuery = true)
    List<CallRecordForHumanMachine> findCallRecordByTaskId(String taskId);

    @Query(value = "select count(*) from call_record_for_human_machine r", nativeQuery = true)
    Integer findAllCallRecordNums();

    @Query(value = "select count(*) from call_record_for_human_machine where call_out_time is null", nativeQuery = true)
    Integer findAllCallOutTimeNullRecordNums();

    @Query(value = "select * from call_record_for_human_machine t \n" +
            "where t.id in :callIdList", nativeQuery = true)
    List<CallRecordForHumanMachine> findCallRecordListByIdList(List<String> callIdList);

    @Modifying
    @Transactional
    @Query(value = "TRUNCATE TABLE call_record_for_human_machine", nativeQuery = true)
    void truncateCallRecord();

    @Query(value = "select t.task_id as taskId, count(*) as recordNum from call_record_for_human_machine t \n" +
            "where  \n" +
            "t.call_out_time >= :startTime \n" +
            "and t.call_out_time < :endTime \n" +
            " group by t.task_id", nativeQuery = true)
    List<Tuple> findCallRecordNumGroupByTaskId(String startTime, String endTime);

    @Query(value = "select * from call_record_for_human_machine t \n" +
            "where  \n" +
            "task_id = :taskId \n"+
            " and call_status is null", nativeQuery = true)
    List<CallRecordForHumanMachine> findUnCallRecordByTaskId(String taskId);

    @Query(value = "select * from call_record_for_human_machine  \n" +
            "where record_id in :recordIdList", nativeQuery = true)
    List<CallRecordForHumanMachine> findCallRecordListByRecordIdList(List<String> recordIdList);
}
