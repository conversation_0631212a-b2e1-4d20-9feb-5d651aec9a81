package com.raipeng.aiClickHouse.repository;



import com.raipeng.aiClickHouse.model.record.CallRecordHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Tuple;
import java.util.List;

public interface CallRecordHistoryRepository extends JpaRepository<CallRecordHistory, Long> {

    @Query(value = "select * from t_call_record_history t \n" +
            "where t.call_out_time >= :startTime \n" +
            "and t.call_out_time < :endTime \n"
            , nativeQuery = true)
    List<CallRecordHistory> findByCallOutTimeRecord(String startTime, String endTime);

    @Query(value = "select count(*) from t_call_record_history r where r.call_out_time >= :startTime\n" +
            "and r.call_out_time < :endTime", nativeQuery = true)
    Integer findCallRecordNums(String startTime, String endTime);

    @Query(value = "select * from t_call_record_history t \n" +
            "where t.call_out_time >= :startTime \n" +
            "and t.call_out_time < :endTime \n" +
            "order by t.id \n" +
            "limit :limitNum OFFSET :offset \n", nativeQuery = true)
    List<CallRecordHistory> findCallRecordByTime(String startTime, String endTime, Integer limitNum, Integer offset);

    @Modifying
    @Transactional
    @Query(value = "delete from t_call_record_history t\n" +
            "where t.task_id = :taskId \n", nativeQuery = true)
    int deleteByTaskId(String taskId);

    @Query(value = "select t.task_id as taskId, count(*) as recordNum from t_call_record_history t \n" +
            "where  \n" +
            "t.call_out_time >= :startTime \n" +
            "and t.call_out_time < :endTime \n"+
            " group by t.task_id", nativeQuery = true)
    List<Tuple> findCallRecordNumGroupByTaskId(String startTime, String endTime);


    @Transactional
    @Modifying
    @Query(value =
            "insert into t_call_record_0319 (\n" +
            "    id,\n" +
            "    call_duration,\n" +
            "    call_duration_sec,\n" +
            "    call_id,\n" +
            "    call_status,\n" +
            "    cause,\n" +
            "    create_time,\n" +
            "    cycle_count,\n" +
            "    error_code,\n" +
            "    intention_class,\n" +
//            "    intention_label,\n" +
            "    line_id,\n" +
            "    phone,\n" +
            "    record_id,\n" +
            "    say_count,\n" +
            "    script_string_id,\n" +
            "    speech_craft_id,\n" +
            "    status,\n" +
            "    task_id,\n" +
            "    update_time,\n" +
            "    user_full_answer_content,\n" +
            "    who_hangup,\n" +
            "    whole_audio_file_url,\n" +
            "    call_out_time,\n" +
            "    contact_time,\n" +
            "    intention_label_ids,\n" +
            "    intention_labels,\n" +
            "    talk_time_end,\n" +
            "    talk_time_start,\n" +
            "    task_name,\n" +
            "    hit_answer_ids,\n" +
//            "    call_num,\n" +
            "    city,\n" +
            "    operator,\n" +
            "    province,\n" +
            "    ai_call_ip,\n" +
            "    call_status_str,\n" +
            "    fs_ip,\n" +
            "    if_recall,\n" +
            "    if_send_sms,\n" +
            "    phone_record_id,\n" +
            "    redis_key,\n" +
            "    tenant_name,\n" +
            "    line_code,\n" +
//            "    merchant_code,\n" +
//            "    merchant_id,\n" +
            "    city_code,\n" +
            "    merchant_line_code,\n" +
            "    merchant_line_id,\n" +
            "    account,\n" +
            "    province_code,\n" +
            "    tenant_code,\n" +
            "    hit_advance_ids,\n" +
            "    waitmsec,\n" +
            "    if_test,\n" +
            "    corpus_ids,\n" +
            "    if_fast_recall,\n" +
            "    hit_semantic_ids,\n" +
            "    line_gateway_numbers\n" +
            ")\n" +
            "SELECT \n" +
            "    id,\n" +
            "    call_duration,\n" +
            "    call_duration_sec,\n" +
            "    call_id,\n" +
            "    call_status,\n" +
            "    cause,\n" +
            "    create_time,\n" +
            "    cycle_count,\n" +
            "    error_code,\n" +
            "    intention_class,\n" +
//            "    intention_label,\n" +
            "    line_id,\n" +
            "    phone,\n" +
            "    record_id,\n" +
            "    say_count,\n" +
            "    script_string_id,\n" +
            "    speech_craft_id,\n" +
            "    status,\n" +
            "    task_id,\n" +
            "    update_time,\n" +
            "    user_full_answer_content,\n" +
            "    who_hangup,\n" +
            "    whole_audio_file_url,\n" +
            "    call_out_time,\n" +
            "    contact_time,\n" +
            "    intention_label_ids,\n" +
            "    intention_labels,\n" +
            "    talk_time_end,\n" +
            "    talk_time_start,\n" +
            "    task_name,\n" +
            "    hit_answer_ids,\n" +
//            "    call_num,\n" +
            "    city,\n" +
            "    operator,\n" +
            "    province,\n" +
            "    ai_call_ip,\n" +
            "    call_status_str,\n" +
            "    fs_ip,\n" +
            "    if_recall,\n" +
            "    if_send_sms,\n" +
            "    phone_record_id,\n" +
            "    redis_key,\n" +
            "    tenant_name,\n" +
            "    line_code,\n" +
//            "    merchant_code,\n" +
//            "    merchant_id,\n" +
            "    city_code,\n" +
            "    merchant_line_code,\n" +
            "    merchant_line_id,\n" +
            "    account,\n" +
            "    province_code,\n" +
            "    tenant_code,\n" +
            "    hit_advance_ids,\n" +
            "    waitmsec,\n" +
            "    if_test,\n" +
            "    corpus_ids,\n" +
            "    if_fast_recall,\n" +
            "    hit_semantic_ids,\n" +
            "    line_gateway_numbers\n" +
            "FROM t_call_record_history where call_out_time >= :startTime and call_out_time < :endTime ", nativeQuery = true)
    Integer insertIntoOtherTable(String startTime, String endTime);


    @Query(value = "select distinct task_id from t_call_record_history t \n" +
            "where t.call_out_time >= :startTime \n" +
            "and t.call_out_time < :endTime  \n", nativeQuery = true)
    List<String> findDistinctTaskByCallOutTime(String startTime, String endTime);
}