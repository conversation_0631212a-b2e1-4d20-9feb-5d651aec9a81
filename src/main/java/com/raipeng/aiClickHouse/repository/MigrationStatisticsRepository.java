package com.raipeng.aiClickHouse.repository;

import com.raipeng.aiClickHouse.model.MigrationStatistics;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;


public interface MigrationStatisticsRepository extends JpaRepository<MigrationStatistics, Long> {

    @Query(value = "select * from t_migration_statistics t " +
            "where date = :date limit 1", nativeQuery = true)
    MigrationStatistics findMigrationStatisticsByDate(String date);

    @Transactional
    @Modifying
    @Query(value = "update t_migration_statistics " +
            "set db_call_record_num = :dbCallRecordNum," +
            "db_phone_record_num = :dbPhoneRecordNum" +
            " where date = :date", nativeQuery = true)
    void updateMigrationStatisticsByDate(String date, Integer dbCallRecordNum, Integer dbPhoneRecordNum);
}
