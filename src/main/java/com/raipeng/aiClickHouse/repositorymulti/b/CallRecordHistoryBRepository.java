package com.raipeng.aiClickHouse.repositorymulti.b;


import com.raipeng.aiClickHouse.model.record.CallRecordHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Tuple;
import java.util.List;

public interface CallRecordHistoryBRepository extends JpaRepository<CallRecordHistory, Long> {

    @Query(value = "select * from t_call_record_history t \n" +
            "where t.call_out_time >= :startTime \n" +
            "and t.call_out_time < :endTime \n"
            , nativeQuery = true)
    List<CallRecordHistory> findByCallOutTimeRecord(String startTime, String endTime);

    @Query(value = "select count(*) from t_call_record_history r where r.call_out_time >= :startTime\n" +
            "and r.call_out_time < :endTime", nativeQuery = true)
    Integer findCallRecordNums(String startTime, String endTime);

    @Query(value = "select * from t_call_record_history t \n" +
            "where t.call_out_time >= :startTime \n" +
            "and t.call_out_time < :endTime \n" +
            "limit :limitNum OFFSET :offset \n", nativeQuery = true)
    List<CallRecordHistory> findCallRecordByTime(String startTime, String endTime, Integer limitNum, Integer offset);

    @Modifying
    @Transactional
    @Query(value = "delete from t_call_record_history t\n" +
            "where t.task_id = :taskId and  t.call_out_time >= :startTime and  t.call_out_time < :endTime \n", nativeQuery = true)
    int deleteByTaskIdAndTime(String taskId, String startTime, String endTime);

    @Modifying
    @Transactional
    @Query(value = "delete from t_call_record_history t\n" +
            "where t.task_id = :taskId ", nativeQuery = true)
    int deleteByTaskId(String taskId);

    @Query(value = "select t.task_id as taskId, count(*) as recordNum from t_call_record_history t \n" +
            "where  \n" +
            "t.call_out_time >= :startTime \n" +
            "and t.call_out_time < :endTime \n" +
            " group by t.task_id", nativeQuery = true)
    List<Tuple> findCallRecordNumGroupByTaskId(String startTime, String endTime);

    @Query(value = "select distinct task_id from t_call_record_history  \n" +
            "where call_out_time >= :startTime \n" +
            "and call_out_time < :endTime \n", nativeQuery = true)
    List<String> findDistinctTaskByCallOutTime(String startTime, String endTime);

}