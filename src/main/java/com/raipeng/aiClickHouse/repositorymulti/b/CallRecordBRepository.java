package com.raipeng.aiClickHouse.repositorymulti.b;


import com.raipeng.aiClickHouse.model.record.CallRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Tuple;
import java.util.List;

public interface CallRecordBRepository extends JpaRepository<CallRecord,Long> {

    @Query(value = "select * from t_call_record t \n" +
            "where t.call_out_time >= :startTime \n" +
            "and t.call_out_time < :endTime \n"
            , nativeQuery = true)
    List<CallRecord> findByCallOutTimeRecord(String startTime, String endTime);

    @Query(value="select * from t_call_record " +
            "where update_time >= cast(:startTime AS timestamp) and update_time < cast(:endTime AS timestamp)", nativeQuery = true)
    List<CallRecord> findByUpdateTimeRecord(String startTime, String endTime);

    @Query(value = "select * from t_call_record t \n" +
            "where t.call_out_time >= :startTime \n" +
            "and t.call_out_time < :endTime limit 5000\n"
            , nativeQuery = true)
    List<CallRecord> findByCallOutTimeRecord5000(String startTime, String endTime);

    @Query(value = "select * from t_call_record t1 inner join t_call_record_0319 t2 on t1.record_id = t2.record_id\n" +
            "where t1.call_out_time >= :startTime \n" +
            "and t1.call_out_time < :endTime \n" +
            "and t1.task_id = :taskId"
            , nativeQuery = true)
    List<CallRecord> recordTest(String startTime, String endTime, String taskId);


    @Query(value="select * from t_call_record " +
            "where update_time >= cast(:startTime AS timestamp) and update_time < cast(:endTime AS timestamp) and call_status is not null", nativeQuery = true)
    List<CallRecord> findCallRecordWithPeriod(String startTime, String endTime);

    @Query(value = "select count(*) from t_call_record r where r.call_out_time >= :startTime\n" +
            "and r.call_out_time < :endTime", nativeQuery = true)
    Integer findCallRecordNums(String startTime, String endTime);


    @Query(value = "select * from t_call_record t \n" +
            "where t.record_id = :recordId limit 1\n"
            , nativeQuery = true)
    CallRecord findCallRecordNumsByRecordId(String recordId);

    @Query(value = "select distinct t.task_id from t_call_record t \n" +
            "where t.call_out_time >= :startTime \n" +
            "and t.call_out_time < :endTime \n", nativeQuery = true)
    List<String> findTaskIdListByTime(String startTime, String endTime);

    @Query(value = "select * from t_call_record t \n" +
            "where t.task_id in :taskIdList \n", nativeQuery = true)
    List<CallRecord> findCallRecordByTaskIdList(List<String> taskIdList);


    @Query(value = "select * from t_call_record t \n" +
            "where t.task_id = :taskId \n" +
            "and t.call_out_time >= :startTime \n" +
            "and t.call_out_time < :endTime", nativeQuery = true)
    List<CallRecord> findCallRecordByTaskIdAndCallOutTime(String taskId, String startTime, String endTime);

    @Query(value = "select * from t_call_record t \n" +
            "where t.task_id = :taskId \n" , nativeQuery = true)
    List<CallRecord> findCallRecordByTaskId(String taskId);

    @Query(value = "select count(*) from t_call_record r", nativeQuery = true)
    Integer findAllCallRecordNums();

    @Query(value = "select count(*) from t_call_record where call_out_time is null", nativeQuery = true)
    Integer findAllCallOutTimeNullRecordNums();

    @Query(value = "select * from t_call_record t \n" +
            "where t.id in :callIdList", nativeQuery = true)
    List<CallRecord> findCallRecordListByIdList(List<String> callIdList);

    @Modifying
    @Transactional
    @Query(value = "TRUNCATE TABLE t_call_record", nativeQuery = true)
    void truncateCallRecord();

    @Query(value = "select t.task_id as taskId, count(*) as recordNum from t_call_record t \n" +
            "where  \n" +
            "t.call_out_time >= :startTime \n" +
            "and t.call_out_time < :endTime \n"+
            " group by t.task_id", nativeQuery = true)
    List<Tuple> findCallRecordNumGroupByTaskId(String startTime, String endTime);

    @Query(value = "select * from t_call_record \n" +
            " where task_id = :taskId \n"+
            " and call_status is null", nativeQuery = true)
    List<CallRecord> findUnCallRecordByTaskId(String taskId);


    @Query(value = "select * from t_call_record  \n" +
            " where record_id in :recordIdList", nativeQuery = true)
    List<CallRecord> findCallRecordListByRecordIdList(List<String> recordIdList);

}
