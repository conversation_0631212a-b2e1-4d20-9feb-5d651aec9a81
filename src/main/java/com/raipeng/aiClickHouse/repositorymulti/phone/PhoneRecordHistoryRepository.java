package com.raipeng.aiClickHouse.repositorymulti.phone;


import com.raipeng.aiClickHouse.model.phone.PhoneRecordHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Tuple;
import java.time.LocalDateTime;
import java.util.List;

public interface PhoneRecordHistoryRepository extends JpaRepository<PhoneRecordHistory, Long> {
    @Query(value = "select count(*) from t_phone_record_history t\n" +
            "where t.create_time >= :startTime \n" +
            "and t.create_time < :endTime", nativeQuery = true)
    Integer findPhoneRecordNums(LocalDateTime startTime, LocalDateTime endTime);


    @Query(value = "select * from t_phone_record_history t \n" +
            "where t.create_time >= :startTime \n" +
            "and t.create_time < :endTime \n" +
            "limit :limitNum OFFSET :offset  \n", nativeQuery = true)
    List<PhoneRecordHistory> findPhoneRecordByTime(LocalDateTime startTime, LocalDateTime endTime, Integer limitNum, Integer offset);


    @Modifying
    @Transactional
    @Query(value = "delete from t_phone_record_history t\n" +
            "where t.task_id = :taskId and  t.create_time >= :startTime and  t.create_time < :endTime \n", nativeQuery = true)
    int deleteByTaskIdAndTime(String taskId, LocalDateTime startTime, LocalDateTime endTime);

    @Modifying
    @Transactional
    @Query(value = "delete from t_phone_record_history t\n" +
            "where t.task_id = :taskId ", nativeQuery = true)
    int deleteByTaskId(String taskId);

    @Query(value = "select t.task_id as taskId, count(*) as recordNum from t_phone_record_history t \n" +
            "where t.create_time >= :startTime \n" +
            "and t.create_time < :endTime \n" +
            "group by t.task_id", nativeQuery = true)
    List<Tuple> findPhoneRecordNumGroupByTaskId(LocalDateTime startTime, LocalDateTime endTime);

    @Query(value = "select distinct task_id from t_phone_record_history t \n" +
            "where t.create_time >= :startTime \n" +
            "and t.create_time < :endTime  \n", nativeQuery = true)
    List<String> findDistinctTaskByCallOutTime(LocalDateTime startTime, LocalDateTime endTime);
}
