package com.raipeng.aiClickHouse;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.client.RestTemplate;

//@EnableJpaRepositories
@EnableCaching
@SpringBootApplication
@EnableFeignClients
@EnableScheduling
//@EntityScan("com.raipeng.aiClickHouse.model")
//@ServletComponentScan(basePackages = "com.raipeng.aispeech.listener")
//@MapperScan(value = "com.raipeng.aispeech.dal.mapper")
@EnableDiscoveryClient
public class AiClickHouseApplication {

    public static void main(String[] args) {
        SpringApplication.run(AiClickHouseApplication.class, args);
    }

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

}
