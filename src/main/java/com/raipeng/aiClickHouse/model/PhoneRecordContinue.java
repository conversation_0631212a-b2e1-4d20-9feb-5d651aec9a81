package com.raipeng.aiClickHouse.model;

import com.raipeng.aidatacommon.entity.VariableSmsValuePojo;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.Type;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "t_phone_record_continue")
@Data
@ToString(callSuper = true)
@NoArgsConstructor
public class PhoneRecordContinue implements Serializable {
    @Id
    private String id;
    @Column(name = "create_time", nullable = false, columnDefinition = "TIMESTAMP")
    private LocalDateTime createTime;
    @Column(name = "update_time", nullable = false, columnDefinition = "TIMESTAMP")
    private LocalDateTime updateTime;

    @Column(name = "task_id", nullable = true, columnDefinition = "varchar(100)")
    private String taskId;

    @Column(name = "task_name", nullable = true, columnDefinition = "varchar(100)")
    private String taskName;

    @Column(name = "phone", nullable = true, columnDefinition = "varchar(50) ")
    private String phone;

    @Column(name = "tenant_name", columnDefinition = " varchar(100) DEFAULT NULL ")
    private String tenantName;

    @Column(name = "operator", nullable = true, columnDefinition = "varchar(50) ")
    private String operator;

    @Column(name = "province", nullable = true, columnDefinition = "varchar(50) ")
    private String province;
    @Column(name = "province_code", nullable = true, columnDefinition = "varchar(50) ")
    private String provinceCode;
    @Column(name = "city", nullable = true, columnDefinition = "varchar(50) ")
    private String city;

    @Column(name = "cityCode", nullable = true, columnDefinition = "varchar(50) ")
    private String cityCode;

    @Column(name = "callStatus", nullable = true, columnDefinition = "varchar(50) ")
    private String callStatus;

    @Column(name = "put_through_num", nullable = true, columnDefinition = "int")
    private Integer putThroughNum;

    @Column(name = "called_num", nullable = true, columnDefinition = "int")
    private Integer calledNum;

    @Column(name = "if_send_sms", nullable = false, columnDefinition = "varchar(10) default '否'")
    private String ifSendSms = "否";

    @Column(name = "script_string_id", nullable = true, columnDefinition = "varchar(100) ")
    private String scriptStringId;

    @Column(name = "script_name", nullable = true, columnDefinition = "varchar(100)")
    private String scriptName;

    @Column(name = "script_long_id", nullable = true, columnDefinition = "int")
    private Long scriptLongId;

    @Column(name = "version", nullable = true, columnDefinition = "int")
    private Integer version;

    @Column(name = "record_id", nullable = true, columnDefinition = "varchar(100)")
    private String recordId;

    @Column(name = "latest_record_id", nullable = true, columnDefinition = "varchar(100)")
    private String latestRecordId;

    @Column(name = "add_time", nullable = true, columnDefinition = "varchar(100)")
    private String addTime;

    @Column(name = "msg", nullable = true, columnDefinition = "varchar(200)")
    private String msg;

    @Column(name = "final_call_time", nullable = true, columnDefinition = "varchar(100)")
    private String finalCallTime;

    @Column(name = "if_recalling", nullable = true, columnDefinition = "int")
    private Integer ifRecalling = 0;

    @Column(name = "latest_intention_class", nullable = true, columnDefinition = "varchar(100)")
    private String latestIntentionClass;

    @Type(type = "jsonb")
    @Column(name = "variable_sms_value", nullable = true, columnDefinition = "jsonb")
    private List<VariableSmsValuePojo> variableSmsValue;

    @Column(name = "name", nullable = true, columnDefinition = "varchar(50)")
    private String name;

    @Column(name = "company", nullable = true, columnDefinition = "varchar(100)")
    private String company;

    @Column(name = "remarks", nullable = true, columnDefinition = "varchar(200)")
    private String remarks;
}
