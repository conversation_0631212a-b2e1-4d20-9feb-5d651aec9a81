package com.raipeng.aiClickHouse.service;


import com.raipeng.aiClickHouse.entity.ClusterSyncMonitorRecord;
import com.raipeng.aiClickHouse.feign.CommonMonitorFeign;
import com.raipeng.aiClickHouse.feign.DialogPT1RecordFeign;
import com.raipeng.aiClickHouse.feign.DialogPT2RecordFeign;
import com.raipeng.aiClickHouse.feign.DialogPT3RecordFeign;
import com.raipeng.aiClickHouse.repository.CallRecordForHumanMachineHistoryRepository;
import com.raipeng.aiClickHouse.repository.CallRecordForManualDirectRepository;
import com.raipeng.aiClickHouse.repository.SmsRecordHistoryRepository;
import com.raipeng.aiClickHouse.repositorymulti.a.CallRecordHistoryARepository;
import com.raipeng.aiClickHouse.repositorymulti.b.CallRecordHistoryBRepository;
import com.raipeng.aiClickHouse.repositorymulti.c.CallRecordHistoryCRepository;
import com.raipeng.aiClickHouse.repositorymulti.phone.PhoneRecordHistoryRepository;
import com.raipeng.common.util.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UploadDataService {
    @Autowired
    public CommonMonitorFeign commonMonitorFeign;

    @Autowired
    public CallRecordHistoryARepository callRecordHistoryARepository;

    @Autowired
    public CallRecordHistoryBRepository callRecordHistoryBRepository;

    @Autowired
    public CallRecordHistoryCRepository callRecordHistoryCRepository;

    @Autowired
    public PhoneRecordHistoryRepository phoneRecordHistoryRepository;
    @Autowired
    public DialogPT1RecordFeign dialogPT1RecordFeign;
    @Autowired
    public DialogPT2RecordFeign dialogPT2RecordFeign;
    @Autowired
    public DialogPT3RecordFeign dialogPT3RecordFeign;

    @Autowired
    public CallRecordForHumanMachineHistoryRepository callRecordForHumanMachineHistoryRepository;

    @Autowired
    public SmsRecordHistoryRepository smsRecordHistoryRepository;

    @Autowired
    public CallRecordForManualDirectRepository callRecordForManualDirectRepository;


    public void uploadDbData(String day, String startCallOutTime, String endCallOutTime, List<String> typeList) {
        if (typeList.contains("t_call_record")) {
            Integer callRecordNumsA = callRecordHistoryARepository.findCallRecordNums(startCallOutTime, endCallOutTime);
            Integer callRecordNumsB = callRecordHistoryBRepository.findCallRecordNums(startCallOutTime, endCallOutTime);
            Integer callRecordNumsC = callRecordHistoryCRepository.findCallRecordNums(startCallOutTime, endCallOutTime);
            commonMonitorFeign.uploadDbData("t_call_record", day, (long) (callRecordNumsA + callRecordNumsB + callRecordNumsC));
        }
        if (typeList.contains("call_record_for_human_machine")) {
            Integer callRecordForHumanMachineNums = callRecordForHumanMachineHistoryRepository.findCallRecordNums(startCallOutTime, endCallOutTime);
            commonMonitorFeign.uploadDbData("call_record_for_human_machine", day, (long) callRecordForHumanMachineNums);

        }
        if (typeList.contains("t_phone_record")) {
            LocalDateTime startDate = LocalDateTime.parse(startCallOutTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            LocalDateTime endDate = LocalDateTime.parse(endCallOutTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

            Integer phoneRecordNums = phoneRecordHistoryRepository.findPhoneRecordNums(startDate, endDate);
            commonMonitorFeign.uploadDbData("t_phone_record", day, (long) phoneRecordNums);
        }
        if (typeList.contains("t_sms_record")) {
            Integer smsRecordNum = smsRecordHistoryRepository.findCountByTime(startCallOutTime, endCallOutTime);
            commonMonitorFeign.uploadDbData("t_sms_record", day, (long) smsRecordNum);
        }

        if (typeList.contains("call_record_for_manual_direct")) {
            Integer callRecordNums = callRecordForManualDirectRepository.findCallRecordNums(startCallOutTime, endCallOutTime);
            commonMonitorFeign.uploadDbData("call_record_for_manual_direct", day, (long) callRecordNums);
        }
        if (typeList.contains("t_dialog_record")) {
            Integer dialogRecordCountByDate1 = dialogPT1RecordFeign.getDialogRecordCountByDate(day);
            Integer dialogRecordCountByDate2 = dialogPT2RecordFeign.getDialogRecordCountByDate(day);
            Integer dialogRecordCountByDate3 = dialogPT3RecordFeign.getDialogRecordCountByDate(day);
            commonMonitorFeign.uploadDbData("t_dialog_record", day, (long) dialogRecordCountByDate1 + dialogRecordCountByDate2 + dialogRecordCountByDate3);
        }
        if (typeList.contains("t_cdr_record")) {
            Integer dialogRecordCountByDate1 = dialogPT1RecordFeign.getCdrRecordCountByDate(day);
            Integer dialogRecordCountByDate2 = dialogPT2RecordFeign.getCdrRecordCountByDate(day);
            Integer dialogRecordCountByDate3 = dialogPT3RecordFeign.getCdrRecordCountByDate(day);
            commonMonitorFeign.uploadDbData("t_cdr_record", day, (long) dialogRecordCountByDate1 + dialogRecordCountByDate2 + dialogRecordCountByDate3);

        }
        if (typeList.contains("t_early_media_dialog_record")) {
            Integer dialogRecordCountByDate1 = dialogPT1RecordFeign.getEarlyMediaDialogRecordCountByDate(day);
            Integer dialogRecordCountByDate2 = dialogPT2RecordFeign.getEarlyMediaDialogRecordCountByDate(day);
            Integer dialogRecordCountByDate3 = dialogPT3RecordFeign.getEarlyMediaDialogRecordCountByDate(day);
            commonMonitorFeign.uploadDbData("t_early_media_dialog_record", day, (long) dialogRecordCountByDate1 + dialogRecordCountByDate2 + dialogRecordCountByDate3);

        }


    }

    public void checkDbData(LocalDateTime startDate, String table) {
        String localDate = startDate.toLocalDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        List<ClusterSyncMonitorRecord> tCallRecord = commonMonitorFeign.getDaysTableNameData(table, localDate);
        if (CollectionUtil.isEmpty(tCallRecord)) {
            log.info("Exception: 该日期{}，表{}的数据未同步", localDate, table);
        } else {
            Map<String, ClusterSyncMonitorRecord> collect = tCallRecord.stream().collect(Collectors.toMap(ClusterSyncMonitorRecord::getSyncDt, Function.identity()));
            ClusterSyncMonitorRecord clusterSyncMonitorRecord = collect.get(localDate);
            if (clusterSyncMonitorRecord == null) {
                log.info("Exception: 该日期{}，表{}的数据未同步", localDate, table);
            } else {
                if (clusterSyncMonitorRecord.getSyncDbNum() == null || clusterSyncMonitorRecord.getSyncClusterNum() == null) {
                    log.info("Exception: 该日期{}，表{}的数据集群数量{}   数据库数量{}", localDate, table, clusterSyncMonitorRecord.getSyncClusterNum(), clusterSyncMonitorRecord.getSyncDbNum());
                } else if (clusterSyncMonitorRecord.getSyncDbNum() > clusterSyncMonitorRecord.getSyncClusterNum()) {
                    log.info("Exception: 该日期{}，表{} 数据库表数据量{} 大于集群数据{}的数据，可能是话术训练数据导致的",
                            localDate, table,
                            clusterSyncMonitorRecord.getSyncDbNum(), clusterSyncMonitorRecord.getSyncClusterNum());
                } else if (clusterSyncMonitorRecord.getSyncDbNum() < clusterSyncMonitorRecord.getSyncClusterNum()) {
                    log.info("该日期{}，表{} 数据库表数据量{} 小于集群数据{}的数据",
                            localDate, table,
                            clusterSyncMonitorRecord.getSyncDbNum(), clusterSyncMonitorRecord.getSyncClusterNum());
                }
            }

        }
    }
}
