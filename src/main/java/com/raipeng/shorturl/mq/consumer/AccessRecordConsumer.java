package com.raipeng.shorturl.mq.consumer;

import com.raipeng.shorturl.constant.RabbitMqConstants;
import com.raipeng.shorturl.model.AccessRecord;
import com.raipeng.shorturl.service.AccessRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description: 授信成功-消费者
 * @date ：2021/12/14
 */
@Slf4j
@Component
public class AccessRecordConsumer {

    @Autowired
    private AccessRecordService accessRecordService;

    @RabbitListener(queues = {RabbitMqConstants.ACCESS_RECORD_QUEUE},concurrency="6")
    public void handle(AccessRecord accessRecord) {
        if (accessRecord != null) {
            try {
                accessRecordService.save(accessRecord);
            } catch (Exception e) {
                log.error("short long url save to dbError:", e);
            }
        }
    }
}
