package com.raipeng.shorturl.controller.entity;

import lombok.Data;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;

@Data
public class CrcExpireListKeyData implements Delayed {
    private String key;
    private final Long delayTime;

    public CrcExpireListKeyData(Long delayTime,String key){
        this.delayTime = LocalDateTime.now().plusSeconds(delayTime/1000).toInstant(ZoneOffset.of("+8")).toEpochMilli();
        this.key = key;
    }

    @Override
    public long getDelay(TimeUnit unit) {
        return unit.convert(delayTime - System.currentTimeMillis(), TimeUnit.MILLISECONDS);
    }

    @Override
    public int compareTo(Delayed o) {
        if (this == o) {
            return 0;
        }
        if (o instanceof CrcExpireListKeyData) {
            CrcExpireListKeyData other = (CrcExpireListKeyData) o;
            return Long.compare(this.getDelay(TimeUnit.MILLISECONDS), other.getDelay(TimeUnit.MILLISECONDS));
        }
        return 0;
    }
}
