package com.raipeng.shorturl.controller.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(description = "禁止词参数")
public class ForbiddenWordParam implements Serializable {

    @ApiModelProperty(value = "ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "敏感词", example = "赌博")
    private String forbiddenWord;

    @ApiModelProperty(value = "更新者", example = "zwb")
    private String updateBy;

    @ApiModelProperty(value = "是否正则", example = "0")
    private Boolean isRegex;

    @ApiModelProperty(value = "来源", example = "bz")
    private String source;

    @ApiModelProperty(value = "备注", example = "这是一个敏感词")
    private String remarks;

}
