package com.raipeng.shorturl.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.*;

@Slf4j
public class ExceptionUtils {

    public static String getExceptionStr(Exception e)  {
        //读取异常栈信息
        StringBuilder exceptionStr=new StringBuilder();
        try{
            ByteArrayOutputStream arrayOutputStream=new ByteArrayOutputStream();
            e.printStackTrace(new PrintStream(arrayOutputStream));
            //通过ByteArray转换输入输出流
            BufferedReader fr=new BufferedReader(new InputStreamReader(new ByteArrayInputStream(arrayOutputStream.toByteArray())));
            String str;

            while ((str=fr.readLine())!=null){
                exceptionStr.append(str).append("\n");
            }
            //关闭流
            fr.close();
        }catch (Exception ex){
            log.error(ex.getMessage());
        }
        return exceptionStr.toString();
    }}
