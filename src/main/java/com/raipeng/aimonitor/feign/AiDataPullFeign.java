package com.raipeng.aimonitor.feign;

import com.raipeng.aidatacommon.model.dto.TenantSupplySampling;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(name = "ai-data-pull", url = "http://${service.ai.data.pull.address}")
public interface AiDataPullFeign {
    @PostMapping("/TenantSupply/samplingAIAutoRecord")
    Map<String, Integer> samplingAIAutoRecord(@RequestBody List<TenantSupplySampling> records,
                                              @RequestParam String tenantLineNumber,
                                              @RequestParam String supplyLineNumber);


    @PostMapping("/TenantSupply/tenantSupplyLineCache")
    Integer tenantSupplyLineCache(@RequestParam String tenantLineNumber,
                                              @RequestParam String supplyLineNumber);
}
