package com.raipeng.aimonitor.controller.response;

import com.raipeng.aidatacommon.enums.LineType;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@EqualsAndHashCode(callSuper = true)
public class SupplyLineMonitor extends MonitorBase {
    /**
     * 供应线路名称
     */
    private String supplyLineName;

    /**
     * 供应线路编号
     */
    private String supplyLineNumber;

    /**
     * 供应线路类型
     */
    private LineType supplyLineType;

    /**
     * 主叫号码
     */
    private String masterCallNumber;
    /**
     * 前缀
     */
    private String prefix;

    /**
     * 并发（实际/占用/上限）
     */
    private String concurrency;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商编号
     */
    private String supplierNumber;

    /**
     * 挂起状态
     */
    private Boolean isPending;

    /**
     * 优先级状态
     */
    private String priorityStatus;
}
