package com.raipeng.aimonitor.controller.response;

import com.raipeng.aidatacommon.enums.LineType;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class TenantLineMonitor extends MonitorBase {
    /**
     * 商户线路名称
     */
    private String tenantLineName;

    /**
     * 商户线路编号
     */
    private String tenantLineNumber;

    /**
     * 商户线路类型
     */
    private LineType tenantLineType;

    /**
     * 所属账号
     */
    private String account;

    /**
     * 并发
     */
    private String concurrency;

    /**
     * 商户名称
     */
    private String tenantName;

    /**
     * 商户编号
     */
    private String tenantNumber;

    /**
     * 优先级状态
     */

    private String priorityStatus;
}
