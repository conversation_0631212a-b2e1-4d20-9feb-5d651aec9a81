package com.raipeng.aimonitor.controller.response;

import com.raipeng.aidatacommon.enums.LineType;
import com.raipeng.common.enums.EnableStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class TenantLineMonitorDetail extends MonitorBase {
    /**
     * 供应线路名称
     */
    private String supplyLineName;

    /**
     * 供应线路编号
     */
    private String supplyLineNumber;

    /**
     * 供应线路类型
     */
    private LineType supplyLineType;


    /**
     * 生效状态
     */
    private EnableStatus status;

    /**
     * 所属供应商
     */
    private String callSupplierName;

    /**
     * 供应商编号
     */
    private String callSupplierNumber;

    /**
     * 挂起状态
     */
    private Boolean isPending;

    /**
     * 临时停用
     */
    private Boolean isTempStop;

    /**
     * 并发（实际/占用/上限）
     */
    private String concurrency;

    /**
     * 主叫号码
     */
    private String masterCallNumber;

    /**
     * 前缀
     */
    private String prefix;

    /**
     * 可支配并发
     */
    private Integer concurrentLimit;

    /**
     * 优先队列
     */
    private Boolean isPriority;


    private  String priorityStatus;
}
