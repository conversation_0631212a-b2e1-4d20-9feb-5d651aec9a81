package com.raipeng.aimonitor.controller.response;

import lombok.Data;

@Data
public class MonitorBase {
    /**
     * 今日呼叫总数
     */
    private int totalCallNum;

    /**
     * 当前接通率
     */
    private String currentlyConnectedRate;

    /**
     * 当前接通量
     */
    private String currentlyConnectedNum;

    /**
     * 当前接通（当前）
     */
    private String currentlyConnected;

    /**
     * 当前呼叫（当前）
     */
    private int currentlyCallNum;

    /**
     * 接通率（今日）
     */
    private String todayConnectedRate;

    /**
     * 接通量（今日）
     */
    private String todayConnectedNum;

    /**
     * 接通（今日）
     */
    private String todayConnected;

    /**
     * 无声通话占比（（当前）
     */
    private String currentlySilenceCall;

    /**
     * 秒通（1s)（当前）
     */
    private String currentlyOneSecondConnected;

    /**
     * 秒通（2s）（当前）
     */
    private String currentlyTwoSecondConnected;

    /**
     * 无声挂断（当前）
     */
    private String currentlySilenceHangup;

    /**
     * 小助手（当前）
     */
    private String currentlyAssistant;

    /**
     * 提示音（当前）
     */
    private String currentlyPromptSound;

    /**
     * 转人工（当前）
     */
    private String currentlyTransCallSeatNum;

    /**
     * A类（当前）
     */
    private String currentlyClassANum;

    /**
     * B类（当前）
     */
    private String currentlyClassBNum;

    /**
     * C类（当前）
     */
    private String currentlyClassCNum;

    /**
     * D类（当前）
     */
    private String currentlyClassDNum;

    /**
     * 送呼失败（当前）
     */
    private String currentlyCallFailed;

    /**
     * 路由失败（当前）
     */
    private String currentlyRoutingFail;

    /**
     * 等待时长（当前）
     */
    private String currentlyWaitSecond;

    /**
     * 呼叫时长（当前）
     */
    private String currentlyCallDurationSecond;

    /**
     * 无声通话占比（今日）
     */
    private String todaySilenceCall;

    /**
     * 秒通（1s）占比（今日）
     */
    private String todayOneSecondConnected;

    /**
     * 秒通（2s）占比（今日）
     */
    private String todayTwoSecondConnected;

    /**
     * 无声挂断（今日）
     */
    private String todaySilenceHangup;

    /**
     * 小助手（今日）
     */
    private String todayAssistant;

    /**
     * 提示音（今日）
     */
    private String todayPromptSound;

    /**
     * 转人工（今日）
     */
    private String todayTransCallSeatNum;

    /**
     * A类（今日）
     */
    private String todayClassANum;

    /**
     * B类（今日）
     */
    private String todayClassBNum;

    /**
     * C类（今日）
     */
    private String todayClassCNum;

    /**
     * D类（今日）
     */
    private String todayClassDNum;

    /**
     * 送呼失败（今日）
     */
    private String todayCallFailed;

    /**
     * 路由失败（今日)
     */
    private String todayRoutingFail;

    /**
     * 等待时长（今日）
     */
    private String todayWaitSecond;

    /**
     * 呼叫时长（今日）
     */
    private String todayCallDurationSecond;
}
