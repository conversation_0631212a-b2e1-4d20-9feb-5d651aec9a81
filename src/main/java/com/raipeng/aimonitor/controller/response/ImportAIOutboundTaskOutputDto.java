package com.raipeng.aimonitor.controller.response;

import com.raipeng.aidatacommon.entity.ScriptSmsTriggerPojo;
import com.raipeng.aidatacommon.entity.VariableSmsPojo;
import com.raipeng.common.enums.AIManualCallTeamHandleType;
import com.raipeng.common.enums.AIManualCallTeamPushType;
import com.raipeng.common.enums.AIOutboundTaskType;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class ImportAIOutboundTaskOutputDto {

    private Long id;
    private LocalDateTime createTime;

    private String groupId;

    private String taskName;

    private Integer phoneNum = 0;

    private String tenantName;

    private Integer calledPhoneNum = 0;

    private Integer callingPhoneNum = 0;

    private Integer recallingPhoneNum = 0;

    private Integer finishedPhoneNum = 0;

    private Integer putThroughPhoneNum = 0;

    private Integer callCycle = 0;

    //触达率
    private BigDecimal calledPhoneRate;

    //完成率
    private BigDecimal finishedPhoneRate;

    //接通率
    private BigDecimal putThroughPhoneRate;


    private Integer aiAnswerNum;

    private String ifSendSms = "否";

    //未完成，进行中，已停止，待执行
    private String callStatus;

    private String phoneOpPercent;

    private String batchStatus;


    private String speechCraftName;

    private Long speechCraftId;

    private String scriptStringId;

    private Integer version;

    private String lineName;

    private String lineCode;

    private Long lineId;

    private String startWorkTimes;

    private String endWorkTimes;

    private Integer autoReCall;

    //1-首呼优先分配， 2-多轮次呼叫按比例分配
    private Integer callRatioType;

    private Integer firstRecallTime;

    private Integer secondRecallTime;

    private String allRestrictProvince;

    private String allRestrictCity;

    private String ydRestrictProvince;

    private String ydRestrictCity;

    private String ltRestrictProvince;

    private String ltRestrictCity;

    private String dxRestrictCity;

    private String dxRestrictProvince;

    private String virtualRestrictCity;

    private String virtualRestrictProvince;

    private String unknownRestrictCity;

    private String unknownRestrictProvince;

    private Integer feeMinute;

    private String taskStartTime;

    private String taskEndTime;

    //是否自动止损
    private Integer isAutoStop;

    private Integer callRecordNum;

    private Integer phoneIntentionNum;

    private List<Long> callTeamIds;

    private AIOutboundTaskType taskType;

    private AIManualCallTeamPushType callTeamPushType;

    private AIManualCallTeamHandleType callTeamHandleType;

    private Double lineRatio;

    private Integer occupyRate;

    private List<Long> taskClueIds;

    private Integer ifLock;

    private Double virtualSeatRatio;

    private String programId;

    private String productId;

    private String industrySecondFieldId;

    //预计结束时间
    private String expectedFinishTime;

    //话术短信触发
    private List<ScriptSmsTriggerPojo> scriptSms;

    //挂机短信触发信息
    private List<VariableSmsPojo> hangUpSms;

    // 挂机排除短信触发
    private List<String> hangUpExcluded;

    //短信变量
    private List<VariableSmsPojo> variableSms;

    //短信模板状态
    private Integer smsTemplateAbnormal;

    //是否续呼
    private Integer nextDayCall;

    private String templateId;

    private List<Long> tenantBlackList;

    private List<String> startWorkTimeList;

    private List<String> endWorkTimeList;

    private String taskIds;

    private String account;

    private String tenantCode;

    //触发短信数
    private Integer triggerSmsNumber;

    //短信发送数
    private Integer sendSmsNumber;


    List<TaskClassRatioDto> classList;
    private Long totalCallDurationNum;
    private Integer totalConnectNum;
    private Integer averageDuration;
    private Integer billingDuration;


}
