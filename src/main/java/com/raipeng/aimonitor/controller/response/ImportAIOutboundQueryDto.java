package com.raipeng.aimonitor.controller.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.raipeng.common.enums.AIOutboundTaskType;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class ImportAIOutboundQueryDto {
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    private List<Long> taskIdList;
    private Long taskId;
    private AIOutboundTaskType taskType;

}
