package com.raipeng.aimonitor.controller;

import com.raipeng.aimonitor.controller.response.ImportAIOutboundQueryDto;
import com.raipeng.aimonitor.controller.response.ImportAIOutboundTaskOutputCityDto;
import com.raipeng.aimonitor.controller.response.ImportAIOutboundTaskOutputDto;
import com.raipeng.aimonitor.entity.Response;
import com.raipeng.aimonitor.service.TaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/aiOutboundTask")
@Api(value = "/aiOutboundTask", tags = {"AI外呼任务"})
public class TaskController {

    @Autowired
    private TaskService aiOutboundTaskService;

    @ApiOperation(value = "importTaskStatisticList")
    @PostMapping(value = "/importTaskStatisticList")
    public Response<List<ImportAIOutboundTaskOutputDto>> importTaskStatisticList(@RequestBody ImportAIOutboundQueryDto aiOutboundQueryDto) {
        List<ImportAIOutboundTaskOutputDto> data = aiOutboundTaskService.preImportTaskStatisticList(aiOutboundQueryDto);
        Response<List<ImportAIOutboundTaskOutputDto>> res = new Response<>();
        res.setData(data);
        res.setResponseSuccess();
        return res;
    }

    @ApiOperation(value = "importTaskOperatorCityList")
    @PostMapping(value = "/importTaskOperatorCityList")
    public Response<List<ImportAIOutboundTaskOutputCityDto>> importTaskOperatorCityList(@RequestBody ImportAIOutboundQueryDto aiOutboundQueryDto) {
        List<ImportAIOutboundTaskOutputCityDto> data = aiOutboundTaskService.importTaskOperatorCityList(aiOutboundQueryDto);
        Response<List<ImportAIOutboundTaskOutputCityDto>> res = new Response<>();
        res.setData(data);
        res.setResponseSuccess();
        return res;
    }
}
