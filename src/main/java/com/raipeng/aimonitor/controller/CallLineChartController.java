package com.raipeng.aimonitor.controller;

import com.raipeng.aidatacommon.model.LineGateway;
import com.raipeng.aimonitor.controller.request.*;
import com.raipeng.aimonitor.controller.response.SecondIndustryConcurrentMonitor;
import com.raipeng.aimonitor.entity.Response;
import com.raipeng.aimonitor.service.AiOutboundTaskService;
import com.raipeng.aimonitor.service.CallLineChartService;
import com.raipeng.aimonitor.service.CallLineChartService.UnitData;
import com.raipeng.aimonitor.service.CallLineDotService;
import com.raipeng.common.util.CombineLineUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/callLineChart")
@Api(value = "/callLineChart", tags = {"线路图表"})
public class CallLineChartController {
    @Autowired
    private CallLineChartService callLineChartService;

    @Autowired
    private CallLineDotService callLineDotService;

    @Autowired
    private AiOutboundTaskService aiOutboundTaskService;

    @ApiOperation(value = "当天的时间段细分图表")
    @PostMapping("/getRateChartToday")
    public Response<Map<String, UnitData>> getRateChartToday(@RequestBody LineRateChartParam param) {
        Map<String, UnitData> rateChartToday = callLineChartService.getRateChartToday(param.getTenantLineNumber(), param.getSupplyLineNumber(), param.getSize(), param.getOperators(), param.getType());
        Response<Map<String, UnitData>> response = new Response<>();
        response.setResponseSuccess();
        response.setData(rateChartToday);
        return response;
    }

    @ApiOperation(value = "当天的全国区域表")
    @PostMapping("/getCountryChartToday")
    public Response<Map<String, UnitData>> getCountryChartToday(@RequestBody LineCountryChartParam param) {
        Map<String, UnitData> rateChartToday = callLineChartService.getCountryChartToday(param.getMinutes(), param.getTenantLineNumber(), param.getSupplyLineNumber(), param.getOperators(), param.getType());
        Response<Map<String, UnitData>> response = new Response<>();
        response.setResponseSuccess();
        response.setData(rateChartToday);
        return response;
    }

    @ApiOperation(value = "当天的全省区域表")
    @PostMapping("/getProvinceChartToday")
    public Response<Map<String, UnitData>> getProvinceChartToday(@RequestBody LineProvinceChartParam param) {
        Map<String, UnitData> rateChartToday = callLineChartService.getProvinceChartToday(param.getMinutes(), param.getTenantLineNumber(), param.getSupplyLineNumber(), param.getOperators(), param.getType(), param.getProvince());
        Response<Map<String, UnitData>> response = new Response<>();
        response.setResponseSuccess();
        response.setData(rateChartToday);
        return response;
    }

    @ApiOperation(value = "昨天的时间段细分图表")
    @PostMapping("/getRateChartYesterday")
    public Response<Map<String, UnitData>> getRateChartYesterday(@RequestBody LineRateChartParam param) {
        Map<String, UnitData> rateChartToday = callLineChartService.getRateChartYesterday(param.getTenantLineNumber(), param.getSupplyLineNumber(), param.getSize(), param.getOperators(), param.getType());
        Response<Map<String, UnitData>> response = new Response<>();
        response.setResponseSuccess();
        response.setData(rateChartToday);
        return response;
    }

    @ApiOperation(value = "昨天的全国区域表")
    @PostMapping("/getCountryChartYesterday")
    public Response<Map<String, UnitData>> getCountryChartYesterday(@RequestBody LineCountryChartParam param) {
        Map<String, UnitData> rateChartToday = callLineChartService.getCountryChartYesterday(param.getMinutes(), param.getTenantLineNumber(), param.getSupplyLineNumber(), param.getOperators(), param.getType());
        Response<Map<String, UnitData>> response = new Response<>();
        response.setResponseSuccess();
        response.setData(rateChartToday);
        return response;
    }

    @ApiOperation(value = "昨天的全省区域表")
    @PostMapping("/getProvinceChartYesterday")
    public Response<Map<String, UnitData>> getProvinceChartYesterday(@RequestBody LineProvinceChartParam param) {
        Map<String, UnitData> rateChartToday = callLineChartService.getProvinceChartYesterday(param.getMinutes(), param.getTenantLineNumber(), param.getSupplyLineNumber(), param.getOperators(), param.getType(), param.getProvince());
        Response<Map<String, UnitData>> response = new Response<>();
        response.setResponseSuccess();
        response.setData(rateChartToday);
        return response;
    }

    @ApiOperation(value = "前天的时间段细分图表")
    @PostMapping("/getRateChartBeforeYesterday")
    public Response<Map<String, UnitData>> getRateChartBeforeYesterday(@RequestBody LineRateChartParam param) {
        Map<String, UnitData> rateChartToday = callLineChartService.getRateChartBeforeYesterday(param.getTenantLineNumber(), param.getSupplyLineNumber(), param.getSize(), param.getOperators(), param.getType());
        Response<Map<String, UnitData>> response = new Response<>();
        response.setResponseSuccess();
        response.setData(rateChartToday);
        return response;
    }

    @ApiOperation(value = "前天的全国区域表")
    @PostMapping("/getCountryChartBeforeYesterday")
    public Response<Map<String, UnitData>> getCountryChartBeforeYesterday(@RequestBody LineCountryChartParam param) {
        Map<String, UnitData> rateChartToday = callLineChartService.getCountryChartBeforeYesterday(param.getMinutes(), param.getTenantLineNumber(), param.getSupplyLineNumber(), param.getOperators(), param.getType());
        Response<Map<String, UnitData>> response = new Response<>();
        response.setResponseSuccess();
        response.setData(rateChartToday);
        return response;
    }

    @ApiOperation(value = "前天的全省区域表")
    @PostMapping("/getProvinceChartBeforeYesterday")
    public Response<Map<String, UnitData>> getProvinceChartBeforeYesterday(@RequestBody LineProvinceChartParam param) {
        Map<String, UnitData> rateChartToday = callLineChartService.getProvinceChartBeforeYesterday(param.getMinutes(), param.getTenantLineNumber(), param.getSupplyLineNumber(), param.getOperators(), param.getType(), param.getProvince());
        Response<Map<String, UnitData>> response = new Response<>();
        response.setResponseSuccess();
        response.setData(rateChartToday);
        return response;
    }

    @ApiOperation(value = "历史监控细分图表")
    @PostMapping("/getRateChartDates")
    public Response<Map<String, UnitData>> getRateChartDates(@RequestBody LineChartRateDatesParam param) {
        Map<String, UnitData> rateChartToday = callLineChartService.getRateChartDates(param.getDates(), param.getTenantLineNumber(), param.getSupplyLineNumber(), param.getOperators(), param.getType());
        Response<Map<String, UnitData>> response = new Response<>();
        response.setResponseSuccess();
        response.setData(rateChartToday);
        return response;
    }

    @ApiOperation(value = "历史监控全国区域表")
    @PostMapping("/getCountryChartDates")
    public Response<Map<String, UnitData>> getCountryChartDates(@RequestBody LineChartCountryDatesParam param) {
        Map<String, UnitData> rateChartToday = callLineChartService.getCountryChartDates(param.getDates(),  param.getTenantLineNumber(), param.getSupplyLineNumber(), param.getOperators(), param.getType());
        Response<Map<String, UnitData>> response = new Response<>();
        response.setResponseSuccess();
        response.setData(rateChartToday);
        return response;
    }

    @ApiOperation(value = "历史监控全省区域表")
    @PostMapping("/getProvinceChartDates")
    public Response<Map<String, UnitData>> getProvinceChartDates(@RequestBody LineChartProvinceDatesParam param) {
        Map<String, UnitData> rateChartToday = callLineChartService.getProvinceChartDates(param.getDates(),  param.getTenantLineNumber(), param.getSupplyLineNumber(), param.getOperators(), param.getProvince(), param.getType());
        Response<Map<String, UnitData>> response = new Response<>();
        response.setResponseSuccess();
        response.setData(rateChartToday);
        return response;
    }

    @ApiOperation(value = "某条线路或者行业的近期并发打点数据")
    @PostMapping("/getConcurrentDotByKeyAndTime")
    public Response<Map<LocalDateTime, Integer>> getConcurrentDotByKeyAndTime(@RequestParam String key, @RequestParam String date) {
        Map<LocalDateTime, Integer> oneCallLineTimeSlot = callLineDotService.getConcurrentDotByKeyAndTime(key, date);
        Response<Map<LocalDateTime, Integer>> response = new Response<>();
        response.setResponseSuccess();
        response.setData(oneCallLineTimeSlot);
        return response;
    }

    @ApiOperation(value = "某条线路或者行业的近期并发打点数据/可支配并发/供应线路并发上限")
    @PostMapping("/getConcurrentAndLimitForSupplyLine")
    public Response<Map<String,Map<LocalDateTime, Integer>>> getConcurrentAndLimitForSupplyLine(@RequestParam String supplyOrTenantLineNumber, @RequestParam String date) {
        Map<LocalDateTime, Integer> oneCallLineTimeSlot = callLineDotService.getConcurrentDotByKeyAndTime(supplyOrTenantLineNumber, date);
        Map<LocalDateTime, Integer> tenantSupplyConcurrentLimit = callLineDotService.getTenantSupplyConcurrentLimitSumMap(supplyOrTenantLineNumber, date);
        Map<LocalDateTime, Integer> tenantSupplyLineLimitMap = callLineDotService.getTenantOrSupplyLineLimitMap(supplyOrTenantLineNumber, date);


        Response<Map<String,Map<LocalDateTime, Integer>>> response = new Response<>();
        Map<String,Map<LocalDateTime, Integer>> result = new HashMap<>();
        response.setResponseSuccess();
        result.put("oneCallLineTimeSlot",oneCallLineTimeSlot);
        result.put("tenantSupplyConcurrentLimit",tenantSupplyConcurrentLimit);
        result.put("tenantSupplyLineLimit",tenantSupplyLineLimitMap);
        response.setData(result);
        return response;
    }

    @ApiOperation(value = "获取商户线路下供应线的实时并发")
    @PostMapping("/getConcurrentDotByKeyAndTimeForTenantSupplyLine")
    public Response<Map<LocalDateTime, Integer>> getConcurrentDotByKeyAndTimeForTenantSupplyLine(
            @RequestParam String tenantLineNumber,
            @RequestParam String supplyLineNumber,
            @RequestParam String date) {
        Map<LocalDateTime, Integer> oneCallLineTimeSlot = callLineDotService.getConcurrentDotByKeyAndTime(
                CombineLineUtil.getTenantSupplyLineNumber(tenantLineNumber, supplyLineNumber), date);
        Response<Map<LocalDateTime, Integer>> response = new Response<>();
        response.setResponseSuccess();
        response.setData(oneCallLineTimeSlot);
        return response;
    }


    @ApiOperation(value = "获取商户线路下供应线的实时并发/可支配并发/供应线路并发上限")
    @PostMapping("/getConcurrentAndLimitForTenantSupplyLine")
    public Response<Map<String, Map<LocalDateTime, Integer>>> getConcurrentAndLimitForTenantSupplyLine(
            @RequestParam String tenantLineNumber,
            @RequestParam String supplyLineNumber,
            @RequestParam String date) {
        Map<LocalDateTime, Integer> oneCallLineTimeSlot = callLineDotService.getConcurrentDotByKeyAndTime(
                CombineLineUtil.getTenantSupplyLineNumber(tenantLineNumber, supplyLineNumber), date);
        Map<LocalDateTime, Integer> tenantSupplyConcurrentLimit = callLineDotService.getTenantOrSupplyLineLimitMap(
                CombineLineUtil.getTenantSupplyLineNumber(tenantLineNumber, supplyLineNumber), date);
        Map<LocalDateTime, Integer> supplyLineLimitMap = callLineDotService.getTenantOrSupplyLineLimitMap(
                supplyLineNumber, date);

        Response<Map<String,Map<LocalDateTime, Integer>>> response = new Response<>();
        Map<String,Map<LocalDateTime, Integer>> result = new HashMap<>();
        response.setResponseSuccess();
        result.put("oneCallLineTimeSlot",oneCallLineTimeSlot);
        result.put("tenantSupplyConcurrentLimit",tenantSupplyConcurrentLimit);
        result.put("supplyLineLimit",supplyLineLimitMap);
        response.setData(result);
        return response;
    }


    @ApiOperation(value = "根据行业展示并发情况")
    @PostMapping("/findIndustryMonitors")
    public Response<List<SecondIndustryConcurrentMonitor>> findIndustryMonitors() {
        List<SecondIndustryConcurrentMonitor> industryMonitors = aiOutboundTaskService.findIndustryMonitors();
        Response<List<SecondIndustryConcurrentMonitor>> response = new Response<>();
        response.setResponseSuccess();
        response.setData(industryMonitors);
        return response;
    }

    @ApiOperation(value = "查询所有网关的实时并发")
    @PostMapping("/getLineGatewayDataList")
    public Response<List<LineGateway>> getLineGatewayDataList() {
        List<LineGateway> lineGateways = callLineChartService.getLineGatewayDataList();
        Response<List<LineGateway>> response = new Response<>();
        response.setResponseSuccess();
        response.setData(lineGateways);
        return response;
    }
}
