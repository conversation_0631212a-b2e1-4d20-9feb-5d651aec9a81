package com.raipeng.aimonitor.controller;

import com.raipeng.aidatacommon.model.TenantLine;
import com.raipeng.aimonitor.controller.request.*;
import com.raipeng.aimonitor.controller.response.*;
import com.raipeng.aimonitor.entity.LineSecStatisticEntity;
import com.raipeng.aimonitor.entity.Response;
import com.raipeng.aimonitor.service.CallLineListService;
import com.raipeng.aimonitor.service.CallLineMonitorDataService;
import com.raipeng.aimonitor.service.CallLineStatisticService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/callLineTenantManager")
@Api(value = "/callLineTenantManager", tags = {"商户线路后台监控"})
public class CallLineTenantManagerController {
    @Autowired
    private CallLineListService callLineListService;

    @Autowired
    private CallLineMonitorDataService callLineMonitorDataService;

    @Autowired
    private CallLineStatisticService callLineStatisticService;

    @ApiOperation(value = "商户线路列表")
    @PostMapping("/findTenantLinesByConditions")
    public Response<List<TenantLineResponse>> findTenantLinesByConditions(@RequestBody TenantLineParam tenantLineParam) {
        Response<List<TenantLineResponse>> response = new Response<>();
        List<TenantLineResponse> tenantLinesByConditions = callLineListService.findTenantLinesByConditions(tenantLineParam);
        response.setData(tenantLinesByConditions);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "根据查询商户线路编号查询商户线路")
    @PostMapping("/findTenantLineByNumber")
    public Response<TenantLine> findTenantLineByNumber(@RequestParam String tenantLineNumber) {
        TenantLine tenantLine = callLineListService.findTenantLineByNumber(tenantLineNumber);
        Response<TenantLine> response = new Response<>();
        response.setData(tenantLine);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "商户线路管理-通时分布")
    @PostMapping("/findTenantLineCallDurationSecDistribution")
    public Response<List<LineSecStatisticEntity>> findTenantLineCallDurationSecDistribution(@RequestParam String tenantLineNumber, @RequestParam int recentMin) {
        Response<List<LineSecStatisticEntity>> response = new Response<>();
        List<LineSecStatisticEntity> tenantLineMonitorList = callLineMonitorDataService.findTenantLineCallDurationSecDistribution(tenantLineNumber, recentMin);
        response.setData(tenantLineMonitorList);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "商户线路管理-监控列表")
    @PostMapping("/findTenantLineMonitorList")
    public Response<List<TenantLineMonitor>> findTenantLineMonitorList(@RequestBody TenantLineMonitorParam tenantLineMonitorParam, @RequestParam Integer isTenant) {
        Response<List<TenantLineMonitor>> response = new Response<>();
        List<TenantLineMonitor> tenantLineMonitorList = callLineMonitorDataService.findTenantLineMonitorList(tenantLineMonitorParam, isTenant);
        response.setData(tenantLineMonitorList);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "商户线路分组列表筛选")
    @PostMapping("/findTenantLineMonitorListByGroupIds")
    public Response<List<TenantLineMonitor>> findTenantLineMonitorListByGroupIds(@RequestBody TenantLineMonitorParam tenantLineMonitorParam) {
        Response<List<TenantLineMonitor>> response = new Response<>();
        List<TenantLineMonitor> tenantLineMonitorList = callLineMonitorDataService.findTenantLineMonitorListByGroupIds(tenantLineMonitorParam);
        response.setData(tenantLineMonitorList);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "商户线路管理-(昨天)监控列表")
    @PostMapping("/findTenantLineMonitorListYesterday")
    public Response<List<TenantLineMonitor>> findTenantLineMonitorListYesterday(@RequestBody TenantLineMonitorParam tenantLineMonitorParam, @RequestParam Integer isTenant) {
        Response<List<TenantLineMonitor>> response = new Response<>();
        List<TenantLineMonitor> tenantLineMonitorList = callLineMonitorDataService.findTenantLineMonitorListYesterday(tenantLineMonitorParam, isTenant);
        response.setData(tenantLineMonitorList);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "商户线路管理-(前天)监控列表")
    @PostMapping("/findTenantLineMonitorListBeforeYesterday")
    public Response<List<TenantLineMonitor>> findTenantLineMonitorListBeforeYesterday(@RequestBody TenantLineMonitorParam tenantLineMonitorParam, @RequestParam Integer isTenant) {
        Response<List<TenantLineMonitor>> response = new Response<>();
        List<TenantLineMonitor> tenantLineMonitorList = callLineMonitorDataService.findTenantLineMonitorListBeforeYesterday(tenantLineMonitorParam, isTenant);
        response.setData(tenantLineMonitorList);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "商户线路-运行监控")
    @PostMapping("/findTenantLineMonitorDetailList")
    public Response<List<TenantLineMonitorDetail>> findTenantLineMonitorDetailList(@RequestBody TenantLineMonitorDetailParam tenantLineMonitorDetailParam) {
        Response<List<TenantLineMonitorDetail>> response = new Response<>();
        List<TenantLineMonitorDetail> tenantLineMonitorDetailList = callLineMonitorDataService.findTenantLineMonitorDetailList(tenantLineMonitorDetailParam);
        response.setData(tenantLineMonitorDetailList);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "商户线路-(昨天)运行监控")
    @PostMapping("/findTenantLineMonitorDetailListYesterday")
    public Response<List<TenantLineMonitorDetail>> findTenantLineMonitorDetailListYesterday(@RequestBody TenantLineMonitorDetailParam tenantLineMonitorDetailParam) {
        Response<List<TenantLineMonitorDetail>> response = new Response<>();
        List<TenantLineMonitorDetail> tenantLineMonitorDetailList = callLineMonitorDataService.findTenantLineMonitorDetailListYesterday(tenantLineMonitorDetailParam);
        response.setData(tenantLineMonitorDetailList);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "商户线路-(前天)运行监控")
    @PostMapping("/findTenantLineMonitorDetailListBeforeYesterday")
    public Response<List<TenantLineMonitorDetail>> findTenantLineMonitorDetailListBeforeYesterday(@RequestBody TenantLineMonitorDetailParam tenantLineMonitorDetailParam) {
        Response<List<TenantLineMonitorDetail>> response = new Response<>();
        List<TenantLineMonitorDetail> tenantLineMonitorDetailList = callLineMonitorDataService.findTenantLineMonitorDetailListBeforeYesterday(tenantLineMonitorDetailParam);
        response.setData(tenantLineMonitorDetailList);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "商户线路用量统计")
    @PostMapping("/findTenantLineStatisticsList")
    public Response<List<TenantLineStatistics>> findTenantLineStatisticsList(@RequestBody TenantLineStatisticsParam tenantLineStatisticsParam) {
        Response<List<TenantLineStatistics>> response = new Response<>();
        List<TenantLineStatistics> tenantLineStatisticsList = callLineStatisticService.findTenantLineStatisticsList(tenantLineStatisticsParam);
        response.setData(tenantLineStatisticsList);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "当日商户线路用量列表")
    @PostMapping("/findTenantLineStatisticsDetailList")
    public Response<List<TenantLineStatisticsDetail>> findTenantLineStatisticsDetailList(@RequestBody TenantLineStatisticsDetailParam tenantLineStatisticsDetailParam) {
        Response<List<TenantLineStatisticsDetail>> response = new Response<>();
        List<TenantLineStatisticsDetail> tenantLineStatisticsDetailList = callLineStatisticService.findTenantLineStatisticsDetailList(tenantLineStatisticsDetailParam);
        response.setData(tenantLineStatisticsDetailList);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "不同日期商户线路用量列表")
    @PostMapping("/findTenantLineStatisticsDetailListByDateList")
    public Response<Map<String, List<TenantLineStatisticsDetail>>> findTenantLineStatisticsDetailListByDateList(@RequestBody List<String> dateList) {
        Response<Map<String, List<TenantLineStatisticsDetail>>> response = new Response<>();
        Map<String, List<TenantLineStatisticsDetail>> tenantLineStatisticsDetailListByDateList = callLineStatisticService.findTenantLineStatisticsDetailListByDateList(dateList);
        response.setData(tenantLineStatisticsDetailListByDateList);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "当日商户线路用量详情")
    @PostMapping("/findTenantLineStatisticsUnitDataList")
    public Response<List<TenantLineStatisticsUnitData>> findTenantLineStatisticsUnitDataList(@RequestBody TenantLineStatisticsUnitDataParam tenantLineStatisticsUnitDataParam) {
        Response<List<TenantLineStatisticsUnitData>> response = new Response<>();
        List<TenantLineStatisticsUnitData> tenantLineStatisticsUnitDataList = callLineStatisticService.findTenantLineStatisticsUnitDataList(tenantLineStatisticsUnitDataParam);
        response.setData(tenantLineStatisticsUnitDataList);
        response.setResponseSuccess();
        return response;
    }
}
