package com.raipeng.aimonitor.controller;


import com.raipeng.aidatacommon.model.SupplyLine;
import com.raipeng.aimonitor.controller.request.*;
import com.raipeng.aimonitor.controller.response.*;
import com.raipeng.aimonitor.entity.LineSecStatisticEntity;
import com.raipeng.aimonitor.entity.Response;
import com.raipeng.aimonitor.service.CallLineListService;
import com.raipeng.aimonitor.service.CallLineMonitorDataService;
import com.raipeng.aimonitor.service.CallLineStatisticService;

import com.raipeng.aimonitor.service.TenantSupplySamplingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/callLineSupplyManager")
@Api(value = "/callLineSupplyManager", tags = {"供应线路后台监控"})
public class CallLineSupplyManagerController {
    @Autowired
    private CallLineListService callLineListService;

    @Autowired
    private CallLineMonitorDataService callLineMonitorDataService;

    @Autowired
    private CallLineStatisticService callLineStatisticService;

    @Autowired
    private TenantSupplySamplingService tenantSupplySamplingService;

    @ApiOperation(value = "供应线路列表")
    @PostMapping("/findSupplyLinesByConditions")
    public Response<List<SupplyLine>> findSupplyLinesByConditions(@RequestBody SupplyLineManagerParam supplyLineManagerParam) {
        List<SupplyLine> supplyLinesByConditions = callLineListService.findSupplyLinesByConditions(supplyLineManagerParam);
        Response<List<SupplyLine>> response = new Response<>();
        response.setData(supplyLinesByConditions);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "根据查询供应线路编号查询供应线路")
    @PostMapping("/findSupplyLineByNumber")
    public Response<SupplyLine> findSupplyLineByNumber(@RequestParam String supplyLineNumber) {
        SupplyLine supplyLine = callLineListService.findSupplyLineByNumber(supplyLineNumber);
        Response<SupplyLine> response = new Response<>();
        response.setData(supplyLine);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "供应线路管理-通时分布")
    @PostMapping("/findSupplyLineCallDurationSecDistribution")
    public Response<List<LineSecStatisticEntity>> findSupplyLineCallDurationSecDistribution(@RequestParam String supplyLineNumber, @RequestParam Integer recentMin) {
        Response<List<LineSecStatisticEntity>> response = new Response<>();
        List<LineSecStatisticEntity> supplyLineMonitorList = callLineMonitorDataService.findSupplyLineCallDurationSecDistribution(supplyLineNumber, recentMin);
        response.setData(supplyLineMonitorList);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "供应线路管理-监控列表")
    @PostMapping("/findSupplyLineMonitorList")
    public Response<List<SupplyLineMonitor>> findSupplyLineMonitorList(@RequestBody SupplyLineMonitorParam supplyLineMonitorParam) {
        Response<List<SupplyLineMonitor>> response = new Response<>();
        List<SupplyLineMonitor> supplyLineMonitorList = callLineMonitorDataService.findSupplyLineMonitorList(supplyLineMonitorParam);
        response.setData(supplyLineMonitorList);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "供应线路管理-(昨天)监控列表")
    @PostMapping("/findSupplyLineMonitorListYesterday")
    public Response<List<SupplyLineMonitor>> findSupplyLineMonitorListYesterday(@RequestBody SupplyLineMonitorParam supplyLineMonitorParam) {
        Response<List<SupplyLineMonitor>> response = new Response<>();
        List<SupplyLineMonitor> supplyLineMonitorList = callLineMonitorDataService.findSupplyLineMonitorListYesterday(supplyLineMonitorParam);
        response.setData(supplyLineMonitorList);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "供应线路管理-(前天)监控列表")
    @PostMapping("/findSupplyLineMonitorListBeforeYesterday")
    public Response<List<SupplyLineMonitor>> findSupplyLineMonitorListBeforeYesterday(@RequestBody SupplyLineMonitorParam supplyLineMonitorParam) {
        Response<List<SupplyLineMonitor>> response = new Response<>();
        List<SupplyLineMonitor> supplyLineMonitorList = callLineMonitorDataService.findSupplyLineMonitorListBeforeYesterday(supplyLineMonitorParam);
        response.setData(supplyLineMonitorList);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "供应线路管理-运行监控详情")
    @PostMapping("/findSupplyLineMonitorDetailList")
    public Response<List<SupplyLineMonitorDetail>> findSupplyLineMonitorDetailList(@RequestBody SupplyLineMonitorDetailParam supplyLineMonitorDetailParam) {
        Response<List<SupplyLineMonitorDetail>> response = new Response<>();
        List<SupplyLineMonitorDetail> supplyLineMonitorDetailList = callLineMonitorDataService.findSupplyLineMonitorDetailList(supplyLineMonitorDetailParam);
        response.setData(supplyLineMonitorDetailList);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "供应线路管理-(昨天)运行监控详情")
    @PostMapping("/findSupplyLineMonitorDetailListYesterday")
    public Response<List<SupplyLineMonitorDetail>> findSupplyLineMonitorDetailListYesterday(@RequestBody SupplyLineMonitorDetailParam supplyLineMonitorDetailParam) {
        Response<List<SupplyLineMonitorDetail>> response = new Response<>();
        List<SupplyLineMonitorDetail> supplyLineMonitorDetailList = callLineMonitorDataService.findSupplyLineMonitorDetailListYesterday(supplyLineMonitorDetailParam);
        response.setData(supplyLineMonitorDetailList);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "供应线路管理-(前天)运行监控详情")
    @PostMapping("/findSupplyLineMonitorDetailListBeforeYesterday")
    public Response<List<SupplyLineMonitorDetail>> findSupplyLineMonitorDetailListBeforeYesterday(@RequestBody SupplyLineMonitorDetailParam supplyLineMonitorDetailParam) {
        Response<List<SupplyLineMonitorDetail>> response = new Response<>();
        List<SupplyLineMonitorDetail> supplyLineMonitorDetailList = callLineMonitorDataService.findSupplyLineMonitorDetailListBeforeYesterday(supplyLineMonitorDetailParam);
        response.setData(supplyLineMonitorDetailList);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "供应线路管理-用量统计")
    @PostMapping("/findSupplyLineStatisticsList")
    public Response<List<SupplyLineStatistics>> findSupplyLineStatisticsList(@RequestBody SupplyLineStatisticsParam supplyLineStatisticsParam) {
        Response<List<SupplyLineStatistics>> response = new Response<>();
        List<SupplyLineStatistics> supplyLineStatisticsList = callLineStatisticService.findSupplyLineStatisticsList(supplyLineStatisticsParam);
        response.setData(supplyLineStatisticsList);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "当日供应线路用量列表")
    @PostMapping("/findSupplyLineStatisticsDetailList")
    public Response<List<SupplyLineStatisticsDetail>> findSupplyLineStatisticsDetailList(@RequestBody SupplyLineStatisticsDetailParam supplyLineStatisticsDetailParam) {
        Response<List<SupplyLineStatisticsDetail>> response = new Response<>();
        List<SupplyLineStatisticsDetail> supplyLineStatisticsDetailList = callLineStatisticService.findSupplyLineStatisticsDetailList(supplyLineStatisticsDetailParam);
        response.setData(supplyLineStatisticsDetailList);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "不同日期供应线路用量列表")
    @PostMapping("/findSupplyLineStatisticsDetailListByDateList")
    public Response<Map<String, List<SupplyLineStatisticsDetail>>> findSupplyLineStatisticsDetailListByDateList(@RequestBody List<String> dateList) {
        Response<Map<String, List<SupplyLineStatisticsDetail>>> response = new Response<>();
        Map<String, List<SupplyLineStatisticsDetail>> supplyLineStatisticsDetailListByDateList = callLineStatisticService.findSupplyLineStatisticsDetailListByDateList(dateList);
        response.setData(supplyLineStatisticsDetailListByDateList);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "编辑不同日期的线路价格")
    @PostMapping("/editSupplyLineEverydayPrice")
    public Response<Void> editSupplyLineEverydayPrice(@RequestBody SupplyLineEverydayPriceParam param) {
        callLineStatisticService.editSupplyLineEverydayPrice(param.getDate(), param.getSupplyLineNumber(), param.getUnitPrice());
        Response<Void> response = new Response<>();
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "当日供应线路用量详情")
    @PostMapping("/findSupplyLineStatisticsUnitDataList")
    public Response<List<SupplyLineStatisticsUnitData>> findSupplyLineStatisticsUnitDataList(@RequestBody SupplyLineStatisticsUnitDataParam supplyLineStatisticsUnitDataParam) {
        Response<List<SupplyLineStatisticsUnitData>> response = new Response<>();
        List<SupplyLineStatisticsUnitData> supplyLineStatisticsUnitDataList = callLineStatisticService.findSupplyLineStatisticsUnitDataList(supplyLineStatisticsUnitDataParam);
        response.setData(supplyLineStatisticsUnitDataList);
        response.setResponseSuccess();
        return response;
    }



    @ApiOperation(value = "抽样检测")
    @PostMapping("/tenantSupplyLineSampling")
    public Response<Map<String,Integer>> tenantSupplyLineSampling(@RequestBody SupplyLineStatisticsUnitDataParam supplyLineStatisticsUnitDataParam) {
        Response<Map<String,Integer>> response = new Response<>();
        String tenantLineNumber = supplyLineStatisticsUnitDataParam.getTenantLineNumber();
        String supplyLineNumber = supplyLineStatisticsUnitDataParam.getSupplyLineNumber();
        if(StringUtils.isEmpty(tenantLineNumber) || StringUtils.isEmpty(supplyLineNumber)){
            response.setResponseFail();
            response.setMsg("商户线路 或 供应线路为空");
            return response;
        }
        Map<String, Integer> result = tenantSupplySamplingService.samplingAIAutoRecord(tenantLineNumber, supplyLineNumber);
        response.setData(result);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "缓存号码")
    @PostMapping("/tenantSupplyLineCache")
    public Response<Integer> tenantSupplyLineCache(@RequestBody SupplyLineStatisticsUnitDataParam supplyLineStatisticsUnitDataParam) {
        Response<Integer> response = new Response<>();
        String tenantLineNumber = supplyLineStatisticsUnitDataParam.getTenantLineNumber();
        String supplyLineNumber = supplyLineStatisticsUnitDataParam.getSupplyLineNumber();
        if(StringUtils.isEmpty(tenantLineNumber) || StringUtils.isEmpty(supplyLineNumber)){
            response.setResponseFail();
            response.setMsg("商户线路 或 供应线路为空");
            return response;
        }
        Integer result = tenantSupplySamplingService.tenantSupplyLineCache(tenantLineNumber, supplyLineNumber);
        response.setData(result);
        response.setResponseSuccess();
        return response;
    }
}
