package com.raipeng.aimonitor.schedule;


import com.raipeng.aimonitor.service.CallLineDotService;
import com.raipeng.aimonitor.service.CallLineMonitorScanService;
import com.raipeng.aimonitor.service.CallLineStatisticService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import static com.raipeng.aimonitor.constant.CommonConstant.*;


@Component
@Slf4j
@RefreshScope
public class CallLineScanTask {
    @Autowired
    private CallLineMonitorScanService callLineMonitorScanService;

    @Autowired
    private CallLineStatisticService callLineStatisticService;

    @Autowired
    private CallLineDotService callLineDotService;

    // 整1分钟 扫描线路
    @XxlJob("collectLineDataEveryMoment")
    public ReturnT<String> collectLineDataEveryMoment(String s) {
        XxlJobLogger.log("XXL-JOB, collectLineDataEveryMoment.param=" + s);
        try {
            log.info("start collect line data every moment");

            // 1. 计算时间槽位
            LocalDateTime now = LocalDateTime.now();
            int timeSlot = now.getHour() * MINUTES_OF_ONE_HOUR + now.getMinute() - MOMENT_SCAN_ADVANCE_MINUTES - MOMENT_SCAN_SPLIT_MINUTES;

            // 2. 收集多个时间槽的线路数据
            callLineMonitorScanService.collectLineDataByLocalDateTime(timeSlot);
            callLineMonitorScanService.collectLineDataByLocalDateTime(timeSlot - MOMENT_SCAN_SPLIT_MINUTES);
            callLineMonitorScanService.collectLineDataByLocalDateTime(timeSlot - MOMENT_SCAN_SPLIT_MINUTES * 2);
            log.info("end collect line data every moment");
        } catch (Exception e) {
            XxlJobLogger.log("XXL-JOB, momentScan.Error=" + e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 汇总当日所有线路的监控数据
     */
    @XxlJob("collectOneDayMonitorData")
    public ReturnT<String> collectOneDayMonitorData(String s) {
        XxlJobLogger.log("XXL-JOB, collectOneDayMonitorData.param=" + s);
        try {
            log.info("start collect one day monitor data every day");
            callLineMonitorScanService.collectOneDayMonitorData(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            log.info("end collect one day monitor data every day");
        } catch (Exception e) {
            XxlJobLogger.log("XXL-JOB, collectOneDayMonitorData.Error=" + e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 每日收集线路统计数据任务
     *
     */
    @XxlJob("collectLineStatisticDataByLocalDate")
    public ReturnT<String> collectLineStatisticDataByLocalDate(String s) {
        XxlJobLogger.log("XXL-JOB, collectLineStatisticDataByLocalDate.param=" + s);
        try {
            log.info("start collect line statistic data every day");
            callLineStatisticService.collectLineStatisticDataByLocalDate(LocalDate.now());
            log.info("end collect line statistic data every day");
        } catch (Exception e) {
            XxlJobLogger.log("XXL-JOB, dayScan.Error=" + e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 每分钟更新Redis中线路信息任务, 用于展示线路使用并发趋势
     *
     */
    @XxlJob("dotCallLineInfoIntoRedis")
    public ReturnT<String> dotCallLineInfoIntoRedis(String s) {
        XxlJobLogger.log("XXL-JOB, dotCallLineInfoIntoRedis.param=" + s);
        try {
            log.info("start dot call line information every minute");
            callLineDotService.dotCallLineInfoIntoRedis();
            log.info("end dot call line information every minute");
        } catch (Exception e) {
            XxlJobLogger.log("XXL-JOB, dotCallLineInfoIntoRedis.Error=" + e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 每日清理Redis中过期线路信息任务
     *
     */
    @XxlJob("deleteDotCallLineInfoInRedis")
    public ReturnT<String> deleteDotCallLineInfoInRedis(String s) {
        XxlJobLogger.log("XXL-JOB, deleteDotCallLineInfoInRedis.param=" + s);
        try {
            log.info("start delete dot call line information every day");
            callLineDotService.deleteDotCallLineInfoInRedis();
            log.info("end delete dot call line information every day");
        } catch (Exception e) {
            XxlJobLogger.log("XXL-JOB, deleteDotCallLineInfoInRedis.Error=" + e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}
