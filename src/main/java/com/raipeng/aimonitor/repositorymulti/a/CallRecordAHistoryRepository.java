package com.raipeng.aimonitor.repositorymulti.a;


import com.raipeng.aidatacommon.model.record.CallRecordHistory;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import javax.persistence.Tuple;
import java.util.List;

public interface CallRecordAHistoryRepository extends JpaRepository<CallRecordHistory, Long> {
    @Query(value = "select f.* " +
            "from t_call_record_history f " +
            "where f.call_out_time >=:startTime " +
            "and f.call_out_time <:endTime " +
            "and f.call_status = '7' " +
            "and f.whole_audio_file_url != '' " +
            "and (f.if_test != '1' or f.if_test is null)",nativeQuery = true)
    List<CallRecordHistory> findPutThroughRecordsByTime(String startTime, String endTime);

    @Query(value = "select f.* " +
            "from t_call_record_history f " +
            "where f.call_out_time >=:startTime " +
            "and f.call_out_time <:endTime " +
            "and f.call_status = '7' " +
            "and f.whole_audio_file_url != '' " +
            "and f.task_id in :taskIds " +
            "and (f.if_test != '1' or f.if_test is null)",nativeQuery = true)
    List<CallRecordHistory> findPutThroughRecordsByTaskIds(String startTime, String endTime, List<String> taskIds);

    @Query(value = "SELECT " +
            "SUM(1) AS totalCallNum," +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN 1 ELSE 0 END) AS totalConnectNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN t.call_duration ELSE 0 END) / 60000 AS totalConnectedMinutes, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN call_duration % 60000.0 >= 100 THEN ceil(call_duration / 60000.0) ELSE floor(call_duration / 60000.0) END) ELSE 0 END) AS calculateNumOfSixty, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN call_duration % 6000.0 >= 100 THEN ceil(call_duration / 6000.0) ELSE floor(call_duration / 6000.0) END) ELSE 0 END) AS calculateNumOfSix, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class='A' THEN 1 ELSE 0 END) AS classANum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class='B' THEN 1 ELSE 0 END) AS classBNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class='C' THEN 1 ELSE 0 END) AS classCNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class='D' THEN 1 ELSE 0 END) AS classDNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class='E' THEN 1 ELSE 0 END) AS classENum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class='F' THEN 1 ELSE 0 END) AS classFNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class='G' THEN 1 ELSE 0 END) AS classGNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class='其他' THEN 1 ELSE 0 END) AS classOtherNum, " +
            "t.account AS account " +
            "FROM t_call_record_history t " +
            "WHERE t.call_out_time >= :startTime " +
            "AND t.call_out_time < :endTime " +
            "AND t.call_status IS NOT NULL " +
            "AND t.waitmsec IS NOT NULL " +
            "AND t.waitmsec >= 0 " +
            "AND (t.if_test is NULL OR t.if_test != '1') " +
            "GROUP BY t.account;", nativeQuery = true)
    List<Tuple> findCallDataByAccount(String startTime, String endTime);

    @Query(value = "SELECT " +
            "SUM(1) AS totalCallNum," +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN 1 ELSE 0 END) AS totalConnectNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN t.call_duration ELSE 0 END) AS totalConnectedDuration, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN call_duration % 60000.0 >= 100 THEN ceil(call_duration / 60000.0) ELSE floor(call_duration / 60000.0) END) ELSE 0 END) AS calculateNumOfSixty, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN call_duration % 6000.0 >= 100 THEN ceil(call_duration / 6000.0) ELSE floor(call_duration / 6000.0) END) ELSE 0 END) AS calculateNumOfSix, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN t.call_duration <= 1000 THEN 1 ELSE 0 END) ELSE 0 END) AS oneSecondConnectedNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN (t.call_duration <= 2000 AND t.call_duration > 1000) THEN 1 ELSE 0 END) ELSE 0 END) AS twoSecondConnectedNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.say_count = 0 THEN 1 ELSE 0 END) AS silenceCallNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_labels LIKE '%沉默挂机%' THEN 1 ELSE 0 END) AS silenceHangupNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_labels LIKE '%小助理%' THEN 1 ELSE 0 END) AS assistantNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_labels LIKE '%运营商提示音%' THEN 1 ELSE 0 END) AS promptSoundNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class = 'A' THEN 1 ELSE 0 END) AS classANum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class = 'B' THEN 1 ELSE 0 END) AS classBNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class = 'C' THEN 1 ELSE 0 END) AS classCNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class = 'D' THEN 1 ELSE 0 END) AS classDNum, " +
            "SUM(CASE WHEN (t.waitmsec <= 1000 AND t.waitmsec >= 0) THEN 1 ELSE 0 END) AS callFailedNum, " +
            "t.merchant_line_code AS tenantLineNumber, " +
            "t.line_id AS supplyLineNumber " +
            "FROM t_call_record_history t " +
            "WHERE t.call_out_time >= :startTime " +
            "AND t.call_out_time < :endTime " +
            "AND t.call_status IS NOT NULL " +
            "AND t.waitmsec IS NOT NULL " +
            "AND t.waitmsec >= 0 " +
            "GROUP BY t.merchant_line_code, t.line_id;", nativeQuery = true)
    List<Tuple> findOneDayCallRecordsForLineStatisticHistory(String startTime, String endTime);

    @Query(value = "SELECT " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN call_duration % 60000.0 >= 100 THEN ceil(call_duration / 60000.0) ELSE floor(call_duration / 60000.0) END) ELSE 0 END) AS calculateNumOfSixty, " +
            "t.task_id AS taskId " +
            "FROM t_call_record_history t " +
            "WHERE t.task_id in :taskIdList " +
            "AND t.call_status IS NOT NULL " +
            "AND t.waitmsec IS NOT NULL " +
            "AND t.waitmsec >= 0 " +
            "GROUP BY t.task_id ", nativeQuery = true)
    List<Tuple> findOneDayCallRecordsForTask(List<String> taskIdList);
}