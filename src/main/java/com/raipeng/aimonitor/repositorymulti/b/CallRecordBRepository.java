package com.raipeng.aimonitor.repositorymulti.b;

import com.raipeng.aidatacommon.model.record.CallRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import javax.persistence.Tuple;
import java.time.LocalDateTime;
import java.util.List;

public interface CallRecordBRepository extends JpaRepository<CallRecord,Long> {
    @Query(value="select " +
            "SUM(1) AS totalCallNum, " +
            "SUM(CASE WHEN t.call_status = '7' THEN 1 ELSE 0 END) AS totalConnectNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.say_count = 0 THEN 1 ELSE 0 END) AS silenceCallNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.call_duration <= 1000 THEN 1 ELSE 0 END) AS oneSecondConnectedNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.call_duration <= 2000 THEN 1 ELSE 0 END) AS twoSecondConnectedNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.intention_labels LIKE '%沉默挂机%' THEN 1 ELSE 0 END) AS silenceHangupNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.intention_labels LIKE '%小助理%' THEN 1 ELSE 0 END) AS assistantNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.intention_labels LIKE '%运营商提示音%' THEN 1 ELSE 0 END) AS promptSoundNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.intention_class = 'A' THEN 1 ELSE 0 END) AS classANum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.intention_class = 'B' THEN 1 ELSE 0 END) AS classBNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.intention_class = 'C' THEN 1 ELSE 0 END) AS classCNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.intention_class = 'D' THEN 1 ELSE 0 END) AS classDNum, " +
            "SUM(CASE WHEN t.waitmsec <= 1000 AND t.waitmsec >= 0 THEN 1 ELSE 0 END) AS callFailedNum, " +
            "SUM(t.waitmsec)/1000 AS waitSecond, " +
            "SUM(t.call_duration_sec) AS callDurationSecond, " +
            "t.merchant_line_code AS tenantLineNumber, " +
            "t.line_id AS supplyLineNumber, " +
            "t.province AS province," +
            "t.city AS city, " +
            "t.operator AS operator " +
            "from t_call_record t " +
            "where t.update_time>=:startTime " +
            "and t.update_time<:endTime " +
            "and t.call_status is not null " +
            "and t.merchant_line_code is not null " +
            "and waitmsec is not null " +
            "and waitmsec >= 0 " +
            "group by t.merchant_line_code, t.line_id, t.province, t.city, t.operator", nativeQuery = true)
    List<Tuple> scanCallRecordsByStartAndEndTimes(LocalDateTime startTime, LocalDateTime endTime);

    @Query(value = "SELECT " +
            "COUNT(1) AS routingFailNum, " +
            "t.merchant_line_code AS tenantLineNumber " +
            "FROM t_call_record t " +
            "WHERE t.call_status = '1' " +
            "AND t.merchant_line_code is not null " +
            "AND t.update_time>=:startTime " +
            "AND t.update_time<:endTime " +
            "GROUP BY t.merchant_line_code", nativeQuery = true)
    List<Tuple> scanCallRecordsForRoutingFailByStartAndEndTimes(LocalDateTime startTime, LocalDateTime endTime);


    @Query(value = "SELECT " +
            "SUM(1) AS totalCallNum," +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN 1 ELSE 0 END) AS totalConnectNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN t.call_duration ELSE 0 END) / 60000 AS totalConnectedMinutes, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN call_duration % 60000.0 >= 100 THEN ceil(call_duration / 60000.0) ELSE floor(call_duration / 60000.0) END) ELSE 0 END) AS calculateNumOfSixty, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN call_duration % 6000.0 >= 100 THEN ceil(call_duration / 6000.0) ELSE floor(call_duration / 6000.0) END) ELSE 0 END) AS calculateNumOfSix, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class='A' THEN 1 ELSE 0 END) AS classANum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class='B' THEN 1 ELSE 0 END) AS classBNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class='C' THEN 1 ELSE 0 END) AS classCNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class='D' THEN 1 ELSE 0 END) AS classDNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class='E' THEN 1 ELSE 0 END) AS classENum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class='F' THEN 1 ELSE 0 END) AS classFNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class='G' THEN 1 ELSE 0 END) AS classGNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class='其他' THEN 1 ELSE 0 END) AS classOtherNum, " +
            "t.account AS account " +
            "FROM t_call_record t " +
            "WHERE t.call_out_time >= :callOutTime " +
            "AND t.call_status IS NOT NULL " +
            "AND t.waitmsec IS NOT NULL " +
            "AND t.waitmsec >= 0 " +
            "AND (t.if_test is NULL OR t.if_test != '1') " +
            "GROUP BY t.account;", nativeQuery = true)
    List<Tuple> findCallDataByAccount(String callOutTime);

    @Query(value = "SELECT " +
            "SUM(1) AS totalCallNum," +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN 1 ELSE 0 END) AS totalConnectNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN t.call_duration ELSE 0 END) AS totalConnectedDuration, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN call_duration % 60000.0 >= 100 THEN ceil(call_duration / 60000.0) ELSE floor(call_duration / 60000.0) END) ELSE 0 END) AS calculateNumOfSixty, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN call_duration % 6000.0 >= 100 THEN ceil(call_duration / 6000.0) ELSE floor(call_duration / 6000.0) END) ELSE 0 END) AS calculateNumOfSix, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN t.call_duration <= 1000 THEN 1 ELSE 0 END) ELSE 0 END) AS oneSecondConnectedNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN (t.call_duration <= 2000 AND t.call_duration > 1000) THEN 1 ELSE 0 END) ELSE 0 END) AS twoSecondConnectedNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.say_count = 0 THEN 1 ELSE 0 END) AS silenceCallNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.intention_labels LIKE '%沉默挂机%' THEN 1 ELSE 0 END) AS silenceHangupNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.intention_labels LIKE '%小助理%' THEN 1 ELSE 0 END) AS assistantNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.intention_labels LIKE '%运营商提示音%' THEN 1 ELSE 0 END) AS promptSoundNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.intention_class = 'A' THEN 1 ELSE 0 END) AS classANum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.intention_class = 'B' THEN 1 ELSE 0 END) AS classBNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.intention_class = 'C' THEN 1 ELSE 0 END) AS classCNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.intention_class = 'D' THEN 1 ELSE 0 END) AS classDNum, " +
            "SUM(CASE WHEN (t.waitmsec <= 1000 AND t.waitmsec >= 0) THEN 1 ELSE 0 END) AS callFailedNum, " +
            "t.merchant_line_code AS tenantLineNumber, " +
            "t.line_id AS supplyLineNumber " +
            "FROM t_call_record t " +
            "WHERE t.call_out_time >= :startTime " +
            "AND t.call_out_time < :endTime " +
            "AND t.call_status IS NOT NULL " +
            "AND t.waitmsec IS NOT NULL " +
            "AND t.waitmsec >= 0 " +
            "GROUP BY t.merchant_line_code, t.line_id;", nativeQuery = true)
    List<Tuple> findOneDayCallRecordsForLineStatistic(String startTime, String endTime);

    @Query(value = "select * from t_call_record t " +
            "where t.call_out_time>=:startTime " +
            "and t.call_out_time<:endTime " +
            "and t.call_status is not null " +
            "and waitmsec is not null " +
            "and waitmsec >= 0", nativeQuery = true)
    List<CallRecord> findCallRecordsByStartAndEndTime(String startTime, String endTime);

    @Query(value = "select \n" +
            "row_number() over (order by z.fs_ip asc) as num,\n" +
            "z.fs_ip as fsIp,\n" +
            "z.all as calledCount,\n" +
            "z.success as calledThroughCount,\n" +
            "z.sec as callDurationSec,\n" +
            "z.time530 as time530,\n" +
            "z.cycleCount as cycleCount,\n" +
            "z.abcdCount as abcdCount,\n" +
            "round(cast(z.success as numeric)/cast(coalesce(nullif(z.all,0),1) as numeric)*100,2) as calledRatio,\n" +
            "round(cast(z.sec as numeric)/cast(coalesce(nullif(z.success,0),1) as numeric),2) as averageDuration,\n" +
            "round(cast(z.time530 as numeric)/cast(coalesce(nullif(z.success,0),1) as numeric)*100,2) as time530Percent,\n" +
            "round(cast(z.cycleCount as numeric)/cast(coalesce(nullif(z.success,0),1) as numeric),2) as averageCycleCount,\n" +
            "round(cast(z.abcdCount as numeric)/cast(coalesce(nullif(z.success,0),1) as numeric)*100,2) as abcdPercent\n" +
            " from (\n" +
            "SELECT\n" +
            "  f.fs_ip,\n" +
            "  sum(case when f.call_status is not null and f.call_status !='' then 1\n" +
            "  else 0\n" +
            "  end) as all,\n" +
            "  sum(case when f.call_status = '7' then 1\n" +
            "  else 0\n" +
            "  end) as success,\n" +
            "sum(call_duration_sec) as sec,\n" +
            "sum(case when f.call_duration_sec > 360 then 1\n" +
            "  else 0\n" +
            "  end) as time530,\n" +
            "sum(cycle_count) as cycleCount,\n" +
            "sum(case when f.intention_class in ('A','B','C','D')then 1\n" +
            "  else 0\n" +
            "  end) as abcdCount\n" +
            "FROM\n" +
            "t_call_record f\n" +
            "WHERE 1=1 \n" +
            "and f.call_out_time > :startTime \n" +
            "and f.call_out_time < :endTime \n" +
            "AND f.call_status IS NOT NULL\n" +
            "AND f.call_status != ''\n" +
            "and f.fs_ip not like '%215.168'\n" +
            "group by f.fs_ip\n" +
            "order by f.fs_ip asc\n" +
            ")z\n",nativeQuery = true)
    List<Tuple> findFsStatistics(String startTime,String endTime);

    @Query(value = "select \n" +
            "row_number() over (order by z.fs_ip asc) as num,\n" +
            "z.fs_ip as fsIp,\n" +
            "z.all as calledCount,\n" +
            "z.success as calledThroughCount,\n" +
            "z.sec as callDurationSec,\n" +
            "z.time530 as time530,\n" +
            "z.cycleCount as cycleCount,\n" +
            "z.abcdCount as abcdCount,\n" +
            "z.manHuangUpCount,\n" +
            "z.robotHuangUpCount,\n" +
            "z.noSayCount,\n" +
            "round(cast(z.success as numeric)/cast(coalesce(nullif(z.all,0),1) as numeric)*100,2) as calledRatio,\n" +
            "round(cast(z.sec as numeric)/cast(coalesce(nullif(z.success,0),1) as numeric),2) as averageDuration,\n" +
            "round(cast(z.time530 as numeric)/cast(coalesce(nullif(z.success,0),1) as numeric)*100,2) as time530Percent,\n" +
            "round(cast(z.cycleCount as numeric)/cast(coalesce(nullif(z.success,0),1) as numeric),2) as averageCycleCount,\n" +
            "round(cast(z.abcdCount as numeric)/cast(coalesce(nullif(z.success,0),1) as numeric)*100,2) as abcdPercent,\n" +
            "round(cast(z.robotHuangUpCount as numeric)/cast(coalesce(nullif(z.robotHuangUpCount+z.manHuangUpCount,0),1) as numeric),2)*100 as robotHungUpPercent,\n" +
            "round(cast(z.noSayCount as numeric)/cast(coalesce(nullif(z.success,0),1) as numeric),2)*100 as noSayCountPercent\n" +
            " from (\n" +
            "SELECT\n" +
            "  f.fs_ip,\n" +
            "  sum(case when f.call_status is not null and f.call_status !='' then 1\n" +
            "  else 0\n" +
            "  end) as all,\n" +
            "  sum(case when f.call_status = '7' then 1\n" +
            "  else 0\n" +
            "  end) as success,\n" +
            "sum(call_duration_sec) as sec,\n" +
            "sum(case when f.call_duration_sec > 360 then 1\n" +
            "  else 0\n" +
            "  end) as time530,\n" +
            "sum(cycle_count) as cycleCount,\n" +
            "sum(case when f.intention_class in ('A','B','C','D')then 1\n" +
            "  else 0\n" +
            "  end) as abcdCount,\n" +
            "sum(case when f.call_status = '7' and f.who_hangup = '1' then 1\n" +
            "  else 0\n" +
            "  end) as manHuangUpCount,\n" +
            "sum(case when f.call_status = '7'  and f.who_hangup = '0' then 1\n" +
            "  else 0\n" +
            "  end) as robotHuangUpCount,\n" +
            "sum(case when f.call_status = '7'  and f.say_count = '0' then 1\n" +
            "  else 0\n" +
            "  end) as noSayCount\n" +
            "FROM\n" +
            "t_call_record f\n" +
            "WHERE 1=1 \n" +
            "and f.call_out_time > :startTime\n" +
            "and f.call_out_time < :endTime\n" +
            "AND f.call_status IS NOT NULL\n" +
            "AND f.call_status != ''\n" +
            "and f.fs_ip not like '%215.168'\n" +
            "and (:fsIp is null or :fsIp = '' or f.fs_ip like concat('%',:fsIp,'%'))\n" +
            "and f.if_recall in (:ifRecallList)\n" +
            "and f.call_id not like 'C%'\n" +
            "group by f.fs_ip\n" +
            "order by f.fs_ip asc\n" +
            ")z\n",nativeQuery = true)
    List<Tuple> findFsStatistics1(String startTime,String endTime,String fsIp,List<Integer> ifRecallList);


    @Query(value = "select \n" +
            "row_number() over (order by z.line_code asc) as num,\n" +
            "z.line_code as lineCode,\n" +
            "z.all as calledCount,\n" +
            "z.success as calledThroughCount,\n" +
            "z.sec as callDurationSec,\n" +
            "z.time530 as time530,\n" +
            "z.cycleCount as cycleCount,\n" +
            "z.abcdCount as abcdCount,\n" +
            "round(cast(z.success as numeric)/cast(coalesce(nullif(z.all,0),1) as numeric)*100,2) as calledRatio,\n" +
            "round(cast(z.sec as numeric)/cast(coalesce(nullif(z.success,0),1) as numeric),2) as averageDuration,\n" +
            "round(cast(z.time530 as numeric)/cast(coalesce(nullif(z.success,0),1) as numeric)*100,2) as time530Percent,\n" +
            "round(cast(z.cycleCount as numeric)/cast(coalesce(nullif(z.success,0),1) as numeric),2) as averageCycleCount,\n" +
            "round(cast(z.abcdCount as numeric)/cast(coalesce(nullif(z.success,0),1) as numeric)*100,2) as abcdPercent\n" +
            " from (\n" +
            "SELECT\n" +
            "  f.line_code,\n" +
            "  sum(case when f.call_status is not null and f.call_status !='' then 1\n" +
            "  else 0\n" +
            "  end) as all,\n" +
            "  sum(case when f.call_status = '7' then 1\n" +
            "  else 0\n" +
            "  end) as success,\n" +
            "sum(call_duration_sec) as sec,\n" +
            "sum(case when f.call_duration_sec > 360 then 1\n" +
            "  else 0\n" +
            "  end) as time530,\n" +
            "sum(cycle_count) as cycleCount,\n" +
            "sum(case when f.intention_class in ('A','B','C','D')then 1\n" +
            "  else 0\n" +
            "  end) as abcdCount\n" +
            "FROM\n" +
            "t_call_record f\n" +
            "WHERE 1=1 \n" +
            "and f.call_out_time > :startTime \n" +
            "and f.call_out_time <  :endTime \n" +
            "AND f.call_status IS NOT NULL\n" +
            "AND f.call_status != ''\n" +
            "and f.line_code is not null\n" +
            "and f.fs_ip not like '%215.168'\n" +
            "group by f.line_code\n" +
            "order by f.line_code asc\n" +
            "\n" +
            ")z where z.success <= :count \n",nativeQuery = true)
    List<Tuple> lineStatistics(String startTime,String endTime,Integer count);

    @Query(value =
            "select z.fs_ip as fsIp,z.line_code as lineCode,z.all1 as calledCount,z.outtime as outTimeCount ,z.outtime*100/z.all1 as percent\n" +
                    "from (SELECT \n" +
                    "f.fs_ip,\n" +
                    "f.line_code,\n" +
                    "count(1) as all1,\n" +
                    "sum(case when f.cause = 'RECOVERY_ON_TIMER_EXPIRE' then 1\n" +
                    "else 0 end) as outtime FROM \n" +
                    "   t_call_record f\n" +
                    "WHERE 1=1 \n" +
                    "and f.call_out_time > :startTime\n" +
                    "and  f.call_out_time < :endTime\n" +
                    "group by f.fs_ip,f.line_code\n" +
                    " )z where outtime > :outTimeCount\n" +
                    " and outtime*100/all1 > :percent\n",nativeQuery = true)
    List<Tuple> lineTimeOutStatistics(String startTime,String endTime,Integer outTimeCount,Integer percent);

    @Query(value = "select f.* " +
            "from t_call_record f " +
            "where f.call_out_time >=:startTime " +
            "and f.call_out_time <:endTime " +
            "and f.call_status = '7' " +
            "and f.task_id in :taskIds " +
            "and f.whole_audio_file_url != '' " +
            "and (f.if_test != '1' or f.if_test is null)",nativeQuery = true)
    List<CallRecord> findPutThroughRecordsByTaskIds(String startTime, String endTime, List<String> taskIds);

    @Query(value = "select f.* " +
            "from t_call_record f " +
            "where f.call_out_time >=:startTime " +
            "and f.call_out_time <:endTime " +
            "and f.call_status = '7' " +
            "and f.whole_audio_file_url != '' " +
            "and (f.if_test != '1' or f.if_test is null)",nativeQuery = true)
    List<CallRecord> findPutThroughRecordsByTime(String startTime, String endTime);

    @Query(value = "select f.phone as phone, " +
            " f.intention_class as intention " +
            "from t_call_record f " +
            "where f.call_out_time >=:startTime " +
            "and f.call_out_time <:endTime " +
            "and f.call_status = '7' " +
            "and f.task_id in :taskIds " +
            "and f.whole_audio_file_url != '' " +
            "and f.intention_class in ('A','B','C','F','G') " +
            "and (f.if_test != '1' or f.if_test is null)",nativeQuery = true)
    List<Tuple> findVolcanoRecordsByTaskIds(String startTime, String endTime, List<String> taskIds);

    @Query(value="select " +
            "SUM(1) AS totalCallNum, " +
            "SUM(CASE WHEN call_status = '7' THEN 1 ELSE 0 END) AS totalConnectNum, " +
            "SUM(CASE WHEN call_status = '7' AND say_count = 0  THEN 1 ELSE 0 END) AS silenceCallNum, " +
            "SUM(CASE WHEN call_status = '7' AND call_duration <= 1000 THEN 1 ELSE 0 END) AS oneSecondConnectedNum, " +
            "SUM(CASE WHEN call_status = '7' AND call_duration > 1000 AND call_duration <= 2000 THEN 1 ELSE 0 END) AS twoSecondConnectedNum, " +
            "SUM(CASE WHEN call_status = '7' AND intention_labels LIKE '%沉默挂机%' THEN 1 ELSE 0 END) AS silenceHangupNum, " +
            "SUM(CASE WHEN call_status = '7' AND intention_labels LIKE '%小助理%' THEN 1 ELSE 0 END) AS assistantNum, " +
            "string_agg(intention_labels, ',') AS intentionLabelNames , " +
            "SUM(CASE WHEN call_status = '7' AND intention_class = 'A' THEN 1 ELSE 0 END) AS classANum, " +
            "SUM(CASE WHEN call_status = '7' AND intention_class = 'B' THEN 1 ELSE 0 END) AS classBNum, " +
            "SUM(CASE WHEN call_status = '7' AND intention_class = 'C' THEN 1 ELSE 0 END) AS classCNum, " +
            "SUM(CASE WHEN call_status = '7' AND intention_class = 'D' THEN 1 ELSE 0 END) AS classDNum, " +
            "SUM(CASE WHEN call_status = '7' AND intention_class = 'E' THEN 1 ELSE 0 END) AS classENum, " +
            "SUM(CASE WHEN call_status = '7' AND intention_class = 'F' THEN 1 ELSE 0 END) AS classFNum, " +
            "SUM(CASE WHEN call_status = '7' AND intention_class = 'G' THEN 1 ELSE 0 END) AS classGNum, " +
            "sum(call_duration) as totalCallDuration, " +
            "SUM(CASE WHEN call_status = '7' AND intention_class = 'A' THEN call_duration ELSE 0 END) AS classACallDuration, " +
            "SUM(CASE WHEN call_status = '7' AND intention_class = 'B' THEN call_duration ELSE 0 END) AS classBCallDuration, " +
            "SUM(CASE WHEN call_status = '7' AND intention_class = 'C' THEN call_duration ELSE 0 END) AS classCCallDuration, " +
            "SUM(CASE WHEN call_status = '7' AND intention_class = 'D' THEN call_duration ELSE 0 END) AS classDCallDuration, " +
            "SUM(CASE WHEN call_status = '7' AND intention_class = 'E' THEN call_duration ELSE 0 END) AS classECallDuration, " +
            "SUM(CASE WHEN call_status = '7' AND intention_class = 'F' THEN call_duration ELSE 0 END) AS classFCallDuration, " +
            "SUM(CASE WHEN call_status = '7' AND intention_class = 'G' THEN call_duration ELSE 0 END) AS classGCallDuration, " +
            "account , " +
            "task_id AS taskId, " +
            "script_string_id AS scriptStringId, " +
            "province_code AS provinceCode," +
            "max(province) AS province," +
            "city_code AS cityCode, " +
            "max(city) AS city, " +
            "operator AS operator " +
            "from t_call_record " +
            "where call_out_time>=:startTime " +
            "and call_out_time<:endTime " +
            "and call_status is not null " +
            "and call_status != '3' \n" +
            "and waitmsec is not null \n" +
            "and waitmsec >= 0 " +
            "and (if_fast_recall is null or if_fast_recall != '1') \n" +
            "group by account, task_id, script_string_id, task_id, province_code, city_code, operator", nativeQuery = true)
    List<Tuple> scanCallRecordStatisticsByStartAndEndTimes(String startTime, String endTime);

    @Query(value = "select * from t_call_record t \n" +
            "where t.call_out_time >= :callOutTimeStart \n " +
            "and  t.call_out_time < :callOutTimeEnd \n" +
            "and t.record_id in :recordIds " +
            "and (:ifIntentionClassListEmpty ='1' or  t.intention_class in :intentionClass ) ", nativeQuery = true)
    List<CallRecord> findCallRecordByCallOUtTimeAndIntentionTagsAndNumbers(String callOutTimeStart,
                                                                           String callOutTimeEnd, List<String> intentionClass,
                                                                           String ifIntentionClassListEmpty, List<String> recordIds);

    @Query(value = "SELECT " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN call_duration % 60000.0 >= 100 THEN ceil(call_duration / 60000.0) ELSE floor(call_duration / 60000.0) END) ELSE 0 END) AS calculateNumOfSixty, " +
            "t.task_id AS taskId " +
            "FROM t_call_record t " +
            "WHERE t.task_id in :taskIdList " +
            "AND t.call_status IS NOT NULL " +
            "AND t.waitmsec IS NOT NULL " +
            "AND t.waitmsec >= 0 " +
            "GROUP BY t.task_id ", nativeQuery = true)
    List<Tuple> findOneDayCallRecordsForTask(List<String> taskIdList);
}
