package com.raipeng.aimonitor.service;

import com.raipeng.aidatacommon.model.AIOutboundTask;
import com.raipeng.aidatacommon.model.dto.PhoneData;
import com.raipeng.aidatacommon.model.dto.TenantSupplySampling;
import com.raipeng.aimonitor.feign.AiDataPullFeign;
import com.raipeng.aimonitor.repository.AIOutboundTaskRepository;
import com.raipeng.aimonitor.repository.PhoneRecordRepository;
import com.raipeng.common.util.JpaResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RQueue;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.persistence.Tuple;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
@RefreshScope
public class TenantSupplySamplingService {
    @Autowired
    private PhoneRecordRepository phoneRecordRepository;

    @Autowired
    private AIOutboundTaskRepository aiOutboundTaskRepository;

    @Autowired
    private AiDataPullFeign aiDataPullFeign;

    @Value("${tenant.supply.sampling.size:1000}")
    private int tenantSupplySamplingSize;


    @Value("${tenant.supply.extra.size:500}")
    private int tenantSupplyExtraSize;

    @Autowired
    private RedissonClient redissonClient;

    public Map<String, Integer> samplingAIAutoRecord(String tenantLineNumber, String supplyLineNumber) {
        long startTime1 = System.currentTimeMillis();
        Map<String, Integer> result = new HashMap<>();
        List<Tuple> tuples = aiOutboundTaskRepository.findAllRunningByTime(LocalDate.now().atStartOfDay(),
                LocalDate.now().plusDays(1L).atStartOfDay());
        List<AIOutboundTask> aiOutboundTaskList = JpaResultUtils.processResult(tuples, AIOutboundTask.class);
        List<AIOutboundTask> allRunningTasks = aiOutboundTaskList.stream()
                .filter(aiOutboundTask -> aiOutboundTask.getLineCode().equals(tenantLineNumber))
                .collect(Collectors.toList());


        if (allRunningTasks.isEmpty()) {
            result.put("运行任务数", 0);
            return result;
        } else {
            result.put("运行任务数", allRunningTasks.size());
        }

        int size = (int) Math.ceil((double) (tenantSupplySamplingSize + tenantSupplyExtraSize) / tuples.size());
        List<String> totalPhones = new ArrayList<>();
        Set<Long> emptyTask = new HashSet<>();
        for (int pushCount = 1; pushCount <= 2 && totalPhones.size() < tenantSupplySamplingSize; pushCount++) {
            if (pushCount == 1) {
                for (AIOutboundTask task : allRunningTasks) {
                    RQueue<String> queue = redissonClient.getQueue(task.getId().toString());
                    Iterator<String> iterator = queue.iterator();
                    int count = 0;
                    while (iterator.hasNext() && count < size) {
                        String phone = iterator.next();
                        totalPhones.add(phone);
                        count++;
                    }
//                    System.out.println("一轮首呼队列 " + task.getId() + " 商户线路 " + task.getLineCode() + " 目标 " + size + " 目前 " + count);
                    if (count < size) {
                        RScoredSortedSet<String> recallScoredSortedSet = redissonClient.getScoredSortedSet(task.getId().toString() + "_recall");
                        Iterator<String> recallIterator = recallScoredSortedSet.iterator();
                        while (recallIterator.hasNext() && count < size) {
                            String phone = recallIterator.next();
                            totalPhones.add(phone);
                            count++;
                        }
                    }
                    if (count < size) {
                        emptyTask.add(task.getId());
                    }
//                    System.out.println("一轮首呼队列 " + task.getId() + " 商户线路 " + task.getLineCode() + " 目标 " + size + " 目前 " + count);
                }
            } else {
                if (totalPhones.size() < tenantSupplySamplingSize) {
                    List<AIOutboundTask> validTask = new ArrayList<>();
                    for (AIOutboundTask task : allRunningTasks) {
                        if (!emptyTask.contains(task.getId())) {
                            validTask.add(task);
                        }
                    }
                    if (validTask.isEmpty()) {
                        result.put("队列号码不足 ", totalPhones.size());
                        log.info("抽检结果1 {} {} {} ", tenantLineNumber, supplyLineNumber, result);
                        return result;
                    }
                    size = (int) Math.ceil((double) (tenantSupplySamplingSize + tenantSupplyExtraSize) / validTask.size());
                    for (AIOutboundTask task : validTask) {
                        RQueue<String> queue = redissonClient.getQueue(task.getId().toString());
                        Iterator<String> iterator = queue.iterator();
                        int count = 0;
                        while (iterator.hasNext() && count < size) {
                            String phone = iterator.next();
                            totalPhones.add(phone);
                            count++;
                        }
//                        System.out.println("二轮首呼队列 " + task.getId() + " 商户线路 " + task.getLineCode() + " 目标 " + size + " 目前 " + count);
                        if (count < size) {
                            RScoredSortedSet<String> recallScoredSortedSet = redissonClient.getScoredSortedSet(task.getId().toString() + "_recall");
                            Iterator<String> recallIterator = recallScoredSortedSet.iterator();
                            while (recallIterator.hasNext() && count < size) {
                                String phone = recallIterator.next();
                                totalPhones.add(phone);
                                count++;
                            }
                        }
                        if (count < size) {
                            emptyTask.add(task.getId());
                        }
//                        System.out.println("二轮首呼队列 " + task.getId() + " 商户线路 " + task.getLineCode() + " 目标 " + size + " 目前 " + count);
                    }
                }
            }
        }
        if (totalPhones.size() < tenantSupplySamplingSize) {
            result.put("号码数" + totalPhones.size() + "低于检测数 ", tenantSupplySamplingSize);
            log.info("抽检结果2 {} {} {} ", tenantLineNumber, supplyLineNumber, result);
            return result;
        }
        if (totalPhones.size() > tenantSupplySamplingSize) {
            Collections.shuffle(totalPhones);
            totalPhones = totalPhones.subList(0, tenantSupplySamplingSize);
        }
        List<PhoneData> data = new ArrayList<>();
        for (String phone : totalPhones) {
            String[] split = phone.split(",");
            PhoneData phoneData = new PhoneData();
            phoneData.setPlainPhone(split[0]);
            phoneData.setSpeechCallId(split[1]);
            phoneData.setPhone(split[2]);
            phoneData.setProvince(split[3]);
            phoneData.setProvinceCode(split[4]);
            phoneData.setCity(split[5]);
            phoneData.setCityCode(split[6]);
            phoneData.setOperator(split[7]);
            if (phone.contains("firstCall")) {
                phoneData.setFirstCallOrReCall(split[8]);
            } else if (phone.contains("reCall")) {
                phoneData.setTargetTime(split[8]);//补呼时间
                phoneData.setFirstCallOrReCall(split[9]);
            } else {
                phoneData.setFirstCallOrReCall("firstCall");
            }
            data.add(phoneData);
        }

        long startTime2 = System.currentTimeMillis();
        List<TenantSupplySampling> checkPhones = new ArrayList<>();
        for (PhoneData phone : data) {
            TenantSupplySampling checkPhone = new TenantSupplySampling();
            checkPhone.setPhone(phone.getPhone());
            checkPhone.setPlainPhone(phone.getPlainPhone());
            checkPhone.setProvince(phone.getProvince());
            checkPhone.setProvinceCode(phone.getProvinceCode());
            checkPhone.setCity(phone.getCity());
            checkPhone.setCityCode(phone.getCityCode());
            checkPhone.setOperator(phone.getOperator());

            checkPhones.add(checkPhone);
        }
        result = aiDataPullFeign.samplingAIAutoRecord(checkPhones, tenantLineNumber, supplyLineNumber);

        log.info("抽检结果3 {} {} {} 总耗时:{} {}", tenantLineNumber, supplyLineNumber, result, System.currentTimeMillis() - startTime1, startTime2 - startTime1);
        return result;
    }

    public Integer tenantSupplyLineCache(String tenantLineNumber, String supplyLineNumber) {
        return aiDataPullFeign.tenantSupplyLineCache(tenantLineNumber, supplyLineNumber);
    }
}
