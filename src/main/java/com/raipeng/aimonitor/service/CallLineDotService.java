package com.raipeng.aimonitor.service;

import com.raipeng.aidatacommon.model.IndustrySecondField;
import com.raipeng.aidatacommon.model.SupplyLine;
import com.raipeng.aidatacommon.model.TenantLine;
import com.raipeng.aidatacommon.model.TenantSupplyLine;
import com.raipeng.aimonitor.repository.IndustrySecondFieldRepository;
import com.raipeng.aimonitor.repository.SupplyLineRepository;
import com.raipeng.aimonitor.repository.TenantLineRepository;
import com.raipeng.aimonitor.repository.TenantSupplyLineRepository;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.Tuple;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CallLineDotService {
    public static final String CONCURRENT_DOT = "CONCURRENT_DOT::";

    public static final String CONCURRENT_DOT_KEY_SET = "CONCURRENT_DOT_KEY_SET";

    public static final String TENANT_SUPPLY_LIMIT = "TENANT_SUPPLY_LIMIT::";

    public static final String TENANT_SUPPLY_CONCURRENT_LIMIT_SUM = "TENANT_SUPPLY_CONCURRENT_LIMIT_SUM::";


    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private AiManagerClusterService aiManagerClusterService;

    @Autowired
    private TenantLineRepository tenantLineRepository;

    @Autowired
    private TenantSupplyLineRepository tenantSupplyLineRepository;

    @Autowired
    private SupplyLineRepository supplyLineRepository;
    
    
    @Autowired
    private IndustrySecondFieldRepository industrySecondFieldRepository;

    public Map<LocalDateTime, Integer> getConcurrentDotByKeyAndTime(String key, String date) {
        return redissonClient.getMap(getKey(date, key));
    }

    public Map<LocalDateTime, Integer> getTenantOrSupplyLineLimitMap(String  key, String date) {
        return redissonClient.getMap(getTenantOrLineLimit(date, key));
    }

    public Map<LocalDateTime, Integer> getTenantSupplyConcurrentLimitSumMap(String  key, String date) {
        return redissonClient.getMap(getTenantSupplyConcurrentLimitSum(date, key));
    }

    public void dotCallLineInfoIntoRedis() {
        LocalDateTime now = LocalDateTime.now().withSecond(0).withNano(0);
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        Map<String, Integer> supplyLineSizeInUsing = aiManagerClusterService.getSupplyLineSizeInUsing();
        Map<String, Integer> tenantLineSizeInUsing = aiManagerClusterService.getTenantLineSizeInUsing();
        Map<String, Integer> lineGatewaySizeInUsing = aiManagerClusterService.getLineGatewaySizeInUsing();
        Map<String, Integer> tenantSupplyLineSizeInUsing = aiManagerClusterService.getTenantSupplyLineSizeInUsing();
        Map<String, String> tenantLineSecondIndustryMap = getTenantLineSecondIndustryMap();

        supplyLineSizeInUsing.forEach((lineNumber, lineConcurrent)-> {
            RMap<Object, Object> map = redissonClient.getMap(getKey(today, lineNumber));
            map.put(now, lineConcurrent);
        });
        tenantLineSizeInUsing.forEach((lineNumber, lineConcurrent)-> {
            RMap<Object, Object> map = redissonClient.getMap(getKey(today, lineNumber));
            map.put(now, lineConcurrent);
            String secondIndustry = tenantLineSecondIndustryMap.get(lineNumber);

            RMap<Object, Object> industryConcurrentMap = redissonClient.getMap(getKey(today, secondIndustry));
            Integer currentValue = (Integer) industryConcurrentMap.get(now);
            Integer newValue = currentValue == null ? lineConcurrent : lineConcurrent + currentValue;
            industryConcurrentMap.put(now, newValue);
        });
        tenantSupplyLineSizeInUsing.forEach((lineNumber, lineConcurrent)-> {
            RMap<Object, Object> map = redissonClient.getMap(getKey(today, lineNumber));
            map.put(now, lineConcurrent);
        });
        lineGatewaySizeInUsing.forEach((lineNumber, lineConcurrent)-> {
            RMap<Object, Object> map = redissonClient.getMap(getKey(today, lineNumber));
            map.put(now, lineConcurrent);
        });
        RSet<String> callLineDotSet = redissonClient.getSet(CONCURRENT_DOT_KEY_SET);
        callLineDotSet.addAll(supplyLineSizeInUsing.keySet());
        callLineDotSet.addAll(tenantLineSizeInUsing.keySet());
        callLineDotSet.addAll(tenantLineSecondIndustryMap.keySet());

        //支配并发 并发上限
        List<Tuple> allTenantLineLimit = tenantLineRepository.findAllTenantLineLimit();
        List<Tuple> allSupplyLineLimit = supplyLineRepository.findAllSupplyLineLimit();
        List<Tuple> allTenantSupplyLineLimit = tenantSupplyLineRepository.findAllTenantSupplyLineLimit();


        Map<String,Integer> supplyLineConcurrentLimitSum = new HashMap<>();
        Map<String,Integer> tenantLineConcurrentLimitSum = new HashMap<>();

        allTenantLineLimit.forEach(tuple -> {
            String lineNumber = tuple.get("tenantLineNum", String.class);
            Integer concurrentLimit = tuple.get("concurrentLimit", Integer.class);
            RMap<Object, Object> map = redissonClient.getMap(getTenantOrLineLimit(today, lineNumber));
            map.put(now, concurrentLimit);
        });

        allSupplyLineLimit.forEach(tuple -> {
            String lineNumber = tuple.get("supplyLineNum", String.class);
            Integer concurrentLimit = tuple.get("concurrentLimit", Integer.class);
            RMap<Object, Object> map = redissonClient.getMap(getTenantOrLineLimit(today, lineNumber));
            map.put(now, concurrentLimit);
        });

        allTenantSupplyLineLimit.forEach(tuple -> {
            String tenantLineNumber = tuple.get("tenantLineNum", String.class);
            String supplyLineNumber = tuple.get("supplyLineNum", String.class);
            String lineNumber = tenantLineNumber + "_" + supplyLineNumber;
            Integer concurrentLimit = tuple.get("concurrentLimit", Integer.class);
            RMap<Object, Object> map = redissonClient.getMap(getTenantOrLineLimit(today, lineNumber));
            map.put(now, concurrentLimit);
            supplyLineConcurrentLimitSum.put(supplyLineNumber , supplyLineConcurrentLimitSum.getOrDefault(supplyLineNumber,0) + concurrentLimit);
            tenantLineConcurrentLimitSum.put(tenantLineNumber , tenantLineConcurrentLimitSum.getOrDefault(tenantLineNumber,0) + concurrentLimit);

        });

        if(!supplyLineConcurrentLimitSum.isEmpty()){
            supplyLineConcurrentLimitSum.forEach((supplyLineNumber, concurrentLimit)-> {
                RMap<Object, Object> map = redissonClient.getMap(getTenantSupplyConcurrentLimitSum(today, supplyLineNumber));
                map.put(now, concurrentLimit);
            });
        }

        if(!tenantLineConcurrentLimitSum.isEmpty()){
            tenantLineConcurrentLimitSum.forEach((tenantLineNumber, concurrentLimit)-> {
                RMap<Object, Object> map = redissonClient.getMap(getTenantSupplyConcurrentLimitSum(today, tenantLineNumber));
                map.put(now, concurrentLimit);
            });
        }


    }

    public void deleteDotCallLineInfoInRedis() {
        String date = LocalDate.now().minusDays(3).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        log.info("删除并发趋势图数据 {}" , date);
        List<TenantLine> allTenantLine = tenantLineRepository.findAll();
        List<SupplyLine> allSupplyLine = supplyLineRepository.findAll();
        List<TenantSupplyLine> allTenantSupplyLine = tenantSupplyLineRepository.findAll();
        List<IndustrySecondField> allSecondIndustry = industrySecondFieldRepository.findAll();

        allTenantLine.forEach(tenantLine -> {
            redissonClient.getMap(getKey(date, tenantLine.getLineNumber())).delete();
            redissonClient.getMap(getTenantOrLineLimit(date, tenantLine.getLineNumber())).delete();
            redissonClient.getMap(getTenantSupplyConcurrentLimitSum(date, tenantLine.getLineNumber() )).delete();
        });
        allSupplyLine.forEach(supplyLine -> {
            redissonClient.getMap(getKey(date, supplyLine.getLineNumber())).delete();
            redissonClient.getMap(getTenantOrLineLimit(date, supplyLine.getLineNumber())).delete();
            redissonClient.getMap(getTenantSupplyConcurrentLimitSum(date, supplyLine.getLineNumber() )).delete();
        });
        allTenantSupplyLine.forEach(tenantSupplyLine -> {
            redissonClient.getMap(getKey(date, tenantSupplyLine.getTenantLineNumber() + "_" + tenantSupplyLine.getSupplyLineNumber())).delete();
            redissonClient.getMap(getTenantOrLineLimit(date, tenantSupplyLine.getTenantLineNumber() + "_" + tenantSupplyLine.getSupplyLineNumber())).delete();
            redissonClient.getMap(getTenantSupplyConcurrentLimitSum(date, tenantSupplyLine.getTenantLineNumber() + "_" + tenantSupplyLine.getSupplyLineNumber() )).delete();
        });


        RSet<String> callLineDotSet = redissonClient.getSet(CONCURRENT_DOT_KEY_SET);
        callLineDotSet.forEach(key -> {
            redissonClient.getMap(getKey(date, key)).delete();
        });
        callLineDotSet.delete();

        //行业并发趋势
        allSecondIndustry.forEach(secondIndustry -> {
            redissonClient.getMap(getKey(date, secondIndustry.getSecondIndustry())).delete();
        });

    }

    private Map<String, String> getTenantLineSecondIndustryMap() {
        List<TenantLine> tenantLines = tenantLineRepository.findAll();
        return tenantLines.stream().collect(Collectors.toMap(TenantLine::getLineNumber,
                tenantLine -> tenantLine.getSecondIndustries().get(0)));
    }

    private String getKey(String date, String key) {
        return CONCURRENT_DOT + date + "::" + key;
    }

    private String getTenantOrLineLimit(String date, String key){
        return TENANT_SUPPLY_LIMIT + date + "::" + key;
    }

    private String getTenantSupplyConcurrentLimitSum(String date, String key) {
        return TENANT_SUPPLY_CONCURRENT_LIMIT_SUM + date + "::" + key;
    }
}
