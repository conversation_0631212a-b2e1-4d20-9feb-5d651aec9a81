package com.raipeng.aimonitor.service.clickhouse;

import com.alibaba.fastjson.JSONObject;
import com.clickhouse.jdbc.ClickHouseConnection;
import com.clickhouse.jdbc.ClickHouseDataSource;
import com.raipeng.aimonitor.controller.request.StatisticRequestParam;
import com.raipeng.aimonitor.exceptionadvice.exception.ClickSQLException;
import com.raipeng.aimonitor.model.CallRecordStatisticUnitData;
import com.raipeng.aimonitor.model.CallRecordStatisticUnitDataDto;
import com.raipeng.aimonitor.service.clickhouse.sql.CallRecordStatusUnitClickSql;
import com.raipeng.aimonitor.service.clickhouse.sql.CallRecordUnitSql;
import com.raipeng.aimonitor.utils.ClickHouseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


@Slf4j
@Service
public class CallRecordStatusUnitClickService {

    @Autowired
    private ClickHouseDataSource dataSource;

    public void insertIntoCallRecordUnitData(List<CallRecordStatisticUnitData> unitDataList, String table) {
        try (ClickHouseConnection connection = dataSource.getConnection()) {
            String saveAllDateUnitSql = CallRecordUnitSql.SAVE_UNIT_SQL;
            String sql = String.format(saveAllDateUnitSql, table);
            PreparedStatement ps = connection.prepareStatement(sql);
            for (CallRecordStatisticUnitData data : unitDataList) {
                ps.setInt(1, data.getTimeSlot());
                ps.setString(2, ClickHouseUtil.getStringOrEmpty(data.getAccount()));
                ps.setString(3, ClickHouseUtil.getStringOrEmpty(data.getTaskId()));
                ps.setString(4, ClickHouseUtil.getStringOrEmpty(data.getScriptStringId()));
                ps.setInt(5, data.getTotalCallNum());
                ps.setInt(6, data.getTotalConnectNum());
                ps.setInt(7, data.getSilenceCallNum());
                ps.setInt(8, data.getOneSecondConnectedNum());
                ps.setInt(9, data.getTwoSecondConnectedNum());
                ps.setInt(10, data.getSilenceHangupNum());
                ps.setInt(11, data.getAssistantNum());
                ps.setInt(12, data.getClassANum());
                ps.setInt(13, data.getClassBNum());
                ps.setInt(14, data.getClassCNum());
                ps.setInt(15, data.getClassDNum());
                ps.setInt(16, data.getClassENum());
                ps.setInt(17, data.getClassFNum());
                ps.setInt(18, data.getClassGNum());
                ps.setInt(19, data.getClassOtherNum());
                ps.setLong(20, data.getClassACallDuration());
                ps.setLong(21, data.getClassBCallDuration());
                ps.setLong(22, data.getClassCCallDuration());
                ps.setLong(23, data.getClassDCallDuration());
                ps.setLong(24, data.getClassECallDuration());
                ps.setLong(25, data.getClassFCallDuration());
                ps.setLong(26, data.getClassGCallDuration());
                ps.setLong(27, data.getClassOtherCallDuration());
                ps.setLong(28, data.getTotalCallDuration());
                ps.setString(29, JSONObject.toJSONString(data.getIntentionLabelCount()));
                ps.setString(30, ClickHouseUtil.getStringOrEmpty(data.getProvince()));
                ps.setString(31, ClickHouseUtil.getStringOrEmpty(data.getProvinceCode()));
                ps.setString(32, ClickHouseUtil.getStringOrEmpty(data.getCity()));
                ps.setString(33, ClickHouseUtil.getStringOrEmpty(data.getCityCode()));
                ps.setString(34, ClickHouseUtil.getStringOrEmpty(data.getOperator()));
                ps.setString(35, data.getStatisticDate());
                ps.addBatch();
            }
            ps.executeBatch();
        } catch (Exception e) {
            log.error("Exception: insertIntoCallRecordUnitData:{}", e.getMessage());
            e.printStackTrace();
            throw new ClickSQLException("insert into call line monitor unit data error");
        }
    }


    public boolean existsByTimeSlot(int timeSlot) {
        try (ClickHouseConnection connection = dataSource.getConnection()) {
            PreparedStatement ps = connection.prepareStatement(CallRecordUnitSql.EXISTS_BY_TIME_SLOT);
            ps.setInt(1, timeSlot);
            try (ResultSet resultSet = ps.executeQuery()) {
                if (resultSet.next()) {
                    return resultSet.getInt(1) == 1;
                }
            }
        } catch (Exception e) {
            log.error("Exception: exists by time slot error: {}", e.getMessage());
        }
        return false;
    }


    public void cleanDataClickHouse(int timeSlot) {
        try {
            Connection connection = dataSource.getConnection();
            String sql = String.format("ALTER TABLE %s DROP PARTITION ? ", "aiSpeech.call_record_statistics_unit_data");
            PreparedStatement ps = connection.prepareStatement(sql);
            ps.setInt(1, timeSlot);
            ps.executeUpdate();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void cleanDataClickHouse(String statisticsDate) {

        try {
            Connection connection = dataSource.getConnection();
            String sql = String.format("ALTER TABLE %s DROP PARTITION ?", "aiSpeech.call_record_statistics_unit_all_data");
            PreparedStatement ps = connection.prepareStatement(sql);
            ps.setString(1, statisticsDate);
            ps.executeUpdate();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public CallRecordStatisticUnitDataDto findGroup(StatisticRequestParam statisticRequestParam, String searchTable) {
        CallRecordStatisticUnitDataDto callRecordStatisticUnitDataDto = new CallRecordStatisticUnitDataDto();
        List<String> taskIdList = statisticRequestParam.getTaskIdList().stream().map(Object::toString).collect(Collectors.toList());
        List<String> scriptStringIdList = statisticRequestParam.getScriptStringIdList();
        String operator = statisticRequestParam.getOperator();
        String cityCode = statisticRequestParam.getCityCode();
        String provinceCode = statisticRequestParam.getProvinceCode();
        String queryDate = statisticRequestParam.getQueryDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        String sqlW = CallRecordStatusUnitClickSql.getSql(taskIdList,
                scriptStringIdList,
                operator,
                cityCode, provinceCode
        );
        String sql = String.format(sqlW, searchTable);

        try (Connection connection = dataSource.getConnection()) {
            PreparedStatement statement = connection.prepareStatement(sql);
            statement.setString(1, statisticRequestParam.getAccount());
            statement.setString(2, queryDate);
            int parameterIndex = 3;
            if (CollectionUtils.isNotEmpty(taskIdList)) {
                statement.setArray(parameterIndex, connection.createArrayOf("VARCHAR", taskIdList.toArray(new String[0])));
                parameterIndex++;
            }
            if (CollectionUtils.isNotEmpty(scriptStringIdList)) {
                statement.setArray(parameterIndex, connection.createArrayOf("VARCHAR", scriptStringIdList.toArray(new String[0])));
                parameterIndex++;
            }
            if (StringUtils.isNotEmpty(provinceCode)) {
                statement.setString(parameterIndex, provinceCode);
                parameterIndex++;
            }
            if (StringUtils.isNotEmpty(cityCode)) {
                statement.setString(parameterIndex, cityCode);
                parameterIndex++;
            }
            if (StringUtils.isNotEmpty(operator)) {
                statement.setString(parameterIndex, operator);
            }

            try (ResultSet resultSet = statement.executeQuery()) {
                while (resultSet.next()) {
//                    String[] accountS = (String[]) resultSet.getArray("intentionLabelNames").getArray();
//                    List<String> names = Arrays.asList(accountS);
                    callRecordStatisticUnitDataDto.setClassANum(resultSet.getInt("class_a_num"));
                    callRecordStatisticUnitDataDto.setClassBNum(resultSet.getInt("class_b_num"));
                    callRecordStatisticUnitDataDto.setClassCNum(resultSet.getInt("class_c_num"));
                    callRecordStatisticUnitDataDto.setClassDNum(resultSet.getInt("class_d_num"));
                    callRecordStatisticUnitDataDto.setClassENum(resultSet.getInt("class_e_num"));
                    callRecordStatisticUnitDataDto.setClassFNum(resultSet.getInt("class_f_num"));
                    callRecordStatisticUnitDataDto.setClassGNum(resultSet.getInt("class_g_num"));
                    callRecordStatisticUnitDataDto.setTotalConnectNum(resultSet.getInt("total_connect_num"));
                    callRecordStatisticUnitDataDto.setTotalCallNum(resultSet.getInt("total_call_num"));
//                    callRecordStatisticUnitDataDto.setIntentionLabelNameList(names);
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return callRecordStatisticUnitDataDto;
    }


    public List<CallRecordStatisticUnitDataDto> findAllByDateAndTaskIdList(StatisticRequestParam statisticRequestParam, String searchTable) {
        List<CallRecordStatisticUnitDataDto> callRecordStatisticUnitDataDtoList = new ArrayList<>();
        List<String> taskIdList = statisticRequestParam.getTaskIdList().stream().map(Object::toString).collect(Collectors.toList());
        List<String> scriptStringIdList = statisticRequestParam.getScriptStringIdList();
        String operator = statisticRequestParam.getOperator();
        String cityCode = statisticRequestParam.getCityCode();
        String provinceCode = statisticRequestParam.getProvinceCode();
        String sqlW = CallRecordStatusUnitClickSql.getPutThroughRateSql(taskIdList,
                scriptStringIdList,
                operator,
                cityCode, provinceCode
        );
        String sql = String.format(sqlW, searchTable);
        String queryDate = statisticRequestParam.getQueryDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        try (Connection connection = dataSource.getConnection()) {
            PreparedStatement statement = connection.prepareStatement(sql);
            statement.setString(1, statisticRequestParam.getAccount());
            statement.setString(2, queryDate);
            int parameterIndex = 3;
            if (CollectionUtils.isNotEmpty(taskIdList)) {
                statement.setArray(parameterIndex, connection.createArrayOf("VARCHAR", taskIdList.toArray(new String[0])));
                parameterIndex++;
            }
            if (CollectionUtils.isNotEmpty(scriptStringIdList)) {
                statement.setArray(parameterIndex, connection.createArrayOf("VARCHAR", scriptStringIdList.toArray(new String[0])));
                parameterIndex++;
            }
            if (StringUtils.isNotEmpty(provinceCode)) {
                statement.setString(parameterIndex, provinceCode);
                parameterIndex++;
            }
            if (StringUtils.isNotEmpty(cityCode)) {
                statement.setString(parameterIndex, cityCode);
                parameterIndex++;
            }
            if (StringUtils.isNotEmpty(operator)) {
                statement.setString(parameterIndex, operator);
            }

            try (ResultSet resultSet = statement.executeQuery()) {
                while (resultSet.next()) {
                    CallRecordStatisticUnitDataDto callRecordStatisticUnitDataDto = new CallRecordStatisticUnitDataDto();
                    callRecordStatisticUnitDataDto.setTotalConnectNum(resultSet.getInt("total_connect_num"));
                    callRecordStatisticUnitDataDto.setTotalCallNum(resultSet.getInt("total_call_num"));
                    callRecordStatisticUnitDataDto.setTimeSlot(resultSet.getInt("time_slot"));
                    callRecordStatisticUnitDataDtoList.add(callRecordStatisticUnitDataDto);
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }

        return callRecordStatisticUnitDataDtoList;

    }


    public List<String> getScriptIntentionStatisticList(StatisticRequestParam statisticRequestParam, String searchTable) {
        List<String> callRecordStatisticUnitDataDtoList = new ArrayList<>();
        List<String> taskIdList = statisticRequestParam.getTaskIdList().stream().map(Object::toString).collect(Collectors.toList());
        List<String> scriptStringIdList = statisticRequestParam.getScriptStringIdList();
        String operator = statisticRequestParam.getOperator();
        String cityCode = statisticRequestParam.getCityCode();
        String provinceCode = statisticRequestParam.getProvinceCode();
        String sqlW = CallRecordStatusUnitClickSql.getScriptIntentionlabelSql(taskIdList,
                scriptStringIdList,
                operator,
                cityCode, provinceCode
        );
        String sql = String.format(sqlW, searchTable);
        String queryDate = statisticRequestParam.getQueryDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        try (Connection connection = dataSource.getConnection()) {
            PreparedStatement statement = connection.prepareStatement(sql);
            statement.setString(1, statisticRequestParam.getAccount());
            statement.setString(2, queryDate);
            int parameterIndex = 3;
            if (CollectionUtils.isNotEmpty(taskIdList)) {
                statement.setArray(parameterIndex, connection.createArrayOf("VARCHAR", taskIdList.toArray(new String[0])));
                parameterIndex++;
            }
            if (CollectionUtils.isNotEmpty(scriptStringIdList)) {
                statement.setArray(parameterIndex, connection.createArrayOf("VARCHAR", scriptStringIdList.toArray(new String[0])));
                parameterIndex++;
            }
            if (StringUtils.isNotEmpty(provinceCode)) {
                statement.setString(parameterIndex, provinceCode);
                parameterIndex++;
            }
            if (StringUtils.isNotEmpty(cityCode)) {
                statement.setString(parameterIndex, cityCode);
                parameterIndex++;
            }
            if (StringUtils.isNotEmpty(operator)) {
                statement.setString(parameterIndex, operator);
            }

            try (ResultSet resultSet = statement.executeQuery()) {
                while (resultSet.next()) {
                    callRecordStatisticUnitDataDtoList.add(resultSet.getString("intentionLabelNames"));
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return callRecordStatisticUnitDataDtoList;
    }


    public int getScriptIntentionStatisticCount(StatisticRequestParam statisticRequestParam, String searchTable) {
        int callRecordStatisticUnitDataCount = 0;
        List<String> taskIdList = statisticRequestParam.getTaskIdList().stream().map(Object::toString).collect(Collectors.toList());
        List<String> scriptStringIdList = statisticRequestParam.getScriptStringIdList();
        String operator = statisticRequestParam.getOperator();
        String cityCode = statisticRequestParam.getCityCode();
        String provinceCode = statisticRequestParam.getProvinceCode();
        String sqlW = CallRecordStatusUnitClickSql.getScriptIntentionlabelCountSql(taskIdList,
                scriptStringIdList,
                operator,
                cityCode, provinceCode
        );
        String sql = String.format(sqlW, searchTable);
        String queryDate = statisticRequestParam.getQueryDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        try (Connection connection = dataSource.getConnection()) {
            PreparedStatement statement = connection.prepareStatement(sql);
            statement.setString(1, statisticRequestParam.getAccount());
            statement.setString(2, queryDate);

            int parameterIndex = 3;
            if (CollectionUtils.isNotEmpty(taskIdList)) {
                statement.setArray(parameterIndex, connection.createArrayOf("VARCHAR", taskIdList.toArray(new String[0])));
                parameterIndex++;
            }
            if (CollectionUtils.isNotEmpty(scriptStringIdList)) {
                statement.setArray(parameterIndex, connection.createArrayOf("VARCHAR", scriptStringIdList.toArray(new String[0])));
                parameterIndex++;
            }
            if (StringUtils.isNotEmpty(provinceCode)) {
                statement.setString(parameterIndex, provinceCode);
                parameterIndex++;
            }
            if (StringUtils.isNotEmpty(cityCode)) {
                statement.setString(parameterIndex, cityCode);
                parameterIndex++;
            }
            if (StringUtils.isNotEmpty(operator)) {
                statement.setString(parameterIndex, operator);
            }

            try (ResultSet resultSet = statement.executeQuery()) {
                while (resultSet.next()) {
                    callRecordStatisticUnitDataCount = resultSet.getInt("total_connect_num");
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return callRecordStatisticUnitDataCount;
    }

    public CallRecordStatisticUnitDataDto getScriptIntentionClassStatisticList(StatisticRequestParam statisticRequestParam, String searchTable) {
        CallRecordStatisticUnitDataDto callRecordStatisticUnitDataDto = new CallRecordStatisticUnitDataDto();
        List<String> taskIdList = statisticRequestParam.getTaskIdList().stream().map(Object::toString).collect(Collectors.toList());
        List<String> scriptStringIdList = statisticRequestParam.getScriptStringIdList();
        String operator = statisticRequestParam.getOperator();
        String cityCode = statisticRequestParam.getCityCode();
        String provinceCode = statisticRequestParam.getProvinceCode();

        String sqlW = CallRecordStatusUnitClickSql.getScriptIntentionClassSql(taskIdList,
                scriptStringIdList,
                operator,
                cityCode, provinceCode
        );
        String sql = String.format(sqlW, searchTable);
        String queryDate = statisticRequestParam.getQueryDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        try (Connection connection = dataSource.getConnection()) {
            PreparedStatement statement = connection.prepareStatement(sql);
            statement.setString(1, statisticRequestParam.getAccount());
            statement.setString(2, queryDate);
            int parameterIndex = 3;
            if (CollectionUtils.isNotEmpty(taskIdList)) {
                statement.setArray(parameterIndex, connection.createArrayOf("VARCHAR", taskIdList.toArray(new String[0])));
                parameterIndex++;
            }
            if (CollectionUtils.isNotEmpty(scriptStringIdList)) {
                statement.setArray(parameterIndex, connection.createArrayOf("VARCHAR", scriptStringIdList.toArray(new String[0])));
                parameterIndex++;
            }
            if (StringUtils.isNotEmpty(provinceCode)) {
                statement.setString(parameterIndex, provinceCode);
                parameterIndex++;
            }
            if (StringUtils.isNotEmpty(cityCode)) {
                statement.setString(parameterIndex, cityCode);
                parameterIndex++;
            }
            if (StringUtils.isNotEmpty(operator)) {
                statement.setString(parameterIndex, operator);
            }

            try (ResultSet resultSet = statement.executeQuery()) {
                while (resultSet.next()) {
                    callRecordStatisticUnitDataDto.setClassANum(resultSet.getInt("class_a_num"));
                    callRecordStatisticUnitDataDto.setClassBNum(resultSet.getInt("class_b_num"));
                    callRecordStatisticUnitDataDto.setClassCNum(resultSet.getInt("class_c_num"));
                    callRecordStatisticUnitDataDto.setClassDNum(resultSet.getInt("class_d_num"));
                    callRecordStatisticUnitDataDto.setClassENum(resultSet.getInt("class_e_num"));
                    callRecordStatisticUnitDataDto.setClassFNum(resultSet.getInt("class_f_num"));
                    callRecordStatisticUnitDataDto.setClassGNum(resultSet.getInt("class_g_num"));
                    callRecordStatisticUnitDataDto.setClassOtherNum(resultSet.getInt("class_other_num"));
                    callRecordStatisticUnitDataDto.setTotalConnectNum(resultSet.getInt("total_connect_num"));
                    callRecordStatisticUnitDataDto.setTotalCallNum(resultSet.getInt("total_call_num"));
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return callRecordStatisticUnitDataDto;
    }

    public CallRecordStatisticUnitDataDto findClassRatio(StatisticRequestParam statisticRequestParam, String searchTable) {
        CallRecordStatisticUnitDataDto callRecordStatisticUnitDataDto = new CallRecordStatisticUnitDataDto();
        List<String> taskIdList = statisticRequestParam.getTaskIdList().stream().map(Object::toString).collect(Collectors.toList());
        String sql1 = "SELECT sum(total_connect_num)  as total_connect_num, " +
                "  sum(class_a_num)  as class_a_num, " +
                "  sum(class_b_num)  as class_b_num, " +
                "  sum(class_c_num)  as class_c_num, " +
                "  sum(class_d_num)  as class_d_num, " +
                "  sum(class_e_num)  as class_e_num, " +
                "  sum(class_f_num)  as class_f_num, " +
                "  sum(class_g_num)  as class_g_num, " +
                "  sum(class_other_num)  as class_other_num, " +
                "  sum(total_call_num)  as total_call_num " +
                " FROM %s  \n" +
                "WHERE\n" +
                "\t  task_id in ? \n";
        String sql = String.format(sql1, searchTable);

        try (Connection connection = dataSource.getConnection()) {
            PreparedStatement statement = connection.prepareStatement(sql);
            statement.setArray(1, connection.createArrayOf("VARCHAR", taskIdList.toArray(new String[0])));
            try (ResultSet resultSet = statement.executeQuery()) {
                while (resultSet.next()) {
                    callRecordStatisticUnitDataDto.setClassANum(resultSet.getInt("class_a_num"));
                    callRecordStatisticUnitDataDto.setClassBNum(resultSet.getInt("class_b_num"));
                    callRecordStatisticUnitDataDto.setClassCNum(resultSet.getInt("class_c_num"));
                    callRecordStatisticUnitDataDto.setClassDNum(resultSet.getInt("class_d_num"));
                    callRecordStatisticUnitDataDto.setClassENum(resultSet.getInt("class_e_num"));
                    callRecordStatisticUnitDataDto.setClassFNum(resultSet.getInt("class_f_num"));
                    callRecordStatisticUnitDataDto.setClassGNum(resultSet.getInt("class_g_num"));
                    callRecordStatisticUnitDataDto.setClassOtherNum(resultSet.getInt("class_other_num"));
                    callRecordStatisticUnitDataDto.setTotalConnectNum(resultSet.getInt("total_connect_num"));
                    callRecordStatisticUnitDataDto.setTotalCallNum(resultSet.getInt("total_call_num"));
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return callRecordStatisticUnitDataDto;

    }

    public CallRecordStatisticUnitDataDto getDataRateChartTodayData(StatisticRequestParam statisticRequestParam, String searchTable) {

        CallRecordStatisticUnitDataDto callRecordStatisticUnitDataDto = new CallRecordStatisticUnitDataDto();
        List<String> taskIdList = statisticRequestParam.getTaskIdList().stream().map(Object::toString).collect(Collectors.toList());
        List<String> scriptStringIdList = statisticRequestParam.getScriptStringIdList();
        String operator = statisticRequestParam.getOperator();
        String cityCode = statisticRequestParam.getCityCode();
        String provinceCode = statisticRequestParam.getProvinceCode();

        String sqlW = CallRecordStatusUnitClickSql.getDataRateChartTodayDataSql(taskIdList,
                scriptStringIdList,
                operator,
                cityCode,
                provinceCode
        );
        String sql = String.format(sqlW, searchTable);
        String queryDate = statisticRequestParam.getQueryDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        try (Connection connection = dataSource.getConnection()) {
            PreparedStatement statement = connection.prepareStatement(sql);
            statement.setString(1, statisticRequestParam.getAccount());
            statement.setString(2, queryDate);
            int parameterIndex = 3;
            if (CollectionUtils.isNotEmpty(taskIdList)) {
                statement.setArray(parameterIndex, connection.createArrayOf("VARCHAR", taskIdList.toArray(new String[0])));
                parameterIndex++;
            }
            if (CollectionUtils.isNotEmpty(scriptStringIdList)) {
                statement.setArray(parameterIndex, connection.createArrayOf("VARCHAR", scriptStringIdList.toArray(new String[0])));
                parameterIndex++;
            }
            if (StringUtils.isNotEmpty(provinceCode)) {
                statement.setString(parameterIndex, provinceCode);
                parameterIndex++;
            }
            if (StringUtils.isNotEmpty(cityCode)) {
                statement.setString(parameterIndex, cityCode);
                parameterIndex++;
            }
            if (StringUtils.isNotEmpty(operator)) {
                statement.setString(parameterIndex, operator);
            }

            try (ResultSet resultSet = statement.executeQuery()) {
                while (resultSet.next()) {
                    getCommonField(resultSet, callRecordStatisticUnitDataDto);
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }


        return callRecordStatisticUnitDataDto;
    }

    private static void getCommonField(ResultSet resultSet, CallRecordStatisticUnitDataDto callRecordStatisticUnitDataDto) throws SQLException {
        callRecordStatisticUnitDataDto.setClassANum(resultSet.getInt("class_a_num"));
        callRecordStatisticUnitDataDto.setClassBNum(resultSet.getInt("class_b_num"));
        callRecordStatisticUnitDataDto.setClassCNum(resultSet.getInt("class_c_num"));
        callRecordStatisticUnitDataDto.setClassDNum(resultSet.getInt("class_d_num"));
        callRecordStatisticUnitDataDto.setClassENum(resultSet.getInt("class_e_num"));
        callRecordStatisticUnitDataDto.setClassFNum(resultSet.getInt("class_f_num"));
        callRecordStatisticUnitDataDto.setClassGNum(resultSet.getInt("class_g_num"));
        callRecordStatisticUnitDataDto.setClassOtherNum(resultSet.getInt("class_other_num"));

        callRecordStatisticUnitDataDto.setClassACallDuration(resultSet.getLong("class_a_call_duration"));
        callRecordStatisticUnitDataDto.setClassBCallDuration(resultSet.getLong("class_b_call_duration"));
        callRecordStatisticUnitDataDto.setClassCCallDuration(resultSet.getLong("class_c_call_duration"));
        callRecordStatisticUnitDataDto.setClassDCallDuration(resultSet.getLong("class_d_call_duration"));
        callRecordStatisticUnitDataDto.setClassECallDuration(resultSet.getLong("class_e_call_duration"));
        callRecordStatisticUnitDataDto.setClassFCallDuration(resultSet.getLong("class_f_call_duration"));
        callRecordStatisticUnitDataDto.setClassGCallDuration(resultSet.getLong("class_g_call_duration"));
        callRecordStatisticUnitDataDto.setClassOtherCallDuration(resultSet.getLong("class_other_call_duration"));
        callRecordStatisticUnitDataDto.setTotalCallDuration(resultSet.getLong("call_duration"));

        callRecordStatisticUnitDataDto.setSilenceCallNum(resultSet.getInt("silence_call_num"));
        callRecordStatisticUnitDataDto.setOneSecondConnectedNum(resultSet.getInt("one_second_connected_num"));
        callRecordStatisticUnitDataDto.setTwoSecondConnectedNum(resultSet.getInt("two_second_connected_num"));
        callRecordStatisticUnitDataDto.setSilenceHangupNum(resultSet.getInt("silence_hangup_Num"));
        callRecordStatisticUnitDataDto.setAssistantNum(resultSet.getInt("assistant_num"));
        callRecordStatisticUnitDataDto.setTotalConnectNum(resultSet.getInt("total_connect_num"));
        callRecordStatisticUnitDataDto.setTotalCallNum(resultSet.getInt("total_call_num"));
    }


    public List<CallRecordStatisticUnitDataDto> getRateChartDataList(StatisticRequestParam statisticRequestParam, String searchTable) {

        List<CallRecordStatisticUnitDataDto> callRecordStatisticUnitDataDtoList = new ArrayList<>();
        List<String> taskIdList = statisticRequestParam.getTaskIdList().stream().map(Object::toString).collect(Collectors.toList());
        List<String> scriptStringIdList = statisticRequestParam.getScriptStringIdList();
        String operator = statisticRequestParam.getOperator();
        String cityCode = statisticRequestParam.getCityCode();
        String provinceCode = statisticRequestParam.getProvinceCode();

        String sqlW = CallRecordStatusUnitClickSql.getRateChartTodayDataSql(taskIdList,
                scriptStringIdList,
                operator,
                cityCode, provinceCode
        );
        String sql = String.format(sqlW, searchTable);
        String queryDate = statisticRequestParam.getQueryDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        try (Connection connection = dataSource.getConnection()) {
            PreparedStatement statement = connection.prepareStatement(sql);
            statement.setString(1, statisticRequestParam.getAccount());
            statement.setString(2, queryDate);

            int parameterIndex = 3;
            if (CollectionUtils.isNotEmpty(taskIdList)) {
                statement.setArray(parameterIndex, connection.createArrayOf("VARCHAR", taskIdList.toArray(new String[0])));
                parameterIndex++;
            }
            if (CollectionUtils.isNotEmpty(scriptStringIdList)) {
                statement.setArray(parameterIndex, connection.createArrayOf("VARCHAR", scriptStringIdList.toArray(new String[0])));
                parameterIndex++;
            }
            if (StringUtils.isNotEmpty(provinceCode)) {
                statement.setString(parameterIndex, provinceCode);
                parameterIndex++;
            }
            if (StringUtils.isNotEmpty(cityCode)) {
                statement.setString(parameterIndex, cityCode);
                parameterIndex++;
            }
            if (StringUtils.isNotEmpty(operator)) {
                statement.setString(parameterIndex, operator);
            }

            try (ResultSet resultSet = statement.executeQuery()) {
                while (resultSet.next()) {
                    CallRecordStatisticUnitDataDto callRecordStatisticUnitDataDto = new CallRecordStatisticUnitDataDto();
                    getCommonField(resultSet, callRecordStatisticUnitDataDto);
                    callRecordStatisticUnitDataDto.setTimeSlot(resultSet.getInt("time_slot"));
                    callRecordStatisticUnitDataDtoList.add(callRecordStatisticUnitDataDto);
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }


        return callRecordStatisticUnitDataDtoList;
    }

    public List<CallRecordStatisticUnitDataDto> getCountryChartDataList(StatisticRequestParam statisticRequestParam, String searchTable) {

        List<CallRecordStatisticUnitDataDto> callRecordStatisticUnitDataDtoList = new ArrayList<>();
        List<String> taskIdList = statisticRequestParam.getTaskIdList().stream().map(Object::toString).collect(Collectors.toList());
        List<String> scriptStringIdList = statisticRequestParam.getScriptStringIdList();
        String operator = statisticRequestParam.getOperator();
        String city = statisticRequestParam.getCity();
        String cityCode = statisticRequestParam.getCityCode();
        String province = statisticRequestParam.getProvince();
        String provinceCode = statisticRequestParam.getProvinceCode();

        String sqlW = CallRecordStatusUnitClickSql.getProvinceChartTodayDataSql(taskIdList,
                scriptStringIdList,
                operator,
                city, cityCode, province, provinceCode
        );
        String sql = String.format(sqlW, searchTable);
        String queryDate = statisticRequestParam.getQueryDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        try (Connection connection = dataSource.getConnection()) {
            PreparedStatement statement = connection.prepareStatement(sql);
            statement.setString(1, statisticRequestParam.getAccount());
            statement.setString(2, queryDate);

            int parameterIndex = 3;
            if (CollectionUtils.isNotEmpty(taskIdList)) {
                statement.setArray(parameterIndex, connection.createArrayOf("VARCHAR", taskIdList.toArray(new String[0])));
                parameterIndex++;
            }
            if (CollectionUtils.isNotEmpty(scriptStringIdList)) {
                statement.setArray(parameterIndex, connection.createArrayOf("VARCHAR", scriptStringIdList.toArray(new String[0])));
                parameterIndex++;
            }
            if (StringUtils.isNotEmpty(province)) {
                statement.setString(parameterIndex, province);
                parameterIndex++;
            }
            if (StringUtils.isNotEmpty(provinceCode)) {
                statement.setString(parameterIndex, provinceCode);
                parameterIndex++;
            }
            if (StringUtils.isNotEmpty(city)) {
                statement.setString(parameterIndex, city);
                parameterIndex++;
            }
            if (StringUtils.isNotEmpty(cityCode)) {
                statement.setString(parameterIndex, cityCode);
                parameterIndex++;
            }
            if (StringUtils.isNotEmpty(operator)) {
                statement.setString(parameterIndex, operator);
            }

            try (ResultSet resultSet = statement.executeQuery()) {
                while (resultSet.next()) {
                    CallRecordStatisticUnitDataDto callRecordStatisticUnitDataDto = new CallRecordStatisticUnitDataDto();
                    getCommonField(resultSet, callRecordStatisticUnitDataDto);
                    callRecordStatisticUnitDataDto.setProvince(resultSet.getString("province"));

                    callRecordStatisticUnitDataDtoList.add(callRecordStatisticUnitDataDto);
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return callRecordStatisticUnitDataDtoList;
    }

    public List<CallRecordStatisticUnitDataDto> getProvinceChartDataList(StatisticRequestParam statisticRequestParam, String searchTable) {
        List<CallRecordStatisticUnitDataDto> callRecordStatisticUnitDataDtoList = new ArrayList<>();
        List<String> taskIdList = statisticRequestParam.getTaskIdList().stream().map(Object::toString).collect(Collectors.toList());
        List<String> scriptStringIdList = statisticRequestParam.getScriptStringIdList();
        String operator = statisticRequestParam.getOperator();
        String city = statisticRequestParam.getCity();
        String cityCode = statisticRequestParam.getCityCode();
        String province = statisticRequestParam.getProvince();
        String provinceCode = statisticRequestParam.getProvinceCode();

        String sqlW = CallRecordStatusUnitClickSql.getCityChartTodayDataSql(taskIdList,
                scriptStringIdList,
                operator,
                city, cityCode, province, provinceCode
        );
        String sql = String.format(sqlW, searchTable);
        String queryDate = statisticRequestParam.getQueryDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        try (Connection connection = dataSource.getConnection()) {
            PreparedStatement statement = connection.prepareStatement(sql);
            statement.setString(1, statisticRequestParam.getAccount());
            statement.setString(2, queryDate);

            int parameterIndex = 3;
            if (CollectionUtils.isNotEmpty(taskIdList)) {
                statement.setArray(parameterIndex, connection.createArrayOf("VARCHAR", taskIdList.toArray(new String[0])));
                parameterIndex++;
            }
            if (CollectionUtils.isNotEmpty(scriptStringIdList)) {
                statement.setArray(parameterIndex, connection.createArrayOf("VARCHAR", scriptStringIdList.toArray(new String[0])));
                parameterIndex++;
            }
            if (StringUtils.isNotEmpty(province)) {
                statement.setString(parameterIndex, province);
                parameterIndex++;
            }
            if (StringUtils.isNotEmpty(provinceCode)) {
                statement.setString(parameterIndex, provinceCode);
                parameterIndex++;
            }
            if (StringUtils.isNotEmpty(city)) {
                statement.setString(parameterIndex, city);
                parameterIndex++;
            }
            if (StringUtils.isNotEmpty(cityCode)) {
                statement.setString(parameterIndex, cityCode);
                parameterIndex++;
            }
            if (StringUtils.isNotEmpty(operator)) {
                statement.setString(parameterIndex, operator);
            }

            try (ResultSet resultSet = statement.executeQuery()) {
                while (resultSet.next()) {
                    CallRecordStatisticUnitDataDto callRecordStatisticUnitDataDto = new CallRecordStatisticUnitDataDto();
                    getCommonField(resultSet, callRecordStatisticUnitDataDto);
                    callRecordStatisticUnitDataDto.setCity(resultSet.getString("city"));
                    callRecordStatisticUnitDataDtoList.add(callRecordStatisticUnitDataDto);
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return callRecordStatisticUnitDataDtoList;
    }


    public void cleanDayDataClickHouse() {
        try (ClickHouseConnection connection = dataSource.getConnection()) {
            PreparedStatement ps = connection.prepareStatement(CallRecordUnitSql.CLEAR_DAY_UNIT_DATA);
            ps.execute();
        } catch (Exception e) {
            log.error("Exception: clear call_record_statistics_unit_data error: {}", e.getMessage());
            throw new ClickSQLException("clear call_record_statistics_unit_data error");
        }

    }

    public void cleanExpireDayCallRecordData(String format) {
        try (ClickHouseConnection connection = dataSource.getConnection()) {
            PreparedStatement ps = connection.prepareStatement(CallRecordUnitSql.CLEAR_EXPIRE_UNIT_DATA);
            ps.setString(1, format);
            ps.execute();
        } catch (Exception e) {
            log.error("Exception: clear call_record_statistics_unit_data error: {}", e.getMessage());
            throw new ClickSQLException("clear call_record_statistics_unit_data error");
        }
    }


    public List<CallRecordStatisticUnitDataDto> findClassRatioByTaskIdList(List<Long> taskIds, LocalDate queryDate, String searchTable) {
        List<CallRecordStatisticUnitDataDto> callRecordStatisticUnitDataDtoList = new ArrayList<>();

        List<String> taskIdList = taskIds.stream().map(Object::toString).collect(Collectors.toList());
        String sql1 = "SELECT task_id, " +
                "  sum(total_connect_num)  as total_connect_num, " +
                "  sum(class_a_num)  as class_a_num, " +
                "  sum(class_b_num)  as class_b_num, " +
                "  sum(class_c_num)  as class_c_num, " +
                "  sum(class_d_num)  as class_d_num, " +
                "  sum(class_e_num)  as class_e_num, " +
                "  sum(class_f_num)  as class_f_num, " +
                "  sum(class_g_num)  as class_g_num, " +
                "  sum(class_other_num)  as class_other_num, " +
                "  sum(total_call_num)  as total_call_num " +
                " FROM %s  \n" +
                "WHERE\n" +
                "\t  task_id in ?  and statistic_date = ? group by task_id\n";
        String sql = String.format(sql1, searchTable);

        try (Connection connection = dataSource.getConnection()) {
            PreparedStatement statement = connection.prepareStatement(sql);
            statement.setArray(1, connection.createArrayOf("VARCHAR", taskIdList.toArray(new String[0])));
            statement.setString(2, queryDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));

            try (ResultSet resultSet = statement.executeQuery()) {
                while (resultSet.next()) {
                    CallRecordStatisticUnitDataDto callRecordStatisticUnitDataDto = new CallRecordStatisticUnitDataDto();

                    callRecordStatisticUnitDataDto.setTaskId(resultSet.getString("task_id"));
                    callRecordStatisticUnitDataDto.setClassANum(resultSet.getInt("class_a_num"));
                    callRecordStatisticUnitDataDto.setClassBNum(resultSet.getInt("class_b_num"));
                    callRecordStatisticUnitDataDto.setClassCNum(resultSet.getInt("class_c_num"));
                    callRecordStatisticUnitDataDto.setClassDNum(resultSet.getInt("class_d_num"));
                    callRecordStatisticUnitDataDto.setClassENum(resultSet.getInt("class_e_num"));
                    callRecordStatisticUnitDataDto.setClassFNum(resultSet.getInt("class_f_num"));
                    callRecordStatisticUnitDataDto.setClassGNum(resultSet.getInt("class_g_num"));
                    callRecordStatisticUnitDataDto.setClassOtherNum(resultSet.getInt("class_other_num"));
                    callRecordStatisticUnitDataDto.setTotalConnectNum(resultSet.getInt("total_connect_num"));
                    callRecordStatisticUnitDataDto.setTotalCallNum(resultSet.getInt("total_call_num"));
                    callRecordStatisticUnitDataDtoList.add(callRecordStatisticUnitDataDto);
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return callRecordStatisticUnitDataDtoList;

    }


    public List<CallRecordStatisticUnitDataDto> findCallDurationByTaskIdList(List<Long> taskIds,String queryDate, String searchTable) {
        List<CallRecordStatisticUnitDataDto> callRecordStatisticUnitDataDtoList = new ArrayList<>();

        List<String> taskIdList = taskIds.stream().map(Object::toString).collect(Collectors.toList());
        String sql1 = "SELECT task_id, " +
                "  sum(total_connect_num)  as total_connect_num, " +
                "  SUM(total_call_duration) as call_duration " +
                " FROM %s  \n" +
                "WHERE\n" +
                "\t  task_id in ? and statistic_date = ?  group by task_id\n";
        String sql = String.format(sql1, searchTable);

        try (Connection connection = dataSource.getConnection()) {
            PreparedStatement statement = connection.prepareStatement(sql);
            statement.setArray(1, connection.createArrayOf("VARCHAR", taskIdList.toArray(new String[0])));
            statement.setString(2, queryDate);
            try (ResultSet resultSet = statement.executeQuery()) {
                while (resultSet.next()) {
                    CallRecordStatisticUnitDataDto callRecordStatisticUnitDataDto = new CallRecordStatisticUnitDataDto();
                    callRecordStatisticUnitDataDto.setTaskId(resultSet.getString("task_id"));
                    callRecordStatisticUnitDataDto.setTotalConnectNum(resultSet.getInt("total_connect_num"));
                    callRecordStatisticUnitDataDto.setTotalCallDuration(resultSet.getLong("call_duration"));
                    callRecordStatisticUnitDataDtoList.add(callRecordStatisticUnitDataDto);
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return callRecordStatisticUnitDataDtoList;

    }
}