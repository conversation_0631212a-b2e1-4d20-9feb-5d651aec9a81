package com.raipeng.aimonitor.service.clickhouse;

import com.clickhouse.jdbc.ClickHouseConnection;
import com.clickhouse.jdbc.ClickHouseDataSource;
import com.raipeng.aimonitor.entity.ResultSetMapper;
import com.raipeng.aimonitor.exceptionadvice.exception.ClickSQLException;
import com.raipeng.aimonitor.model.CallLineMonitorDayData;
import com.raipeng.aimonitor.model.CallLineMonitorUnitData;
import com.raipeng.aimonitor.service.clickhouse.sql.CallLineMonitorDaySql;
import com.raipeng.aimonitor.service.clickhouse.sql.CallLineMonitorUnitSql;
import com.raipeng.aimonitor.utils.ClickHouseUtil;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;


@Slf4j
@Service
public class CallLineMonitorUnitClickService {

    @Autowired
    private ClickHouseDataSource dataSource;

    public void insertIntoCallLineMonitorUnitData(List<CallLineMonitorUnitData> unitDataList) {
        try (ClickHouseConnection connection = dataSource.getConnection()) {
            PreparedStatement ps = connection.prepareStatement(CallLineMonitorUnitSql.SAVE_UNIT_SQL);
            for (CallLineMonitorUnitData data : unitDataList) {
                ps.setInt(1, data.getTimeSlot());
                ps.setString(2, ClickHouseUtil.getStringOrEmpty(data.getTenantLineNumber()));
                ps.setString(3, ClickHouseUtil.getStringOrEmpty(data.getSupplyLineNumber()));
                ps.setInt(4, data.getTotalCallNum());
                ps.setInt(5, data.getTotalConnectNum());
                ps.setInt(6, data.getSilenceCallNum());
                ps.setInt(7, data.getOneSecondConnectedNum());
                ps.setInt(8, data.getTwoSecondConnectedNum());
                ps.setInt(9, data.getSilenceHangupNum());
                ps.setInt(10, data.getAssistantNum());
                ps.setInt(11, data.getPromptSoundNum());
                ps.setInt(12, data.getTransCallSeatNum());
                ps.setInt(13, data.getClassANum());
                ps.setInt(14, data.getClassBNum());
                ps.setInt(15, data.getClassCNum());
                ps.setInt(16, data.getClassDNum());
                ps.setInt(17, data.getCallFailedNum());
                ps.setInt(18, data.getRoutingFailNum());
                ps.setInt(19, data.getWaitSecond());
                ps.setInt(20, data.getCallDurationSecond());
                ps.setString(21, ClickHouseUtil.getStringOrEmpty(data.getProvince()));
                ps.setString(22, ClickHouseUtil.getStringOrEmpty(data.getCity()));
                ps.setString(23, ClickHouseUtil.getStringOrEmpty(data.getOperator()));
                ps.addBatch();
            }
            ps.executeBatch();
        } catch (Exception e) {
            log.error("Exception: insert into call line monitor unit data error:{}", e.getMessage());
            throw new ClickSQLException("insert into call line monitor unit data error");
        }
    }

    public List<CallLineMonitorUnitData> findRecentSuppliesData(List<Integer> timeSlots, Set<String> supplyLineNumbers) {
        try (ClickHouseConnection connection = dataSource.getConnection()) {
            PreparedStatement ps = connection.prepareStatement(CallLineMonitorUnitSql.FIND_RECENT_SUPPLIES_DATA_SQL);
            ps.setArray(1, connection.createArrayOf("Int32", timeSlots.toArray(new Integer[0])));
            ps.setArray(2, connection.createArrayOf("VARCHAR", supplyLineNumbers.toArray(new String[0])));
            try(ResultSet resultSet = ps.executeQuery()) {
                ResultSetMapper<CallLineMonitorUnitData> mapper = new ResultSetMapper<>(CallLineMonitorUnitData.class);
                return mapper.mapResultSetToObject(resultSet);
            }
        } catch (Exception e) {
            log.error("Exception: find past supplies data error:{}", e.getMessage());
        }
        return new ArrayList<>();
    }

    public List<CallLineMonitorUnitData> findPastSuppliesData(Set<String> supplyLineNumbers) {
        try (ClickHouseConnection connection = dataSource.getConnection()) {
            PreparedStatement ps = connection.prepareStatement(CallLineMonitorUnitSql.FIND_PAST_SUPPLIES_DATA_SQL);
            ps.setArray(1, connection.createArrayOf("VARCHAR", supplyLineNumbers.toArray(new String[0])));
            try(ResultSet resultSet = ps.executeQuery()) {
                ResultSetMapper<CallLineMonitorUnitData> mapper = new ResultSetMapper<>(CallLineMonitorUnitData.class);
                return mapper.mapResultSetToObject(resultSet);
            }
        } catch (Exception e) {
            log.error("Exception: find past supplies data error:{}", e.getMessage());
        }
        return new ArrayList<>();
    }

    public List<CallLineMonitorUnitData> findPastSuppliesDataYesterday(Set<String> supplyLineNumbers) {
        try (ClickHouseConnection connection = dataSource.getConnection()) {
            PreparedStatement ps = connection.prepareStatement(CallLineMonitorUnitSql.FIND_PAST_SUPPLIES_DATA_SQL_YESTERDAY);
            ps.setArray(1, connection.createArrayOf("VARCHAR", supplyLineNumbers.toArray(new String[0])));
            try(ResultSet resultSet = ps.executeQuery()) {
                ResultSetMapper<CallLineMonitorUnitData> mapper = new ResultSetMapper<>(CallLineMonitorUnitData.class);
                return mapper.mapResultSetToObject(resultSet);
            }
        } catch (Exception e) {
            log.error("Exception: find past supplies data error:{}", e.getMessage());
        }
        return new ArrayList<>();
    }

    public List<CallLineMonitorUnitData> findPastSuppliesDataBeforeYesterday(Set<String> supplyLineNumbers) {
        try (ClickHouseConnection connection = dataSource.getConnection()) {
            PreparedStatement ps = connection.prepareStatement(CallLineMonitorUnitSql.FIND_PAST_SUPPLIES_DATA_SQL_BEFORE_YESTERDAY);
            ps.setArray(1, connection.createArrayOf("VARCHAR", supplyLineNumbers.toArray(new String[0])));
            try(ResultSet resultSet = ps.executeQuery()) {
                ResultSetMapper<CallLineMonitorUnitData> mapper = new ResultSetMapper<>(CallLineMonitorUnitData.class);
                return mapper.mapResultSetToObject(resultSet);
            }
        } catch (Exception e) {
            log.error("Exception: find past supplies data error:{}", e.getMessage());
        }
        return new ArrayList<>();
    }

    public List<CallLineMonitorUnitData> findRecentSupplyData(List<Integer> timeSlots, Set<String> tenantLineNumbers, String supplyLineNumber) {
        try (ClickHouseConnection connection = dataSource.getConnection()) {
            PreparedStatement ps = connection.prepareStatement(CallLineMonitorUnitSql.FIND_RECENT_SUPPLY_DATA_SQL);
            ps.setArray(1, connection.createArrayOf("Int32", timeSlots.toArray(new Integer[0])));
            ps.setArray(2, connection.createArrayOf("VARCHAR", tenantLineNumbers.toArray(new String[0])));
            ps.setString(3, supplyLineNumber);
            try(ResultSet resultSet = ps.executeQuery()) {
                ResultSetMapper<CallLineMonitorUnitData> mapper = new ResultSetMapper<>(CallLineMonitorUnitData.class);
                return mapper.mapResultSetToObject(resultSet);
            }
        } catch (Exception e) {
            log.error("Exception: find past supply data error: {}", e.getMessage());
        }
        return new ArrayList<>();
    }

    public List<CallLineMonitorUnitData> findPastSupplyData(Set<String> tenantLineNumbers, String supplyLineNumber) {
        try (ClickHouseConnection connection = dataSource.getConnection()) {
            PreparedStatement ps = connection.prepareStatement(CallLineMonitorUnitSql.FIND_PAST_SUPPLY_DATA_SQL);
            ps.setArray(1, connection.createArrayOf("VARCHAR", tenantLineNumbers.toArray(new String[0])));
            ps.setString(2, supplyLineNumber);
            try(ResultSet resultSet = ps.executeQuery()) {
                ResultSetMapper<CallLineMonitorUnitData> mapper = new ResultSetMapper<>(CallLineMonitorUnitData.class);
                return mapper.mapResultSetToObject(resultSet);
            }
        } catch (Exception e) {
            log.error("Exception: find past supply data error: {}", e.getMessage());
        }
        return new ArrayList<>();
    }

    public List<CallLineMonitorUnitData> findPastSupplyDataYesterday(Set<String> tenantLineNumbers, String supplyLineNumber) {
        try (ClickHouseConnection connection = dataSource.getConnection()) {
            PreparedStatement ps = connection.prepareStatement(CallLineMonitorUnitSql.FIND_PAST_SUPPLY_DATA_SQL_YESTERDAY);
            ps.setArray(1, connection.createArrayOf("VARCHAR", tenantLineNumbers.toArray(new String[0])));
            ps.setString(2, supplyLineNumber);
            try(ResultSet resultSet = ps.executeQuery()) {
                ResultSetMapper<CallLineMonitorUnitData> mapper = new ResultSetMapper<>(CallLineMonitorUnitData.class);
                return mapper.mapResultSetToObject(resultSet);
            }
        } catch (Exception e) {
            log.error("Exception: find past supply data yesterday error: {}", e.getMessage());
        }
        return new ArrayList<>();
    }

    public List<CallLineMonitorUnitData> findPastSupplyDataBeforeYesterday(Set<String> tenantLineNumbers, String supplyLineNumber) {
        try (ClickHouseConnection connection = dataSource.getConnection()) {
            PreparedStatement ps = connection.prepareStatement(CallLineMonitorUnitSql.FIND_PAST_SUPPLY_DATA_SQL_BEFORE_YESTERDAY);
            ps.setArray(1, connection.createArrayOf("VARCHAR", tenantLineNumbers.toArray(new String[0])));
            ps.setString(2, supplyLineNumber);
            try(ResultSet resultSet = ps.executeQuery()) {
                ResultSetMapper<CallLineMonitorUnitData> mapper = new ResultSetMapper<>(CallLineMonitorUnitData.class);
                return mapper.mapResultSetToObject(resultSet);
            }
        } catch (Exception e) {
            log.error("Exception: find past supply data yesterday error: {}", e.getMessage());
        }
        return new ArrayList<>();
    }

    public List<CallLineMonitorUnitData> findRecentTenantsData(List<Integer> timeSlots, Set<String> tenantLineNumbers) {
        try (ClickHouseConnection connection = dataSource.getConnection()) {
            PreparedStatement ps = connection.prepareStatement(CallLineMonitorUnitSql.FIND_RECENT_TENANTS_DATA_SQL);
            ps.setArray(1, connection.createArrayOf("Int32", timeSlots.toArray(new Integer[0])));
            ps.setArray(2, connection.createArrayOf("VARCHAR", tenantLineNumbers.toArray(new String[0])));
            try(ResultSet resultSet = ps.executeQuery()) {
                ResultSetMapper<CallLineMonitorUnitData> mapper = new ResultSetMapper<>(CallLineMonitorUnitData.class);
                return mapper.mapResultSetToObject(resultSet);
            }
        } catch (Exception e) {
            log.error("Exception: find past tenants data error: {}", e.getMessage());
        }
        return new ArrayList<>();
    }

    public List<CallLineMonitorUnitData> findPastTenantsData(Set<String> tenantLineNumbers) {
        try (ClickHouseConnection connection = dataSource.getConnection()) {
            PreparedStatement ps = connection.prepareStatement(CallLineMonitorUnitSql.FIND_PAST_TENANTS_DATA_SQL);
            ps.setArray(1, connection.createArrayOf("VARCHAR", tenantLineNumbers.toArray(new String[0])));
            try(ResultSet resultSet = ps.executeQuery()) {
                ResultSetMapper<CallLineMonitorUnitData> mapper = new ResultSetMapper<>(CallLineMonitorUnitData.class);
                return mapper.mapResultSetToObject(resultSet);
            }
        } catch (Exception e) {
            log.error("Exception: find past tenants data error: {}", e.getMessage());
        }
        return new ArrayList<>();
    }

    public List<CallLineMonitorUnitData> findPastTenantsDataYesterday(Set<String> tenantLineNumbers) {
        try (ClickHouseConnection connection = dataSource.getConnection()) {
            PreparedStatement ps = connection.prepareStatement(CallLineMonitorUnitSql.FIND_PAST_TENANTS_DATA_SQL_YESTERDAY);
            ps.setArray(1, connection.createArrayOf("VARCHAR", tenantLineNumbers.toArray(new String[0])));
            try(ResultSet resultSet = ps.executeQuery()) {
                ResultSetMapper<CallLineMonitorUnitData> mapper = new ResultSetMapper<>(CallLineMonitorUnitData.class);
                return mapper.mapResultSetToObject(resultSet);
            }
        } catch (Exception e) {
            log.error("Exception: find past tenants data error: {}", e.getMessage());
        }
        return new ArrayList<>();
    }

    public List<CallLineMonitorUnitData> findPastTenantsDataBeforeYesterday(Set<String> tenantLineNumbers) {
        try (ClickHouseConnection connection = dataSource.getConnection()) {
            PreparedStatement ps = connection.prepareStatement(CallLineMonitorUnitSql.FIND_PAST_TENANTS_DATA_SQL_BEFORE_YESTERDAY);
            ps.setArray(1, connection.createArrayOf("VARCHAR", tenantLineNumbers.toArray(new String[0])));
            try(ResultSet resultSet = ps.executeQuery()) {
                ResultSetMapper<CallLineMonitorUnitData> mapper = new ResultSetMapper<>(CallLineMonitorUnitData.class);
                return mapper.mapResultSetToObject(resultSet);
            }
        } catch (Exception e) {
            log.error("Exception: find past tenants data error: {}", e.getMessage());
        }
        return new ArrayList<>();
    }

    public List<CallLineMonitorUnitData> findRecentTenantData(List<Integer> timeSlots, String tenantLineNumber, Set<String> supplyNumbers) {
        try (ClickHouseConnection connection = dataSource.getConnection()) {
            PreparedStatement ps = connection.prepareStatement(CallLineMonitorUnitSql.FIND_RECENT_TENANT_DATA_SQL);
            ps.setArray(1, connection.createArrayOf("Int32", timeSlots.toArray(new Integer[0])));
            ps.setString(2, tenantLineNumber);
            ps.setArray(3, connection.createArrayOf("VARCHAR", supplyNumbers.toArray(new String[0])));
            try(ResultSet resultSet = ps.executeQuery()) {
                ResultSetMapper<CallLineMonitorUnitData> mapper = new ResultSetMapper<>(CallLineMonitorUnitData.class);
                return mapper.mapResultSetToObject(resultSet);
            }
        } catch (Exception e) {
            log.error("Exception: find past tenant data error: {}", e.getMessage());
        }
        return new ArrayList<>();
    }

    public List<CallLineMonitorUnitData> findPastTenantData(String tenantLineNumber, Set<String> supplyNumbers) {
        try (ClickHouseConnection connection = dataSource.getConnection()) {
            PreparedStatement ps = connection.prepareStatement(CallLineMonitorUnitSql.FIND_PAST_TENANT_DATA_SQL);
            ps.setString(1, tenantLineNumber);
            ps.setArray(2, connection.createArrayOf("VARCHAR", supplyNumbers.toArray(new String[0])));
            try(ResultSet resultSet = ps.executeQuery()) {
                ResultSetMapper<CallLineMonitorUnitData> mapper = new ResultSetMapper<>(CallLineMonitorUnitData.class);
                return mapper.mapResultSetToObject(resultSet);
            }
        } catch (Exception e) {
            log.error("Exception: find past tenant data error: {}", e.getMessage());
        }
        return new ArrayList<>();
    }

    public List<CallLineMonitorUnitData> findPastTenantDataYesterday(String tenantLineNumber, Set<String> supplyNumbers) {
        try (ClickHouseConnection connection = dataSource.getConnection()) {
            PreparedStatement ps = connection.prepareStatement(CallLineMonitorUnitSql.FIND_PAST_TENANT_DATA_SQL_YESTERDAY);
            ps.setString(1, tenantLineNumber);
            ps.setArray(2, connection.createArrayOf("VARCHAR", supplyNumbers.toArray(new String[0])));
            try(ResultSet resultSet = ps.executeQuery()) {
                ResultSetMapper<CallLineMonitorUnitData> mapper = new ResultSetMapper<>(CallLineMonitorUnitData.class);
                return mapper.mapResultSetToObject(resultSet);
            }
        } catch (Exception e) {
            log.error("Exception: find past tenant data error: {}", e.getMessage());
        }
        return new ArrayList<>();
    }

    public List<CallLineMonitorUnitData> findPastTenantDataBeforeYesterday(String tenantLineNumber, Set<String> supplyNumbers) {
        try (ClickHouseConnection connection = dataSource.getConnection()) {
            PreparedStatement ps = connection.prepareStatement(CallLineMonitorUnitSql.FIND_PAST_TENANT_DATA_SQL_BEFORE_YESTERDAY);
            ps.setString(1, tenantLineNumber);
            ps.setArray(2, connection.createArrayOf("VARCHAR", supplyNumbers.toArray(new String[0])));
            try(ResultSet resultSet = ps.executeQuery()) {
                ResultSetMapper<CallLineMonitorUnitData> mapper = new ResultSetMapper<>(CallLineMonitorUnitData.class);
                return mapper.mapResultSetToObject(resultSet);
            }
        } catch (Exception e) {
            log.error("Exception: find past tenant data error: {}", e.getMessage());
        }
        return new ArrayList<>();
    }

    public List<CallLineMonitorUnitData> getRateChartTodayData(String tenantLineNumber, String supplyLineNumber, List<String> operators) {
        try {
            ClickHouseConnection connection = dataSource.getConnection();
            PreparedStatement ps = connection.prepareStatement(CallLineMonitorUnitSql.GET_RATE_CHART_TODAY_DATA_SQL);
            ps.setString(1, tenantLineNumber);
            ps.setString(2, tenantLineNumber);
            ps.setString(3, supplyLineNumber);
            ps.setString(4, supplyLineNumber);
            ps.setArray(5, connection.createArrayOf("VARCHAR", operators.toArray(new String[0])));
            try(ResultSet resultSet = ps.executeQuery()) {
                ResultSetMapper<CallLineMonitorUnitData> mapper = new ResultSetMapper<>(CallLineMonitorUnitData.class);
                return mapper.mapResultSetToObject(resultSet);
            }
        } catch (Exception e) {
            log.error("Exception: get rate chart today data error: {}", e.getMessage());
        }
        return new ArrayList<>();
    }

    public List<CallLineMonitorUnitData> getCountryChartTodayData(List<Integer> searchMomentList, String tenantLineNumber, String supplyLineNumber, List<String> operators) {
        try {
            ClickHouseConnection connection = dataSource.getConnection();
            PreparedStatement ps = connection.prepareStatement(CallLineMonitorUnitSql.GET_COUNTRY_CHART_TODAY_DATA_SQL);
            ps.setArray(1, connection.createArrayOf("Int32", searchMomentList.toArray(new Integer[0])));
            ps.setString(2, tenantLineNumber);
            ps.setString(3, tenantLineNumber);
            ps.setString(4, supplyLineNumber);
            ps.setString(5, supplyLineNumber);
            ps.setArray(6, connection.createArrayOf("VARCHAR", operators.toArray(new String[0])));
            try(ResultSet resultSet = ps.executeQuery()) {
                ResultSetMapper<CallLineMonitorUnitData> mapper = new ResultSetMapper<>(CallLineMonitorUnitData.class);
                return mapper.mapResultSetToObject(resultSet);
            }
        } catch (Exception e) {
            log.error("Exception: get country chart today data error: {}", e.getMessage());
        }
        return new ArrayList<>();
    }

    public List<CallLineMonitorUnitData> getProvinceChartTodayData(List<Integer> searchMomentList, String tenantLineNumber, String supplyLineNumber, List<String> operators, String province) {
        try (ClickHouseConnection connection = dataSource.getConnection()) {
            PreparedStatement ps = connection.prepareStatement(CallLineMonitorUnitSql.GET_PROVINCE_CHART_TODAY_DATA_SQL);
            ps.setArray(1, connection.createArrayOf("Int32", searchMomentList.toArray(new Integer[0])));
            ps.setString(2, tenantLineNumber);
            ps.setString(3, tenantLineNumber);
            ps.setString(4, supplyLineNumber);
            ps.setString(5, supplyLineNumber);
            ps.setArray(6, connection.createArrayOf("VARCHAR", operators.toArray(new String[0])));
            ps.setString(7, province);
            try(ResultSet resultSet = ps.executeQuery()) {
                ResultSetMapper<CallLineMonitorUnitData> mapper = new ResultSetMapper<>(CallLineMonitorUnitData.class);
                return mapper.mapResultSetToObject(resultSet);
            }
        } catch (Exception e) {
            log.error("Exception: get province chart today data error: {}", e.getMessage());
        }
        return new ArrayList<>();
    }

    public List<CallLineMonitorUnitData> getRateChartYesterdayData(String tenantLineNumber, String supplyLineNumber, List<String> operators) {
        try {
            ClickHouseConnection connection = dataSource.getConnection();
            PreparedStatement ps = connection.prepareStatement(CallLineMonitorUnitSql.GET_RATE_CHART_YESTERDAY_DATA_SQL);
            ps.setString(1, tenantLineNumber);
            ps.setString(2, tenantLineNumber);
            ps.setString(3, supplyLineNumber);
            ps.setString(4, supplyLineNumber);
            ps.setArray(5, connection.createArrayOf("VARCHAR", operators.toArray(new String[0])));
            try(ResultSet resultSet = ps.executeQuery()) {
                ResultSetMapper<CallLineMonitorUnitData> mapper = new ResultSetMapper<>(CallLineMonitorUnitData.class);
                return mapper.mapResultSetToObject(resultSet);
            }
        } catch (Exception e) {
            log.error("Exception: get rate chart today data error: {}", e.getMessage());
        }
        return new ArrayList<>();
    }

    public List<CallLineMonitorUnitData> getCountryChartYesterdayData(List<Integer> searchMomentList, String tenantLineNumber, String supplyLineNumber, List<String> operators) {
        try {
            ClickHouseConnection connection = dataSource.getConnection();
            PreparedStatement ps = connection.prepareStatement(CallLineMonitorUnitSql.GET_COUNTRY_CHART_YESTERDAY_DATA_SQL);
            ps.setArray(1, connection.createArrayOf("Int32", searchMomentList.toArray(new Integer[0])));
            ps.setString(2, tenantLineNumber);
            ps.setString(3, tenantLineNumber);
            ps.setString(4, supplyLineNumber);
            ps.setString(5, supplyLineNumber);
            ps.setArray(6, connection.createArrayOf("VARCHAR", operators.toArray(new String[0])));
            try(ResultSet resultSet = ps.executeQuery()) {
                ResultSetMapper<CallLineMonitorUnitData> mapper = new ResultSetMapper<>(CallLineMonitorUnitData.class);
                return mapper.mapResultSetToObject(resultSet);
            }
        } catch (Exception e) {
            log.error("Exception: get country chart today data error: {}", e.getMessage());
        }
        return new ArrayList<>();
    }

    public List<CallLineMonitorUnitData> getProvinceChartYesterdayData(List<Integer> searchMomentList, String tenantLineNumber, String supplyLineNumber, List<String> operators, String province) {
        try (ClickHouseConnection connection = dataSource.getConnection()) {
            PreparedStatement ps = connection.prepareStatement(CallLineMonitorUnitSql.GET_PROVINCE_CHART_YESTERDAY_DATA_SQL);
            ps.setArray(1, connection.createArrayOf("Int32", searchMomentList.toArray(new Integer[0])));
            ps.setString(2, tenantLineNumber);
            ps.setString(3, tenantLineNumber);
            ps.setString(4, supplyLineNumber);
            ps.setString(5, supplyLineNumber);
            ps.setArray(6, connection.createArrayOf("VARCHAR", operators.toArray(new String[0])));
            ps.setString(7, province);
            try(ResultSet resultSet = ps.executeQuery()) {
                ResultSetMapper<CallLineMonitorUnitData> mapper = new ResultSetMapper<>(CallLineMonitorUnitData.class);
                return mapper.mapResultSetToObject(resultSet);
            }
        } catch (Exception e) {
            log.error("Exception: get province chart today data error: {}", e.getMessage());
        }
        return new ArrayList<>();
    }

    public List<CallLineMonitorUnitData> getRateChartBeforeYesterdayData(String tenantLineNumber, String supplyLineNumber, List<String> operators) {
        try {
            ClickHouseConnection connection = dataSource.getConnection();
            PreparedStatement ps = connection.prepareStatement(CallLineMonitorUnitSql.GET_RATE_CHART_BEFORE_YESTERDAY_DATA_SQL);
            ps.setString(1, tenantLineNumber);
            ps.setString(2, tenantLineNumber);
            ps.setString(3, supplyLineNumber);
            ps.setString(4, supplyLineNumber);
            ps.setArray(5, connection.createArrayOf("VARCHAR", operators.toArray(new String[0])));
            try(ResultSet resultSet = ps.executeQuery()) {
                ResultSetMapper<CallLineMonitorUnitData> mapper = new ResultSetMapper<>(CallLineMonitorUnitData.class);
                return mapper.mapResultSetToObject(resultSet);
            }
        } catch (Exception e) {
            log.error("Exception: get rate chart today data error: {}", e.getMessage());
        }
        return new ArrayList<>();
    }

    public List<CallLineMonitorUnitData> getCountryChartBeforeYesterdayData(List<Integer> searchMomentList, String tenantLineNumber, String supplyLineNumber, List<String> operators) {
        try {
            ClickHouseConnection connection = dataSource.getConnection();
            PreparedStatement ps = connection.prepareStatement(CallLineMonitorUnitSql.GET_COUNTRY_CHART_BEFORE_YESTERDAY_DATA_SQL);
            ps.setArray(1, connection.createArrayOf("Int32", searchMomentList.toArray(new Integer[0])));
            ps.setString(2, tenantLineNumber);
            ps.setString(3, tenantLineNumber);
            ps.setString(4, supplyLineNumber);
            ps.setString(5, supplyLineNumber);
            ps.setArray(6, connection.createArrayOf("VARCHAR", operators.toArray(new String[0])));
            try(ResultSet resultSet = ps.executeQuery()) {
                ResultSetMapper<CallLineMonitorUnitData> mapper = new ResultSetMapper<>(CallLineMonitorUnitData.class);
                return mapper.mapResultSetToObject(resultSet);
            }
        } catch (Exception e) {
            log.error("Exception: get country chart today data error: {}", e.getMessage());
        }
        return new ArrayList<>();
    }

    public List<CallLineMonitorUnitData> getProvinceChartBeforeYesterdayData(List<Integer> searchMomentList, String tenantLineNumber, String supplyLineNumber, List<String> operators, String province) {
        try (ClickHouseConnection connection = dataSource.getConnection()) {
            PreparedStatement ps = connection.prepareStatement(CallLineMonitorUnitSql.GET_PROVINCE_CHART_BEFORE_YESTERDAY_DATA_SQL);
            ps.setArray(1, connection.createArrayOf("Int32", searchMomentList.toArray(new Integer[0])));
            ps.setString(2, tenantLineNumber);
            ps.setString(3, tenantLineNumber);
            ps.setString(4, supplyLineNumber);
            ps.setString(5, supplyLineNumber);
            ps.setArray(6, connection.createArrayOf("VARCHAR", operators.toArray(new String[0])));
            ps.setString(7, province);
            try(ResultSet resultSet = ps.executeQuery()) {
                ResultSetMapper<CallLineMonitorUnitData> mapper = new ResultSetMapper<>(CallLineMonitorUnitData.class);
                return mapper.mapResultSetToObject(resultSet);
            }
        } catch (Exception e) {
            log.error("Exception: get province chart today data error: {}", e.getMessage());
        }
        return new ArrayList<>();
    }

    public boolean existsByTimeSlot(int timeSlot) {
        try (ClickHouseConnection connection = dataSource.getConnection()) {
            PreparedStatement ps = connection.prepareStatement(CallLineMonitorUnitSql.EXISTS_BY_TIME_SLOT);
            ps.setInt(1, timeSlot);
            try(ResultSet resultSet = ps.executeQuery()) {
                if (resultSet.next()) {
                    return resultSet.getInt(1) == 1;
                }
            }
        } catch (Exception e) {
            log.error("Exception: exists by time slot error: {}", e.getMessage());
        }
        return false;
    }

    public void clearBeforeYesterdayUnitData() {
        try (ClickHouseConnection connection = dataSource.getConnection()) {
            PreparedStatement ps = connection.prepareStatement(CallLineMonitorUnitSql.CLEAR_BEFORE_YESTERDAY_UNIT_DATA);
            ps.execute();
        } catch (Exception e) {
            log.error("Exception: clear before yesterday data error: {}", e.getMessage());
            throw new ClickSQLException("clear before yesterday data error");
        }
    }

    public void renameTableBeforeYesterday() {
        try (ClickHouseConnection connection = dataSource.getConnection()) {
            PreparedStatement ps = connection.prepareStatement(CallLineMonitorUnitSql.RENAME_TABLE_BEFORE_YESTERDAY);
            ps.execute();
        } catch (Exception e) {
            log.error("Exception: rename table before yesterday error: {}", e.getMessage());
            throw new ClickSQLException("rename table before yesterday error");
        }
    }

    public void renameTableYesterday() {
        try (ClickHouseConnection connection = dataSource.getConnection()) {
            PreparedStatement ps = connection.prepareStatement(CallLineMonitorUnitSql.RENAME_TABLE_YESTERDAY);
            ps.execute();
        } catch (Exception e) {
            log.error("Exception: rename table yesterday error: {}", e.getMessage());
            throw new ClickSQLException("rename table yesterday error");
        }
    }

    public void renameTableToday() {
        try (ClickHouseConnection connection = dataSource.getConnection()) {
            PreparedStatement ps = connection.prepareStatement(CallLineMonitorUnitSql.RENAME_TABLE_TODAY);
            ps.execute();
        } catch (Exception e) {
            log.error("Exception: rename table today error: {}", e.getMessage());
            throw new ClickSQLException("rename table today error");
        }
    }

    public void renameTableTemp() {
        try (ClickHouseConnection connection = dataSource.getConnection()) {
            PreparedStatement ps = connection.prepareStatement(CallLineMonitorUnitSql.RENAME_TABLE_TEMP);
            ps.execute();
        } catch (Exception e) {
            log.error("Exception: rename table temp error: {}", e.getMessage());
            throw new ClickSQLException("rename table temp error");
        }
    }
}