package com.raipeng.aimonitor.service.clickhouse.sql;

public class CallLineMonitorUnitSql {
    public static final String SAVE_UNIT_SQL = "INSERT INTO aiSpeech.call_line_monitor_unit_data " +
            "(time_slot, " +
            "tenant_line_number, " +
            "supply_line_number, " +
            "total_call_num, " +
            "total_connect_num, " +
            "silence_call_num, " +
            "one_second_connected_num, " +
            "two_second_connected_num,  " +
            "silence_hangup_num, " +
            "assistant_num, " +
            "prompt_sound_num, " +
            "trans_call_seat_num, " +
            "class_a_num, " +
            "class_b_num, " +
            "class_c_num, " +
            "class_d_num, " +
            "call_failed_num, " +
            "routing_fail_num, " +
            "wait_second, " +
            "call_duration_second, " +
            "province, " +
            "city, " +
            "operator) " +
            "VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? );";

    public static final String FIND_RECENT_SUPPLIES_DATA_SQL = "SELECT " +
            "SUM(t.total_call_num) AS total_call_num, " +
            "SUM(t.total_connect_num) AS total_connect_num, " +
            "SUM(t.silence_call_num) AS silence_call_num, " +
            "SUM(t.one_second_connected_num) AS one_second_connected_num, " +
            "SUM(t.two_second_connected_num) AS two_second_connected_num, " +
            "SUM(t.silence_hangup_num) AS silence_hangup_num, " +
            "SUM(t.assistant_num) AS assistant_num, " +
            "SUM(t.prompt_sound_num) AS prompt_sound_num, " +
            "SUM(t.trans_call_seat_num) AS trans_call_seat_num, " +
            "SUM(t.class_a_num) AS class_a_num, " +
            "SUM(t.class_b_num) AS class_b_num, " +
            "SUM(t.class_c_num) AS class_c_num, " +
            "SUM(t.class_d_num) AS class_d_num, " +
            "SUM(t.call_failed_num) AS call_failed_num, " +
            "SUM(t.wait_second) AS wait_second, " +
            "SUM(t.call_duration_second) AS call_duration_second, " +
            "t.supply_line_number as supply_line_number " +
            "FROM aiSpeech.call_line_monitor_unit_data t " +
            "WHERE t.time_slot in ? " +
            "AND t.supply_line_number in ? " +
            "GROUP BY t.supply_line_number";

    public static final String FIND_PAST_SUPPLIES_DATA_SQL = "SELECT " +
            "SUM(t.total_call_num) AS total_call_num, " +
            "SUM(t.total_connect_num) AS total_connect_num, " +
            "SUM(t.silence_call_num) AS silence_call_num, " +
            "SUM(t.one_second_connected_num) AS one_second_connected_num, " +
            "SUM(t.two_second_connected_num) AS two_second_connected_num, " +
            "SUM(t.silence_hangup_num) AS silence_hangup_num, " +
            "SUM(t.assistant_num) AS assistant_num, " +
            "SUM(t.prompt_sound_num) AS prompt_sound_num, " +
            "SUM(t.trans_call_seat_num) AS trans_call_seat_num, " +
            "SUM(t.class_a_num) AS class_a_num, " +
            "SUM(t.class_b_num) AS class_b_num, " +
            "SUM(t.class_c_num) AS class_c_num, " +
            "SUM(t.class_d_num) AS class_d_num, " +
            "SUM(t.call_failed_num) AS call_failed_num, " +
            "SUM(t.wait_second) AS wait_second, " +
            "SUM(t.call_duration_second) AS call_duration_second, " +
            "t.supply_line_number as supply_line_number " +
            "FROM aiSpeech.call_line_monitor_unit_data t " +
            "WHERE t.supply_line_number in ? " +
            "GROUP BY t.supply_line_number";

    public static final String FIND_PAST_SUPPLIES_DATA_SQL_YESTERDAY = "SELECT " +
            "SUM(t.total_call_num) AS total_call_num, " +
            "SUM(t.total_connect_num) AS total_connect_num, " +
            "SUM(t.silence_call_num) AS silence_call_num, " +
            "SUM(t.one_second_connected_num) AS one_second_connected_num, " +
            "SUM(t.two_second_connected_num) AS two_second_connected_num, " +
            "SUM(t.silence_hangup_num) AS silence_hangup_num, " +
            "SUM(t.assistant_num) AS assistant_num, " +
            "SUM(t.prompt_sound_num) AS prompt_sound_num, " +
            "SUM(t.trans_call_seat_num) AS trans_call_seat_num, " +
            "SUM(t.class_a_num) AS class_a_num, " +
            "SUM(t.class_b_num) AS class_b_num, " +
            "SUM(t.class_c_num) AS class_c_num, " +
            "SUM(t.class_d_num) AS class_d_num, " +
            "SUM(t.call_failed_num) AS call_failed_num, " +
            "SUM(t.wait_second) AS wait_second, " +
            "SUM(t.call_duration_second) AS call_duration_second, " +
            "t.supply_line_number as supply_line_number " +
            "FROM aiSpeech.call_line_monitor_unit_data_yesterday t " +
            "WHERE t.supply_line_number in ? " +
            "GROUP BY t.supply_line_number";

    public static final String FIND_PAST_SUPPLIES_DATA_SQL_BEFORE_YESTERDAY = "SELECT " +
            "SUM(t.total_call_num) AS total_call_num, " +
            "SUM(t.total_connect_num) AS total_connect_num, " +
            "SUM(t.silence_call_num) AS silence_call_num, " +
            "SUM(t.one_second_connected_num) AS one_second_connected_num, " +
            "SUM(t.two_second_connected_num) AS two_second_connected_num, " +
            "SUM(t.silence_hangup_num) AS silence_hangup_num, " +
            "SUM(t.assistant_num) AS assistant_num, " +
            "SUM(t.prompt_sound_num) AS prompt_sound_num, " +
            "SUM(t.trans_call_seat_num) AS trans_call_seat_num, " +
            "SUM(t.class_a_num) AS class_a_num, " +
            "SUM(t.class_b_num) AS class_b_num, " +
            "SUM(t.class_c_num) AS class_c_num, " +
            "SUM(t.class_d_num) AS class_d_num, " +
            "SUM(t.call_failed_num) AS call_failed_num, " +
            "SUM(t.wait_second) AS wait_second, " +
            "SUM(t.call_duration_second) AS call_duration_second, " +
            "t.supply_line_number as supply_line_number " +
            "FROM aiSpeech.call_line_monitor_unit_data_before_yesterday t " +
            "WHERE t.supply_line_number in ? " +
            "GROUP BY t.supply_line_number";

    public static final String FIND_RECENT_SUPPLY_DATA_SQL = "SELECT " +
            "SUM(t.total_call_num) AS total_call_num, " +
            "SUM(t.total_connect_num) AS total_connect_num, " +
            "SUM(t.silence_call_num) AS silence_call_num, " +
            "SUM(t.one_second_connected_num) AS one_second_connected_num, " +
            "SUM(t.two_second_connected_num) AS two_second_connected_num, " +
            "SUM(t.silence_hangup_num) AS silence_hangup_num, " +
            "SUM(t.assistant_num) AS assistant_num, " +
            "SUM(t.prompt_sound_num) AS prompt_sound_num, " +
            "SUM(t.trans_call_seat_num) AS trans_call_seat_num, " +
            "SUM(t.class_a_num) AS class_a_num, " +
            "SUM(t.class_b_num) AS class_b_num, " +
            "SUM(t.class_c_num) AS class_c_num, " +
            "SUM(t.class_d_num) AS class_d_num, " +
            "SUM(t.call_failed_num) AS call_failed_num, " +
            "SUM(t.wait_second) AS wait_second, " +
            "SUM(t.call_duration_second) AS call_duration_second, " +
            "t.tenant_line_number as tenant_line_number, " +
            "t.supply_line_number as supply_line_number " +
            "FROM aiSpeech.call_line_monitor_unit_data t " +
            "WHERE t.time_slot in ? " +
            "AND t.tenant_line_number in ? " +
            "AND t.supply_line_number = ? " +
            "GROUP BY t.tenant_line_number, t.supply_line_number";

    public static final String FIND_PAST_SUPPLY_DATA_SQL = "SELECT " +
            "SUM(t.total_call_num) AS total_call_num, " +
            "SUM(t.total_connect_num) AS total_connect_num, " +
            "SUM(t.silence_call_num) AS silence_call_num, " +
            "SUM(t.one_second_connected_num) AS one_second_connected_num, " +
            "SUM(t.two_second_connected_num) AS two_second_connected_num, " +
            "SUM(t.silence_hangup_num) AS silence_hangup_num, " +
            "SUM(t.assistant_num) AS assistant_num, " +
            "SUM(t.prompt_sound_num) AS prompt_sound_num, " +
            "SUM(t.trans_call_seat_num) AS trans_call_seat_num, " +
            "SUM(t.class_a_num) AS class_a_num, " +
            "SUM(t.class_b_num) AS class_b_num, " +
            "SUM(t.class_c_num) AS class_c_num, " +
            "SUM(t.class_d_num) AS class_d_num, " +
            "SUM(t.call_failed_num) AS call_failed_num, " +
            "SUM(t.wait_second) AS wait_second, " +
            "SUM(t.call_duration_second) AS call_duration_second, " +
            "t.tenant_line_number as tenant_line_number, " +
            "t.supply_line_number as supply_line_number " +
            "FROM aiSpeech.call_line_monitor_unit_data t " +
            "WHERE t.tenant_line_number in ? " +
            "AND t.supply_line_number = ? " +
            "GROUP BY t.tenant_line_number, t.supply_line_number";

    public static final String FIND_PAST_SUPPLY_DATA_SQL_YESTERDAY = "SELECT " +
            "SUM(t.total_call_num) AS total_call_num, " +
            "SUM(t.total_connect_num) AS total_connect_num, " +
            "SUM(t.silence_call_num) AS silence_call_num, " +
            "SUM(t.one_second_connected_num) AS one_second_connected_num, " +
            "SUM(t.two_second_connected_num) AS two_second_connected_num, " +
            "SUM(t.silence_hangup_num) AS silence_hangup_num, " +
            "SUM(t.assistant_num) AS assistant_num, " +
            "SUM(t.prompt_sound_num) AS prompt_sound_num, " +
            "SUM(t.trans_call_seat_num) AS trans_call_seat_num, " +
            "SUM(t.class_a_num) AS class_a_num, " +
            "SUM(t.class_b_num) AS class_b_num, " +
            "SUM(t.class_c_num) AS class_c_num, " +
            "SUM(t.class_d_num) AS class_d_num, " +
            "SUM(t.call_failed_num) AS call_failed_num, " +
            "SUM(t.wait_second) AS wait_second, " +
            "SUM(t.call_duration_second) AS call_duration_second, " +
            "t.tenant_line_number as tenant_line_number, " +
            "t.supply_line_number as supply_line_number " +
            "FROM aiSpeech.call_line_monitor_unit_data_yesterday t " +
            "WHERE t.tenant_line_number in ? " +
            "AND t.supply_line_number = ? " +
            "GROUP BY t.tenant_line_number, t.supply_line_number";

    public static final String FIND_PAST_SUPPLY_DATA_SQL_BEFORE_YESTERDAY = "SELECT " +
            "SUM(t.total_call_num) AS total_call_num, " +
            "SUM(t.total_connect_num) AS total_connect_num, " +
            "SUM(t.silence_call_num) AS silence_call_num, " +
            "SUM(t.one_second_connected_num) AS one_second_connected_num, " +
            "SUM(t.two_second_connected_num) AS two_second_connected_num, " +
            "SUM(t.silence_hangup_num) AS silence_hangup_num, " +
            "SUM(t.assistant_num) AS assistant_num, " +
            "SUM(t.prompt_sound_num) AS prompt_sound_num, " +
            "SUM(t.trans_call_seat_num) AS trans_call_seat_num, " +
            "SUM(t.class_a_num) AS class_a_num, " +
            "SUM(t.class_b_num) AS class_b_num, " +
            "SUM(t.class_c_num) AS class_c_num, " +
            "SUM(t.class_d_num) AS class_d_num, " +
            "SUM(t.call_failed_num) AS call_failed_num, " +
            "SUM(t.wait_second) AS wait_second, " +
            "SUM(t.call_duration_second) AS call_duration_second, " +
            "t.tenant_line_number as tenant_line_number, " +
            "t.supply_line_number as supply_line_number " +
            "FROM aiSpeech.call_line_monitor_unit_data_before_yesterday t " +
            "WHERE t.tenant_line_number in ? " +
            "AND t.supply_line_number = ? " +
            "GROUP BY t.tenant_line_number, t.supply_line_number";

    public static final String FIND_RECENT_TENANTS_DATA_SQL = "SELECT " +
            "SUM(t.total_call_num) AS total_call_num, " +
            "SUM(t.total_connect_num) AS total_connect_num, " +
            "SUM(t.silence_call_num) AS silence_call_num, " +
            "SUM(t.one_second_connected_num) AS one_second_connected_num, " +
            "SUM(t.two_second_connected_num) AS two_second_connected_num, " +
            "SUM(t.silence_hangup_num) AS silence_hangup_num, " +
            "SUM(t.assistant_num) AS assistant_num, " +
            "SUM(t.prompt_sound_num) AS prompt_sound_num, " +
            "SUM(t.trans_call_seat_num) AS trans_call_seat_num, " +
            "SUM(t.class_a_num) AS class_a_num, " +
            "SUM(t.class_b_num) AS class_b_num, " +
            "SUM(t.class_c_num) AS class_c_num, " +
            "SUM(t.class_d_num) AS class_d_num, " +
            "SUM(t.call_failed_num) AS call_failed_num, " +
            "SUM(t.routing_fail_num) AS routing_fail_num, " +
            "SUM(t.wait_second) AS wait_second, " +
            "SUM(t.call_duration_second) AS call_duration_second, " +
            "t.tenant_line_number as tenant_line_number " +
            "FROM aiSpeech.call_line_monitor_unit_data t " +
            "WHERE t.time_slot in ? " +
            "AND t.tenant_line_number in ? " +
            "GROUP BY t.tenant_line_number";

    public static final String FIND_PAST_TENANTS_DATA_SQL = "SELECT " +
            "SUM(t.total_call_num) AS total_call_num, " +
            "SUM(t.total_connect_num) AS total_connect_num, " +
            "SUM(t.silence_call_num) AS silence_call_num, " +
            "SUM(t.one_second_connected_num) AS one_second_connected_num, " +
            "SUM(t.two_second_connected_num) AS two_second_connected_num, " +
            "SUM(t.silence_hangup_num) AS silence_hangup_num, " +
            "SUM(t.assistant_num) AS assistant_num, " +
            "SUM(t.prompt_sound_num) AS prompt_sound_num, " +
            "SUM(t.trans_call_seat_num) AS trans_call_seat_num, " +
            "SUM(t.class_a_num) AS class_a_num, " +
            "SUM(t.class_b_num) AS class_b_num, " +
            "SUM(t.class_c_num) AS class_c_num, " +
            "SUM(t.class_d_num) AS class_d_num, " +
            "SUM(t.call_failed_num) AS call_failed_num, " +
            "SUM(t.routing_fail_num) AS routing_fail_num, " +
            "SUM(t.wait_second) AS wait_second, " +
            "SUM(t.call_duration_second) AS call_duration_second, " +
            "t.tenant_line_number as tenant_line_number " +
            "FROM aiSpeech.call_line_monitor_unit_data t " +
            "WHERE t.tenant_line_number in ? " +
            "GROUP BY t.tenant_line_number";

    public static final String FIND_PAST_TENANTS_DATA_SQL_YESTERDAY = "SELECT " +
            "SUM(t.total_call_num) AS total_call_num, " +
            "SUM(t.total_connect_num) AS total_connect_num, " +
            "SUM(t.silence_call_num) AS silence_call_num, " +
            "SUM(t.one_second_connected_num) AS one_second_connected_num, " +
            "SUM(t.two_second_connected_num) AS two_second_connected_num, " +
            "SUM(t.silence_hangup_num) AS silence_hangup_num, " +
            "SUM(t.assistant_num) AS assistant_num, " +
            "SUM(t.prompt_sound_num) AS prompt_sound_num, " +
            "SUM(t.trans_call_seat_num) AS trans_call_seat_num, " +
            "SUM(t.class_a_num) AS class_a_num, " +
            "SUM(t.class_b_num) AS class_b_num, " +
            "SUM(t.class_c_num) AS class_c_num, " +
            "SUM(t.class_d_num) AS class_d_num, " +
            "SUM(t.call_failed_num) AS call_failed_num, " +
            "SUM(t.routing_fail_num) AS routing_fail_num, " +
            "SUM(t.wait_second) AS wait_second, " +
            "SUM(t.call_duration_second) AS call_duration_second, " +
            "t.tenant_line_number as tenant_line_number " +
            "FROM aiSpeech.call_line_monitor_unit_data_yesterday t " +
            "WHERE t.tenant_line_number in ? " +
            "GROUP BY t.tenant_line_number";

    public static final String FIND_PAST_TENANTS_DATA_SQL_BEFORE_YESTERDAY = "SELECT " +
            "SUM(t.total_call_num) AS total_call_num, " +
            "SUM(t.total_connect_num) AS total_connect_num, " +
            "SUM(t.silence_call_num) AS silence_call_num, " +
            "SUM(t.one_second_connected_num) AS one_second_connected_num, " +
            "SUM(t.two_second_connected_num) AS two_second_connected_num, " +
            "SUM(t.silence_hangup_num) AS silence_hangup_num, " +
            "SUM(t.assistant_num) AS assistant_num, " +
            "SUM(t.prompt_sound_num) AS prompt_sound_num, " +
            "SUM(t.trans_call_seat_num) AS trans_call_seat_num, " +
            "SUM(t.class_a_num) AS class_a_num, " +
            "SUM(t.class_b_num) AS class_b_num, " +
            "SUM(t.class_c_num) AS class_c_num, " +
            "SUM(t.class_d_num) AS class_d_num, " +
            "SUM(t.call_failed_num) AS call_failed_num, " +
            "SUM(t.routing_fail_num) AS routing_fail_num, " +
            "SUM(t.wait_second) AS wait_second, " +
            "SUM(t.call_duration_second) AS call_duration_second, " +
            "t.tenant_line_number as tenant_line_number " +
            "FROM aiSpeech.call_line_monitor_unit_data_before_yesterday t " +
            "WHERE t.tenant_line_number in ? " +
            "GROUP BY t.tenant_line_number";

    public static final String FIND_RECENT_TENANT_DATA_SQL = "SELECT " +
            "SUM(t.total_call_num) AS total_call_num, " +
            "SUM(t.total_connect_num) AS total_connect_num, " +
            "SUM(t.silence_call_num) AS silence_call_num, " +
            "SUM(t.one_second_connected_num) AS one_second_connected_num, " +
            "SUM(t.two_second_connected_num) AS two_second_connected_num, " +
            "SUM(t.silence_hangup_num) AS silence_hangup_num, " +
            "SUM(t.assistant_num) AS assistant_num, " +
            "SUM(t.prompt_sound_num) AS prompt_sound_num, " +
            "SUM(t.trans_call_seat_num) AS trans_call_seat_num, " +
            "SUM(t.class_a_num) AS class_a_num, " +
            "SUM(t.class_b_num) AS class_b_num, " +
            "SUM(t.class_c_num) AS class_c_num, " +
            "SUM(t.class_d_num) AS class_d_num, " +
            "SUM(t.call_failed_num) AS call_failed_num, " +
            "SUM(t.wait_second) AS wait_second, " +
            "SUM(t.call_duration_second) AS call_duration_second, " +
            "t.tenant_line_number as tenant_line_number, " +
            "t.supply_line_number as supply_line_number " +
            "FROM aiSpeech.call_line_monitor_unit_data t " +
            "WHERE t.time_slot in ? " +
            "AND t.tenant_line_number = ? " +
            "AND t.supply_line_number in ? " +
            "GROUP BY t.tenant_line_number, t.supply_line_number";

    public static final String FIND_PAST_TENANT_DATA_SQL = "SELECT " +
            "SUM(t.total_call_num) AS total_call_num, " +
            "SUM(t.total_connect_num) AS total_connect_num, " +
            "SUM(t.silence_call_num) AS silence_call_num, " +
            "SUM(t.one_second_connected_num) AS one_second_connected_num, " +
            "SUM(t.two_second_connected_num) AS two_second_connected_num, " +
            "SUM(t.silence_hangup_num) AS silence_hangup_num, " +
            "SUM(t.assistant_num) AS assistant_num, " +
            "SUM(t.prompt_sound_num) AS prompt_sound_num, " +
            "SUM(t.trans_call_seat_num) AS trans_call_seat_num, " +
            "SUM(t.class_a_num) AS class_a_num, " +
            "SUM(t.class_b_num) AS class_b_num, " +
            "SUM(t.class_c_num) AS class_c_num, " +
            "SUM(t.class_d_num) AS class_d_num, " +
            "SUM(t.call_failed_num) AS call_failed_num, " +
            "SUM(t.wait_second) AS wait_second, " +
            "SUM(t.call_duration_second) AS call_duration_second, " +
            "t.tenant_line_number as tenant_line_number, " +
            "t.supply_line_number as supply_line_number " +
            "FROM aiSpeech.call_line_monitor_unit_data t " +
            "WHERE t.tenant_line_number = ? " +
            "AND t.supply_line_number in ? " +
            "GROUP BY t.tenant_line_number, t.supply_line_number";

    public static final String FIND_PAST_TENANT_DATA_SQL_YESTERDAY = "SELECT " +
            "SUM(t.total_call_num) AS total_call_num, " +
            "SUM(t.total_connect_num) AS total_connect_num, " +
            "SUM(t.silence_call_num) AS silence_call_num, " +
            "SUM(t.one_second_connected_num) AS one_second_connected_num, " +
            "SUM(t.two_second_connected_num) AS two_second_connected_num, " +
            "SUM(t.silence_hangup_num) AS silence_hangup_num, " +
            "SUM(t.assistant_num) AS assistant_num, " +
            "SUM(t.prompt_sound_num) AS prompt_sound_num, " +
            "SUM(t.trans_call_seat_num) AS trans_call_seat_num, " +
            "SUM(t.class_a_num) AS class_a_num, " +
            "SUM(t.class_b_num) AS class_b_num, " +
            "SUM(t.class_c_num) AS class_c_num, " +
            "SUM(t.class_d_num) AS class_d_num, " +
            "SUM(t.call_failed_num) AS call_failed_num, " +
            "SUM(t.wait_second) AS wait_second, " +
            "SUM(t.call_duration_second) AS call_duration_second, " +
            "t.tenant_line_number as tenant_line_number, " +
            "t.supply_line_number as supply_line_number " +
            "FROM aiSpeech.call_line_monitor_unit_data_yesterday t " +
            "WHERE t.tenant_line_number = ? " +
            "AND t.supply_line_number in ? " +
            "GROUP BY t.tenant_line_number, t.supply_line_number";

    public static final String FIND_PAST_TENANT_DATA_SQL_BEFORE_YESTERDAY = "SELECT " +
            "SUM(t.total_call_num) AS total_call_num, " +
            "SUM(t.total_connect_num) AS total_connect_num, " +
            "SUM(t.silence_call_num) AS silence_call_num, " +
            "SUM(t.one_second_connected_num) AS one_second_connected_num, " +
            "SUM(t.two_second_connected_num) AS two_second_connected_num, " +
            "SUM(t.silence_hangup_num) AS silence_hangup_num, " +
            "SUM(t.assistant_num) AS assistant_num, " +
            "SUM(t.prompt_sound_num) AS prompt_sound_num, " +
            "SUM(t.trans_call_seat_num) AS trans_call_seat_num, " +
            "SUM(t.class_a_num) AS class_a_num, " +
            "SUM(t.class_b_num) AS class_b_num, " +
            "SUM(t.class_c_num) AS class_c_num, " +
            "SUM(t.class_d_num) AS class_d_num, " +
            "SUM(t.call_failed_num) AS call_failed_num, " +
            "SUM(t.wait_second) AS wait_second, " +
            "SUM(t.call_duration_second) AS call_duration_second, " +
            "t.tenant_line_number as tenant_line_number, " +
            "t.supply_line_number as supply_line_number " +
            "FROM aiSpeech.call_line_monitor_unit_data_before_yesterday t " +
            "WHERE t.tenant_line_number = ? " +
            "AND t.supply_line_number in ? " +
            "GROUP BY t.tenant_line_number, t.supply_line_number";

    public static final String GET_RATE_CHART_TODAY_DATA_SQL = "SELECT " +
            "SUM(t.total_call_num) AS total_call_num, " +
            "SUM(t.total_connect_num) AS total_connect_num, " +
            "SUM(t.silence_call_num) AS silence_call_num, " +
            "SUM(t.one_second_connected_num) AS one_second_connected_num, " +
            "SUM(t.two_second_connected_num) AS two_second_connected_num, " +
            "SUM(t.silence_hangup_num) AS silence_hangup_num, " +
            "SUM(t.assistant_num) AS assistant_num, " +
            "SUM(t.prompt_sound_num) AS prompt_sound_num, " +
            "SUM(t.trans_call_seat_num) AS trans_call_seat_num, " +
            "SUM(t.class_a_num) AS class_a_num, " +
            "SUM(t.class_b_num) AS class_b_num, " +
            "SUM(t.class_c_num) AS class_c_num, " +
            "SUM(t.class_d_num) AS class_d_num, " +
            "SUM(t.call_failed_num) AS call_failed_num, " +
            "t.time_slot as time_slot " +
            "FROM aiSpeech.call_line_monitor_unit_data t " +
            "WHERE (? IS NULL OR t.tenant_line_number=?) " +
            "AND   (? IS NULL OR t.supply_line_number=?) " +
            "AND t.operator in ? " +
            "GROUP BY t.time_slot";

    public static final String GET_COUNTRY_CHART_TODAY_DATA_SQL = "SELECT " +
            "SUM(t.total_call_num) AS total_call_num, " +
            "SUM(t.total_connect_num) AS total_connect_num, " +
            "SUM(t.silence_call_num) AS silence_call_num, " +
            "SUM(t.one_second_connected_num) AS one_second_connected_num, " +
            "SUM(t.two_second_connected_num) AS two_second_connected_num, " +
            "SUM(t.silence_hangup_num) AS silence_hangup_num, " +
            "SUM(t.assistant_num) AS assistant_num, " +
            "SUM(t.prompt_sound_num) AS prompt_sound_num, " +
            "SUM(t.trans_call_seat_num) AS trans_call_seat_num, " +
            "SUM(t.class_a_num) AS class_a_num, " +
            "SUM(t.class_b_num) AS class_b_num, " +
            "SUM(t.class_c_num) AS class_c_num, " +
            "SUM(t.class_d_num) AS class_d_num, " +
            "SUM(t.call_failed_num) AS call_failed_num, " +
            "t.province as province " +
            "FROM aiSpeech.call_line_monitor_unit_data t " +
            "WHERE t.time_slot in ? " +
            "AND (? IS NULL OR t.tenant_line_number=?) " +
            "AND (? IS NULL OR t.supply_line_number=?) " +
            "AND t.operator in ? " +
            "GROUP BY t.province";

    public static final String GET_PROVINCE_CHART_TODAY_DATA_SQL = "SELECT " +
            "SUM(t.total_call_num) AS total_call_num, " +
            "SUM(t.total_connect_num) AS total_connect_num, " +
            "SUM(t.silence_call_num) AS silence_call_num, " +
            "SUM(t.one_second_connected_num) AS one_second_connected_num, " +
            "SUM(t.two_second_connected_num) AS two_second_connected_num, " +
            "SUM(t.silence_hangup_num) AS silence_hangup_num, " +
            "SUM(t.assistant_num) AS assistant_num, " +
            "SUM(t.prompt_sound_num) AS prompt_sound_num, " +
            "SUM(t.trans_call_seat_num) AS trans_call_seat_num, " +
            "SUM(t.class_a_num) AS class_a_num, " +
            "SUM(t.class_b_num) AS class_b_num, " +
            "SUM(t.class_c_num) AS class_c_num, " +
            "SUM(t.class_d_num) AS class_d_num, " +
            "SUM(t.call_failed_num) AS call_failed_num, " +
            "t.city as city " +
            "FROM aiSpeech.call_line_monitor_unit_data t " +
            "WHERE t.time_slot in ? " +
            "AND (? IS NULL OR t.tenant_line_number=?) " +
            "AND (? IS NULL OR t.supply_line_number=?) " +
            "AND t.operator in ? " +
            "AND t.province = ? " +
            "GROUP BY t.city";

    public static final String GET_RATE_CHART_YESTERDAY_DATA_SQL = "SELECT " +
            "SUM(t.total_call_num) AS total_call_num, " +
            "SUM(t.total_connect_num) AS total_connect_num, " +
            "SUM(t.silence_call_num) AS silence_call_num, " +
            "SUM(t.one_second_connected_num) AS one_second_connected_num, " +
            "SUM(t.two_second_connected_num) AS two_second_connected_num, " +
            "SUM(t.silence_hangup_num) AS silence_hangup_num, " +
            "SUM(t.assistant_num) AS assistant_num, " +
            "SUM(t.prompt_sound_num) AS prompt_sound_num, " +
            "SUM(t.trans_call_seat_num) AS trans_call_seat_num, " +
            "SUM(t.class_a_num) AS class_a_num, " +
            "SUM(t.class_b_num) AS class_b_num, " +
            "SUM(t.class_c_num) AS class_c_num, " +
            "SUM(t.class_d_num) AS class_d_num, " +
            "SUM(t.call_failed_num) AS call_failed_num, " +
            "t.time_slot as time_slot " +
            "FROM aiSpeech.call_line_monitor_unit_data_yesterday t " +
            "WHERE (? IS NULL OR t.tenant_line_number=?) " +
            "AND   (? IS NULL OR t.supply_line_number=?) " +
            "AND t.operator in ? " +
            "GROUP BY t.time_slot";

    public static final String GET_COUNTRY_CHART_YESTERDAY_DATA_SQL = "SELECT " +
            "SUM(t.total_call_num) AS total_call_num, " +
            "SUM(t.total_connect_num) AS total_connect_num, " +
            "SUM(t.silence_call_num) AS silence_call_num, " +
            "SUM(t.one_second_connected_num) AS one_second_connected_num, " +
            "SUM(t.two_second_connected_num) AS two_second_connected_num, " +
            "SUM(t.silence_hangup_num) AS silence_hangup_num, " +
            "SUM(t.assistant_num) AS assistant_num, " +
            "SUM(t.prompt_sound_num) AS prompt_sound_num, " +
            "SUM(t.trans_call_seat_num) AS trans_call_seat_num, " +
            "SUM(t.class_a_num) AS class_a_num, " +
            "SUM(t.class_b_num) AS class_b_num, " +
            "SUM(t.class_c_num) AS class_c_num, " +
            "SUM(t.class_d_num) AS class_d_num, " +
            "SUM(t.call_failed_num) AS call_failed_num, " +
            "t.province as province " +
            "FROM aiSpeech.call_line_monitor_unit_data_yesterday t " +
            "WHERE t.time_slot in ? " +
            "AND (? IS NULL OR t.tenant_line_number=?) " +
            "AND (? IS NULL OR t.supply_line_number=?) " +
            "AND t.operator in ? " +
            "GROUP BY t.province";

    public static final String GET_PROVINCE_CHART_YESTERDAY_DATA_SQL = "SELECT " +
            "SUM(t.total_call_num) AS total_call_num, " +
            "SUM(t.total_connect_num) AS total_connect_num, " +
            "SUM(t.silence_call_num) AS silence_call_num, " +
            "SUM(t.one_second_connected_num) AS one_second_connected_num, " +
            "SUM(t.two_second_connected_num) AS two_second_connected_num, " +
            "SUM(t.silence_hangup_num) AS silence_hangup_num, " +
            "SUM(t.assistant_num) AS assistant_num, " +
            "SUM(t.prompt_sound_num) AS prompt_sound_num, " +
            "SUM(t.trans_call_seat_num) AS trans_call_seat_num, " +
            "SUM(t.class_a_num) AS class_a_num, " +
            "SUM(t.class_b_num) AS class_b_num, " +
            "SUM(t.class_c_num) AS class_c_num, " +
            "SUM(t.class_d_num) AS class_d_num, " +
            "SUM(t.call_failed_num) AS call_failed_num, " +
            "t.city as city " +
            "FROM aiSpeech.call_line_monitor_unit_data_yesterday t " +
            "WHERE t.time_slot in ? " +
            "AND (? IS NULL OR t.tenant_line_number=?) " +
            "AND (? IS NULL OR t.supply_line_number=?) " +
            "AND t.operator in ? " +
            "AND t.province = ? " +
            "GROUP BY t.city";

    public static final String GET_RATE_CHART_BEFORE_YESTERDAY_DATA_SQL = "SELECT " +
            "SUM(t.total_call_num) AS total_call_num, " +
            "SUM(t.total_connect_num) AS total_connect_num, " +
            "SUM(t.silence_call_num) AS silence_call_num, " +
            "SUM(t.one_second_connected_num) AS one_second_connected_num, " +
            "SUM(t.two_second_connected_num) AS two_second_connected_num, " +
            "SUM(t.silence_hangup_num) AS silence_hangup_num, " +
            "SUM(t.assistant_num) AS assistant_num, " +
            "SUM(t.prompt_sound_num) AS prompt_sound_num, " +
            "SUM(t.trans_call_seat_num) AS trans_call_seat_num, " +
            "SUM(t.class_a_num) AS class_a_num, " +
            "SUM(t.class_b_num) AS class_b_num, " +
            "SUM(t.class_c_num) AS class_c_num, " +
            "SUM(t.class_d_num) AS class_d_num, " +
            "SUM(t.call_failed_num) AS call_failed_num, " +
            "t.time_slot as time_slot " +
            "FROM aiSpeech.call_line_monitor_unit_data_before_yesterday t " +
            "WHERE (? IS NULL OR t.tenant_line_number=?) " +
            "AND   (? IS NULL OR t.supply_line_number=?) " +
            "AND t.operator in ? " +
            "GROUP BY t.time_slot";

    public static final String GET_COUNTRY_CHART_BEFORE_YESTERDAY_DATA_SQL = "SELECT " +
            "SUM(t.total_call_num) AS total_call_num, " +
            "SUM(t.total_connect_num) AS total_connect_num, " +
            "SUM(t.silence_call_num) AS silence_call_num, " +
            "SUM(t.one_second_connected_num) AS one_second_connected_num, " +
            "SUM(t.two_second_connected_num) AS two_second_connected_num, " +
            "SUM(t.silence_hangup_num) AS silence_hangup_num, " +
            "SUM(t.assistant_num) AS assistant_num, " +
            "SUM(t.prompt_sound_num) AS prompt_sound_num, " +
            "SUM(t.trans_call_seat_num) AS trans_call_seat_num, " +
            "SUM(t.class_a_num) AS class_a_num, " +
            "SUM(t.class_b_num) AS class_b_num, " +
            "SUM(t.class_c_num) AS class_c_num, " +
            "SUM(t.class_d_num) AS class_d_num, " +
            "SUM(t.call_failed_num) AS call_failed_num, " +
            "t.province as province " +
            "FROM aiSpeech.call_line_monitor_unit_data_before_yesterday t " +
            "WHERE t.time_slot in ? " +
            "AND (? IS NULL OR t.tenant_line_number=?) " +
            "AND (? IS NULL OR t.supply_line_number=?) " +
            "AND t.operator in ? " +
            "GROUP BY t.province";

    public static final String GET_PROVINCE_CHART_BEFORE_YESTERDAY_DATA_SQL = "SELECT " +
            "SUM(t.total_call_num) AS total_call_num, " +
            "SUM(t.total_connect_num) AS total_connect_num, " +
            "SUM(t.silence_call_num) AS silence_call_num, " +
            "SUM(t.one_second_connected_num) AS one_second_connected_num, " +
            "SUM(t.two_second_connected_num) AS two_second_connected_num, " +
            "SUM(t.silence_hangup_num) AS silence_hangup_num, " +
            "SUM(t.assistant_num) AS assistant_num, " +
            "SUM(t.prompt_sound_num) AS prompt_sound_num, " +
            "SUM(t.trans_call_seat_num) AS trans_call_seat_num, " +
            "SUM(t.class_a_num) AS class_a_num, " +
            "SUM(t.class_b_num) AS class_b_num, " +
            "SUM(t.class_c_num) AS class_c_num, " +
            "SUM(t.class_d_num) AS class_d_num, " +
            "SUM(t.call_failed_num) AS call_failed_num, " +
            "t.city as city " +
            "FROM aiSpeech.call_line_monitor_unit_data_before_yesterday t " +
            "WHERE t.time_slot in ? " +
            "AND (? IS NULL OR t.tenant_line_number=?) " +
            "AND (? IS NULL OR t.supply_line_number=?) " +
            "AND t.operator in ? " +
            "AND t.province = ? " +
            "GROUP BY t.city";

    public static final String EXISTS_BY_TIME_SLOT = "SELECT EXISTS (SELECT 1 FROM aiSpeech.call_line_monitor_unit_data t WHERE t.time_slot=? LIMIT 1)";

    public static final String CLEAR_BEFORE_YESTERDAY_UNIT_DATA = "ALTER TABLE aiSpeech.call_line_monitor_unit_data_before_yesterday DROP PARTITION ALL;";

    public static final String RENAME_TABLE_BEFORE_YESTERDAY = "RENAME TABLE aiSpeech.call_line_monitor_unit_data_before_yesterday TO aiSpeech.temp";

    public static final String RENAME_TABLE_YESTERDAY = "RENAME TABLE aiSpeech.call_line_monitor_unit_data_yesterday TO aiSpeech.call_line_monitor_unit_data_before_yesterday";

    public static final String RENAME_TABLE_TODAY = "RENAME TABLE aiSpeech.call_line_monitor_unit_data TO aiSpeech.call_line_monitor_unit_data_yesterday";

    public static final String RENAME_TABLE_TEMP = "RENAME TABLE aiSpeech.temp TO aiSpeech.call_line_monitor_unit_data";
}
