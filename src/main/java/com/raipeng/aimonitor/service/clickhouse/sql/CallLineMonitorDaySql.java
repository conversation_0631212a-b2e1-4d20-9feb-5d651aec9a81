package com.raipeng.aimonitor.service.clickhouse.sql;

public class CallLineMonitorDaySql {
    public static final String GET_DATA_BY_DATE_AND_OPERATORS_GROUP_BY_DATE = "SELECT " +
            "SUM(t.total_call_num) AS total_call_num, " +
            "SUM(t.total_connect_num) AS total_connect_num, " +
            "SUM(t.silence_call_num) AS silence_call_num, " +
            "SUM(t.one_second_connected_num) AS one_second_connected_num, " +
            "SUM(t.two_second_connected_num) AS two_second_connected_num, " +
            "SUM(t.silence_hangup_num) AS silence_hangup_num, " +
            "SUM(t.assistant_num) AS assistant_num, " +
            "SUM(t.prompt_sound_num) AS prompt_sound_num, " +
            "SUM(t.trans_call_seat_num) AS trans_call_seat_num, " +
            "SUM(t.class_a_num) AS class_a_num, " +
            "SUM(t.class_b_num) AS class_b_num, " +
            "SUM(t.class_c_num) AS class_c_num, " +
            "SUM(t.class_d_num) AS class_d_num, " +
            "SUM(t.call_failed_num) AS call_failed_num, " +
            "t.statistic_date as statistic_date " +
            "FROM aiSpeech.call_line_monitor_day_data t " +
            "WHERE t.statistic_date in ? " +
            "AND (? IS NULL OR t.tenant_line_number=?) " +
            "AND (? IS NULL OR t.supply_line_number=?) " +
            "AND t.operator in ?" +
            "GROUP BY t.statistic_date";

    public static final String GET_DATA_BY_DATE_AND_OPERATORS_GROUP_BY_PROVINCE = "SELECT " +
            "SUM(t.total_call_num) AS total_call_num, " +
            "SUM(t.total_connect_num) AS total_connect_num, " +
            "SUM(t.silence_call_num) AS silence_call_num, " +
            "SUM(t.one_second_connected_num) AS one_second_connected_num, " +
            "SUM(t.two_second_connected_num) AS two_second_connected_num, " +
            "SUM(t.silence_hangup_num) AS silence_hangup_num, " +
            "SUM(t.assistant_num) AS assistant_num, " +
            "SUM(t.prompt_sound_num) AS prompt_sound_num, " +
            "SUM(t.trans_call_seat_num) AS trans_call_seat_num, " +
            "SUM(t.class_a_num) AS class_a_num, " +
            "SUM(t.class_b_num) AS class_b_num, " +
            "SUM(t.class_c_num) AS class_c_num, " +
            "SUM(t.class_d_num) AS class_d_num, " +
            "SUM(t.call_failed_num) AS call_failed_num, " +
            "t.province as province " +
            "FROM aiSpeech.call_line_monitor_day_data t " +
            "WHERE t.statistic_date in ? " +
            "AND (? IS NULL OR t.tenant_line_number=?) " +
            "AND (? IS NULL OR t.supply_line_number=?) " +
            "AND t.operator in ?" +
            "GROUP BY t.province";

    public static final String GET_PROVINCE_DATA_LIST_GROUP_BY_CITY = "SELECT " +
            "SUM(t.total_call_num) AS total_call_num, " +
            "SUM(t.total_connect_num) AS total_connect_num, " +
            "SUM(t.silence_call_num) AS silence_call_num, " +
            "SUM(t.one_second_connected_num) AS one_second_connected_num, " +
            "SUM(t.two_second_connected_num) AS two_second_connected_num, " +
            "SUM(t.silence_hangup_num) AS silence_hangup_num, " +
            "SUM(t.assistant_num) AS assistant_num, " +
            "SUM(t.prompt_sound_num) AS prompt_sound_num, " +
            "SUM(t.trans_call_seat_num) AS trans_call_seat_num, " +
            "SUM(t.class_a_num) AS class_a_num, " +
            "SUM(t.class_b_num) AS class_b_num, " +
            "SUM(t.class_c_num) AS class_c_num, " +
            "SUM(t.class_d_num) AS class_d_num, " +
            "SUM(t.call_failed_num) AS call_failed_num, " +
            "t.city as city " +
            "FROM aiSpeech.call_line_monitor_day_data t " +
            "WHERE t.statistic_date in ? " +
            "AND (? IS NULL OR t.tenant_line_number=?) " +
            "AND (? IS NULL OR t.supply_line_number=?) " +
            "AND t.operator in ? " +
            "AND t.province=? " +
            "GROUP BY t.city";

    public static final String SAVE_DAY_DATA_FROM_UNIT_DATA_SQL = "INSERT INTO aiSpeech.call_line_monitor_day_data " +
            "(statistic_date, tenant_line_number, supply_line_number, " +
            "total_call_num, total_connect_num, silence_call_num, one_second_connected_num, two_second_connected_num, " +
            "silence_hangup_num, assistant_num, prompt_sound_num, trans_call_seat_num, class_a_num, class_b_num, class_c_num, class_d_num, " +
            "call_failed_num,wait_second,call_duration_second,province, city, operator) SELECT " +
            "? as statistic_date, " +
            "t.tenant_line_number as tenant_line_number, " +
            "t.supply_line_number as supply_line_number, " +
            "SUM(t.total_call_num) AS total_call_num, " +
            "SUM(t.total_connect_num) AS total_connect_num, " +
            "SUM(t.silence_call_num) AS silence_call_num, " +
            "SUM(t.one_second_connected_num) AS one_second_connected_num, " +
            "SUM(t.two_second_connected_num) AS two_second_connected_num, " +
            "SUM(t.silence_hangup_num) AS silence_hangup_num, " +
            "SUM(t.assistant_num) AS assistant_num, " +
            "SUM(t.prompt_sound_num) AS prompt_sound_num, " +
            "SUM(t.trans_call_seat_num) AS trans_call_seat_num, " +
            "SUM(t.class_a_num) AS class_a_num, " +
            "SUM(t.class_b_num) AS class_b_num, " +
            "SUM(t.class_c_num) AS class_c_num, " +
            "SUM(t.class_d_num) AS class_d_num, " +
            "SUM(t.call_failed_num) AS call_failed_num, " +
            "SUM(t.wait_second) AS wait_second, " +
            "SUM(t.call_duration_second) AS call_duration_second, " +
            "t.province as province, " +
            "t.city as city, " +
            "t.operator as operator " +
            "FROM aiSpeech.call_line_monitor_unit_data t " +
            "GROUP BY t.tenant_line_number, t.supply_line_number, t.province, t.city, t.operator";

    public static final String DELETE_BY_DATE = "ALTER TABLE aiSpeech.call_line_monitor_day_data DROP PARTITION ?";
}
