package com.raipeng.aimonitor.service;


import com.raipeng.aidatacommon.model.CallLineSupplier;
import com.raipeng.aidatacommon.model.SupplyLine;
import com.raipeng.aimonitor.config.HotConfig;
import com.raipeng.aimonitor.enums.CallLineBelong;
import com.raipeng.aimonitor.repository.CallLineSupplierRepository;
import com.raipeng.aimonitor.repository.SupplyLineRepository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class SupplyLineSearchService {
    private static final String SUPPLIER_FOR_OTHER = "白泽";

    private static final String SUPPLIER_FOR_BZ = "限时传送";

    @Autowired
    private HotConfig hotConfig;

    @Autowired
    private SupplyLineRepository supplyLineRepository;

    @Autowired
    private CallLineSupplierRepository callLineSupplierRepository;

    public Map<String, List<String>> getSupplierNumbersMap() {
        List<String> params = Arrays.asList(hotConfig.getSupplierNumbersForThirdSearch().split(";"));
        Map<String, String> supplierNumberNameMap = params.stream().collect(
                Collectors.toMap(param -> param.split("_")[0], param -> param.split("_")[1]));
        List<SupplyLine> supplyLines = supplyLineRepository.findAllByCallLineSupplierNumberIn(supplierNumberNameMap.keySet());
        Map<String, List<String>> supplierNumbersMap = new HashMap<>();
        for (SupplyLine supplyLine : supplyLines) {
            String supplierNumber = supplyLine.getCallLineSupplierNumber();
            String key = supplierNumberNameMap.get(supplierNumber);
            if (supplierNumbersMap.containsKey(key)) {
                supplierNumbersMap.get(key).add(supplyLine.getLineNumber());
            } else {
                List<String> lineNumbers = new ArrayList<>();
                lineNumbers.add(supplyLine.getLineNumber());
                supplierNumbersMap.put(key, lineNumbers);
            }
        }
        List<String> numbers = supplyLineRepository.findSupplyLineNumberBySupplierNumberNotIn(supplierNumberNameMap.keySet());
        supplierNumbersMap.put(SUPPLIER_FOR_OTHER, numbers);
        return supplierNumbersMap;
    }

    public Map<String, String> getSupplyLineBelongMap() {
        List<String> supplierBelongs = hotConfig.getSupplierBelongsForCallLineUsageSearch();
        List<CallLineSupplier> suppliers = callLineSupplierRepository.findBySupplierBelongIn(supplierBelongs);
        Map<String, String> supplierNumberNameMap = suppliers.stream()
                .collect(
                        Collectors.toMap(
                                CallLineSupplier::getSupplierNumber,
                                supplier -> CallLineBelong.getLabelByValue(supplier.getSupplierBelong())));
        List<SupplyLine> supplyLines = supplyLineRepository.findAllByCallLineSupplierNumberIn(supplierNumberNameMap.keySet());
        Map<String, String> supplyLineBelongMap = new HashMap<>();
        for (SupplyLine supplyLine : supplyLines) {
            String supplierNumber = supplyLine.getCallLineSupplierNumber();
            String key = supplierNumberNameMap.get(supplierNumber);
            supplyLineBelongMap.put(supplyLine.getLineNumber(), key);
        }
        return supplyLineBelongMap;
    }
}
