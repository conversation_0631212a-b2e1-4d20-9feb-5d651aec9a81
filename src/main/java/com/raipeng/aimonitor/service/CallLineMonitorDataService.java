package com.raipeng.aimonitor.service;

import com.raipeng.aidatacommon.enums.EnableStatus;
import com.raipeng.aidatacommon.model.*;
import com.raipeng.aimonitor.constant.CommonConstant;
import com.raipeng.aimonitor.context.AIContext;
import com.raipeng.aimonitor.controller.request.SupplyLineMonitorDetailParam;
import com.raipeng.aimonitor.controller.request.SupplyLineMonitorParam;
import com.raipeng.aimonitor.controller.request.TenantLineMonitorDetailParam;
import com.raipeng.aimonitor.controller.request.TenantLineMonitorParam;
import com.raipeng.aimonitor.controller.response.SupplyLineMonitor;
import com.raipeng.aimonitor.controller.response.SupplyLineMonitorDetail;
import com.raipeng.aimonitor.controller.response.TenantLineMonitor;
import com.raipeng.aimonitor.controller.response.TenantLineMonitorDetail;
import com.raipeng.aimonitor.entity.LineSecStatisticEntity;
import com.raipeng.aimonitor.entity.ShowTime;
import com.raipeng.aimonitor.exceptionadvice.exception.LineCheckException;
import com.raipeng.aimonitor.model.CallLineMonitorUnitData;
import com.raipeng.aimonitor.repository.*;
import com.raipeng.aimonitor.service.clickhouse.CallLineMonitorUnitClickService;
import com.raipeng.aimonitor.service.clickhouse.CallRecordStatisticClickService;
import com.raipeng.aimonitor.utils.CallLineUtil;
import com.raipeng.aimonitor.utils.LineUtil;
import com.raipeng.common.util.CombineLineUtil;
import com.raipeng.common.util.JpaResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.Tuple;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CallLineMonitorDataService {
    @Autowired
    private CallLineMonitorUnitClickService callLineMonitorUnitClickService;

    @Autowired
    private SupplyLineRepository supplyLineRepository;

    @Autowired
    private TenantLineRepository tenantLineRepository;

    @Autowired
    private AITenantRepository aiTenantRepository;

    @Autowired
    private AdminRepository adminRepository;

    @Autowired
    private AiManagerClusterService aiManagerClusterService;

    @Autowired
    private TenantSupplyLineRepository tenantSupplyLineRepository;

    @Autowired
    private CallRecordStatisticClickService callRecordStatisticClickService;

    @Autowired
    private CallRecordForHumanMachineRepository callRecordForHumanMachineRepository;

    @Autowired
    private CallRecordForManualDirectRepository callRecordForManualDirectRepository;
    @Autowired
    private AiOutboundTaskService aiOutboundTaskService;

    public List<LineSecStatisticEntity> findSupplyLineCallDurationSecDistribution(String supplyLineNumber, Integer recentMin) {
        LocalDateTime now = LocalDateTime.now();
        if (recentMin == 0) {
            recentMin = now.getHour() * 60 + now.getMinute();
        }
        ShowTime recentShowTime = LineUtil.getRecentShowTime(now, recentMin);
        return getSupplyLineCallDurationSecDistribution(recentShowTime.getStartShowTime(), recentShowTime.getEndShowTime(), supplyLineNumber);
    }

    public List<SupplyLineMonitor> findSupplyLineMonitorList(SupplyLineMonitorParam param) {
        List<SupplyLine> supplyLines = getSupplyLineNumbersBySupplyConditions(param);
        int hour = LocalDateTime.now().getHour();
        int minute = LocalDateTime.now().getMinute();
        Map<String, Integer> supplyLineCountMap = aiManagerClusterService.getSupplyLineSizeInUsing();

        List<SupplyLineMonitor> supplyLineMonitors = findSupplyLineMonitors(hour * 60 + minute, supplyLines, param.getRecentMin(), supplyLineCountMap);

        //供应线路列表 设置可支配并发 优先状态
        if( !supplyLineMonitors.isEmpty()) {
            List<String> supplyLineNumbers = supplyLineMonitors.stream().map(SupplyLineMonitor::getSupplyLineNumber).collect(Collectors.toList());
            List<Tuple> supplyLineStatisticTuple = tenantSupplyLineRepository.findStatisticBySupplyLineNumber(supplyLineNumbers);
            Map<String, Tuple> supplyLineStatisticMap = supplyLineStatisticTuple.stream().collect(Collectors.toMap(tuple -> tuple.get("supplyLineNum", String.class), Function.identity()));
            for(SupplyLineMonitor supplyLineMonitor : supplyLineMonitors){
                String supplyLineNumber = supplyLineMonitor.getSupplyLineNumber();
                Tuple statisticTuple = supplyLineStatisticMap.get(supplyLineNumber);
                if(statisticTuple != null){
                    String concurrency = supplyLineMonitor.getConcurrency();
                    String[] split = concurrency.split("/");
                    if(split.length == 2){
                        supplyLineMonitor.setConcurrency(split[0] + "/" + statisticTuple.get( "concurrentLimit", Number.class).longValue() + "/" + split[1]);
                    }
                    supplyLineMonitor.setPriorityStatus(statisticTuple.get("priorityCount", Number.class).longValue() + "/" + statisticTuple.get("totalCount", Number.class).longValue());
                }
            }
        }
        return supplyLineMonitors;
    }

    public List<SupplyLineMonitor> findSupplyLineMonitorListNoConcurrent(SupplyLineMonitorParam param) {
        List<SupplyLine> supplyLines = getSupplyLineNumbersBySupplyConditions(param);
        int hour = LocalDateTime.now().getHour();
        int minute = LocalDateTime.now().getMinute();
        return findSupplyLineMonitors(hour * 60 + minute, supplyLines, param.getRecentMin(), new HashMap<>());
    }

    public List<SupplyLineMonitor> findSupplyLineMonitorListYesterday(SupplyLineMonitorParam param) {
        List<SupplyLine> supplyLines = getSupplyLineNumbersBySupplyConditions(param);
        return findSupplyLineMonitorsYesterday(supplyLines);
    }

    public List<SupplyLineMonitor> findSupplyLineMonitorListBeforeYesterday(SupplyLineMonitorParam param) {
        List<SupplyLine> supplyLines = getSupplyLineNumbersBySupplyConditions(param);
        return findSupplyLineMonitorsBeforeYesterday(supplyLines);
    }

    public List<SupplyLineMonitorDetail> findSupplyLineMonitorDetailList(SupplyLineMonitorDetailParam param) {
        List<TenantLine> tenantLines = getTenantLineNumbersBySupplyConditions(param);
        int hour = LocalDateTime.now().getHour();
        int minute = LocalDateTime.now().getMinute();
        return findSupplyLineMonitorDetails(hour * 60 + minute, tenantLines, param.getSupplyLineNumber(), param.getRecentMin());
    }

    public List<SupplyLineMonitorDetail> findSupplyLineMonitorDetailListYesterday(SupplyLineMonitorDetailParam param) {
        List<TenantLine> tenantLines = getTenantLineNumbersBySupplyConditions(param);
        return findSupplyLineMonitorDetailsYesterday(tenantLines, param.getSupplyLineNumber());
    }

    public List<SupplyLineMonitorDetail> findSupplyLineMonitorDetailListBeforeYesterday(SupplyLineMonitorDetailParam param) {
        List<TenantLine> tenantLines = getTenantLineNumbersBySupplyConditions(param);
        return findSupplyLineMonitorDetailsBeforeYesterday(tenantLines, param.getSupplyLineNumber());
    }

    public List<LineSecStatisticEntity> findTenantLineCallDurationSecDistribution(String tenantLineNumber, int recentMin) {
        LocalDateTime now = LocalDateTime.now();
        if (recentMin == 0) {
            recentMin = now.getHour() * 60 + now.getMinute();
        }
        ShowTime recentShowTime = LineUtil.getRecentShowTime(now, recentMin);
        return getTenantLineCallDurationSecDistribution(recentShowTime.getStartShowTime(), recentShowTime.getEndShowTime(), tenantLineNumber);
    }

    public List<TenantLineMonitor> findTenantLineMonitorList(TenantLineMonitorParam param, Integer isTenant) {
        List<TenantLine> tenantLines = getTenantLineNumbersByTenantConditions(param);
        if (!Objects.equals(AIContext.getAccountType(), isTenant)) {
            throw new LineCheckException("商户参数错误");
        }
        boolean isTenantSide = false;
        if (Objects.equals(1, isTenant)) {
            String groupId = AIContext.getGroupId();
            tenantLines = filterByGroupId(tenantLines, groupId);
            isTenantSide = true;
        } else {
            if (StringUtils.isNotBlank(param.getGroupId())) {
                tenantLines = filterByGroupId(tenantLines, param.getGroupId());
            }
        }
        int hour = LocalDateTime.now().getHour();
        int minute = LocalDateTime.now().getMinute();

        List<TenantLineMonitor> tenantLineMonitors = findTenantLineMonitors(hour * 60 + minute, tenantLines, param.getRecentMin());
        if (isTenantSide) {
            // 检查tenantLineMonitors是否包含tenantLines中的所有lineNumber，如果不包含则添加空值监控数据
            Set<String> existingLineNumbers = tenantLineMonitors.stream()
                    .map(TenantLineMonitor::getTenantLineNumber)
                    .collect(Collectors.toSet());

            for (TenantLine tenantLine : tenantLines) {
                if (tenantLine.getEnableStatus() == EnableStatus.ENABLE) {
                    String tenantLineNumber = tenantLine.getLineNumber();
                    if (!existingLineNumbers.contains(tenantLineNumber)) {
                        TenantLineMonitor emptyMonitor = getTenantLineMonitor(
                                tenantLine,
                                new CallLineMonitorUnitData(),
                                new CallLineMonitorUnitData(),
                                new HashMap<>(),
                                new HashMap<>()
                        );
                        tenantLineMonitors.add(emptyMonitor);
                    }
                }
            }
        }

        return tenantLineMonitors;
    }

    private List<TenantLine> filterByGroupId(List<TenantLine> tenantLines, String groupId) {
        if (groupId != null) {
            return tenantLines.stream().filter(tenantLine -> Objects.equals(groupId, tenantLine.getGroupId())).collect(Collectors.toList());
        } else {
            return tenantLines;
        }
    }

    public List<TenantLineMonitor> findTenantLineMonitorListByGroupIds(TenantLineMonitorParam param) {
        List<TenantLine> tenantLines = getTenantLineNumbersByTenantConditions(param);
        tenantLines = filterByGroupIds(tenantLines, param.getGroupIds());
        int hour = LocalDateTime.now().getHour();
        int minute = LocalDateTime.now().getMinute();
        return findTenantLineMonitors(hour * 60 + minute, tenantLines, param.getRecentMin());
    }

    private List<TenantLine> filterByGroupIds(List<TenantLine> tenantLines, List<String> groupIds) {
        if (groupIds == null) {
            return new ArrayList<>();
        }
        return tenantLines.stream().filter(tenantLine -> groupIds.contains(tenantLine.getGroupId())).collect(Collectors.toList());
    }

    public List<TenantLineMonitor> findTenantLineMonitorListYesterday(TenantLineMonitorParam param, Integer isTenant) {
        List<TenantLine> tenantLines = getTenantLineNumbersByTenantConditions(param);
        if (!Objects.equals(AIContext.getAccountType(), isTenant)) {
            throw new LineCheckException("商户参数错误");
        }
        if (Objects.equals(1, isTenant)) {
            String groupId = AIContext.getGroupId();
            tenantLines = filterByGroupId(tenantLines, groupId);
        }
        return findTenantLineMonitorsYesterday(tenantLines);
    }

    public List<TenantLineMonitor> findTenantLineMonitorListBeforeYesterday(TenantLineMonitorParam param, Integer isTenant) {
        List<TenantLine> tenantLines = getTenantLineNumbersByTenantConditions(param);
        if (!Objects.equals(AIContext.getAccountType(), isTenant)) {
            throw new LineCheckException("商户参数错误");
        }
        if (Objects.equals(1, isTenant)) {
            String groupId = AIContext.getGroupId();
            tenantLines = filterByGroupId(tenantLines, groupId);
        }
        return findTenantLineMonitorsBeforeYesterday(tenantLines);
    }

    public List<TenantLineMonitorDetail> findTenantLineMonitorDetailList(TenantLineMonitorDetailParam param) {
        List<SupplyLine> supplyLines = getSupplyNumbersByTenantConditions(param);
        int hour = LocalDateTime.now().getHour();
        int minute = LocalDateTime.now().getMinute();
        return findTenantLineMonitorDetails(hour * 60 + minute, param.getTenantLineNumber(), supplyLines, param.getRecentMin());
    }

    public List<TenantLineMonitorDetail> findTenantLineMonitorDetailListYesterday(TenantLineMonitorDetailParam param) {
        List<SupplyLine> supplyLines = getSupplyNumbersByTenantConditions(param);
        return findTenantLineMonitorDetailsYesterday(param.getTenantLineNumber(), supplyLines);
    }

    public List<TenantLineMonitorDetail> findTenantLineMonitorDetailListBeforeYesterday(TenantLineMonitorDetailParam param) {
        List<SupplyLine> supplyLines = getSupplyNumbersByTenantConditions(param);
        return findTenantLineMonitorDetailsBeforeYesterday(param.getTenantLineNumber(), supplyLines);
    }

    private List<SupplyLineMonitor> findSupplyLineMonitors(int timeSlot, List<SupplyLine> supplyLines, Integer recentMin, Map<String, Integer> supplyLineCountMap) {
        Map<String, SupplyLine> supplyLineMap = supplyLines.stream().collect(Collectors.toMap(SupplyLine::getLineNumber, Function.identity()));
        List<CallLineMonitorUnitData> pastSuppliesData = callLineMonitorUnitClickService.findPastSuppliesData(supplyLineMap.keySet());
        List<CallLineMonitorUnitData> recentSuppliesData = recentMin == null ? pastSuppliesData : callLineMonitorUnitClickService.findRecentSuppliesData(LineUtil.getRecentShowMomentList(timeSlot, recentMin), supplyLineMap.keySet());
        Map<String, CallLineMonitorUnitData> recentDataMap = recentSuppliesData.stream().collect(Collectors.toMap(CallLineMonitorUnitData::getSupplyLineNumber, Function.identity()));
        List<SupplyLineMonitor> supplyLineMonitors = new ArrayList<>();
        for (CallLineMonitorUnitData pastData : pastSuppliesData) {
            String supplyLineNumber = pastData.getSupplyLineNumber();
            SupplyLine supplyLine = supplyLineMap.get(supplyLineNumber);
            if (supplyLine != null) {
                CallLineMonitorUnitData recentData = recentDataMap.get(supplyLineNumber);
                supplyLineMonitors.add(getSupplyLineMonitor(supplyLine, recentData == null ? new CallLineMonitorUnitData() : recentData, pastData, supplyLineCountMap));
            }
        }
        return supplyLineMonitors;
    }

    private List<SupplyLineMonitor> findSupplyLineMonitorsYesterday(List<SupplyLine> supplyLines) {
        Map<String, SupplyLine> supplyLineMap = supplyLines.stream().collect(Collectors.toMap(SupplyLine::getLineNumber, Function.identity()));
        List<CallLineMonitorUnitData> pastSuppliesData = callLineMonitorUnitClickService.findPastSuppliesDataYesterday(supplyLineMap.keySet());
        List<SupplyLineMonitor> supplyLineMonitors = new ArrayList<>();
        for (CallLineMonitorUnitData pastData : pastSuppliesData) {
            String supplyLineNumber = pastData.getSupplyLineNumber();
            SupplyLine supplyLine = supplyLineMap.get(supplyLineNumber);
            if (supplyLine != null) {
                supplyLineMonitors.add(getSupplyLineMonitor(supplyLine, new CallLineMonitorUnitData(), pastData, new HashMap<>()));
            }
        }
        return supplyLineMonitors;
    }

    private List<SupplyLineMonitor> findSupplyLineMonitorsBeforeYesterday(List<SupplyLine> supplyLines) {
        Map<String, SupplyLine> supplyLineMap = supplyLines.stream().collect(Collectors.toMap(SupplyLine::getLineNumber, Function.identity()));
        List<CallLineMonitorUnitData> pastSuppliesData = callLineMonitorUnitClickService.findPastSuppliesDataBeforeYesterday(supplyLineMap.keySet());
        List<SupplyLineMonitor> supplyLineMonitors = new ArrayList<>();
        for (CallLineMonitorUnitData pastData : pastSuppliesData) {
            String supplyLineNumber = pastData.getSupplyLineNumber();
            SupplyLine supplyLine = supplyLineMap.get(supplyLineNumber);
            if (supplyLine != null) {
                supplyLineMonitors.add(getSupplyLineMonitor(supplyLine, new CallLineMonitorUnitData(), pastData, new HashMap<>()));
            }
        }
        return supplyLineMonitors;
    }

    private List<SupplyLineMonitorDetail> findSupplyLineMonitorDetails(int timeSlot, List<TenantLine> tenantLines, String supplyLineNumber, Integer recentMin) {
        Map<String, TenantLine> tenantLineMap = tenantLines.stream().collect(Collectors.toMap(TenantLine::getLineNumber, Function.identity()));
        List<CallLineMonitorUnitData> pastSupplyData = callLineMonitorUnitClickService.findPastSupplyData(tenantLineMap.keySet(), supplyLineNumber);
        List<CallLineMonitorUnitData> recentSupplyData = recentMin == null ? pastSupplyData : callLineMonitorUnitClickService.findRecentSupplyData(LineUtil.getRecentShowMomentList(timeSlot, recentMin), tenantLineMap.keySet(), supplyLineNumber);
        Map<String, CallLineMonitorUnitData> recentDataMap = recentSupplyData.stream().collect(Collectors.toMap(CallLineMonitorUnitData::getTenantLineNumber, Function.identity()));
        Map<String, Integer> tenantSupplyLineSizeInUsing = aiManagerClusterService.getTenantSupplyLineSizeInUsing();
        List<TenantSupplyLine> tenantSupplyLines = tenantSupplyLineRepository.findAllBySupplyLineNumber(supplyLineNumber);
        SupplyLine supplyLine = supplyLineRepository.findSupplyLineByLineNumber(supplyLineNumber);
        Map<String, TenantSupplyLine> tenantSupplyLineMap = tenantSupplyLines.stream().collect(Collectors.toMap(TenantSupplyLine::getTenantLineNumber, Function.identity()));
        List<SupplyLineMonitorDetail> supplyLineMonitorDetails = new ArrayList<>();
        for (CallLineMonitorUnitData pastData : pastSupplyData) {
            String tenantLineNumber = pastData.getTenantLineNumber();
            TenantLine tenantLine = tenantLineMap.get(tenantLineNumber);
            if (tenantLine != null) {
                CallLineMonitorUnitData recentData = recentDataMap.get(tenantLineNumber);
                supplyLineMonitorDetails.add(getSupplyLineMonitorDetail(
                        supplyLineNumber,
                        tenantLine,
                        recentData == null ? new CallLineMonitorUnitData() : recentData,
                        pastData,
                        tenantSupplyLineSizeInUsing,
                        tenantSupplyLineMap,
                        supplyLine));
            }
        }
        return supplyLineMonitorDetails;
    }

    private List<SupplyLineMonitorDetail> findSupplyLineMonitorDetailsYesterday(List<TenantLine> tenantLines, String supplyLineNumber) {
        Map<String, TenantLine> tenantLineMap = tenantLines.stream().collect(Collectors.toMap(TenantLine::getLineNumber, Function.identity()));
        List<CallLineMonitorUnitData> pastSupplyData = callLineMonitorUnitClickService.findPastSupplyDataYesterday(tenantLineMap.keySet(), supplyLineNumber);
        SupplyLine supplyLine = supplyLineRepository.findSupplyLineByLineNumber(supplyLineNumber);
        List<SupplyLineMonitorDetail> supplyLineMonitorDetails = new ArrayList<>();
        for (CallLineMonitorUnitData pastData : pastSupplyData) {
            String tenantLineNumber = pastData.getTenantLineNumber();
            TenantLine tenantLine = tenantLineMap.get(tenantLineNumber);
            if (tenantLine != null) {
                supplyLineMonitorDetails.add(getSupplyLineMonitorDetail(
                        supplyLineNumber,
                        tenantLine,
                        new CallLineMonitorUnitData(),
                        pastData,
                        new HashMap<>(),
                        new HashMap<>(),
                        supplyLine));
            }
        }
        return supplyLineMonitorDetails;
    }

    private List<SupplyLineMonitorDetail> findSupplyLineMonitorDetailsBeforeYesterday(List<TenantLine> tenantLines, String supplyLineNumber) {
        Map<String, TenantLine> tenantLineMap = tenantLines.stream().collect(Collectors.toMap(TenantLine::getLineNumber, Function.identity()));
        List<CallLineMonitorUnitData> pastSupplyData = callLineMonitorUnitClickService.findPastSupplyDataBeforeYesterday(tenantLineMap.keySet(), supplyLineNumber);
        SupplyLine supplyLine = supplyLineRepository.findSupplyLineByLineNumber(supplyLineNumber);
        List<SupplyLineMonitorDetail> supplyLineMonitorDetails = new ArrayList<>();
        for (CallLineMonitorUnitData pastData : pastSupplyData) {
            String tenantLineNumber = pastData.getTenantLineNumber();
            TenantLine tenantLine = tenantLineMap.get(tenantLineNumber);
            if (tenantLine != null) {
                supplyLineMonitorDetails.add(getSupplyLineMonitorDetail(
                        supplyLineNumber,
                        tenantLine,
                        new CallLineMonitorUnitData(),
                        pastData,
                        new HashMap<>(),
                        new HashMap<>(),
                        supplyLine));
            }
        }
        return supplyLineMonitorDetails;
    }

    private List<TenantLineMonitor> findTenantLineMonitors(int timeSlot, List<TenantLine> tenantLines, Integer recentMin) {
        Map<String, TenantLine> tenantLineMap = tenantLines.stream().collect(Collectors.toMap(TenantLine::getLineNumber, Function.identity()));
        List<CallLineMonitorUnitData> pastTenantsData = callLineMonitorUnitClickService.findPastTenantsData(tenantLineMap.keySet());
        List<CallLineMonitorUnitData> recentTenantsData = recentMin == null ? pastTenantsData : callLineMonitorUnitClickService.findRecentTenantsData(LineUtil.getRecentShowMomentList(timeSlot, recentMin), tenantLineMap.keySet());
        Map<String, CallLineMonitorUnitData> recentDataMap = recentTenantsData.stream().collect(Collectors.toMap(CallLineMonitorUnitData::getTenantLineNumber, Function.identity()));
        Map<String, Integer> tenantLineCountMap = aiManagerClusterService.getTenantLineSizeInUsing();
        Map<String, Integer> tenantLineAiAnswerCount = aiOutboundTaskService.findTenantLineAiAnswerCount();

        List<TenantLineMonitor> tenantLineMonitors = new ArrayList<>();
        for (CallLineMonitorUnitData pastData : pastTenantsData) {
            String tenantLineNumber = pastData.getTenantLineNumber();
            TenantLine tenantLine = tenantLineMap.get(tenantLineNumber);
            if (tenantLine != null) {
                CallLineMonitorUnitData recentData = recentDataMap.get(tenantLineNumber);
                tenantLineMonitors.add(getTenantLineMonitor(tenantLine, recentData == null ? new CallLineMonitorUnitData() : recentData, pastData, tenantLineCountMap, tenantLineAiAnswerCount));
            }
        }

        if(!tenantLineMonitors.isEmpty()) {
            List<String> tenantList = tenantLineMonitors.stream().map(TenantLineMonitor::getTenantLineNumber).collect(Collectors.toList());
            List<Tuple> tenantLineTuple = tenantSupplyLineRepository.findStatisticByTenantLineNumber(tenantList);
            Map<String, Tuple> tenantLineStatisticMap = tenantLineTuple.stream().collect(Collectors.toMap(tuple -> tuple.get("tenantLineNum", String.class), Function.identity()));
            for(TenantLineMonitor tenantLineMonitor: tenantLineMonitors){
                String tenantLineNumber = tenantLineMonitor.getTenantLineNumber();
                Tuple statisticTuple = tenantLineStatisticMap.get(tenantLineNumber);
                if(statisticTuple != null){
                    String concurrency = tenantLineMonitor.getConcurrency();
                    String[] split = concurrency.split("/");
                    if(split.length == 3){
                        tenantLineMonitor.setConcurrency(split[0] + "/" + split[1] + "/" + statisticTuple.get( "concurrentLimit", Number.class).longValue() + "/" + split[2]);
                    }
                    tenantLineMonitor.setPriorityStatus(statisticTuple.get("priorityCount", Number.class).longValue() + "/" + statisticTuple.get("totalCount", Number.class).longValue());
                }
            }

        }
        return tenantLineMonitors;
    }

    private List<TenantLineMonitor> findTenantLineMonitorsYesterday(List<TenantLine> tenantLines) {
        Map<String, TenantLine> tenantLineMap = tenantLines.stream().collect(Collectors.toMap(TenantLine::getLineNumber, Function.identity()));
        List<CallLineMonitorUnitData> pastTenantsData = callLineMonitorUnitClickService.findPastTenantsDataYesterday(tenantLineMap.keySet());
        List<TenantLineMonitor> tenantLineMonitors = new ArrayList<>();
        for (CallLineMonitorUnitData pastData : pastTenantsData) {
            String tenantLineNumber = pastData.getTenantLineNumber();
            TenantLine tenantLine = tenantLineMap.get(tenantLineNumber);
            if (tenantLine != null) {
                tenantLineMonitors.add(getTenantLineMonitor(tenantLine, new CallLineMonitorUnitData(), pastData, new HashMap<>(), new HashMap<>()));
            }
        }
        return tenantLineMonitors;
    }

    private List<TenantLineMonitor> findTenantLineMonitorsBeforeYesterday(List<TenantLine> tenantLines) {
        Map<String, TenantLine> tenantLineMap = tenantLines.stream().collect(Collectors.toMap(TenantLine::getLineNumber, Function.identity()));
        List<CallLineMonitorUnitData> pastTenantsData = callLineMonitorUnitClickService.findPastTenantsDataBeforeYesterday(tenantLineMap.keySet());
        List<TenantLineMonitor> tenantLineMonitors = new ArrayList<>();
        for (CallLineMonitorUnitData pastData : pastTenantsData) {
            String tenantLineNumber = pastData.getTenantLineNumber();
            TenantLine tenantLine = tenantLineMap.get(tenantLineNumber);
            if (tenantLine != null) {
                tenantLineMonitors.add(getTenantLineMonitor(tenantLine, new CallLineMonitorUnitData(), pastData, new HashMap<>(), new HashMap<>()));
            }
        }
        return tenantLineMonitors;
    }

    private List<TenantLineMonitorDetail> findTenantLineMonitorDetails(int timeSlot, String tenantLineNumber, List<SupplyLine> supplyLines, Integer recentMin) {
        Map<String, SupplyLine> supplyLineMap = supplyLines.stream().collect(Collectors.toMap(SupplyLine::getLineNumber, Function.identity()));
        List<CallLineMonitorUnitData> pastTenantData = callLineMonitorUnitClickService.findPastTenantData(tenantLineNumber, supplyLineMap.keySet());
        List<CallLineMonitorUnitData> recentTenantData = recentMin == null ? pastTenantData : callLineMonitorUnitClickService.findRecentTenantData(LineUtil.getRecentShowMomentList(timeSlot, recentMin), tenantLineNumber, supplyLineMap.keySet());
        Map<String, CallLineMonitorUnitData> recentDataMap = recentTenantData.stream().collect(Collectors.toMap(CallLineMonitorUnitData::getSupplyLineNumber, Function.identity()));
        Map<String, Integer> tenantSupplyLineSizeInUsing = aiManagerClusterService.getTenantSupplyLineSizeInUsing();
        List<TenantSupplyLine> tenantSupplyLines = tenantSupplyLineRepository.findAllByTenantLineNumber(tenantLineNumber);
        Map<String, TenantSupplyLine> tenantSupplyLineMap = tenantSupplyLines.stream().collect(Collectors.toMap(TenantSupplyLine::getSupplyLineNumber, Function.identity()));
        List<TenantLineMonitorDetail> tenantLineMonitorDetails = new ArrayList<>();
        for (CallLineMonitorUnitData pastData : pastTenantData) {
            String supplyLineNumber = pastData.getSupplyLineNumber();
            SupplyLine supplyLine = supplyLineMap.get(supplyLineNumber);
            if (supplyLine != null) {
                CallLineMonitorUnitData recentData = recentDataMap.get(supplyLineNumber);
                tenantLineMonitorDetails.add(getTenantLineMonitorDetail(
                        tenantLineNumber,
                        supplyLine,
                        recentData == null ? new CallLineMonitorUnitData() : recentData,
                        pastData,
                        tenantSupplyLineSizeInUsing,
                        tenantSupplyLineMap));
            }
        }
        return tenantLineMonitorDetails;
    }

    private List<TenantLineMonitorDetail> findTenantLineMonitorDetailsYesterday(String tenantLineNumber, List<SupplyLine> supplyLines) {
        Map<String, SupplyLine> supplyLineMap = supplyLines.stream().collect(Collectors.toMap(SupplyLine::getLineNumber, Function.identity()));
        List<CallLineMonitorUnitData> pastTenantData = callLineMonitorUnitClickService.findPastTenantDataYesterday(tenantLineNumber, supplyLineMap.keySet());
        List<TenantLineMonitorDetail> tenantLineMonitorDetails = new ArrayList<>();
        for (CallLineMonitorUnitData pastData : pastTenantData) {
            String supplyLineNumber = pastData.getSupplyLineNumber();
            SupplyLine supplyLine = supplyLineMap.get(supplyLineNumber);
            if (supplyLine != null) {
                tenantLineMonitorDetails.add(getTenantLineMonitorDetail(
                        tenantLineNumber,
                        supplyLine,
                        new CallLineMonitorUnitData(),
                        pastData,
                        new HashMap<>(),
                        new HashMap<>()));
            }
        }
        return tenantLineMonitorDetails;
    }

    private List<TenantLineMonitorDetail> findTenantLineMonitorDetailsBeforeYesterday(String tenantLineNumber, List<SupplyLine> supplyLines) {
        Map<String, SupplyLine> supplyLineMap = supplyLines.stream().collect(Collectors.toMap(SupplyLine::getLineNumber, Function.identity()));
        List<CallLineMonitorUnitData> pastTenantData = callLineMonitorUnitClickService.findPastTenantDataBeforeYesterday(tenantLineNumber, supplyLineMap.keySet());
        List<TenantLineMonitorDetail> tenantLineMonitorDetails = new ArrayList<>();
        for (CallLineMonitorUnitData pastData : pastTenantData) {
            String supplyLineNumber = pastData.getSupplyLineNumber();
            SupplyLine supplyLine = supplyLineMap.get(supplyLineNumber);
            if (supplyLine != null) {
                tenantLineMonitorDetails.add(getTenantLineMonitorDetail(
                        tenantLineNumber,
                        supplyLine,
                        new CallLineMonitorUnitData(),
                        pastData,
                        new HashMap<>(),
                        new HashMap<>()));
            }
        }
        return tenantLineMonitorDetails;
    }

    private List<SupplyLine> getSupplyLineNumbersBySupplyConditions(SupplyLineMonitorParam param) {
        return supplyLineRepository.findSupplyLinesBySupplyLineMonitorParam(param.getSupplyLineName(),
                param.getSupplyLineNumber(), param.getMasterCallNumber(),
                param.getSupplierName(), param.getSupplierNumber());
    }

    private List<TenantLine> getTenantLineNumbersBySupplyConditions(SupplyLineMonitorDetailParam param) {
        return tenantLineRepository.findTenantLinesBySupplyLineMonitorDetailParam(param.getTenantLineName(),
                param.getEnableStatus(), param.getAccount(), param.getTenantName(), param.getTenantNumber());
    }

    private List<TenantLine> getTenantLineNumbersByTenantConditions(TenantLineMonitorParam param) {
        return tenantLineRepository.findTenantLinesByTenantLineMonitorParam(param.getTenantLineName(),
                param.getTenantLineNumber(), param.getTenantName(), param.getTenantNumber());
    }

    private List<SupplyLine> getSupplyNumbersByTenantConditions(TenantLineMonitorDetailParam param) {
        return supplyLineRepository.findSupplyLinesByTenantLineMonitorDetailParam(
                param.getSupplyLineName(),
                param.getEnableStatus(),
                param.getSupplierName(),
                param.getSupplierNumber());
    }

    private SupplyLineMonitor getSupplyLineMonitor(SupplyLine supplyLine, CallLineMonitorUnitData recentData, CallLineMonitorUnitData pastData, Map<String, Integer> supplyLineCountMap) {
        SupplyLineMonitor supplyLineMonitor = new SupplyLineMonitor();
        supplyLineMonitor.setSupplyLineName(supplyLine.getLineName());
        supplyLineMonitor.setSupplyLineNumber(supplyLine.getLineNumber());
        supplyLineMonitor.setSupplyLineType(supplyLine.getLineType());
        supplyLineMonitor.setMasterCallNumber(supplyLine.getMasterCallNumber());
        supplyLineMonitor.setPrefix(supplyLine.getPrefix());
        supplyLineMonitor.setSupplierName(supplyLine.getCallLineSupplierName());
        supplyLineMonitor.setSupplierNumber(supplyLine.getCallLineSupplierNumber());
        supplyLineMonitor.setIsPending(supplyLine.isPending());
        supplyLineMonitor.setConcurrency(supplyLineCountMap.getOrDefault(supplyLine.getLineNumber(), 0) + "/" + supplyLine.getConcurrentLimit());
        CallLineUtil.assembleMonitorData(supplyLineMonitor, recentData, pastData);
        return supplyLineMonitor;
    }

    private TenantLineMonitor getTenantLineMonitor(TenantLine tenantLine,
                                                   CallLineMonitorUnitData recentData,
                                                   CallLineMonitorUnitData pastData,
                                                   Map<String, Integer> tenantLineCountMap,
                                                   Map<String, Integer> tenantLineAiAnswerCountMap) {
        AITenant tenant = aiTenantRepository.findFirstById(tenantLine.getTenantId());
        Admin admin = adminRepository.findFirstById(Long.valueOf(tenantLine.getGroupId().split(CommonConstant.GROUP_ID_SPLIT)[CommonConstant.GROUP_ID_MAIN_COUNT_ID_POSITION]));
        TenantLineMonitor tenantLineMonitor = new TenantLineMonitor();
        tenantLineMonitor.setTenantLineName(tenantLine.getLineName());
        tenantLineMonitor.setTenantLineType(tenantLine.getLineType());
        tenantLineMonitor.setTenantLineNumber(tenantLine.getLineNumber());
        tenantLineMonitor.setAccount(admin.getAccount());
        tenantLineMonitor.setTenantName(tenant.getTenantName());
        tenantLineMonitor.setTenantNumber(tenant.getTenantNo());
        tenantLineMonitor.setConcurrency(tenantLineCountMap.getOrDefault(tenantLine.getLineNumber(), 0)
                + "/" + tenantLineAiAnswerCountMap.getOrDefault(tenantLine.getLineNumber(), 0)
                + "/" + tenantLine.getConcurrentLimit());
        CallLineUtil.assembleMonitorData(tenantLineMonitor, recentData, pastData);
        return tenantLineMonitor;
    }

    private SupplyLineMonitorDetail getSupplyLineMonitorDetail(String supplyLineNumber,
                                                               TenantLine tenantLine,
                                                               CallLineMonitorUnitData recentData,
                                                               CallLineMonitorUnitData pastData,
                                                               Map<String, Integer> tenantSupplyLineSizeInUsing,
                                                               Map<String, TenantSupplyLine> tenantSupplyLineMap,
                                                               SupplyLine supplyLine) {
        SupplyLineMonitorDetail supplyLineMonitorDetail = new SupplyLineMonitorDetail();
        AITenant tenant = aiTenantRepository.findFirstById(tenantLine.getTenantId());
        String tenantLineNumber = tenantLine.getLineNumber();
        String tenantSupplyLineNumber = CombineLineUtil.getTenantSupplyLineNumber(tenantLineNumber, supplyLineNumber);
        Integer tenantSupplyLineConcurrent = tenantSupplyLineSizeInUsing.getOrDefault(tenantSupplyLineNumber, 0);
        Integer tenantSupplyLineConcurrentLimit = 0;
        Integer supplyLineLimit = 0;
        TenantSupplyLine tenantSupplyLine = tenantSupplyLineMap.get(tenantLineNumber);
        if (tenantSupplyLine != null) {
            tenantSupplyLineConcurrentLimit = tenantSupplyLine.getConcurrentLimit();
            supplyLineMonitorDetail.setIsTempStop(tenantSupplyLine.isPending());
            supplyLineMonitorDetail.setIsPriority(tenantSupplyLine.isPriority());
            supplyLineMonitorDetail.setConcurrentLimit(tenantSupplyLine.getConcurrentLimit());
        }
        if (supplyLine != null) {
            supplyLineMonitorDetail.setIsPending(supplyLine.isPending());
            supplyLineLimit = supplyLine.getConcurrentLimit();
        }
        Admin admin = adminRepository.findFirstById(Long.valueOf(tenantLine.getGroupId().split(CommonConstant.GROUP_ID_SPLIT)[CommonConstant.GROUP_ID_MAIN_COUNT_ID_POSITION]));
        supplyLineMonitorDetail.setTenantLineName(tenantLine.getLineName());
        supplyLineMonitorDetail.setTenantLineNumber(tenantLine.getLineNumber());
        supplyLineMonitorDetail.setTenantLineType(tenantLine.getLineType());
        supplyLineMonitorDetail.setStatus(tenantLine.getEnableStatus());
        supplyLineMonitorDetail.setAccount(admin == null ? null : admin.getAccount());
        supplyLineMonitorDetail.setTenantName(tenant == null ? null : tenant.getTenantName());
        supplyLineMonitorDetail.setTenantNumber(tenant == null ? null : tenant.getTenantNo());
        supplyLineMonitorDetail.setConcurrency(tenantSupplyLineConcurrent +
                "/" + (tenantSupplyLineConcurrentLimit == null ? "-" : tenantSupplyLineConcurrentLimit) +
                "/" + (supplyLineLimit == null ? "-" : supplyLineLimit));
        CallLineUtil.assembleMonitorData(supplyLineMonitorDetail, recentData, pastData);
        return supplyLineMonitorDetail;
    }

    private TenantLineMonitorDetail getTenantLineMonitorDetail(String tenantLineNumber,
                                                               SupplyLine supplyLine,
                                                               CallLineMonitorUnitData recentData,
                                                               CallLineMonitorUnitData pastData,
                                                               Map<String, Integer> tenantSupplyLineSizeInUsing,
                                                               Map<String, TenantSupplyLine> tenantSupplyLineMap) {
        TenantLineMonitorDetail tenantLineMonitorDetail = new TenantLineMonitorDetail();
        String supplyLineNumber = supplyLine.getLineNumber();
        String tenantSupplyLineNumber = CombineLineUtil.getTenantSupplyLineNumber(tenantLineNumber, supplyLineNumber);
        Integer tenantSupplyLineConcurrent = tenantSupplyLineSizeInUsing.getOrDefault(tenantSupplyLineNumber, 0);
        Integer tenantSupplyLineConcurrentLimit = 0;
        TenantSupplyLine tenantSupplyLine = tenantSupplyLineMap.get(supplyLineNumber);
        if (tenantSupplyLine != null) {
            tenantSupplyLineConcurrentLimit = tenantSupplyLine.getConcurrentLimit();
            tenantLineMonitorDetail.setIsTempStop(tenantSupplyLine.isPending());
            tenantLineMonitorDetail.setConcurrentLimit(tenantSupplyLine.getConcurrentLimit());
            tenantLineMonitorDetail.setIsPriority(tenantSupplyLine.isPriority());
        }
        tenantLineMonitorDetail.setSupplyLineName(supplyLine.getLineName());
        tenantLineMonitorDetail.setSupplyLineType(supplyLine.getLineType());
        tenantLineMonitorDetail.setSupplyLineNumber(supplyLine.getLineNumber());
        tenantLineMonitorDetail.setStatus(supplyLine.getEnableStatus());
        tenantLineMonitorDetail.setIsPending(supplyLine.isPending());
        tenantLineMonitorDetail.setCallSupplierName(supplyLine.getCallLineSupplierName());
        tenantLineMonitorDetail.setCallSupplierNumber(supplyLine.getCallLineSupplierNumber());
        tenantLineMonitorDetail.setConcurrency(tenantSupplyLineConcurrent +
                "/" + (tenantSupplyLineConcurrentLimit == null ? "-" : tenantSupplyLineConcurrentLimit) +
                "/" + (supplyLine.getConcurrentLimit() == null ? "-" : supplyLine.getConcurrentLimit()));
        tenantLineMonitorDetail.setPrefix(supplyLine.getPrefix());
        tenantLineMonitorDetail.setMasterCallNumber(supplyLine.getMasterCallNumber());
        CallLineUtil.assembleMonitorData(tenantLineMonitorDetail, recentData, pastData);
        return tenantLineMonitorDetail;
    }

    private List<LineSecStatisticEntity> getSupplyLineCallDurationSecDistribution(String startTime, String endTime, String supplyLineNumber) {
        List<LineSecStatisticEntity> entities = callRecordStatisticClickService.statisticSupplyLineDurationGroupBySecond(startTime, endTime, supplyLineNumber);
        List<Tuple> tuplesForHumanMachine = callRecordForHumanMachineRepository.statisticSupplyLineDurationGroupBySecond(startTime, endTime, supplyLineNumber);
        List<Tuple> tuplesForManualDirect = callRecordForManualDirectRepository.statisticSupplyLineDurationGroupBySecond(startTime, endTime, supplyLineNumber);

        List<LineSecStatisticEntity> entitiesForHumanMachine = JpaResultUtils.processResult(tuplesForHumanMachine, LineSecStatisticEntity.class);
        List<LineSecStatisticEntity> entitiesForManualDirect = JpaResultUtils.processResult(tuplesForManualDirect, LineSecStatisticEntity.class);

        int countUpSecondForPureAI = callRecordStatisticClickService.statisticSupplyLineDurationUpSecond(startTime, endTime, supplyLineNumber);
        int countUpSecondForHumanMachine = callRecordForHumanMachineRepository.statisticSupplyLineDurationUpSecond(startTime, endTime, supplyLineNumber);
        int countUpSecondForManualDirect = callRecordForManualDirectRepository.statisticSupplyLineDurationUpSecond(startTime, endTime, supplyLineNumber);

        return getLineSecStatisticEntities(entities, entitiesForHumanMachine, entitiesForManualDirect,
                countUpSecondForPureAI, countUpSecondForHumanMachine, countUpSecondForManualDirect);
    }

    private List<LineSecStatisticEntity> getTenantLineCallDurationSecDistribution(String startTime, String endTime, String tenantLineNumber) {
        List<LineSecStatisticEntity> entities = callRecordStatisticClickService.statisticTenantLineDurationGroupBySecond(startTime, endTime, tenantLineNumber);
        List<Tuple> tuplesForHumanMachine = callRecordForHumanMachineRepository.statisticTenantLineDurationGroupBySecond(startTime, endTime, tenantLineNumber);
        List<Tuple> tuplesForManualDirect = callRecordForManualDirectRepository.statisticTenantLineDurationGroupBySecond(startTime, endTime, tenantLineNumber);

        List<LineSecStatisticEntity> entitiesForHumanMachine = JpaResultUtils.processResult(tuplesForHumanMachine, LineSecStatisticEntity.class);
        List<LineSecStatisticEntity> entitiesForManualDirect = JpaResultUtils.processResult(tuplesForManualDirect, LineSecStatisticEntity.class);

        int countUpSecondForPureAI = callRecordStatisticClickService.statisticTenantLineDurationUpSecond(startTime, endTime, tenantLineNumber);
        int countUpSecondForHumanMachine = callRecordForHumanMachineRepository.statisticTenantLineDurationUpSecond(startTime, endTime, tenantLineNumber);
        int countUpSecondForManualDirect = callRecordForManualDirectRepository.statisticTenantLineDurationUpSecond(startTime, endTime, tenantLineNumber);

        return getLineSecStatisticEntities(entities, entitiesForHumanMachine, entitiesForManualDirect,
                countUpSecondForPureAI, countUpSecondForHumanMachine, countUpSecondForManualDirect);
    }

    private List<LineSecStatisticEntity> getLineSecStatisticEntities(List<LineSecStatisticEntity> entitiesForPureAI,
                                                                     List<LineSecStatisticEntity> entitiesForHumanMachine,
                                                                     List<LineSecStatisticEntity> entitiesForManualDirect,
                                                                     int countUpSecondForPureAI,
                                                                     int countUpSecondForHumanMachine,
                                                                     int countUpSecondForManualDirect) {
        int sum = 0;
        sum += entitiesForPureAI.stream().mapToInt(LineSecStatisticEntity::getCallNum).sum();
        sum += entitiesForHumanMachine.stream().mapToInt(LineSecStatisticEntity::getCallNum).sum();
        sum += entitiesForManualDirect.stream().mapToInt(LineSecStatisticEntity::getCallNum).sum();
        sum += countUpSecondForPureAI + countUpSecondForHumanMachine + countUpSecondForManualDirect;

        Map<Integer, LineSecStatisticEntity> resultMap = new HashMap<>();
        for (LineSecStatisticEntity entity : entitiesForPureAI) {
            resultMap.put(entity.getSecond(), entity);
        }

        if (entitiesForHumanMachine.size() > 0) {
            for (LineSecStatisticEntity entity : entitiesForHumanMachine) {
                if (resultMap.containsKey(entity.getSecond())) {
                    resultMap.get(entity.getSecond()).setCallNum(resultMap.get(entity.getSecond()).getCallNum() + entity.getCallNum());
                } else {
                    resultMap.put(entity.getSecond(), entity);
                }
            }
        }

        if (entitiesForManualDirect.size() > 0) {
            for (LineSecStatisticEntity entity : entitiesForManualDirect) {
                if (resultMap.containsKey(entity.getSecond())) {
                    resultMap.get(entity.getSecond()).setCallNum(resultMap.get(entity.getSecond()).getCallNum() + entity.getCallNum());
                } else {
                    resultMap.put(entity.getSecond(), entity);
                }
            }
        }
        List<LineSecStatisticEntity> values = new ArrayList<>(resultMap.values());
        for (LineSecStatisticEntity value : values) {
            value.setProportion(calculateProportion(value.getCallNum(), sum));
        }

        LineSecStatisticEntity entity = new LineSecStatisticEntity();
        if (countUpSecondForPureAI + countUpSecondForHumanMachine + countUpSecondForManualDirect != 0) {
            entity.setSecond(71);
            entity.setCallNum(countUpSecondForPureAI);
            entity.setProportion(calculateProportion(countUpSecondForPureAI, sum));
            values.add(entity);
        }
        return values;
    }

    private String calculateProportion(int callNum, int sum) {
        if (sum == 0) {
            return "0.00%";
        }
        return String.format("%.2f", callNum * 1.0 / sum * 100) + "%";
    }
}
