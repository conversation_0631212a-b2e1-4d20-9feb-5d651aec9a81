package com.raipeng.aimonitor.service;

import com.raipeng.aidatacommon.model.AIOutboundTask;
import com.raipeng.aimonitor.context.AIContext;
import com.raipeng.aimonitor.controller.response.ImportAIOutboundQueryDto;
import com.raipeng.aimonitor.controller.response.ImportAIOutboundTaskOutputCityDto;
import com.raipeng.aimonitor.controller.response.ImportAIOutboundTaskOutputDto;
import com.raipeng.aimonitor.controller.response.TaskClassRatioDto;
import com.raipeng.aimonitor.entity.CallRecordStat;
import com.raipeng.aimonitor.model.CallRecordStatisticUnitDataDto;
import com.raipeng.aimonitor.repository.*;
import com.raipeng.aimonitor.repositorymulti.a.CallRecordAHistoryRepository;
import com.raipeng.aimonitor.repositorymulti.a.CallRecordARepository;
import com.raipeng.aimonitor.repositorymulti.b.CallRecordBHistoryRepository;
import com.raipeng.aimonitor.repositorymulti.b.CallRecordBRepository;
import com.raipeng.aimonitor.repositorymulti.c.CallRecordCHistoryRepository;
import com.raipeng.aimonitor.repositorymulti.c.CallRecordCRepository;
import com.raipeng.aimonitor.service.clickhouse.CallRecordStatusUnitClickService;
import com.raipeng.common.enums.AIOutboundTaskType;
import com.raipeng.common.util.JpaResultUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.Tuple;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class TaskService {

    @Autowired
    private SmsRecordRepository smsRecordRepository;

    @Autowired
    private SmsRecordHistoryRepository smsRecordHistoryRepository;

    @Autowired
    private TaskStatisticService taskStatisticService;
    @Autowired
    private CallRecordStatusUnitClickService callRecordStatusUnitClickService;

    @Autowired
    private AIOutboundTaskRepository aiOutboundTaskRepository;
    @Autowired
    private CallRecordARepository callRecordARepository;
    @Autowired
    private CallRecordBRepository callRecordBRepository;
    @Autowired
    private CallRecordCRepository callRecordCRepository;
    @Autowired
    private CallRecordAHistoryRepository callRecordAHistoryRepository;
    @Autowired
    private CallRecordBHistoryRepository callRecordBHistoryRepository;
    @Autowired
    private CallRecordCHistoryRepository callRecordCHistoryRepository;
    @Autowired
    private CallRecordForHumanMachineRepository callRecordForHumanMachineRepository;
    @Autowired
    private CallRecordForHumanMachineHistoryRepository callRecordForHumanMachineHistoryRepository;
    @Autowired
    private PhoneRecordHistoryRepository phoneRecordHistoryRepository;
    @Autowired
    private PhoneRecordRepository phoneRecordRepository;


    public List<ImportAIOutboundTaskOutputDto> preImportTaskStatisticList(ImportAIOutboundQueryDto aiOutboundQueryDto) {
        List<AIOutboundTask> aiOutboundTasks = aiOutboundTaskRepository.findAllByIdIn(aiOutboundQueryDto.getTaskIdList());
        // 按照日期进行判断
        Map<String, List<ImportAIOutboundTaskOutputDto>> importTaskMap = new HashMap<>();
        for (AIOutboundTask aiOutboundTask : aiOutboundTasks) {
            ImportAIOutboundTaskOutputDto importAIOutboundTaskOutputDto = new ImportAIOutboundTaskOutputDto();
            BeanUtils.copyProperties(aiOutboundTask, importAIOutboundTaskOutputDto);
            LocalDateTime createTime = aiOutboundTask.getCreateTime();
            LocalDate localDate = createTime.toLocalDate();
            String localDateString = localDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            List<ImportAIOutboundTaskOutputDto> importAIOutboundTaskOutputDtoList = importTaskMap.getOrDefault(localDateString, new ArrayList<>());
            importAIOutboundTaskOutputDtoList.add(importAIOutboundTaskOutputDto);
            importTaskMap.put(localDateString, importAIOutboundTaskOutputDtoList);
        }

        List<ImportAIOutboundTaskOutputDto> returnList = new ArrayList<>();
        importTaskMap.forEach((key, value) -> {
            fillTaskStatisticList(key, aiOutboundQueryDto.getTaskType(), value);
            returnList.addAll(value);
        });
        return returnList;
    }

    public void fillTaskStatisticList(String queryDate, AIOutboundTaskType taskType, List<ImportAIOutboundTaskOutputDto> importAIOutboundTaskOutputDtoList) {
        List<Long> taskIdList = importAIOutboundTaskOutputDtoList.stream().map(ImportAIOutboundTaskOutputDto::getId).collect(Collectors.toList());
        String now = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate queryDatetime = LocalDate.parse(queryDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDateTime queryStart = queryDatetime.atStartOfDay();
        LocalDateTime queryEndTime = queryDatetime.atStartOfDay().plusDays(1L);
        String start = queryStart.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String end = queryEndTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        Map<String, ImportAIOutboundTaskOutputDto> taskMap = importAIOutboundTaskOutputDtoList.stream()
                .collect(Collectors.toMap(task -> String.valueOf(task.getId()), task -> task));
        List<Tuple> messageRecordStatisticByGroupId = new ArrayList<>();

        boolean isDailySearch = queryDate.equals(now);
        if (isDailySearch) {
            messageRecordStatisticByGroupId = smsRecordRepository.findMessageRecordStatisticByGroupId(AIContext.getGroupId());
        } else {
            messageRecordStatisticByGroupId = smsRecordHistoryRepository.findMessageRecordStatisticByGroupId(start, end, AIContext.getGroupId());
        }
        Map<String, List<TaskClassRatioDto>> stringListMap = taskStatisticService.classRatioMap(taskIdList, queryDatetime);

        String searchTable = "aiSpeech.call_record_statistics_unit_data";
        if (!isDailySearch) {
            searchTable = "aiSpeech.call_record_statistics_unit_all_data";
        }
        List<CallRecordStatisticUnitDataDto> callDurationList = callRecordStatusUnitClickService.findCallDurationByTaskIdList(taskIdList,queryDate, searchTable);
        //增加任务的
        Map<String, CallRecordStatisticUnitDataDto> callDurationMap = callDurationList.stream().collect(Collectors.toMap(CallRecordStatisticUnitDataDto::getTaskId, Function.identity(), (v1, v2) -> v1));

        for (Tuple record : messageRecordStatisticByGroupId) {
            BigInteger totalCountBigInt = (BigInteger) record.get("totalCount");
            Integer totalCount = totalCountBigInt != null ? totalCountBigInt.intValue() : 0;
            BigInteger successCountBigInt = (BigInteger) record.get("successCount");
            Integer successCount = successCountBigInt != null ? successCountBigInt.intValue() : 0;
            String key = (String) record.get("taskId");
            ImportAIOutboundTaskOutputDto aiOutboundTask = taskMap.get(key);
            if (aiOutboundTask != null) {
                aiOutboundTask.setSendSmsNumber(successCount);
                aiOutboundTask.setTriggerSmsNumber(totalCount);
                taskMap.put(key, aiOutboundTask);
            }
        }

        List<String> taskStringIdList = taskIdList.stream().map(a -> a.toString()).collect(Collectors.toList());
        List<Tuple> billingDurationList = new ArrayList<>();
        if (AIOutboundTaskType.AI_AUTO.equals(taskType)) {
            if (isDailySearch) {
                billingDurationList = callRecordARepository.findOneDayCallRecordsForTask(taskStringIdList);
                billingDurationList.addAll(callRecordBRepository.findOneDayCallRecordsForTask(taskStringIdList));
                billingDurationList.addAll(callRecordCRepository.findOneDayCallRecordsForTask(taskStringIdList));
            } else {
                billingDurationList = callRecordAHistoryRepository.findOneDayCallRecordsForTask(taskStringIdList);
                billingDurationList.addAll(callRecordBHistoryRepository.findOneDayCallRecordsForTask(taskStringIdList));
                billingDurationList.addAll(callRecordCHistoryRepository.findOneDayCallRecordsForTask(taskStringIdList));
            }

        } else {
            if (isDailySearch) {
                billingDurationList = callRecordForHumanMachineRepository.findOneDayCallRecordsForTask(taskStringIdList);
            } else {
                billingDurationList = callRecordForHumanMachineHistoryRepository.findOneDayCallRecordsForTask(taskStringIdList);
            }
        }

        List<CallRecordStat> callRecordBillingDurationList = JpaResultUtils.processResult(billingDurationList, CallRecordStat.class);

        HashMap<String, Integer> billingDurationMap = new HashMap();
        for (CallRecordStat callRecordStat : callRecordBillingDurationList) {
            Integer orDefault = billingDurationMap.getOrDefault(callRecordStat.getTaskId(), 0);
            int v = orDefault + callRecordStat.getCalculateNumOfSixty();
            billingDurationMap.put(callRecordStat.getTaskId(), v);
        }

        taskMap.forEach((key, value) -> {
            Long id = value.getId();
            value.setClassList(stringListMap.getOrDefault(id.toString(), new ArrayList<>()));
            CallRecordStatisticUnitDataDto orDefault = callDurationMap.getOrDefault(id.toString(), new CallRecordStatisticUnitDataDto());
            Long totalCallDuration = orDefault.getTotalCallDuration() == null ? 0 : orDefault.getTotalCallDuration();
            Integer totalConnectNum = orDefault.getTotalConnectNum() == null ? 0 : orDefault.getTotalConnectNum();
            int averageDuration = totalConnectNum == 0 ? 0 : Math.toIntExact(totalCallDuration / totalConnectNum);
            value.setTotalConnectNum(totalConnectNum);
            value.setTotalCallDurationNum(totalCallDuration);
            value.setAverageDuration(averageDuration);
            value.setBillingDuration(billingDurationMap.getOrDefault(id.toString(), 0));
        });
    }

    public List<ImportAIOutboundTaskOutputCityDto> importTaskOperatorCityList(ImportAIOutboundQueryDto aiOutboundQueryDto) {
        LocalDateTime now = LocalDate.now().atStartOfDay();
        Optional<AIOutboundTask> byId = aiOutboundTaskRepository.findById(aiOutboundQueryDto.getTaskId());
        if (!byId.isPresent()) {
            return new ArrayList<>();
        }
        AIOutboundTask aiOutboundTask = byId.get();
        List<Tuple> taskStatisticList = new ArrayList<>();
        if (aiOutboundTask.getCreateTime().isBefore(now)) {
            taskStatisticList = phoneRecordHistoryRepository.findStatisticByTaskId(aiOutboundQueryDto.getTaskId().toString());
        } else {
            taskStatisticList = phoneRecordRepository.findStatisticByTaskId(aiOutboundQueryDto.getTaskId().toString());
        }
        return JpaResultUtils.processResult(taskStatisticList, ImportAIOutboundTaskOutputCityDto.class);
    }
}
