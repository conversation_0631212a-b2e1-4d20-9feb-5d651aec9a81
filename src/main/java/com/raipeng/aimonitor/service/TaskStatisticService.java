package com.raipeng.aimonitor.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.raipeng.aidatacommon.model.Admin;
import com.raipeng.aidatacommon.model.CallSetting;
import com.raipeng.aimonitor.controller.request.StatisticRequestParam;
import com.raipeng.aimonitor.controller.response.*;
import com.raipeng.aimonitor.enums.CallRecordCharDataType;
import com.raipeng.aimonitor.model.CallRecordStatisticUnitDataDto;
import com.raipeng.aimonitor.model.IntentionLabelCount;
import com.raipeng.aimonitor.model.PhoneRecordStatisticDataDto;
import com.raipeng.aimonitor.repository.AdminRepository;
import com.raipeng.aimonitor.repository.CallSettingRepository;
import com.raipeng.aimonitor.repository.SmsRecordHistoryRepository;
import com.raipeng.aimonitor.repository.SmsRecordRepository;
import com.raipeng.aimonitor.service.clickhouse.CallRecordStatusUnitClickService;
import com.raipeng.aimonitor.service.clickhouse.PhoneRecordClickService;
import com.raipeng.aimonitor.utils.CallLineUtil;
import com.raipeng.common.entity.statistics.FunnelChartPojo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.raipeng.aimonitor.constant.CommonConstant.MOMENT_SCAN_SPLIT_MINUTES;

@RefreshScope
@Slf4j
@Service
public class TaskStatisticService {
    @Autowired
    private CallRecordStatusUnitClickService callRecordStatusUnitClickService;
    @Autowired
    private PhoneRecordClickService phoneRecordClickService;
    @Autowired
    private CallSettingRepository callSettingRepository;
    @Autowired
    private SmsRecordRepository smsRecordRepository;
    @Autowired
    private SmsRecordHistoryRepository smsRecordHistoryRepository;
    @Autowired
    private AdminRepository adminRepository;
    @Autowired
    private RedissonClient redissonClient;
    public static final String REDIS_STATISTIC_TMP = "REDIS_STATISTIC_TMP::";

    public TaskFunnelChartDto finTaskFunnelChartFromRedis(StatisticRequestParam statisticRequestParam) {
        String groupId = statisticRequestParam.getGroupId();
        Admin adminById = adminRepository.findFirstById(Long.valueOf(groupId.split("_")[2]));
        statisticRequestParam.setAccount(adminById.getAccount());

        List<String> taskIdList = statisticRequestParam.getTaskIdList().stream().map(Object::toString).collect(Collectors.toList());
        LocalDate queryDate = statisticRequestParam.getQueryDate();
        int phoneNum = 0;
        int calledNum = 0;
        int putThroughNum = 0;
        // 没有任务以外的细分的条件时 直接查redis
        if (statisticRequestParam.isScriptOperatorProvinceCityEmpty()) {
            TaskFunnelChartDto taskFunnelChartFromRedis = findTaskFunnelChartFromRedis(queryDate, taskIdList, groupId);
            phoneNum = taskFunnelChartFromRedis.getPhoneNum();
            calledNum = taskFunnelChartFromRedis.getCalledNum();
            putThroughNum = taskFunnelChartFromRedis.getPutThroughNum();
        } else {
            //有任务以外细分条件时查clickhouse
            PhoneRecordStatisticDataDto phoneRecordStatisticDataDto = phoneRecordClickService.finPhoneRecordStatus(statisticRequestParam);
            phoneNum = phoneRecordStatisticDataDto.getTotalCallNum();
            calledNum = phoneRecordStatisticDataDto.getTotalCalledNum();
            putThroughNum = phoneRecordStatisticDataDto.getTotalConnectNum();
        }

        String searchTable = getSearchTableByQueryDate(queryDate);
        CallRecordStatisticUnitDataDto callRecordStatisticUnitDataDto = callRecordStatusUnitClickService.findGroup(statisticRequestParam, searchTable);

        List<String> intentionClass = Arrays.asList("A", "B", "C", "D");
        CallSetting callSetting = callSettingRepository.findByGroupId(groupId);
        if (null != callSetting && StringUtils.isNotBlank(callSetting.getIntentionClass())) {
            intentionClass = Arrays.asList(callSetting.getIntentionClass().split(","));
        }
        int intentionNum = 0;
        for (String className : intentionClass) {
            if ("A".equals(className)) {
                intentionNum += callRecordStatisticUnitDataDto.getClassANum();
            } else if ("B".equals(className)) {
                intentionNum += callRecordStatisticUnitDataDto.getClassBNum();
            } else if ("C".equals(className)) {
                intentionNum += callRecordStatisticUnitDataDto.getClassCNum();
            } else if ("D".equals(className)) {
                intentionNum += callRecordStatisticUnitDataDto.getClassDNum();
            } else if ("E".equals(className)) {
                intentionNum += callRecordStatisticUnitDataDto.getClassENum();
            } else if ("F".equals(className)) {
                intentionNum += callRecordStatisticUnitDataDto.getClassFNum();
            } else if ("G".equals(className)) {
                intentionNum += callRecordStatisticUnitDataDto.getClassGNum();
            }
        }
        BigDecimal putThroughRate = calledNum == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(putThroughNum).divide(BigDecimal.valueOf(calledNum), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
        BigDecimal intentionRates = putThroughNum == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(intentionNum).divide(BigDecimal.valueOf(putThroughNum), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));

        String startTime = queryDate.atStartOfDay().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String endTime = queryDate.atStartOfDay().plusDays(1L).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        int successCount;
        int triggerCount;
        // 临界条件的判断
        LocalDate today = LocalDate.now();
        boolean isDailySearch = today.isEqual(queryDate);
        if (isDailySearch) {
            successCount = smsRecordRepository.findSuccessCountByTaskId(startTime, endTime, taskIdList,
                    statisticRequestParam.getProvinceCode(), statisticRequestParam.getCityCode(), statisticRequestParam.getOperator(),
                    CollectionUtils.isEmpty(statisticRequestParam.getScriptStringIdList()) ? Collections.singletonList("EMPTY") : statisticRequestParam.getScriptStringIdList()
            );
            triggerCount = smsRecordRepository.findTriggerCountByTaskId(startTime, endTime, taskIdList,
                    statisticRequestParam.getProvinceCode(), statisticRequestParam.getCityCode(), statisticRequestParam.getOperator(),
                    CollectionUtils.isEmpty(statisticRequestParam.getScriptStringIdList()) ? Collections.singletonList("EMPTY") : statisticRequestParam.getScriptStringIdList()
            );
        } else {
            successCount = smsRecordHistoryRepository.findSuccessCountByTaskId(startTime, endTime, taskIdList,
                    statisticRequestParam.getProvinceCode(), statisticRequestParam.getCityCode(), statisticRequestParam.getOperator(),
                    CollectionUtils.isEmpty(statisticRequestParam.getScriptStringIdList()) ? Collections.singletonList("EMPTY") : statisticRequestParam.getScriptStringIdList()
            );
            triggerCount = smsRecordHistoryRepository.findTriggerCountByTaskId(startTime, endTime, taskIdList,
                    statisticRequestParam.getProvinceCode(), statisticRequestParam.getCityCode(), statisticRequestParam.getOperator(),
                    CollectionUtils.isEmpty(statisticRequestParam.getScriptStringIdList()) ? Collections.singletonList("EMPTY") : statisticRequestParam.getScriptStringIdList()
            );
        }

        BigDecimal sendSmsRate = triggerCount == 0 ? BigDecimal.ZERO :
                BigDecimal.valueOf(successCount).divide(BigDecimal.valueOf(triggerCount), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));

        TaskFunnelChartDto taskFunnelChartDto = new TaskFunnelChartDto();
        taskFunnelChartDto.setPhoneNum(phoneNum);
        taskFunnelChartDto.setCalledNum(calledNum);
        taskFunnelChartDto.setPutThroughNum(putThroughNum);
        taskFunnelChartDto.setPutThroughRate(putThroughRate);
        taskFunnelChartDto.setIntentionNum(intentionNum);
        taskFunnelChartDto.setIntentionRate(intentionRates);
        taskFunnelChartDto.setSuccessSmsNum(successCount);
        taskFunnelChartDto.setTriggerSmsNum(triggerCount);
        taskFunnelChartDto.setSuccessSmsRate(sendSmsRate);
        return taskFunnelChartDto;
    }


    public TaskFunnelChartDto findTaskFunnelChartFromRedis(LocalDate queryDate, List<String> taskIdList, String groupId) {
        RMap<String, Map<String, FunnelChartPojo>> groupFunnelChartMap = redissonClient.getMap(REDIS_STATISTIC_TMP + queryDate + ":FUNNEL_CHART_NEW");
        Map<String, FunnelChartPojo> taskIdMap = groupFunnelChartMap.getOrDefault(groupId, new HashMap<>());

        List<FunnelChartPojo> funnelChartPojoList = new ArrayList<>();
        for (String taskId : taskIdList) {
            FunnelChartPojo funnelChartPojo = taskIdMap.getOrDefault(taskId, new FunnelChartPojo());
            funnelChartPojoList.add(funnelChartPojo);
        }
        int phoneNum = funnelChartPojoList.stream().filter(funnelChartPojo -> funnelChartPojo.getPhoneNum() != null)
                .mapToInt(FunnelChartPojo::getPhoneNum).sum();
        int calledNum = funnelChartPojoList.stream().filter(funnelChartPojo -> funnelChartPojo.getCalledNum() != null)
                .mapToInt(FunnelChartPojo::getCalledNum).sum();
        int putThroughNum = funnelChartPojoList.stream().filter(funnelChartPojo -> funnelChartPojo.getPutThroughNum() != null)
                .mapToInt(FunnelChartPojo::getPutThroughNum).sum();
        TaskFunnelChartDto taskFunnelChartDto = new TaskFunnelChartDto();
        taskFunnelChartDto.setPhoneNum(phoneNum);
        taskFunnelChartDto.setCalledNum(calledNum);
        taskFunnelChartDto.setPutThroughNum(putThroughNum);
        return  taskFunnelChartDto;
    }

    public ScriptLabelStatisticResponse getScriptIntentionStatisticList(StatisticRequestParam statisticRequestParam) {
        String groupId = statisticRequestParam.getGroupId();
        Admin adminById = adminRepository.findFirstById(Long.valueOf(groupId.split("_")[2]));
        statisticRequestParam.setAccount(adminById.getAccount());
        String searchTable = getSearchTableByQueryDate(statisticRequestParam.getQueryDate());
        List<String> scriptIntentionStatisticList = callRecordStatusUnitClickService.getScriptIntentionStatisticList(statisticRequestParam, searchTable);
        Map<String, Integer> labelCountMap = new HashMap<>();
        int putThroughNum = callRecordStatusUnitClickService.getScriptIntentionStatisticCount(statisticRequestParam, searchTable);
        for (String scriptIntentionStatistic : scriptIntentionStatisticList) {
            List<IntentionLabelCount> intentionLabelCountPojoList = JSON.parseObject(scriptIntentionStatistic, new TypeReference<List<IntentionLabelCount>>() {
            });
            for (IntentionLabelCount intentionLabelCountPojo : intentionLabelCountPojoList) {
                labelCountMap.put(intentionLabelCountPojo.getName(),
                        labelCountMap.getOrDefault(intentionLabelCountPojo.getName(), 0) + intentionLabelCountPojo.getNum());
            }
        }
        // 将累加后的结果转换为 对象列表
        List<ScriptLabelStatistic> resultList = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : labelCountMap.entrySet()) {
            ScriptLabelStatistic resultStatistic = new ScriptLabelStatistic();
            resultStatistic.setIntentionLabelName(entry.getKey());
            resultStatistic.setIntentionLabelNum(entry.getValue());
            resultStatistic.setLabelHitRates(putThroughNum == 0 ? BigDecimal.ZERO :
                    BigDecimal.valueOf(resultStatistic.getIntentionLabelNum())
                            .divide(BigDecimal.valueOf(putThroughNum), 4, RoundingMode.HALF_UP)
                            .multiply(BigDecimal.valueOf(100)));
            resultList.add(resultStatistic);
        }
        ScriptLabelStatisticResponse response = new ScriptLabelStatisticResponse();
        response.setLabelList(resultList);
        response.setPutThroughNum(putThroughNum);
        return response;
    }

    public ScriptIntentionClassStatisticResponse getScriptIntentionClassStatisticList(StatisticRequestParam statisticRequestParam) {
        String groupId = statisticRequestParam.getGroupId();
        Admin adminById = adminRepository.findFirstById(Long.valueOf(groupId.split("_")[2]));
        statisticRequestParam.setAccount(adminById.getAccount());
        String searchTable = getSearchTableByQueryDate(statisticRequestParam.getQueryDate());
        CallRecordStatisticUnitDataDto dataDto = callRecordStatusUnitClickService.getScriptIntentionClassStatisticList(statisticRequestParam, searchTable);
        ScriptIntentionClassStatisticResponse response = new ScriptIntentionClassStatisticResponse();
        BeanUtils.copyProperties(dataDto, response);
        response.setPutThroughNum(dataDto.getTotalConnectNum());
        response.setClassOtherNum(dataDto.getClassOtherNum());
        return response;
    }

    private static String getSearchTableByQueryDate(LocalDate statisticRequestParam) {
        LocalDate today = LocalDate.now();
        boolean isDailySearch = today.isEqual(statisticRequestParam);
        String searchTable = "aiSpeech.call_record_statistics_unit_data";
        if (!isDailySearch) {
            searchTable = "aiSpeech.call_record_statistics_unit_all_data";
        }
        return searchTable;
    }


    public List<TaskClassRatioDto> classRatio(StatisticRequestParam statisticRequestParam) {
        if (CollectionUtils.isEmpty(statisticRequestParam.getTaskIdList())) {
            return new ArrayList<>();
        }
        String groupId = statisticRequestParam.getGroupId();
        Admin adminById = adminRepository.findFirstById(Long.valueOf(groupId.split("_")[2]));
        statisticRequestParam.setAccount(adminById.getAccount());
        LocalDate today = LocalDate.now();
        boolean isDailySearch = today.isEqual(statisticRequestParam.getQueryDate());
        String searchTable = "aiSpeech.call_record_statistics_unit_data";
        if (!isDailySearch) {
            searchTable = "aiSpeech.call_record_statistics_unit_all_data";
        }
        CallRecordStatisticUnitDataDto callRecordStatisticUnitDataDto = callRecordStatusUnitClickService.findClassRatio(statisticRequestParam, searchTable);
        int totalNum = callRecordStatisticUnitDataDto.getTotalConnectNum();

        List<String> intentionClass = Arrays.asList("A", "B", "C", "D", "E", "F", "G", "其他");
        List<TaskClassRatioDto> taskClassRatioDtos = new ArrayList<>();
        intentionClass.forEach(className -> {
            TaskClassRatioDto taskClassRatioDto = new TaskClassRatioDto();
            taskClassRatioDto.setClassName(className);
            //过滤意向数量为0的分类
            if ("A".equals(className)) {
                taskClassRatioDto.setNum(callRecordStatisticUnitDataDto.getClassANum());
            } else if ("B".equals(className)) {
                taskClassRatioDto.setNum(callRecordStatisticUnitDataDto.getClassBNum());
            } else if ("C".equals(className)) {
                taskClassRatioDto.setNum(callRecordStatisticUnitDataDto.getClassCNum());
            } else if ("D".equals(className)) {
                taskClassRatioDto.setNum(callRecordStatisticUnitDataDto.getClassDNum());
            } else if ("E".equals(className)) {
                taskClassRatioDto.setNum(callRecordStatisticUnitDataDto.getClassENum());
            } else if ("F".equals(className)) {
                taskClassRatioDto.setNum(callRecordStatisticUnitDataDto.getClassFNum());
            } else if ("G".equals(className)) {
                taskClassRatioDto.setNum(callRecordStatisticUnitDataDto.getClassGNum());
            } else if ("其他".equals(className)) {
                taskClassRatioDto.setNum(callRecordStatisticUnitDataDto.getClassOtherNum());
            } else {
                taskClassRatioDto.setNum(0);
            }
            taskClassRatioDto.setRatio(totalNum == 0 ? BigDecimal.ZERO
                    : BigDecimal.valueOf(taskClassRatioDto.getNum()).divide(BigDecimal.valueOf(totalNum), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
            taskClassRatioDtos.add(taskClassRatioDto);
        });
        return taskClassRatioDtos;
    }

    public CallRecordStatisticUnitDataResponse getDataRateChartToday(StatisticRequestParam statisticRequestParam) {
        String groupId = statisticRequestParam.getGroupId();
        Admin adminById = adminRepository.findFirstById(Long.valueOf(groupId.split("_")[2]));
        statisticRequestParam.setAccount(adminById.getAccount());
        LocalDate today = LocalDate.now();
        boolean isDailySearch = today.isEqual(statisticRequestParam.getQueryDate());
        String searchTable = "aiSpeech.call_record_statistics_unit_data";
        if (!isDailySearch) {
            searchTable = "aiSpeech.call_record_statistics_unit_all_data";
        }
        CallRecordStatisticUnitDataDto data = callRecordStatusUnitClickService.getDataRateChartTodayData(statisticRequestParam, searchTable);
        CallRecordStatisticUnitDataResponse res = new CallRecordStatisticUnitDataResponse();
        BeanUtils.copyProperties(data, res);
        List<String> intentionClassList = Arrays.asList("A", "B", "C", "D");
        CallSetting callSetting = callSettingRepository.findByGroupId(groupId);
        if (null != callSetting && StringUtils.isNotBlank(callSetting.getIntentionClass())) {
            intentionClassList = Arrays.asList(callSetting.getIntentionClass().split(","));
        }
        int num = 0;
        for (String className : intentionClassList) {
            if ("A".equals(className)) {
                num += data.getClassANum();
            } else if ("B".equals(className)) {
                num += data.getClassBNum();
            } else if ("C".equals(className)) {
                num += data.getClassCNum();
            } else if ("D".equals(className)) {
                num += data.getClassDNum();
            } else if ("E".equals(className)) {
                num += data.getClassENum();
            } else if ("F".equals(className)) {
                num += data.getClassFNum();
            } else if ("G".equals(className)) {
                num += data.getClassGNum();
            }
        }
        res.setTwoSecondConnectedNum(data.getOneSecondConnectedNum() + data.getTwoSecondConnectedNum());
        res.setIntentionNum(num);
        return res;
    }

    public Map<String, UnitDateResponse> getRateChart(StatisticRequestParam statisticRequestParam) {
        String groupId = statisticRequestParam.getGroupId();
        Admin adminById = adminRepository.findFirstById(Long.valueOf(groupId.split("_")[2]));
        statisticRequestParam.setAccount(adminById.getAccount());
        LocalDate today = LocalDate.now();
        boolean isDailySearch = today.isEqual(statisticRequestParam.getQueryDate());
        String searchTable = "aiSpeech.call_record_statistics_unit_data";
        if (!isDailySearch) {
            searchTable = "aiSpeech.call_record_statistics_unit_all_data";
        }
        List<CallRecordStatisticUnitDataDto> dataList = callRecordStatusUnitClickService.getRateChartDataList(statisticRequestParam, searchTable);
        List<String> intentionClass = Arrays.asList("A", "B", "C", "D");
        CallSetting callSetting = callSettingRepository.findByGroupId(groupId);
        if (null != callSetting && StringUtils.isNotBlank(callSetting.getIntentionClass())) {
            intentionClass = Arrays.asList(callSetting.getIntentionClass().split(","));
        }
        return getRateDataTodayMap(dataList, statisticRequestParam.getType(), statisticRequestParam.getTimeGap(), intentionClass);
    }

    public Map<String, UnitDateResponse> getCountryChart(StatisticRequestParam statisticRequestParam) {
        LocalDate today = LocalDate.now();
        boolean isDailySearch = today.isEqual(statisticRequestParam.getQueryDate());
        String searchTable = "aiSpeech.call_record_statistics_unit_data";
        if (!isDailySearch) {
            searchTable = "aiSpeech.call_record_statistics_unit_all_data";
        }
        String groupId = statisticRequestParam.getGroupId();
        Admin adminById = adminRepository.findFirstById(Long.valueOf(groupId.split("_")[2]));
        statisticRequestParam.setAccount(adminById.getAccount());
        List<CallRecordStatisticUnitDataDto> dataList = callRecordStatusUnitClickService.getCountryChartDataList(statisticRequestParam, searchTable);
        List<String> intentionClass = Arrays.asList("A", "B", "C", "D");
        CallSetting callSetting = callSettingRepository.findByGroupId(groupId);
        if (null != callSetting && StringUtils.isNotBlank(callSetting.getIntentionClass())) {
            intentionClass = Arrays.asList(callSetting.getIntentionClass().split(","));
        }
        return getProvinceMap(dataList, statisticRequestParam.getType(), intentionClass);
    }

    public Map<String, UnitDateResponse> getProvinceChart(StatisticRequestParam statisticRequestParam) {
        LocalDate today = LocalDate.now();
        boolean isDailySearch = today.isEqual(statisticRequestParam.getQueryDate());
        String searchTable = "aiSpeech.call_record_statistics_unit_data";
        if (!isDailySearch) {
            searchTable = "aiSpeech.call_record_statistics_unit_all_data";
        }
        String groupId = statisticRequestParam.getGroupId();
        Admin adminById = adminRepository.findFirstById(Long.valueOf(groupId.split("_")[2]));
        statisticRequestParam.setAccount(adminById.getAccount());
        List<CallRecordStatisticUnitDataDto> dataList = callRecordStatusUnitClickService.getProvinceChartDataList(statisticRequestParam, searchTable);
        List<String> intentionClass = Arrays.asList("A", "B", "C", "D");
        CallSetting callSetting = callSettingRepository.findByGroupId(groupId);
        if (null != callSetting && StringUtils.isNotBlank(callSetting.getIntentionClass())) {
            intentionClass = Arrays.asList(callSetting.getIntentionClass().split(","));
        }
        return getCityMap(dataList, statisticRequestParam.getType(), intentionClass);
    }


    private Map<String, UnitDateResponse> getRateDataTodayMap(List<CallRecordStatisticUnitDataDto> dataList, CallRecordCharDataType type, int size, List<String> intentionClassList) {
        Map<String, String> timeSlotMap = getTimeSlotMap(size);
        Map<String, UnitDateResponse> resultMap = new HashMap<>();
        for (CallRecordStatisticUnitDataDto data : dataList) {
            String key = timeSlotMap.get(Integer.toString(data.getTimeSlot()));
            if (key != null) {
                UnitDateResponse unitData = resultMap.computeIfAbsent(key, k -> new UnitDateResponse());
                collectNum(unitData, data, type, intentionClassList);
            }
        }
        return resultMap;
    }


    private Map<String, UnitDateResponse> getProvinceMap(List<CallRecordStatisticUnitDataDto> dataList, CallRecordCharDataType type, List<String> intentionClassList) {
        Map<String, UnitDateResponse> provinceMap = new HashMap<>();
        for (CallRecordStatisticUnitDataDto data : dataList) {
            UnitDateResponse unitData = provinceMap.computeIfAbsent(data.getProvince(), k -> new UnitDateResponse());
            collectNum(unitData, data, type, intentionClassList);
//            unitData.setTotal(unitData.getTotal() + data.getTotalConnectNum());
        }
        return provinceMap;
    }

    private Map<String, UnitDateResponse> getCityMap(List<CallRecordStatisticUnitDataDto> dataList, CallRecordCharDataType type, List<String> intentionClassList) {
        Map<String, UnitDateResponse> cityMap = new HashMap<>();
        for (CallRecordStatisticUnitDataDto data : dataList) {
            UnitDateResponse unitData = cityMap.computeIfAbsent(data.getCity(), k -> new UnitDateResponse());
            collectNum(unitData, data, type, intentionClassList);
//            unitData.setTotal(unitData.getTotal() + data.getTotalConnectNum());
        }
        return cityMap;
    }


    private static Map<String, String> getTimeSlotMap(int size) {
        Map<String, String> timeSlotMap = new HashMap<>();
        int endMinutes = 22 * 60;
        int startMinutes = 8 * 60;
        while (startMinutes + size <= endMinutes) {
            String startTime = CallLineUtil.minuteConvertLocalDateTime(startMinutes, LocalDateTime.now()).toString().substring(11, 16);
            String endTime = CallLineUtil.minuteConvertLocalDateTime(startMinutes + size, LocalDateTime.now()).toString().substring(11, 16);
            String value = startTime + "-" + endTime;

            for (int i = startMinutes; i < startMinutes + size; i += MOMENT_SCAN_SPLIT_MINUTES) {
                timeSlotMap.put(Integer.toString(i), value);
            }
            startMinutes += size;
        }
        return timeSlotMap;
    }


    private void collectNum(UnitDateResponse unitData, CallRecordStatisticUnitDataDto data, CallRecordCharDataType type, List<String> intentionClassList) {
        switch (type) {
            case CONNECT:
                unitData.setNum(unitData.getNum() + data.getTotalConnectNum());
                unitData.setTotal(unitData.getTotal() + data.getTotalCallNum());
                break;
            case INTENTION:
                int num = 0;
                for (String className : intentionClassList) {
                    if ("A".equals(className)) {
                        num += data.getClassANum();
                    } else if ("B".equals(className)) {
                        num += data.getClassBNum();
                    } else if ("C".equals(className)) {
                        num += data.getClassCNum();
                    } else if ("D".equals(className)) {
                        num += data.getClassDNum();
                    } else if ("E".equals(className)) {
                        num += data.getClassENum();
                    } else if ("F".equals(className)) {
                        num += data.getClassFNum();
                    } else if ("G".equals(className)) {
                        num += data.getClassGNum();
                    }
                }
                unitData.setNum(unitData.getNum() + num);
                unitData.setTotal(unitData.getTotal() + data.getTotalCallNum());
                break;
            case SILENCE:
                unitData.setNum(unitData.getNum() + data.getSilenceCallNum());
                unitData.setTotal(unitData.getTotal() + data.getTotalConnectNum());
                break;
            case ONE_SECOND:
                unitData.setNum(unitData.getNum() + data.getOneSecondConnectedNum());
                unitData.setTotal(unitData.getTotal() + data.getTotalConnectNum());

                break;
            case TWO_SECOND:
                unitData.setNum(unitData.getNum() + data.getOneSecondConnectedNum() + data.getTwoSecondConnectedNum());
                unitData.setTotal(unitData.getTotal() + data.getTotalConnectNum());

                break;
            case SILENCE_HANGUP:
                unitData.setNum(unitData.getNum() + data.getSilenceHangupNum());
                unitData.setTotal(unitData.getTotal() + data.getTotalConnectNum());

                break;
            case ASSISTANT:
                unitData.setNum(unitData.getNum() + data.getAssistantNum());
                unitData.setTotal(unitData.getTotal() + data.getTotalConnectNum());

                break;
            case CLASS_A:
                unitData.setNum(unitData.getNum() + data.getClassANum());
                unitData.setTotal(unitData.getTotal() + data.getTotalConnectNum());

                break;
            case CLASS_B:
                unitData.setNum(unitData.getNum() + data.getClassBNum());
                unitData.setTotal(unitData.getTotal() + data.getTotalConnectNum());

                break;
            case CLASS_C:
                unitData.setNum(unitData.getNum() + data.getClassCNum());
                unitData.setTotal(unitData.getTotal() + data.getTotalConnectNum());

                break;
            case CLASS_D:
                unitData.setNum(unitData.getNum() + data.getClassDNum());
                unitData.setTotal(unitData.getTotal() + data.getTotalConnectNum());

                break;
            case CLASS_E:
                unitData.setNum(unitData.getNum() + data.getClassENum());
                unitData.setTotal(unitData.getTotal() + data.getTotalConnectNum());

                break;
            case CLASS_F:
                unitData.setNum(unitData.getNum() + data.getClassFNum());
                unitData.setTotal(unitData.getTotal() + data.getTotalConnectNum());

                break;
            case CLASS_G:
                unitData.setNum(unitData.getNum() + data.getClassGNum());
                unitData.setTotal(unitData.getTotal() + data.getTotalConnectNum());

                break;
            case CLASS_OTHER:
                unitData.setNum(unitData.getNum() + data.getClassOtherNum());
                unitData.setTotal(unitData.getTotal() + data.getTotalConnectNum());
                break;
            case CLASS_ALL_DURATION:
                unitData.setNum(unitData.getNum() + data.getTotalCallDuration());
                unitData.setTotal(unitData.getTotal() + data.getTotalConnectNum());

                break;
            case CLASS_A_DURATION:
                unitData.setTotal(unitData.getTotal() + data.getClassANum());
                unitData.setNum(unitData.getNum() + data.getClassACallDuration());
                break;
            case CLASS_B_DURATION:
                unitData.setTotal(unitData.getTotal() + data.getClassBNum());
                unitData.setNum(unitData.getNum() + data.getClassBCallDuration());
                break;
            case CLASS_C_DURATION:
                unitData.setTotal(unitData.getTotal() + data.getClassCNum());
                unitData.setNum(unitData.getNum() + data.getClassCCallDuration());
                break;
            case CLASS_D_DURATION:
                unitData.setTotal(unitData.getTotal() + data.getClassDNum());
                unitData.setNum(unitData.getNum() + data.getClassDCallDuration());
                break;
            case CLASS_E_DURATION:
                unitData.setTotal(unitData.getTotal() + data.getClassENum());
                unitData.setNum(unitData.getNum() + data.getClassECallDuration());
                break;
            case CLASS_F_DURATION:
                unitData.setTotal(unitData.getTotal() + data.getClassFNum());
                unitData.setNum(unitData.getNum() + data.getClassFCallDuration());
                break;
            case CLASS_G_DURATION:
                unitData.setTotal(unitData.getTotal() + data.getClassGNum());
                unitData.setNum(unitData.getNum() + data.getClassGCallDuration());
                break;
            case CLASS_OTHER_DURATION:
                unitData.setTotal(unitData.getTotal() + data.getClassOtherNum());
                unitData.setNum(unitData.getNum() + data.getClassOtherCallDuration());
                break;
            default:
                break;
        }
    }


    public Map<String, List<TaskClassRatioDto>> classRatioMap(List<Long> taskIdList, LocalDate queryDate) {
        LocalDate today = LocalDate.now();
        boolean isDailySearch = today.isEqual(queryDate);
        String searchTable = "aiSpeech.call_record_statistics_unit_data";
        if (!isDailySearch) {
            searchTable = "aiSpeech.call_record_statistics_unit_all_data";
        }
        List<CallRecordStatisticUnitDataDto> callRecordStatisticUnitDataDtoList = callRecordStatusUnitClickService.findClassRatioByTaskIdList(taskIdList,queryDate, searchTable);

        List<String> intentionClass = Arrays.asList("A", "B", "C", "D", "E", "F", "G", "其他");
        Map<String, List<TaskClassRatioDto>> taskClassRatioDtoMap = new HashMap<>();

        for (CallRecordStatisticUnitDataDto callRecordStatisticUnitDataDto : callRecordStatisticUnitDataDtoList) {
            List<TaskClassRatioDto> taskClassRatioDtos = new ArrayList<>();

            int totalNum = callRecordStatisticUnitDataDto.getTotalConnectNum();

            intentionClass.forEach(className -> {

                TaskClassRatioDto taskClassRatioDto = new TaskClassRatioDto();
                taskClassRatioDto.setClassName(className);
                //过滤意向数量为0的分类
                if ("A".equals(className)) {
                    taskClassRatioDto.setNum(callRecordStatisticUnitDataDto.getClassANum());
                } else if ("B".equals(className)) {
                    taskClassRatioDto.setNum(callRecordStatisticUnitDataDto.getClassBNum());
                } else if ("C".equals(className)) {
                    taskClassRatioDto.setNum(callRecordStatisticUnitDataDto.getClassCNum());
                } else if ("D".equals(className)) {
                    taskClassRatioDto.setNum(callRecordStatisticUnitDataDto.getClassDNum());
                } else if ("E".equals(className)) {
                    taskClassRatioDto.setNum(callRecordStatisticUnitDataDto.getClassENum());
                } else if ("F".equals(className)) {
                    taskClassRatioDto.setNum(callRecordStatisticUnitDataDto.getClassFNum());
                } else if ("G".equals(className)) {
                    taskClassRatioDto.setNum(callRecordStatisticUnitDataDto.getClassGNum());
                } else if ("其他".equals(className)) {
                    taskClassRatioDto.setNum(callRecordStatisticUnitDataDto.getClassOtherNum());
                } else {
                    taskClassRatioDto.setNum(0);
                }
                taskClassRatioDto.setRatio(totalNum == 0 ? BigDecimal.ZERO
                        : BigDecimal.valueOf(taskClassRatioDto.getNum()).divide(BigDecimal.valueOf(totalNum), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
                taskClassRatioDtos.add(taskClassRatioDto);
            });

            taskClassRatioDtoMap.put(callRecordStatisticUnitDataDto.getTaskId(), taskClassRatioDtos);
        }

        return taskClassRatioDtoMap;
    }
}
