package com.raipeng.aimonitor.service;

import com.raipeng.aidatacommon.model.record.CallRecord;
import com.raipeng.aidatacommon.model.record.CallRecordHistory;
import com.raipeng.aimonitor.model.CallLineMonitorUnitData;
import com.raipeng.aimonitor.model.CallRecordStatisticUnitData;
import com.raipeng.aimonitor.model.IntentionLabelCount;
import com.raipeng.aimonitor.repositorymulti.a.CallRecordAHistoryRepository;
import com.raipeng.aimonitor.repositorymulti.a.CallRecordARepository;
import com.raipeng.aimonitor.repositorymulti.b.CallRecordBHistoryRepository;
import com.raipeng.aimonitor.repositorymulti.b.CallRecordBRepository;
import com.raipeng.aimonitor.repositorymulti.c.CallRecordCHistoryRepository;
import com.raipeng.aimonitor.repositorymulti.c.CallRecordCRepository;
import com.raipeng.aimonitor.utils.ThreadManagerUtil;
import com.raipeng.common.util.JpaResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.Tuple;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.raipeng.aimonitor.constant.CommonConstant.DEFAULT_DIALOG;

@Slf4j
@Service
public class CallRecordMultiService {

    @Autowired
    private CallRecordARepository callRecordARepository;
    @Autowired
    private CallRecordBRepository callRecordBRepository;
    @Autowired
    private CallRecordCRepository callRecordCRepository;

    @Autowired
    private CallRecordAHistoryRepository callRecordAHistoryRepository;

    @Autowired
    private CallRecordBHistoryRepository callRecordBHistoryRepository;

    @Autowired
    private CallRecordCHistoryRepository callRecordCHistoryRepository;

    @Autowired
    private DialogSearchService dialogSearchService;

    public List<CallRecord> findPutThroughRecordsByTime(String startTime, String endTime) {
        List<CallRecord> callRecordList = Collections.synchronizedList(new ArrayList<>());

        List<Future<?>> futures = new ArrayList<>();
        Future<?> submitA = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordARepository.findPutThroughRecordsByTime(startTime, endTime));
        });
        futures.add(submitA);

        Future<?> submitB = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordBRepository.findPutThroughRecordsByTime(startTime, endTime));
        });
        futures.add(submitB);

        Future<?> submitC = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordCRepository.findPutThroughRecordsByTime(startTime, endTime));
        });
        futures.add(submitC);

        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("Exception:查询通话记录失败", e);
                throw new RuntimeException(e);
            }
        }
        return callRecordList;
    }

    public List<CallRecord> findPutThroughRecordsByTaskIds(String startTime, String endTime, List<String> taskIds) {
        List<CallRecord> callRecordList = Collections.synchronizedList(new ArrayList<>());

        List<Future<?>> futures = new ArrayList<>();
        Future<?> submitA = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordARepository.findPutThroughRecordsByTaskIds(startTime, endTime, taskIds));
        });
        futures.add(submitA);

        Future<?> submitB = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordBRepository.findPutThroughRecordsByTaskIds(startTime, endTime, taskIds));
        });
        futures.add(submitB);

        Future<?> submitC = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordCRepository.findPutThroughRecordsByTaskIds(startTime, endTime, taskIds));
        });
        futures.add(submitC);

        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("Exception:查询通话记录失败", e);
                throw new RuntimeException(e);
            }
        }
        return callRecordList;
    }

    public List<CallRecordHistory> findPutThroughRecordsHistoryByTime(String startTime, String endTime) {
        List<CallRecordHistory> callRecordList = Collections.synchronizedList(new ArrayList<>());

        List<Future<?>> futures = new ArrayList<>();
        Future<?> submitA = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordAHistoryRepository.findPutThroughRecordsByTime(startTime, endTime));
        });
        futures.add(submitA);

        Future<?> submitB = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordBHistoryRepository.findPutThroughRecordsByTime(startTime, endTime));
        });
        futures.add(submitB);

        Future<?> submitC = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordCHistoryRepository.findPutThroughRecordsByTime(startTime, endTime));
        });
        futures.add(submitC);

        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("Exception:查询通话记录失败", e);
                throw new RuntimeException(e);
            }
        }
        return callRecordList;
    }

    public List<CallRecordHistory> findPutThroughRecordsHistoryByTaskIds(String startTime, String endTime, List<String> taskIds) {
        List<CallRecordHistory> callRecordList = Collections.synchronizedList(new ArrayList<>());

        List<Future<?>> futures = new ArrayList<>();
        Future<?> submitA = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordAHistoryRepository.findPutThroughRecordsByTaskIds(startTime, endTime, taskIds));
        });
        futures.add(submitA);

        Future<?> submitB = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordBHistoryRepository.findPutThroughRecordsByTaskIds(startTime, endTime, taskIds));
        });
        futures.add(submitB);

        Future<?> submitC = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordCHistoryRepository.findPutThroughRecordsByTaskIds(startTime, endTime, taskIds));
        });
        futures.add(submitC);

        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("Exception:查询通话记录失败", e);
                throw new RuntimeException(e);
            }
        }
        return callRecordList;
    }

    public List<CallRecord> findPutThroughRecordsByTimeWithDialog(String startTime, String endTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime dateTime = LocalDateTime.parse(startTime, formatter);
        DateTimeFormatter dateOnlyFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String date = dateOnlyFormatter.format(dateTime);

        List<CallRecord> callRecordList = Collections.synchronizedList(new ArrayList<>());

        List<Future<?>> futures = new ArrayList<>();
        Future<?> submitA = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            List<CallRecord> putThroughRecordsByTime = callRecordARepository.findPutThroughRecordsByTime(startTime, endTime);
            fillCallRecordDialog(putThroughRecordsByTime, "PT1_", date);
            callRecordList.addAll(putThroughRecordsByTime);
        });
        futures.add(submitA);

        Future<?> submitB = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            List<CallRecord> putThroughRecordsByTime = callRecordBRepository.findPutThroughRecordsByTime(startTime, endTime);
            fillCallRecordDialog(putThroughRecordsByTime, "PT2_", date);
            callRecordList.addAll(putThroughRecordsByTime);
        });
        futures.add(submitB);

        Future<?> submitC = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            List<CallRecord> putThroughRecordsByTime = callRecordCRepository.findPutThroughRecordsByTime(startTime, endTime);
            fillCallRecordDialog(putThroughRecordsByTime, "PT3_", date);
            callRecordList.addAll(putThroughRecordsByTime);
        });
        futures.add(submitC);

        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("Exception:查询携带对话详情的通话记录失败", e);
                throw new RuntimeException(e);
            }
        }
        return callRecordList;
    }

    private void fillCallRecordDialog(List<CallRecord> callRecordList, String plateNum, String date) {
        if (CollectionUtils.isEmpty(callRecordList)) {
            return;
        }
        LocalDateTime sTime = LocalDateTime.now();
        List<String> collect = callRecordList.stream().map(CallRecord::getCallId).collect(Collectors.toList());
        Map<String, String> callIdDialogRecordMap = dialogSearchService.findCallRecordDialog(collect, plateNum, date);
        log.info("AI记录查询 fillCallRecordDialog量级:{},返回量级{}, 耗时：{}ms,库{}", collect.size(),
                callIdDialogRecordMap.size(), Duration.between(sTime, LocalDateTime.now()).toMillis(), plateNum);
        List<String> callIdNoDialog = new ArrayList<>();
        boolean sizeIncorrect = collect.size() != callIdDialogRecordMap.size();
        for (CallRecord callRecord : callRecordList) {
            if (sizeIncorrect && !callIdDialogRecordMap.containsKey(callRecord.getCallId())) {
                callIdNoDialog.add(callRecord.getCallId());
            }
            String dialog = null == callIdDialogRecordMap.get(callRecord.getCallId()) ? DEFAULT_DIALOG : callIdDialogRecordMap.get(callRecord.getCallId());
            callRecord.setDialogContents(dialog);
        }
        if (CollectionUtils.isNotEmpty(callIdNoDialog)) {
            log.info("Exception: callIds:{},不存在对话数据请核对", callIdNoDialog);
        }
    }


    private void fillCallRecordHistoryDialog(List<CallRecordHistory> callRecordList, String plateNum, String date) {
        if (CollectionUtils.isEmpty(callRecordList)) {
            return;
        }
        List<String> collect = callRecordList.stream().map(CallRecordHistory::getCallId).collect(Collectors.toList());
        Map<String, String> callIdDialogRecordMap = dialogSearchService.findCallRecordDialog(collect, plateNum, date);
        for (CallRecordHistory callRecord : callRecordList) {
            callRecord.setDialogContents(callIdDialogRecordMap.get(callRecord.getCallId()));
        }
    }

    public List<CallRecord> findPutThroughRecordsByTaskIdsWithDialog(String startTime, String endTime, List<String> taskIds) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime dateTime = LocalDateTime.parse(startTime, formatter);
        DateTimeFormatter dateOnlyFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String date = dateOnlyFormatter.format(dateTime);


        List<CallRecord> callRecordList = Collections.synchronizedList(new ArrayList<>());

        List<Future<?>> futures = new ArrayList<>();
        Future<?> submitA = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            List<CallRecord> putThroughRecordsByTaskIds = callRecordARepository.findPutThroughRecordsByTaskIds(startTime, endTime, taskIds);
            fillCallRecordDialog(putThroughRecordsByTaskIds, "PT1_", date);
            callRecordList.addAll(putThroughRecordsByTaskIds);
        });
        futures.add(submitA);

        Future<?> submitB = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            List<CallRecord> putThroughRecordsByTaskIds = callRecordBRepository.findPutThroughRecordsByTaskIds(startTime, endTime, taskIds);
            fillCallRecordDialog(putThroughRecordsByTaskIds, "PT2_", date);
            callRecordList.addAll(putThroughRecordsByTaskIds);
        });
        futures.add(submitB);

        Future<?> submitC = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            List<CallRecord> putThroughRecordsByTaskIds = callRecordCRepository.findPutThroughRecordsByTaskIds(startTime, endTime, taskIds);
            fillCallRecordDialog(putThroughRecordsByTaskIds, "PT3_", date);
            callRecordList.addAll(putThroughRecordsByTaskIds);
        });
        futures.add(submitC);

        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("Exception:查询通话记录失败", e);
                throw new RuntimeException(e);
            }
        }
        return callRecordList;
    }

    public List<CallRecordHistory> findPutThroughRecordsHistoryByTimeWithDialog(String startTime, String endTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime dateTime = LocalDateTime.parse(startTime, formatter);
        DateTimeFormatter dateOnlyFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String date = dateOnlyFormatter.format(dateTime);
        List<CallRecordHistory> callRecordList = Collections.synchronizedList(new ArrayList<>());

        List<Future<?>> futures = new ArrayList<>();
        Future<?> submitA = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            List<CallRecordHistory> putThroughRecordsByTime = callRecordAHistoryRepository.findPutThroughRecordsByTime(startTime, endTime);
            fillCallRecordHistoryDialog(putThroughRecordsByTime, "PT1_", date);
            callRecordList.addAll(putThroughRecordsByTime);
        });
        futures.add(submitA);

        Future<?> submitB = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            List<CallRecordHistory> putThroughRecordsByTime = callRecordBHistoryRepository.findPutThroughRecordsByTime(startTime, endTime);
            fillCallRecordHistoryDialog(putThroughRecordsByTime, "PT2_", date);
            callRecordList.addAll(putThroughRecordsByTime);

        });
        futures.add(submitB);

        Future<?> submitC = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            List<CallRecordHistory> putThroughRecordsByTime = callRecordCHistoryRepository.findPutThroughRecordsByTime(startTime, endTime);
            fillCallRecordHistoryDialog(putThroughRecordsByTime, "PT3_", date);
            callRecordList.addAll(putThroughRecordsByTime);

        });
        futures.add(submitC);

        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("Exception:查询通话记录失败", e);
                throw new RuntimeException(e);
            }
        }
        return callRecordList;
    }

    public List<CallRecordHistory> findPutThroughRecordsHistoryByTaskIdsWithDialog(String startTime, String endTime, List<String> taskIds) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime dateTime = LocalDateTime.parse(startTime, formatter);
        DateTimeFormatter dateOnlyFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String date = dateOnlyFormatter.format(dateTime);
        List<CallRecordHistory> callRecordList = Collections.synchronizedList(new ArrayList<>());

        List<Future<?>> futures = new ArrayList<>();
        Future<?> submitA = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            List<CallRecordHistory> putThroughRecordsByTaskIds = callRecordAHistoryRepository.findPutThroughRecordsByTaskIds(startTime, endTime, taskIds);
            fillCallRecordHistoryDialog(putThroughRecordsByTaskIds, "PT1_", date);
            callRecordList.addAll(putThroughRecordsByTaskIds);
        });
        futures.add(submitA);

        Future<?> submitB = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            List<CallRecordHistory> putThroughRecordsByTaskIds = callRecordBHistoryRepository.findPutThroughRecordsByTaskIds(startTime, endTime, taskIds);
            fillCallRecordHistoryDialog(putThroughRecordsByTaskIds, "PT2_", date);
            callRecordList.addAll(putThroughRecordsByTaskIds);

        });
        futures.add(submitB);

        Future<?> submitC = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            List<CallRecordHistory> putThroughRecordsByTaskIds = callRecordCHistoryRepository.findPutThroughRecordsByTaskIds(startTime, endTime, taskIds);
            fillCallRecordHistoryDialog(putThroughRecordsByTaskIds, "PT3_", date);
            callRecordList.addAll(putThroughRecordsByTaskIds);
        });
        futures.add(submitC);

        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("Exception:查询通话记录失败", e);
                throw new RuntimeException(e);
            }
        }
        return callRecordList;
    }

    public List<CallLineMonitorUnitData> scanCallRecordsByStartAndEndTimes(LocalDateTime startTime, LocalDateTime endTime) {
        List<Tuple> callRecordList = Collections.synchronizedList(new ArrayList<>());

        List<Future<?>> futures = new ArrayList<>();

        Future<?> submitA = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordARepository.scanCallRecordsByStartAndEndTimes(startTime, endTime));
        });
        futures.add(submitA);

        Future<?> submitB = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordBRepository.scanCallRecordsByStartAndEndTimes(startTime, endTime));
        });
        futures.add(submitB);

        Future<?> submitC = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordCRepository.scanCallRecordsByStartAndEndTimes(startTime, endTime));
        });
        futures.add(submitC);

        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("Exception:查询通话记录失败", e);
                throw new RuntimeException(e);
            }
        }
        // 将Tuple列表转换为CallLineMonitorUnitData列表
        List<CallLineMonitorUnitData> unitDataOfAI = JpaResultUtils.processResult(callRecordList, CallLineMonitorUnitData.class);
        // 按照指定字段进行分组和汇总
        return summarizeUnitData(unitDataOfAI);
    }

    public List<CallLineMonitorUnitData> scanCallRecordsForRoutingFailByStartAndEndTimes(LocalDateTime startTime, LocalDateTime endTime) {
        List<Tuple> callRecordList = Collections.synchronizedList(new ArrayList<>());

        List<Future<?>> futures = new ArrayList<>();

        Future<?> submitA = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordARepository.scanCallRecordsForRoutingFailByStartAndEndTimes(startTime, endTime));
        });
        futures.add(submitA);

        Future<?> submitB = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordBRepository.scanCallRecordsForRoutingFailByStartAndEndTimes(startTime, endTime));
        });
        futures.add(submitB);

        Future<?> submitC = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordCRepository.scanCallRecordsForRoutingFailByStartAndEndTimes(startTime, endTime));
        });
        futures.add(submitC);

        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("Exception:查询通话记录失败", e);
                throw new RuntimeException(e);
            }
        }
        // 将Tuple列表转换为CallLineMonitorUnitData列表
        List<CallLineMonitorUnitData> unitDataOfAI = JpaResultUtils.processResult(callRecordList, CallLineMonitorUnitData.class);
        // 按照指定字段进行分组和汇总
        return summarizeUnitData(unitDataOfAI);
    }

    private List<CallLineMonitorUnitData> summarizeUnitData(List<CallLineMonitorUnitData> unitDataOfAI) {
        // 使用Stream对unitDataOfAI进行分组并合并相同数据
        Map<String, List<CallLineMonitorUnitData>> groupedData = unitDataOfAI.stream()
                .collect(Collectors.groupingBy(data ->
                        data.getTenantLineNumber() + "_" + data.getSupplyLineNumber() + "_" +
                                data.getProvince() + "_" + data.getCity() + "_" +
                                data.getOperator()));
        // 对每组数据进行汇总统计
        return groupedData.values().stream()
                .map(this::mergeData)
                .collect(Collectors.toList());
    }

    private CallLineMonitorUnitData mergeData(List<CallLineMonitorUnitData> dataList) {
        // 合并相同数据并汇总其他统计数据
        CallLineMonitorUnitData mergedData = new CallLineMonitorUnitData();
        mergedData.setTimeSlot(dataList.get(0).getTimeSlot());
        mergedData.setTenantLineNumber(dataList.get(0).getTenantLineNumber());
        mergedData.setSupplyLineNumber(dataList.get(0).getSupplyLineNumber());
        mergedData.setProvince(dataList.get(0).getProvince());
        mergedData.setCity(dataList.get(0).getCity());
        mergedData.setOperator(dataList.get(0).getOperator());

        int totalCallNum = 0, totalConnectNum = 0, silenceCallNum = 0, oneSecondConnectedNum = 0,
                twoSecondConnectedNum = 0, callFailedNum = 0, silenceHangupNum = 0, assistantNum = 0,
                promptSoundNum = 0, routingFailNum = 0, classANum = 0, classBNum = 0, classCNum = 0, classDNum = 0 ,waitSecond = 0, callDurationSecond = 0;

        for (CallLineMonitorUnitData data : dataList) {
            totalCallNum += data.getTotalCallNum();
            totalConnectNum += data.getTotalConnectNum();
            silenceCallNum += data.getSilenceCallNum();
            oneSecondConnectedNum += data.getOneSecondConnectedNum();
            twoSecondConnectedNum += data.getTwoSecondConnectedNum();
            callFailedNum += data.getCallFailedNum();
            silenceHangupNum += data.getSilenceHangupNum();
            assistantNum += data.getAssistantNum();
            promptSoundNum += data.getPromptSoundNum();
            routingFailNum += data.getRoutingFailNum();
            classANum += data.getClassANum();
            classBNum += data.getClassBNum();
            classCNum += data.getClassCNum();
            classDNum += data.getClassDNum();
            waitSecond += data.getWaitSecond();
            callDurationSecond += data.getCallDurationSecond();
        }

        mergedData.setTotalCallNum(totalCallNum);
        mergedData.setTotalConnectNum(totalConnectNum);
        mergedData.setSilenceCallNum(silenceCallNum);
        mergedData.setOneSecondConnectedNum(oneSecondConnectedNum);
        mergedData.setTwoSecondConnectedNum(twoSecondConnectedNum);
        mergedData.setCallFailedNum(callFailedNum);
        mergedData.setSilenceHangupNum(silenceHangupNum);
        mergedData.setAssistantNum(assistantNum);
        mergedData.setPromptSoundNum(promptSoundNum);
        mergedData.setRoutingFailNum(routingFailNum);
        mergedData.setClassANum(classANum);
        mergedData.setClassBNum(classBNum);
        mergedData.setClassCNum(classCNum);
        mergedData.setClassDNum(classDNum);
        mergedData.setWaitSecond(waitSecond);
        mergedData.setCallDurationSecond(callDurationSecond);

        return mergedData;
    }

    public List<Tuple> getAccountCallData(String callOutTime) {
        List<Tuple> accountCallDataList = Collections.synchronizedList(new ArrayList<>());

        List<Future<?>> futures = new ArrayList<>();

        Future<?> submitA = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            accountCallDataList.addAll(callRecordARepository.findCallDataByAccount(callOutTime));
        });
        futures.add(submitA);

        Future<?> submitB = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            accountCallDataList.addAll(callRecordBRepository.findCallDataByAccount(callOutTime));
        });
        futures.add(submitB);

        Future<?> submitC = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            accountCallDataList.addAll(callRecordCRepository.findCallDataByAccount(callOutTime));
        });
        futures.add(submitC);

        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("Exception:查询通话记录失败", e);
                throw new RuntimeException(e);
            }
        }
        return accountCallDataList;
    }

    public List<Tuple> getAccountCallDataHistory(String startTime, String endTime) {
        List<Tuple> accountCallDataList = Collections.synchronizedList(new ArrayList<>());

        List<Future<?>> futures = new ArrayList<>();

        Future<?> submitA = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            accountCallDataList.addAll(callRecordAHistoryRepository.findCallDataByAccount(startTime, endTime));
        });
        futures.add(submitA);

        Future<?> submitB = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            accountCallDataList.addAll(callRecordBHistoryRepository.findCallDataByAccount(startTime, endTime));
        });
        futures.add(submitB);

        Future<?> submitC = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            accountCallDataList.addAll(callRecordCHistoryRepository.findCallDataByAccount(startTime, endTime));
        });
        futures.add(submitC);

        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("Exception:查询通话记录失败", e);
                throw new RuntimeException(e);
            }
        }
        return accountCallDataList;
    }

    public List<Tuple> findOneDayCallRecordsForLineStatistic(String startTime, String endTime) {
        List<Tuple> callRecordList = Collections.synchronizedList(new ArrayList<>());

        List<Future<?>> futures = new ArrayList<>();

        Future<?> submitA = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordARepository.findOneDayCallRecordsForLineStatistic(startTime, endTime));
        });
        futures.add(submitA);

        Future<?> submitB = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordBRepository.findOneDayCallRecordsForLineStatistic(startTime, endTime));
        });
        futures.add(submitB);

        Future<?> submitC = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordCRepository.findOneDayCallRecordsForLineStatistic(startTime, endTime));
        });
        futures.add(submitC);

        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("Exception:查询通话记录失败", e);
                throw new RuntimeException(e);
            }
        }
        return callRecordList;
    }



    public List<Tuple> findOneDayCallRecordsForLineStatisticHistory(String startTime, String endTime) {
        List<Tuple> callRecordList = Collections.synchronizedList(new ArrayList<>());

        List<Future<?>> futures = new ArrayList<>();

        Future<?> submitA = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordAHistoryRepository.findOneDayCallRecordsForLineStatisticHistory(startTime, endTime));
        });
        futures.add(submitA);

        Future<?> submitB = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordBHistoryRepository.findOneDayCallRecordsForLineStatisticHistory(startTime, endTime));
        });
        futures.add(submitB);

        Future<?> submitC = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordCHistoryRepository.findOneDayCallRecordsForLineStatisticHistory(startTime, endTime));
        });
        futures.add(submitC);

        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("Exception:查询通话记录失败", e);
                throw new RuntimeException(e);
            }
        }
        return callRecordList;
    }

    public List<CallRecord> findCallRecordsByStartAndEndTime(String startTime, String endTime) {
        List<CallRecord> callRecordList = Collections.synchronizedList(new ArrayList<>());

        List<Future<?>> futures = new ArrayList<>();

        Future<?> submitA = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordARepository.findCallRecordsByStartAndEndTime(startTime, endTime));
        });
        futures.add(submitA);

        Future<?> submitB = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordBRepository.findCallRecordsByStartAndEndTime(startTime, endTime));
        });
        futures.add(submitB);

        Future<?> submitC = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordCRepository.findCallRecordsByStartAndEndTime(startTime, endTime));
        });
        futures.add(submitC);

        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("Exception:查询通话记录失败", e);
                throw new RuntimeException(e);
            }
        }
        return callRecordList;
    }

    public List<Tuple> findVolcanoRecordsByTaskIds(String startTime, String endTime, List<String> taskIds) {
        List<Tuple> callRecordList = Collections.synchronizedList(new ArrayList<>());

        List<Future<?>> futures = new ArrayList<>();

        Future<?> submitA = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordARepository.findVolcanoRecordsByTaskIds(startTime, endTime, taskIds));
        });
        futures.add(submitA);

        Future<?> submitB = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordBRepository.findVolcanoRecordsByTaskIds(startTime, endTime, taskIds));
        });
        futures.add(submitB);

        Future<?> submitC = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordCRepository.findVolcanoRecordsByTaskIds(startTime, endTime, taskIds));
        });
        futures.add(submitC);

        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("Exception:火山查询通话记录失败", e);
                throw new RuntimeException(e);
            }
        }
        return callRecordList;
    }

    public List<CallRecordStatisticUnitData> scanCallRecordStatisticsByStartAndEndTimes(String startTime, String endTime) {

        List<Tuple> callRecordList = Collections.synchronizedList(new ArrayList<>());
        List<Future<?>> futures = new ArrayList<>();

        Future<?> submitA = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordARepository.scanCallRecordStatisticsByStartAndEndTimes(startTime, endTime));
        });
        futures.add(submitA);

        Future<?> submitB = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordBRepository.scanCallRecordStatisticsByStartAndEndTimes(startTime, endTime));
        });
        futures.add(submitB);

        Future<?> submitC = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordCRepository.scanCallRecordStatisticsByStartAndEndTimes(startTime, endTime));
        });
        futures.add(submitC);

        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("Exception:查询通话记录失败", e);
                throw new RuntimeException(e);
            }
        }
        // 将Tuple列表转换为CallLineMonitorUnitData列表
        List<CallRecordStatisticUnitData> unitDataOfAI = JpaResultUtils.processResult(callRecordList, CallRecordStatisticUnitData.class);
        // 按照指定字段进行分组和汇总
        return summarizeUnitData1(unitDataOfAI);
    }

    private List<CallRecordStatisticUnitData> summarizeUnitData1(List<CallRecordStatisticUnitData> unitDataOfAI) {
        // 使用Stream对unitDataOfAI进行分组并合并相同数据
        Map<String, List<CallRecordStatisticUnitData>> groupedData = unitDataOfAI.stream()
                .collect(Collectors.groupingBy(data ->
                        data.getAccount() + "_" + data.getTaskId() + "_" + data.getScriptStringId() + "_" +
                                data.getProvinceCode() + "_" + data.getCityCode() + "_" +
                                data.getOperator()));
        // 对每组数据进行汇总统计
        return groupedData.values().stream()
                .map(this::mergeData1)
                .collect(Collectors.toList());
    }

    private CallRecordStatisticUnitData mergeData1(List<CallRecordStatisticUnitData> dataList) {
        // 合并相同数据并汇总其他统计数据
        CallRecordStatisticUnitData mergedData = new CallRecordStatisticUnitData();
        mergedData.setTimeSlot(dataList.get(0).getTimeSlot());
        mergedData.setAccount(dataList.get(0).getAccount());
        mergedData.setTaskId(dataList.get(0).getTaskId());
        mergedData.setScriptStringId(dataList.get(0).getScriptStringId());
        mergedData.setProvince(dataList.get(0).getProvince());
        mergedData.setProvinceCode(dataList.get(0).getProvinceCode());
        mergedData.setCity(dataList.get(0).getCity());
        mergedData.setCityCode(dataList.get(0).getCityCode());
        mergedData.setOperator(dataList.get(0).getOperator());

        int totalCallNum = 0, totalConnectNum = 0, silenceCallNum = 0, oneSecondConnectedNum = 0,
                twoSecondConnectedNum = 0, silenceHangupNum = 0, assistantNum = 0,
                classANum = 0, classBNum = 0, classCNum = 0, classDNum = 0, classENum = 0,
                classFNum = 0, classGNum = 0;
        long totalCallDuration= 0L,
        classACallDuration= 0L,
        classBCallDuration= 0L,
                classCCallDuration= 0L,
        classDCallDuration= 0L,
                classECallDuration= 0L,
        classFCallDuration= 0L,
                classGCallDuration= 0L;

        Map<String, Integer> map = new HashMap<>();
        for (CallRecordStatisticUnitData data : dataList) {
            totalCallNum += data.getTotalCallNum();
            totalConnectNum += data.getTotalConnectNum();
            totalCallDuration += data.getTotalCallDuration();
            silenceCallNum += data.getSilenceCallNum();
            oneSecondConnectedNum += data.getOneSecondConnectedNum();
            twoSecondConnectedNum += data.getTwoSecondConnectedNum();
            silenceHangupNum += data.getSilenceHangupNum();
            assistantNum += data.getAssistantNum();

            classANum += data.getClassANum();
            classBNum += data.getClassBNum();
            classCNum += data.getClassCNum();
            classDNum += data.getClassDNum();
            classENum += data.getClassENum();
            classFNum += data.getClassFNum();
            classGNum += data.getClassGNum();
            classACallDuration += data.getClassACallDuration();
            classBCallDuration += data.getClassBCallDuration();
            classCCallDuration += data.getClassCCallDuration();
            classDCallDuration += data.getClassDCallDuration();
            classECallDuration += data.getClassECallDuration();
            classFCallDuration += data.getClassFCallDuration();
            classGCallDuration += data.getClassGCallDuration();

            String intentionLabelNames = data.getIntentionLabelNames();

            //进行判空
            if (StringUtils.isNotBlank(intentionLabelNames)) {
                //对其进行，切割 切割后再次对值进行判空 存放在map中
                String[] split = intentionLabelNames.split(",");
                for (String s : split) {
                    if (StringUtils.isNotBlank(s)) {
                        Integer orDefault = map.getOrDefault(s, 0);
                        int i = orDefault + 1;
                        map.put(s, i);
                    }
                }
            }
        }
        //对map中进行封装为list
        mergedData.setTotalCallNum(totalCallNum);
        mergedData.setTotalConnectNum(totalConnectNum);
        mergedData.setTotalCallDuration(totalCallDuration);
        mergedData.setSilenceCallNum(silenceCallNum);
        mergedData.setOneSecondConnectedNum(oneSecondConnectedNum);
        mergedData.setTwoSecondConnectedNum(twoSecondConnectedNum);
        mergedData.setSilenceHangupNum(silenceHangupNum);
        mergedData.setAssistantNum(assistantNum);
        mergedData.setClassANum(classANum);
        mergedData.setClassBNum(classBNum);
        mergedData.setClassCNum(classCNum);
        mergedData.setClassDNum(classDNum);
        mergedData.setClassENum(classENum);
        mergedData.setClassFNum(classFNum);
        mergedData.setClassGNum(classGNum);
        mergedData.setClassACallDuration(classACallDuration);
        mergedData.setClassBCallDuration(classBCallDuration);
        mergedData.setClassCCallDuration(classCCallDuration);
        mergedData.setClassDCallDuration(classDCallDuration);
        mergedData.setClassECallDuration(classECallDuration);
        mergedData.setClassFCallDuration(classFCallDuration);
        mergedData.setClassGCallDuration(classGCallDuration);

        List<IntentionLabelCount> countPojos = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : map.entrySet()) {
            IntentionLabelCount intentionLabelCountPojo = new IntentionLabelCount();
            Integer value = entry.getValue();
            intentionLabelCountPojo.setName(entry.getKey());
            intentionLabelCountPojo.setNum(value);
            countPojos.add(intentionLabelCountPojo);
        }
        mergedData.setIntentionLabelCount(countPojos);
        return mergedData;
    }

    public List<CallRecord> findCallRecordByCallOUtTimeAndIntentionTagsAndNumbers(String startTime, String endTime, List<String> intentionClass,
                                                                                  String ifIntentionClassListEmpty, List<String> recordIds) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
            LocalDateTime end = LocalDateTime.now();
            endTime = end.format(formatter);
            LocalDateTime start = end.minusDays(30);
            startTime = start.format(formatter);
        }
        List<CallRecord> callRecordList = Collections.synchronizedList(new ArrayList<>());
        List<Future<?>> futures = new ArrayList<>();
        String finalStartTime = startTime;
        String finalEndTime = endTime;
        Future<?> submitA = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordARepository.findCallRecordByCallOUtTimeAndIntentionTagsAndNumbers(finalStartTime, finalEndTime,
                    intentionClass, ifIntentionClassListEmpty, recordIds));
        });
        futures.add(submitA);
        Future<?> submitB = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordBRepository.findCallRecordByCallOUtTimeAndIntentionTagsAndNumbers(finalStartTime, finalEndTime,
                    intentionClass, ifIntentionClassListEmpty, recordIds));
        });
        futures.add(submitB);
        Future<?> submitC = ThreadManagerUtil.multiDataBasePool.submit(() -> {
            callRecordList.addAll(callRecordCRepository.findCallRecordByCallOUtTimeAndIntentionTagsAndNumbers(finalStartTime, finalEndTime,
                    intentionClass, ifIntentionClassListEmpty, recordIds));
        });
        futures.add(submitC);

        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("Exception:蚂蚁查询任务详情收集通话记录查询失败", e);
                throw new RuntimeException(e);
            }
        }
        return callRecordList;
    }

}
