package com.raipeng.aimonitor.service.supplierManager;


import com.alibaba.fastjson.JSONObject;
import com.raipeng.aidatacommon.model.Admin;
import com.raipeng.aimonitor.config.HotConfig;
import com.raipeng.aimonitor.entity.ChartDataEntity;
import com.raipeng.aimonitor.entity.ChartPulsarDataEntity;
import com.raipeng.aimonitor.entity.ReconciliationDataEntity;
import com.raipeng.aimonitor.repository.AdminRepository;
import com.raipeng.aimonitor.repository.TenantLineRepository;
import com.raipeng.aimonitor.service.CallRecordForManualDirectService;
import com.raipeng.aimonitor.service.SmsRecordOutService;
import com.raipeng.aimonitor.service.SupplyLineSearchService;
import com.raipeng.aimonitor.service.clickhouse.CallLineStatisticForPushClickService;

import lombok.extern.slf4j.Slf4j;

import org.apache.pulsar.client.api.*;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CallLineStatisticForPushService {
    private static final String DX_BELONG = "得心应手";

    private static final String BZ_BELONG = "限时传送";

    private static final String XR_BELONG = "仙人指路";

    private static final String SECOND_INDUSTRY_ID_FOR_INSURANCE = "4";

    private static final String SECOND_INDUSTRY_ID_FOR_LOAN = "6";

    @Autowired
    private PulsarClient pulsarClient;

    @Autowired
    @Qualifier("pulsarSecondClient")
    private PulsarClient pulsarSecondClient;

    @Autowired
    private HotConfig hotConfig;

    @Autowired
    private AdminRepository adminRepository;

    @Autowired
    private SupplyLineSearchService supplyLineSearchService;

    @Autowired
    private CallLineStatisticForPushClickService callLineStatisticForPushClickService;

    @Autowired
    private TenantLineRepository tenantLineRepository;

    @Autowired
    private CallRecordForManualDirectService callRecordForManualDirectService;

    @Autowired
    private SmsRecordOutService smsRecordOutService;

    @Value("${pulsar.topic.callLineStatisticForPush:persistent://datasync/linedatasync/linestatis}")
    private String topic;

    @Value("${pulsar.second.topic.callLineStatisticForPush:persistent://my-tenant/my-namespace/my-partitioned-topic}")
    private String secondTopic;

    private Producer<String> producer;

    private Producer<String> secondProducer;

    @PostConstruct
    public void init() throws PulsarClientException {
        producer = pulsarClient.newProducer(Schema.STRING).topic(topic).create();
        secondProducer = pulsarSecondClient.newProducer(Schema.STRING).topic( secondTopic).create();
    }

    @PreDestroy
    public void close() throws PulsarClientException {
        if (producer != null) {
            producer.close();
        }
        if (secondProducer != null) {
            secondProducer.close();
        }
    }

    public void sendTodayDXMessage() {
        LocalDate today = LocalDate.now();
        String entity = callLineStatisticForPushClickService.getFinalCallLineStatisticPushEntity(
                today.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                CallLineStatisticForPushClickService.DX_BELONG);
        try {
            sendMessage(entity);
            log.info("send dx message success: {}", entity);
        } catch (PulsarClientException e) {
            log.error("[Exception]=>send dx pulsar message error", e);
        }
    }

    public void sendTodayBZMessage() {
        LocalDate today = LocalDate.now();
        String entity = callLineStatisticForPushClickService.getFinalCallLineStatisticPushEntity(
                today.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                CallLineStatisticForPushClickService.BZ_BELONG);
        try {
            sendMessage(entity);
            log.info("send bz message success: {}", entity);
        } catch (PulsarClientException e) {
            log.error("[Exception]=>send bz pulsar message error", e);
        }
    }

    public void sendSomeDayDXMessage(String date) {
        String entity = callLineStatisticForPushClickService.getFinalCallLineStatisticPushEntity(
                date,
                CallLineStatisticForPushClickService.DX_BELONG);
        try {
            sendMessage(entity);
            log.info("send {} dx message success: {}", date, entity);
        } catch (PulsarClientException e) {
            log.error("[Exception]=>send dx pulsar message error", e);
        }
    }

    public void sendSomeDayBZMessage(String date) {
        String entity = callLineStatisticForPushClickService.getFinalCallLineStatisticPushEntity(
                date,
                CallLineStatisticForPushClickService.BZ_BELONG);
        try {
            sendMessage(entity);
            log.info("send {} bz message success: {}", date, entity);
        } catch (PulsarClientException e) {
            log.error("[Exception]=>send bz pulsar message error", e);
        }
    }

    public void sendTodayChartData() {
        String date = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        sendChartData(date);
    }

    public void sendSomeDayChartData(String date) {
        sendChartData(date);
    }

    public void sendChartData(String date) {
        String startTime = date + " 00:00:00";
        String endTime = date + " 23:59:59";

        List<Long> tenantIds = hotConfig.getChartDataStatisticTenantIds();
        List<Admin> admins = adminRepository.findAdminsByIsTenantManagerAndTenantIdIn(true, tenantIds);
        List<String> accounts = admins.stream().map(Admin::getAccount).collect(Collectors.toList());

        List<String> insuranceLine = tenantLineRepository.findTenantLineNumbersByIndustry(Collections.singletonList("保险"));
        String lineMessageForInsurance = getStringLineChartData(date, startTime, endTime, accounts, insuranceLine, "保险");
        try {
            sendMessageToSecond(lineMessageForInsurance);
            log.info("send some day insurance line chart data message success: {}", lineMessageForInsurance);
        } catch (PulsarClientException e) {
            log.error("[Exception]=>send some day insurance line chart data pulsar message error", e);
        }

        List<String> loanLine = tenantLineRepository.findTenantLineNumbersByIndustry(Collections.singletonList("互联网小贷"));
        String lineMessageForLoan = getStringLineChartData(date, startTime, endTime, accounts, loanLine, "小贷");
        try {
            sendMessageToSecond(lineMessageForLoan);
            log.info("send some day loan line chart data message success: {}", lineMessageForLoan);
        } catch (PulsarClientException e) {
            log.error("[Exception]=>send some day loan line chart data pulsar message error", e);
        }

        List<String> otherLine = findOtherTenantLineNumbers(insuranceLine, loanLine);
        String lineMessageForOther = getStringLineChartData(date, startTime, endTime, accounts, otherLine, "其他");
        try {
            sendMessageToSecond(lineMessageForOther);
            log.info("send some day other line chart data message success: {}", lineMessageForOther);
        } catch (PulsarClientException e) {
            log.error("[Exception]=>send some day other line chart data pulsar message error", e);
        }

        String smsMessageForInsurance = getStringSmsChartData(date, startTime, endTime, accounts, SECOND_INDUSTRY_ID_FOR_INSURANCE);
        try {
            sendMessageToSecond(smsMessageForInsurance);
            log.info("send some day insurance sms chart data message success: {}", smsMessageForInsurance);
        } catch (PulsarClientException e) {
            log.error("[Exception]=>send some day insurance sms chart data pulsar message error", e);
        }

        String smsMessageLoan = getStringSmsChartData(date, startTime, endTime, accounts, SECOND_INDUSTRY_ID_FOR_LOAN);
        try {
            sendMessageToSecond(smsMessageLoan);
            log.info("send some day loan sms chart data message success: {}", smsMessageLoan);
        } catch (PulsarClientException e) {
            log.error("[Exception]=>send some day loan sms chart data pulsar message error", e);
        }

        String smsMessageOther = getStringSmsChartDataOther(date, startTime, endTime, accounts,
                Arrays.asList(SECOND_INDUSTRY_ID_FOR_INSURANCE, SECOND_INDUSTRY_ID_FOR_LOAN));
        try {
            sendMessageToSecond(smsMessageOther);
            log.info("send some day other sms chart data message success: {}", smsMessageOther);
        } catch (PulsarClientException e) {
            log.error("[Exception]=>send some day other sms chart data pulsar message error", e);
        }
    }

    private void sendMessage(String message) throws PulsarClientException {
        MessageId send = producer.send(message);
        log.info("pulsar messageId: {}", send);
    }

    private void sendMessageToSecond(String message) throws PulsarClientException {
        MessageId send = secondProducer.send(message);
        log.info("second pulsar messageId: {}", send);
    }

    public List<ReconciliationDataEntity> getReconciliationData(String date) {
        String startTime = date + " 00:00:00";
        String endTime = date + " 23:59:59";

        List<Long> tenantIds = hotConfig.getChartDataStatisticTenantIds();
        List<Admin> admins = adminRepository.findAdminsByIsTenantManagerAndTenantIdIn(true, tenantIds);
        List<String> accounts = admins.stream().map(Admin::getAccount).collect(Collectors.toList());

        List<ReconciliationDataEntity> reconciliationDataForPureAI = callLineStatisticForPushClickService.getReconciliationDataForPureAI(startTime, endTime, accounts);
        List<ReconciliationDataEntity> reconciliationDataForAIMan = callLineStatisticForPushClickService.getReconciliationDataForAIMan(startTime, endTime, accounts);
        List<ReconciliationDataEntity> reconciliationDataForHuman = callRecordForManualDirectService.getReconciliationData(startTime, endTime, accounts);

        System.out.println(reconciliationDataForPureAI);
        System.out.println(reconciliationDataForAIMan);
        System.out.println(reconciliationDataForHuman);

        Map<String, ReconciliationDataEntity> map = new HashMap<>();
        collectReconciliationData(reconciliationDataForPureAI, map);
        collectReconciliationData(reconciliationDataForAIMan, map);
        collectReconciliationData(reconciliationDataForHuman, map);
        return new ArrayList<>(map.values());
    }

    public String getStringLineChartData(String date, String startTime, String endTime, List<String> accounts, List<String> tenantLineNumbers, String industry) {
        Map<String, String> supplyLineBelongMap = supplyLineSearchService.getSupplyLineBelongMap();
        List<ChartDataEntity> entities = callLineStatisticForPushClickService.getChartDataStatisticByLineType(startTime, endTime, accounts, tenantLineNumbers);
        List<ChartDataEntity> humanMachineEntities = callLineStatisticForPushClickService.getChartDataStatisticAIManByLineType(startTime, endTime, accounts, tenantLineNumbers);
        List<ChartDataEntity> manualDirectEntities = callRecordForManualDirectService.getChartDataStatistic(startTime, endTime, accounts, tenantLineNumbers);
        log.info("entities size:{}, humanMachineEntities size:{}, manualDirectEntities size:{}", entities.size(), humanMachineEntities.size(), manualDirectEntities.size());

        Map<String, ChartPulsarDataEntity> pulsarDataEntityMap = new HashMap<>();
        initPulsarDataEntityMap(date, industry, pulsarDataEntityMap, DX_BELONG);
        initPulsarDataEntityMap(date, industry, pulsarDataEntityMap, BZ_BELONG);
        initPulsarDataEntityMap(date, industry, pulsarDataEntityMap, XR_BELONG);

        for (ChartDataEntity entity : entities) {
            String supplyLineNumber = entity.getSupplyLineNumber();
            String belong = supplyLineBelongMap.get(supplyLineNumber);
            if (belong == null) {
                continue;
            }
            String platform = "pureAI";
            String mapKey = belong + "_" + platform;
            ChartPulsarDataEntity chartPulsarDataEntity = pulsarDataEntityMap.get(mapKey);
            chartPulsarDataEntity.setSuccessCount(chartPulsarDataEntity.getSuccessCount() + (entity.getSuccessCount() == null ? 0L :entity.getSuccessCount().longValue()));
            pulsarDataEntityMap.put(mapKey, chartPulsarDataEntity);
        }

        for (ChartDataEntity entity : humanMachineEntities) {
            String supplyLineNumber = entity.getSupplyLineNumber();
            String belong = supplyLineBelongMap.get(supplyLineNumber);
            if (belong == null) {
                continue;
            }
            String platform = "aIMan";
            String mapKey = belong + "_" + platform;
            ChartPulsarDataEntity chartPulsarDataEntity = pulsarDataEntityMap.get(mapKey);
            chartPulsarDataEntity.setSuccessCount(chartPulsarDataEntity.getSuccessCount() + (entity.getSuccessCount() == null ? 0L :entity.getSuccessCount().longValue()));
            pulsarDataEntityMap.put(mapKey, chartPulsarDataEntity);
        }

        for (ChartDataEntity entity : manualDirectEntities) {
            String supplyLineNumber = entity.getSupplyLineNumber();
            String belong = supplyLineBelongMap.get(supplyLineNumber);
            if (belong == null) {
                continue;
            }
            String platform = "human";
            String mapKey = belong + "_" + platform;
            ChartPulsarDataEntity chartPulsarDataEntity = pulsarDataEntityMap.get(mapKey);
            chartPulsarDataEntity.setSuccessCount(chartPulsarDataEntity.getSuccessCount() + (entity.getSuccessCount() == null ? 0L :entity.getSuccessCount().longValue()));
            pulsarDataEntityMap.put(mapKey, chartPulsarDataEntity);
        }

        return JSONObject.toJSONString(new ArrayList<>(pulsarDataEntityMap.values()));
    }

    public String getStringSmsChartData(String date, String startTime, String endTime, List<String> accounts, String secondIndustryId) {
        Integer bzCountByDateAndAccounts = smsRecordOutService.findBZCountByDateAndAccounts(date, startTime, endTime, accounts, secondIndustryId);
        ChartPulsarDataEntity chartPulsarDataEntity = new ChartPulsarDataEntity();
        chartPulsarDataEntity.setDate(date);
        chartPulsarDataEntity.setBelong(BZ_BELONG);
        chartPulsarDataEntity.setPlatform("BZ");
        chartPulsarDataEntity.setSuccessCount(bzCountByDateAndAccounts.longValue());
        chartPulsarDataEntity.setIndustry(Objects.equals(secondIndustryId, SECOND_INDUSTRY_ID_FOR_INSURANCE) ? "保险" : "小贷");
        return JSONObject.toJSONString(Collections.singletonList(chartPulsarDataEntity));
    }

    public String getStringSmsChartDataOther(String date, String startTime, String endTime, List<String> accounts, List<String> secondIndustryIds) {
        Integer bzCountByDateAndAccounts = smsRecordOutService.findBZCountByDateAndAccountsOther(date, startTime, endTime, accounts, secondIndustryIds);
        ChartPulsarDataEntity chartPulsarDataEntity = new ChartPulsarDataEntity();
        chartPulsarDataEntity.setDate(date);
        chartPulsarDataEntity.setBelong(BZ_BELONG);
        chartPulsarDataEntity.setPlatform("BZ");
        chartPulsarDataEntity.setSuccessCount(bzCountByDateAndAccounts.longValue());
        chartPulsarDataEntity.setIndustry("其他");
        return JSONObject.toJSONString(Collections.singletonList(chartPulsarDataEntity));
    }

    private void collectReconciliationData(List<ReconciliationDataEntity> entities, Map<String, ReconciliationDataEntity> map) {
        entities.forEach(entity -> {
            String account = entity.getAccount();
            String supplyLineNumber = entity.getSupplyLineNumber();
            String key = account + "_" + supplyLineNumber;
            ReconciliationDataEntity reconciliationDataEntity = map.computeIfAbsent(key, k -> {
                ReconciliationDataEntity dataEntity = new ReconciliationDataEntity();
                dataEntity.setAccount(account);
                dataEntity.setSupplyLineNumber(supplyLineNumber);
                dataEntity.setDate(entity.getDate());
                return dataEntity;
            });
            reconciliationDataEntity.setSuccessCount(
                    (entity.getSuccessCount() == null ? 0 : entity.getSuccessCount())
                            + (reconciliationDataEntity.getSuccessCount() == null ? 0 : reconciliationDataEntity.getSuccessCount()));
        });
    }

    private void initPulsarDataEntityMap(String date, String industry, Map<String, ChartPulsarDataEntity> pulsarDataEntityMap, String belong) {
        pulsarDataEntityMap.put(belong + "_" + "pureAI", ChartPulsarDataEntity.builder()
                .belong(belong)
                .platform("pureAI")
                .date(date)
                .successCount(0L)
                .industry(industry)
                .build());
        pulsarDataEntityMap.put(belong + "_" + "aIMan", ChartPulsarDataEntity.builder()
                .belong(belong)
                .platform("aIMan")
                .date(date)
                .successCount(0L)
                .industry(industry)
                .build());
        pulsarDataEntityMap.put(belong + "_" + "human", ChartPulsarDataEntity.builder()
                .belong(belong)
                .platform("human")
                .date(date)
                .successCount(0L)
                .industry(industry)
                .build());
    }

    private List<String> findOtherTenantLineNumbers(List<String> insuranceLine, List<String> loanLine) {
        List<String> lines = new ArrayList<>();
        lines.addAll(insuranceLine);
        lines.addAll(loanLine);
        return tenantLineRepository.findOtherLineNumbers(lines);
    }

}
