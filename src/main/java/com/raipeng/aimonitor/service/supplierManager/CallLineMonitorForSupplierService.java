package com.raipeng.aimonitor.service.supplierManager;


import com.alibaba.fastjson.JSONObject;
import com.raipeng.aidatacommon.model.CallRecordForHumanMachine;
import com.raipeng.aidatacommon.model.MonitorForLineSupplier;
import com.raipeng.aidatacommon.model.SupplyLine;
import com.raipeng.aidatacommon.model.record.CallRecord;
import com.raipeng.aimonitor.config.HotConfig;
import com.raipeng.aimonitor.context.AIContext;
import com.raipeng.aimonitor.controller.request.*;
import com.raipeng.aimonitor.controller.response.*;
import com.raipeng.aimonitor.entity.CallLineUsageForSupplier;
import com.raipeng.aimonitor.entity.CallRecordForSupplier;
import com.raipeng.aimonitor.enums.CallRecordExceptType;
import com.raipeng.aimonitor.exceptionadvice.exception.AdminCheckException;
import com.raipeng.aimonitor.exceptionadvice.exception.LineCheckException;
import com.raipeng.aimonitor.repository.CallLineSupplierRepository;
import com.raipeng.aimonitor.repository.MonitorForLineSupplierRepository;
import com.raipeng.aimonitor.repository.SupplyLineRepository;
import com.raipeng.aimonitor.service.CallLineMonitorDataService;
import com.raipeng.aimonitor.service.CallLineStatisticService;
import com.raipeng.aimonitor.service.clickhouse.CallRecordClickService;
import com.raipeng.aimonitor.service.clickhouse.CallRecordForAIManClickService;
import com.raipeng.aimonitor.utils.BeanUtils;
import com.raipeng.aimonitor.utils.PushThirdUtil;
import com.raipeng.common.util.StringUtils;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CallLineMonitorForSupplierService {
    @Autowired
    private CallLineMonitorDataService callLineMonitorDataService;

    @Autowired
    private CallRecordClickService callRecordClickService;

    @Autowired
    private CallRecordForAIManClickService callRecordForAIManClickService;

    @Autowired
    private MonitorForLineSupplierRepository monitorForLineSupplierRepository;

    @Autowired
    private SupplyLineRepository supplyLineRepository;

    @Autowired
    private CallLineStatisticService callLineStatisticService;

    @Autowired
    private CallLineSupplierRepository callLineSupplierRepository;

    @Autowired
    private HotConfig hotConfig;

    public int getRemainDecryptCount() {
        MonitorForLineSupplier monitor = getMonitor();
        return monitor.getRemainDecryptCount();
    }

    public CallRecordForSupplierResponse getRecords(FindExceptRecordParam param) {
        MonitorForLineSupplier monitor = getMonitor();
        CallRecordForSupplierResponse callRecordForSupplierResponse = new CallRecordForSupplierResponse();
        String supplierNumber = monitor.getSupplierNumber();
        List<String> supplyLineNumbers = supplyLineRepository.findNumbersByCallLineSupplierNumber(supplierNumber);

        if (supplyLineNumbers.contains(param.getSupplyLineNumber())) {
            Integer countForPureAI = callRecordClickService.finExceptRecordCount(param);
            Integer countForAIMan = callRecordForAIManClickService.finExceptRecordCount(param);
            callRecordForSupplierResponse.setTotal(countForPureAI + countForAIMan);
            if (countForPureAI > param.getPageSize() * (param.getPageNum() + 1)) {
                List<CallRecord> callRecords = callRecordClickService.finExceptRecords(
                        param.getCallOutTimeStart(),
                        param.getCallOutTimeEnd(),
                        param.getSupplyLineNumber(),
                        param.getExceptTypes(),
                        param.getPageSize(),
                        param.getPageNum() * param.getPageSize());
                callRecordForSupplierResponse.setRecords(getCallRecordsForSupplier(callRecords, false));
                return callRecordForSupplierResponse;
            }

            if (countForPureAI <= param.getPageSize() * (param.getPageNum() + 1) && countForPureAI > param.getPageSize() * param.getPageNum()) {
                List<CallRecord> callRecords = callRecordClickService.finExceptRecords(
                        param.getCallOutTimeStart(),
                        param.getCallOutTimeEnd(),
                        param.getSupplyLineNumber(),
                        param.getExceptTypes(),
                        param.getPageSize(),
                        param.getPageNum() * param.getPageSize());
                List<CallRecordForSupplier> callRecordsForSupplier = getCallRecordsForSupplier(callRecords, false);
                List<CallRecordForHumanMachine> callRecordForHumanMachines = callRecordForAIManClickService.finExceptRecords(
                        param.getCallOutTimeStart(), param.getCallOutTimeEnd(),
                        param.getSupplyLineNumber(), param.getExceptTypes(),
                        param.getPageSize() * (param.getPageNum() + 1) - countForPureAI, 0);
                List<CallRecordForSupplier> callRecordsForSupplierForAIMan = getCallRecordsForSupplierForAIMan(callRecordForHumanMachines, false);
                callRecordsForSupplier.addAll(callRecordsForSupplierForAIMan);
                callRecordForSupplierResponse.setRecords(callRecordsForSupplier);
                return callRecordForSupplierResponse;
            }

            if (countForPureAI <= param.getPageSize() * param.getPageNum()) {
                List<CallRecordForHumanMachine> callRecordForHumanMachines = callRecordForAIManClickService.finExceptRecords(
                        param.getCallOutTimeStart(), param.getCallOutTimeEnd(),
                        param.getSupplyLineNumber(), param.getExceptTypes(),
                        param.getPageSize(), param.getPageSize() * param.getPageNum() - countForPureAI);
                callRecordForSupplierResponse.setRecords(getCallRecordsForSupplierForAIMan(callRecordForHumanMachines, false));
                return callRecordForSupplierResponse;
            }
        }
        return callRecordForSupplierResponse;
    }

    public CallRecordForSupplierResponse exportRecords(ExportExceptRecordParam param) {
        MonitorForLineSupplier monitor = getMonitor();
        CallRecordForSupplierResponse callRecordForSupplierResponse = new CallRecordForSupplierResponse();
        String supplierNumber = monitor.getSupplierNumber();
        List<String> supplyLineNumbers = supplyLineRepository.findNumbersByCallLineSupplierNumber(supplierNumber);
        if (supplyLineNumbers.contains(param.getSupplyLineNumber())) {
            Integer remainDecryptCount = monitor.getRemainDecryptCount();
            if (remainDecryptCount - param.getTotalCount() < 0) {
                throw new LineCheckException("当前余量为:" + remainDecryptCount + "请重新调整导出数量");
            }

            List<CallRecordForSupplier> records = new ArrayList<>();
            int totalCount = param.getTotalCount();
            int totalCountResponse = 0;
            List<CallRecord> callRecords = callRecordClickService.finExceptRecords(param.getCallOutTimeStart(),
                    param.getCallOutTimeEnd(), param.getSupplyLineNumber(), param.getExceptTypes(), totalCount, 0);

            if (callRecords.size() > 0) {
                records.addAll(getCallRecordsForSupplier(callRecords, true));
                totalCountResponse += callRecords.size();
            }
            if (callRecords.size() < totalCount) {
                int remainCount = totalCount - callRecords.size();
                List<CallRecordForHumanMachine> callRecordsForHumanMachine = callRecordForAIManClickService.finExceptRecords(
                        param.getCallOutTimeStart(),
                        param.getCallOutTimeEnd(),
                        param.getSupplyLineNumber(),
                        param.getExceptTypes(), remainCount, 0);
                records.addAll(getCallRecordsForSupplierForAIMan(callRecordsForHumanMachine, true));
                totalCountResponse += callRecordsForHumanMachine.size();
            }
            callRecordForSupplierResponse.setRecords(records);
            callRecordForSupplierResponse.setTotal(totalCountResponse);
            if (totalCountResponse > 0) {
                monitor.setRemainDecryptCount(remainDecryptCount - totalCountResponse);
                monitorForLineSupplierRepository.save(monitor);
            }

        }
        return callRecordForSupplierResponse;
    }

    public CheckPlainPhoneResponse checkPlainPhone(CheckPlainPhoneParam param) {
        MonitorForLineSupplier monitor = getMonitor();
        String supplierNumber = monitor.getSupplierNumber();
        List<String> supplyLineNumbers = supplyLineRepository.findNumbersByCallLineSupplierNumber(supplierNumber);
        if (!supplyLineNumbers.contains(param.getSupplyLineNumber())) {
            throw new LineCheckException("当前号码不属于该供应商");
        }
        Integer remainDecryptCount = monitor.getRemainDecryptCount();
        if (remainDecryptCount <= 0) {
            throw new LineCheckException("当前解密余额已用光");
        }
        CheckPlainPhoneResponse checkPlainPhoneResponse = new CheckPlainPhoneResponse();
        List<CallRecord> callRecords = callRecordClickService.finExceptRecord(
                param.getCallOutTime(),
                param.getRecordId(),
                param.getSupplyLineNumber());
        boolean isDecrypt = false;
        if (callRecords.size() != 0) {
            JSONObject jsonObject = PushThirdUtil.getJsonObject(false, callRecords.stream().map(CallRecord::getPhone).collect(Collectors.toList()));
            checkPlainPhoneResponse.setPhone(jsonObject.getString(callRecords.get(0).getPhone()));
            checkPlainPhoneResponse.setRemainCount(monitor.getRemainDecryptCount() - 1);
            isDecrypt = true;
        } else {
            List<CallRecordForHumanMachine> callRecordForHumanMachines = callRecordForAIManClickService.finExceptRecord(
                    param.getCallOutTime(),
                    param.getRecordId(),
                    param.getSupplyLineNumber());
            if (callRecordForHumanMachines.size() != 0) {
                JSONObject jsonObject = PushThirdUtil.getJsonObject(false, callRecordForHumanMachines.stream().map(CallRecordForHumanMachine::getPhone).collect(Collectors.toList()));
                checkPlainPhoneResponse.setPhone(jsonObject.getString(callRecordForHumanMachines.get(0).getPhone()));
                checkPlainPhoneResponse.setRemainCount(monitor.getRemainDecryptCount() - 1);
                isDecrypt = true;
            }
        }
        if (isDecrypt) {
            monitor.setRemainDecryptCount(monitor.getRemainDecryptCount() - 1);
            monitorForLineSupplierRepository.save(monitor);
        }
        return checkPlainPhoneResponse;
    }

    public List<SupplyLineMonitorForSupplier> findSupplyLineMonitorList(SupplyLineMonitorParam param) {
        MonitorForLineSupplier monitor = getMonitor();
        String supplierNumber = monitor.getSupplierNumber();
        List<SupplyLine> supplyLines = supplyLineRepository.findAllByCallLineSupplierNumber(supplierNumber);
        List<SupplyLineMonitorForSupplier> supplyLineMonitorForSuppliers = new ArrayList<>();
        param.setSupplierNumber(monitor.getSupplierNumber());
        List<SupplyLineMonitor> supplyLineMonitorList = callLineMonitorDataService.findSupplyLineMonitorListNoConcurrent(param);
        supplyLineMonitorList.forEach(supplyLineMonitor -> {
            SupplyLineMonitorForSupplier supplyLineMonitorForSupplier = new SupplyLineMonitorForSupplier();
            BeanUtils.copyProperties(supplyLineMonitor, supplyLineMonitorForSupplier);
            supplyLineMonitorForSuppliers.add(supplyLineMonitorForSupplier);
        });

        if (param.getSupplyLineNumber() != null) {
            if (supplyLineMonitorList.size() == 0) {
                SupplyLine supplyLine = supplyLines.stream()
                        .collect(Collectors.toMap(SupplyLine::getLineNumber, Function.identity()))
                        .get(param.getSupplyLineNumber());
                supplyLineMonitorForSuppliers.add(getEmptyLineMonitor(supplyLine));
            }
        } else {
            Set<String> monitorSupplyLineNumbers = supplyLineMonitorList.stream()
                    .map(SupplyLineMonitor::getSupplyLineNumber)
                    .collect(Collectors.toSet());
            for (SupplyLine supplyLine : supplyLines) {
                if (!monitorSupplyLineNumbers.contains(supplyLine.getLineNumber())) {
                    supplyLineMonitorForSuppliers.add(getEmptyLineMonitor(supplyLine));
                }
            }
        }

        return supplyLineMonitorForSuppliers;
    }

    public void resetRemainDecryptCount() {
        List<MonitorForLineSupplier> all = monitorForLineSupplierRepository.findAll();
        all.forEach(monitor -> monitor.setRemainDecryptCount(monitor.getTotalDecryptCount()));
        monitorForLineSupplierRepository.saveAll(all);
    }

    private List<CallRecordForSupplier> getCallRecordsForSupplier(List<CallRecord> callRecords,
                                                                  boolean ifExport) {
        JSONObject jsonObject = null;
        if (ifExport) {
            List<String> phones = callRecords.stream().map(CallRecord::getPhone).collect(Collectors.toList());
            jsonObject = PushThirdUtil.getJsonObject(false, phones);
            if (jsonObject == null) {
                throw new LineCheckException("号码解密失败");
            }
        }
        List<CallRecordForSupplier> records = new ArrayList<>();
        for (CallRecord callRecord : callRecords) {
            CallRecordForSupplier record = new CallRecordForSupplier();
            record.setRecordId(callRecord.getRecordId());
            record.setProvince(callRecord.getProvince());
            record.setCity(callRecord.getCity());
            record.setCallStatusStr(callRecord.getCallStatusStr());
            record.setCallOutTime(callRecord.getCallOutTime());
            record.setTalkTimeStart(callRecord.getTalkTimeStart());
            record.setTalkTimeEnd(callRecord.getTalkTimeEnd());
            record.setCallDurationSec(callRecord.getCallDurationSec());

            if (ifExport && jsonObject.containsKey(callRecord.getPhone())) {
                String plainPhone = jsonObject.getString(callRecord.getPhone());
                if (plainPhone.length() < 7) {
                    continue;
                }
                record.setPlainPhone(plainPhone);
            }

            List<CallRecordExceptType> types = new ArrayList<>();
            String intentionLabels = callRecord.getIntentionLabels();
            if (StringUtils.isNotEmpty(intentionLabels)) {
                if (intentionLabels.contains("小助理")) {
                    types.add(CallRecordExceptType.ROBOT_ASSISTANT);
                }
                if (intentionLabels.contains("沉默挂机")) {
                    types.add(CallRecordExceptType.SILENCE_HANGUP);
                }
            }
            String userFullAnswerContent = callRecord.getUserFullAnswerContent();
            if (StringUtils.isEmpty(userFullAnswerContent)) {
                types.add(CallRecordExceptType.NO_SOUND);
            }
            record.setTypes(types);
            records.add(record);
        }
        return records;
    }

    private List<CallRecordForSupplier> getCallRecordsForSupplierForAIMan(List<CallRecordForHumanMachine> callRecords,
                                                                          boolean ifExport) {
        JSONObject jsonObject = null;
        if (ifExport) {
            List<String> phones = callRecords.stream().map(CallRecordForHumanMachine::getPhone).collect(Collectors.toList());
            jsonObject = PushThirdUtil.getJsonObject(false, phones);
            if (jsonObject == null) {
                throw new LineCheckException("号码解密失败");
            }
        }

        List<CallRecordForSupplier> records = new ArrayList<>();
        for (CallRecordForHumanMachine callRecord : callRecords) {
            CallRecordForSupplier record = new CallRecordForSupplier();
            record.setRecordId(callRecord.getRecordId());
            record.setProvince(callRecord.getProvince());
            record.setCity(callRecord.getCity());
            record.setCallStatusStr(callRecord.getCallStatusStr());
            record.setCallOutTime(callRecord.getCallOutTime());
            record.setTalkTimeStart(callRecord.getTalkTimeStart());
            record.setTalkTimeEnd(callRecord.getTalkTimeEnd());
            record.setCallDurationSec(callRecord.getCallDurationSec());

            if (ifExport && jsonObject.containsKey(callRecord.getPhone())) {
                String plainPhone = jsonObject.getString(callRecord.getPhone());
                if (plainPhone.length() < 7) {
                    continue;
                }
                record.setPlainPhone(plainPhone);
            }

            List<CallRecordExceptType> types = new ArrayList<>();
            String intentionLabels = callRecord.getIntentionLabels();
            if (StringUtils.isNotEmpty(intentionLabels)) {
                if (intentionLabels.contains("小助理")) {
                    types.add(CallRecordExceptType.ROBOT_ASSISTANT);
                }
                if (intentionLabels.contains("沉默挂机")) {
                    types.add(CallRecordExceptType.SILENCE_HANGUP);
                }
            }
            String userFullAnswerContent = callRecord.getUserFullAnswerContent();
            if (StringUtils.isEmpty(userFullAnswerContent)) {
                types.add(CallRecordExceptType.NO_SOUND);
            }
            record.setTypes(types);
            records.add(record);
        }
        return records;
    }

    public List<CallLineUsageForSupplier> getCallLineUsageForSupplier(CallLineUsageForSupplierParam requestParam) {
        List<String> supplierBelongs = hotConfig.getSupplierBelongsForCallLineUsageSearch();
        List<String> supplierNumbers  = callLineSupplierRepository.findSupplierNumbersBySupplierBelongIn(supplierBelongs);
        List<CallLineUsageForSupplier> callLineUsageForSuppliers = new ArrayList<>();
        for (String supplierNumber : supplierNumbers) {
            SupplyLineStatisticsDetailParam param = new SupplyLineStatisticsDetailParam();
            param.setDate(requestParam.getDate());
            param.setSupplierNumber(supplierNumber);
            List<SupplyLineStatisticsDetail> supplyLineStatisticsDetailList = callLineStatisticService.findSupplyLineStatisticsDetailList(param);
            if (supplyLineStatisticsDetailList.size() > 1) {
                for (int i = 0; i < supplyLineStatisticsDetailList.size() - 1; i++) {
                    SupplyLineStatisticsDetail supplyLineStatisticsDetail = supplyLineStatisticsDetailList.get(i);
                    CallLineUsageForSupplier callLineUsageForSupplier = new CallLineUsageForSupplier();
                    BeanUtils.copyProperties(supplyLineStatisticsDetail, callLineUsageForSupplier);
                    callLineUsageForSuppliers.add(callLineUsageForSupplier);
                }
            }
        }
        return callLineUsageForSuppliers;
    }

    private SupplyLineMonitorForSupplier getEmptyLineMonitor(SupplyLine supplyLine) {
        SupplyLineMonitorForSupplier supplyLineMonitor = new SupplyLineMonitorForSupplier();
        supplyLineMonitor.setSupplyLineNumber(supplyLine.getLineNumber());
        supplyLineMonitor.setSupplyLineName(supplyLine.getLineName());
        supplyLineMonitor.setPrefix(supplyLine.getPrefix());
        supplyLineMonitor.setSupplierNumber(supplyLine.getCallLineSupplierNumber());
        supplyLineMonitor.setMasterCallNumber(supplyLine.getMasterCallNumber());
        return supplyLineMonitor;
    }

    private MonitorForLineSupplier getMonitor() {
        String account = AIContext.getAccount();
        MonitorForLineSupplier monitor = monitorForLineSupplierRepository.findByAccount(account);
        if (monitor == null) {
            throw new AdminCheckException("当前账号还没有权限查看线路数据,请联系管理员");
        }
        return monitor;
    }
}
