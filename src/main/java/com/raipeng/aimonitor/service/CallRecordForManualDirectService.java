package com.raipeng.aimonitor.service;


import com.raipeng.aimonitor.entity.ChartDataEntity;
import com.raipeng.aimonitor.entity.ReconciliationDataEntity;
import com.raipeng.aimonitor.repository.CallRecordForManualDirectRepository;
import com.raipeng.common.util.JpaResultUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.Tuple;
import java.util.List;

@Service
public class CallRecordForManualDirectService {
    @Autowired
    private CallRecordForManualDirectRepository callRecordForManualDirectRepository;

    public List<ChartDataEntity> getChartDataStatistic(String startTime, String endTime, List<String> accounts, List<String> tenantLineNumbers) {
        List<Tuple> tuples = callRecordForManualDirectRepository.chartDataStatistic(startTime, endTime, accounts, tenantLineNumbers);
        List<ChartDataEntity> entities = JpaResultUtils.processResult(tuples, ChartDataEntity.class);
        entities.forEach(entity -> {
            entity.setSuccessCount(entity.getTempCount().doubleValue());
        });
        return entities;
    }

    public List<ReconciliationDataEntity> getReconciliationData(String startTime, String endTime, List<String> accounts) {
        List<Tuple> tuples = callRecordForManualDirectRepository.reconciliationDataStatistic(startTime, endTime, accounts);
        return JpaResultUtils.processResult(tuples, ReconciliationDataEntity.class);
    }
}
