package com.raipeng.aimonitor.model;

import lombok.*;


@Data
public class CallLineMonitorUnitData {
    private Integer timeSlot;

    private String tenantLineNumber;

    private String supplyLineNumber;

    private Integer totalCallNum;

    private Integer totalConnectNum;

    private Integer silenceCallNum;

    private Integer oneSecondConnectedNum;

    private Integer twoSecondConnectedNum;

    private Integer silenceHangupNum;

    private Integer assistantNum;

    private Integer promptSoundNum;

    private Integer transCallSeatNum;

    private Integer classANum;

    private Integer classBNum;

    private Integer classCNum;

    private Integer classDNum;

    private Integer callFailedNum;

    private Integer routingFailNum;

    private String province;

    private String city;

    private String operator;

    private Integer waitSecond;

    private Integer callDurationSecond;

    public CallLineMonitorUnitData() {
        totalCallNum = 0;
        totalConnectNum = 0;
        silenceCallNum = 0;
        oneSecondConnectedNum = 0;
        twoSecondConnectedNum = 0;
        silenceHangupNum = 0;
        callFailedNum = 0;
        routingFailNum = 0;
        assistantNum = 0;
        promptSoundNum = 0;
        transCallSeatNum = 0;
        classANum = 0;
        classBNum = 0;
        classCNum = 0;
        classDNum = 0;
        waitSecond = 0;
        callDurationSecond = 0;
    }
}
