package com.raipeng.aimonitor.utils;

import com.raipeng.aimonitor.controller.response.MonitorBase;
import com.raipeng.aimonitor.model.CallLineMonitorUnitData;

import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class CallLineUtil {
    private static final DecimalFormat decimalFormat = new DecimalFormat("#.##");

    public static List<Integer> SCAN_MINUTES;

    static {
        SCAN_MINUTES = new ArrayList<>();
        for (int i=8 * 60; i <= 22 * 60; i=i+5) {
            SCAN_MINUTES.add(i);
        }
    }

    public static LocalDateTime minuteConvertLocalDateTime(int moment, LocalDateTime localDateTime) {
        int hour = moment / 60;
        int minute = moment % 60;
        return localDateTime.withHour(hour).withMinute(minute).withSecond(0).withNano(0);
    }

    public static <T extends MonitorBase> void assembleMonitorData(T monitor, CallLineMonitorUnitData totalMomentData, CallLineMonitorUnitData totalPastData) {
        String currentlyConnectedRate = totalMomentData.getTotalCallNum() == 0 ? "0": decimalFormat.format(100.0 * totalMomentData.getTotalConnectNum() / totalMomentData.getTotalCallNum()) + "%";
        String currentlyConnectedNum = String.valueOf(totalMomentData.getTotalConnectNum());
        String todayConnectedRate = totalPastData.getTotalCallNum() == 0 ? "0" : decimalFormat.format(100.0 * totalPastData.getTotalConnectNum() / totalPastData.getTotalCallNum()) + "%";
        String todayConnectedNum = String.valueOf(totalPastData.getTotalConnectNum());
        monitor.setCurrentlyCallNum(totalMomentData.getTotalCallNum());
        monitor.setTotalCallNum(totalPastData.getTotalCallNum());
        monitor.setCurrentlyConnectedRate(currentlyConnectedRate);
        monitor.setCurrentlyConnectedNum(currentlyConnectedNum);
        monitor.setCurrentlyConnected(currentlyConnectedRate + " / " + currentlyConnectedNum);
        monitor.setTodayConnectedRate(todayConnectedRate);
        monitor.setTodayConnectedNum(todayConnectedNum);
        monitor.setTodayConnected(todayConnectedRate + " / " + todayConnectedNum);
        monitor.setCurrentlySilenceCall(totalMomentData.getTotalConnectNum() == 0 ? "0" : decimalFormat.format(100.0 * totalMomentData.getSilenceCallNum() / totalMomentData.getTotalConnectNum()) + "% / " + totalMomentData.getSilenceCallNum());
        monitor.setTodaySilenceCall(totalPastData.getTotalConnectNum() == 0 ? "0" : decimalFormat.format(100.0 * totalPastData.getSilenceCallNum() / totalPastData.getTotalConnectNum()) + "% / " + totalPastData.getSilenceCallNum());
        monitor.setCurrentlyOneSecondConnected(totalMomentData.getTotalConnectNum() == 0 ? "0": decimalFormat.format(100.0 * totalMomentData.getOneSecondConnectedNum() / totalMomentData.getTotalConnectNum()) + "% / " + totalMomentData.getOneSecondConnectedNum());
        monitor.setTodayOneSecondConnected(totalPastData.getTotalConnectNum() == 0 ? "0" : decimalFormat.format(100.0 * totalPastData.getOneSecondConnectedNum() / totalPastData.getTotalConnectNum()) + "% / " + totalPastData.getOneSecondConnectedNum());
        monitor.setCurrentlyTwoSecondConnected(totalMomentData.getTotalConnectNum() == 0 ? "0" : decimalFormat.format(100.0 * totalMomentData.getTwoSecondConnectedNum() / totalMomentData.getTotalConnectNum()) + "% / " + totalMomentData.getTwoSecondConnectedNum());
        monitor.setTodayTwoSecondConnected(totalPastData.getTotalConnectNum() == 0 ? "0" : decimalFormat.format(100.0 * totalPastData.getTwoSecondConnectedNum() / totalPastData.getTotalConnectNum()) + "% / " + totalPastData.getTwoSecondConnectedNum());
        monitor.setCurrentlySilenceHangup(totalMomentData.getTotalConnectNum() == 0 ? "0" : decimalFormat.format(100.0 * totalMomentData.getSilenceHangupNum() / totalMomentData.getTotalConnectNum()) + "% / " + totalMomentData.getSilenceHangupNum());
        monitor.setTodaySilenceHangup(totalPastData.getTotalConnectNum() == 0 ? "0" : decimalFormat.format(100.0 * totalPastData.getSilenceHangupNum() / totalPastData.getTotalConnectNum()) + "% / " + totalPastData.getSilenceHangupNum());
        monitor.setCurrentlyAssistant(totalMomentData.getTotalConnectNum() == 0 ? "0" : decimalFormat.format(100.0 * totalMomentData.getAssistantNum() / totalMomentData.getTotalConnectNum()) + "% / " + totalMomentData.getAssistantNum());
        monitor.setTodayAssistant(totalPastData.getTotalConnectNum() == 0 ? "0" : decimalFormat.format(100.0 * totalPastData.getAssistantNum() / totalPastData.getTotalConnectNum()) + "% / " + totalPastData.getAssistantNum());
        monitor.setCurrentlyPromptSound(totalMomentData.getTotalConnectNum() == 0 ? "0" : decimalFormat.format(100.0 * totalMomentData.getPromptSoundNum() / totalMomentData.getTotalConnectNum()) + "% / " + totalMomentData.getPromptSoundNum());
        monitor.setTodayPromptSound(totalPastData.getTotalConnectNum() == 0 ? "0" : decimalFormat.format(100.0 * totalPastData.getPromptSoundNum() / totalPastData.getTotalConnectNum()) + "% / " + totalPastData.getPromptSoundNum());
        monitor.setCurrentlyTransCallSeatNum(totalMomentData.getTotalConnectNum() == 0 ? "0" : decimalFormat.format(100.0 * totalMomentData.getTransCallSeatNum() / totalMomentData.getTotalConnectNum()) + "% / " + totalMomentData.getTransCallSeatNum());
        monitor.setTodayTransCallSeatNum(totalPastData.getTotalConnectNum() == 0 ? "0" : decimalFormat.format(100.0 * totalPastData.getTransCallSeatNum() / totalPastData.getTotalConnectNum()) + "% / " + totalPastData.getTransCallSeatNum());
        monitor.setCurrentlyClassANum(totalMomentData.getTotalConnectNum() == 0 ? "0" : decimalFormat.format(100.0 * totalMomentData.getClassANum() / totalMomentData.getTotalConnectNum()) + "% / " + totalMomentData.getClassANum());
        monitor.setTodayClassANum(totalPastData.getTotalConnectNum() == 0 ? "0" : decimalFormat.format(100.0 * totalPastData.getClassANum() / totalPastData.getTotalConnectNum()) + "% / " + totalPastData.getClassANum());
        monitor.setCurrentlyClassBNum(totalMomentData.getTotalConnectNum() == 0 ? "0" : decimalFormat.format(100.0 * totalMomentData.getClassBNum() / totalMomentData.getTotalConnectNum()) + "% / " + totalMomentData.getClassBNum());
        monitor.setTodayClassBNum(totalPastData.getTotalConnectNum() == 0 ? "0" : decimalFormat.format(100.0 * totalPastData.getClassBNum() / totalPastData.getTotalConnectNum()) + "% / " + totalPastData.getClassBNum());
        monitor.setCurrentlyClassCNum(totalMomentData.getTotalConnectNum() == 0 ? "0" : decimalFormat.format(100.0 * totalMomentData.getClassCNum() / totalMomentData.getTotalConnectNum()) + "% / " + totalMomentData.getClassCNum());
        monitor.setTodayClassCNum(totalPastData.getTotalConnectNum() == 0 ? "0" : decimalFormat.format(100.0 * totalPastData.getClassCNum() / totalPastData.getTotalConnectNum()) + "% / " + totalPastData.getClassCNum());
        monitor.setCurrentlyClassDNum(totalMomentData.getTotalConnectNum() == 0 ? "0" : decimalFormat.format(100.0 * totalMomentData.getClassDNum() / totalMomentData.getTotalConnectNum()) + "% / " + totalMomentData.getClassDNum());
        monitor.setTodayClassDNum(totalPastData.getTotalConnectNum() == 0 ? "0" : decimalFormat.format(100.0 * totalPastData.getClassDNum() / totalPastData.getTotalConnectNum()) + "% / " + totalPastData.getClassDNum());
        monitor.setCurrentlyCallFailed(totalMomentData.getTotalCallNum() == 0 ? "0" : decimalFormat.format(100.0 * totalMomentData.getCallFailedNum() / totalMomentData.getTotalCallNum()) + "% / " + totalMomentData.getCallFailedNum());
        monitor.setTodayCallFailed(totalPastData.getTotalCallNum() == 0 ? "0": decimalFormat.format(100.0 * totalPastData.getCallFailedNum() / totalPastData.getTotalCallNum()) + "% / " + totalPastData.getCallFailedNum());
        monitor.setCurrentlyRoutingFail(totalMomentData.getTotalCallNum() == 0 ? "0" : decimalFormat.format(100.0 * totalMomentData.getRoutingFailNum() / (totalMomentData.getTotalCallNum() + totalMomentData.getRoutingFailNum())) + "% / " + totalMomentData.getRoutingFailNum());
        monitor.setTodayRoutingFail(totalPastData.getTotalCallNum() == 0 ? "0" : decimalFormat.format(100.0 * totalPastData.getRoutingFailNum() / (totalPastData.getTotalCallNum() + totalPastData.getRoutingFailNum())) + "% / " + totalPastData.getRoutingFailNum());
        monitor.setCurrentlyWaitSecond(totalMomentData.getWaitSecond().toString());
        monitor.setTodayWaitSecond(totalPastData.getWaitSecond().toString());
        monitor.setCurrentlyCallDurationSecond(totalMomentData.getCallDurationSecond().toString());
        monitor.setTodayCallDurationSecond(totalPastData.getCallDurationSecond().toString());
    }
}
