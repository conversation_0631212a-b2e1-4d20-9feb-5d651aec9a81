package com.raipeng.aimonitor.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@RefreshScope
@Configuration
public class HotConfig {
    @Value("${supplier.numbers.for.third.search}")
    private String supplierNumbersForThirdSearch;

    @Value("${push.batch.size.for.manual:500}")
    private int pushBatchSizeForManual;

    @Value("${max.push.third.length:1000000}")
    private int pushThirdLength;

    @Value("${push.batch.size.for.update.night:5000}")
    private int pushBatchSizeForUpdateNight;

    @Value("${max.supplier.monitor.count:30}")
    private int maxSupplierMonitorCount;

    @Value("${select.dialog.count:10000}")
    private int selectDialogCount;

    @Value("${find.record.time.slicing:10}")
    private int findRecordTimeSlicing;

    @Value("${supplier.belongs.for.call.line.usage.search:DXYS;XSCS;XRZL}")
    private String supplierBelongsForCallLineUsageSearch;

    @Value("${chart.data.statistic.tenant.ids:1;12}")
    private String chartDataStatisticTenantIds;

    public String getSupplierNumbersForThirdSearch() {
        return supplierNumbersForThirdSearch;
    }

    public int getPushBatchSizeForManual() {
        return pushBatchSizeForManual;
    }

    public int getPushThirdLength() {
        return pushThirdLength;
    }

    public int getSelectDialogCount() {
        return selectDialogCount;
    }

    public int getFindRecordTimeSlicing() {
        return findRecordTimeSlicing;
    }

    public int getPushBatchSizeForUpdateNight() {
        return pushBatchSizeForUpdateNight;
    }

    public int getMaxSupplierMonitorCount() {
        return maxSupplierMonitorCount;
    }

    public List<String> getSupplierBelongsForCallLineUsageSearch() {
        return Arrays.asList(supplierBelongsForCallLineUsageSearch.split(";"));
    }

    public List<Long> getChartDataStatisticTenantIds() {
        List<String> stringIds = new ArrayList<>(Arrays.asList(chartDataStatisticTenantIds.split(";")));
        List<Long> list = new ArrayList<>();
        for (String tenantId : stringIds) {
            list.add(Long.parseLong(tenantId));
        }
        return list;
    }
}
