package com.raipeng.aimonitor.repository;

import com.raipeng.aidatacommon.model.CallLineSupplier;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;


public interface CallLineSupplierRepository extends JpaRepository<CallLineSupplier, Long> {
    @Query(value = "SELECT supplier_number FROM call_line_supplier WHERE supplier_belong IN :supplierBelongs", nativeQuery = true)
    List<String> findSupplierNumbersBySupplierBelongIn(List<String> supplierBelongs);

    List<CallLineSupplier> findBySupplierBelongIn(List<String> supplierBelongs);
}
