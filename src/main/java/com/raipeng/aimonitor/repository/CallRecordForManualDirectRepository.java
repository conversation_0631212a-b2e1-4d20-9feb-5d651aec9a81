package com.raipeng.aimonitor.repository;

import com.raipeng.aidatacommon.model.CallRecordForManualDirect;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import javax.persistence.Tuple;
import java.time.LocalDateTime;
import java.util.List;

public interface CallRecordForManualDirectRepository extends JpaRepository<CallRecordForManualDirect, String> {
    @Query(value="select " +
            "SUM(1) AS totalCallNum, " +
            "SUM(CASE WHEN t.call_status = '7' THEN 1 ELSE 0 END) AS totalConnectNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND (t.user_full_answer_content is null or t.user_full_answer_content = '') THEN 1 ELSE 0 END) AS silenceCallNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.call_duration <= 1000 THEN 1 ELSE 0 END) AS oneSecondConnectedNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.call_duration <= 2000 THEN 1 ELSE 0 END) AS twoSecondConnectedNum, " +
            "SUM(CASE WHEN t.waitmsec <= 1000 AND t.waitmsec >= 0 THEN 1 ELSE 0 END) AS callFailedNum, " +
            "SUM(t.waitmsec)/1000 AS waitSecond, " +
            "SUM(t.call_duration_sec) AS callDurationSecond, " +
            "t.merchant_line_code AS tenantLineNumber, " +
            "t.line_id AS supplyLineNumber, " +
            "t.province AS province," +
            "t.city AS city, " +
            "t.operator AS operator " +
            "from call_record_for_manual_direct t " +
            "where t.update_time>=:startTime " +
            "and t.update_time<:endTime " +
            "and t.call_status is not null " +
            "and t.merchant_line_code is not null " +
            "and waitmsec is not null " +
            "and waitmsec >= 0 " +
            "group by t.merchant_line_code, t.line_id, t.province, t.city, t.operator", nativeQuery = true)
    List<Tuple> scanCallRecordsByStartAndEndTimes(LocalDateTime startTime, LocalDateTime endTime);

    @Query(value = "SELECT " +
            "COUNT(1) AS routingFailNum, " +
            "t.merchant_line_code AS tenantLineNumber " +
            "FROM call_record_for_manual_direct t " +
            "WHERE t.call_status = '1' " +
            "AND t.merchant_line_code is not null " +
            "AND t.update_time>=:startTime " +
            "AND t.update_time<:endTime " +
            "GROUP BY t.merchant_line_code", nativeQuery = true)
    List<Tuple> scanCallRecordsForRoutingFailByStartAndEndTimes(LocalDateTime startTime, LocalDateTime endTime);

    @Query(value = "SELECT " +
            "SUM(1) AS totalCallNum," +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN 1 ELSE 0 END) AS totalConnectNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN t.call_duration ELSE 0 END) / 60000 AS totalConnectedMinutes, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN call_duration % 60000.0 >= 100 THEN ceil(call_duration / 60000.0) ELSE floor(call_duration / 60000.0) END) ELSE 0 END) AS calculateNumOfSixty, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN call_duration % 6000.0 >= 100 THEN ceil(call_duration / 6000.0) ELSE floor(call_duration / 6000.0) END) ELSE 0 END) AS calculateNumOfSix, " +
            "t.account AS account " +
            "FROM call_record_for_manual_direct t " +
            "WHERE t.call_out_time >= :callOutTime " +
            "AND t.call_status IS NOT NULL " +
            "AND t.waitmsec IS NOT NULL " +
            "AND t.waitmsec >= 0 " +
            "GROUP BY t.account;", nativeQuery = true)
    List<Tuple> findCallDataByAccount(String callOutTime);

    @Query(value = "SELECT " +
            "SUM(1) AS totalCallNum," +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN 1 ELSE 0 END) AS totalConnectNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN t.call_duration ELSE 0 END) / 60000 AS totalConnectedMinutes, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN call_duration % 60000.0 >= 100 THEN ceil(call_duration / 60000.0) ELSE floor(call_duration / 60000.0) END) ELSE 0 END) AS calculateNumOfSixty, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN call_duration % 6000.0 >= 100 THEN ceil(call_duration / 6000.0) ELSE floor(call_duration / 6000.0) END) ELSE 0 END) AS calculateNumOfSix, " +
            "t.account AS account " +
            "FROM call_record_for_manual_direct t " +
            "WHERE t.call_out_time >= :startTime " +
            "AND t.call_out_time < :endTime " +
            "AND t.call_status IS NOT NULL " +
            "AND t.waitmsec IS NOT NULL " +
            "AND t.waitmsec >= 0 " +
            "GROUP BY t.account;", nativeQuery = true)
    List<Tuple> findCallDataHistoryByAccount(String startTime, String endTime);

    @Query(value = "SELECT " +
            "SUM(1) AS totalCallNum," +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN 1 ELSE 0 END) AS totalConnectNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN t.call_duration ELSE 0 END) AS totalConnectedDuration, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN call_duration % 60000.0 >= 100 THEN ceil(call_duration / 60000.0) ELSE floor(call_duration / 60000.0) END) ELSE 0 END) AS calculateNumOfSixty, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN call_duration % 6000.0 >= 100 THEN ceil(call_duration / 6000.0) ELSE floor(call_duration / 6000.0) END) ELSE 0 END) AS calculateNumOfSix, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN t.call_duration <= 1000 THEN 1 ELSE 0 END) ELSE 0 END) AS oneSecondConnectedNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN (t.call_duration <= 2000 AND t.call_duration > 1000) THEN 1 ELSE 0 END) ELSE 0 END) AS twoSecondConnectedNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN (t.user_full_answer_content is null or t.user_full_answer_content='') THEN 1 ELSE 0 END) ELSE 0 END) AS silenceCallNum, " +
            "SUM(CASE WHEN (t.waitmsec <= 1000 AND t.waitmsec >= 0) THEN 1 ELSE 0 END) AS callFailedNum, " +
            "t.merchant_line_code AS tenantLineNumber, " +
            "t.line_id AS supplyLineNumber " +
            "FROM call_record_for_manual_direct t " +
            "WHERE t.call_out_time >= :startTime " +
            "AND t.call_out_time < :endTime " +
            "AND t.call_status IS NOT NULL " +
            "AND t.waitmsec IS NOT NULL " +
            "AND t.waitmsec >= 0 " +
            "GROUP BY t.merchant_line_code, t.line_id;", nativeQuery = true)
    List<Tuple> findOneDayCallRecordsForLineStatistic(String startTime, String endTime);

    @Query(value = "SELECT " +
            "SUM(1) AS totalCallNum," +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN 1 ELSE 0 END) AS totalConnectNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN t.call_duration ELSE 0 END) AS totalConnectedDuration, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN call_duration % 60000.0 >= 100 THEN ceil(call_duration / 60000.0) ELSE floor(call_duration / 60000.0) END) ELSE 0 END) AS calculateNumOfSixty, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN call_duration % 6000.0 >= 100 THEN ceil(call_duration / 6000.0) ELSE floor(call_duration / 6000.0) END) ELSE 0 END) AS calculateNumOfSix, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN t.call_duration <= 1000 THEN 1 ELSE 0 END) ELSE 0 END) AS oneSecondConnectedNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN (t.call_duration <= 2000 AND t.call_duration > 1000) THEN 1 ELSE 0 END) ELSE 0 END) AS twoSecondConnectedNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN (t.user_full_answer_content is null or t.user_full_answer_content='') THEN 1 ELSE 0 END) ELSE 0 END) AS silenceCallNum, " +
            "SUM(CASE WHEN (t.waitmsec <= 1000 AND t.waitmsec >= 0) THEN 1 ELSE 0 END) AS callFailedNum, " +
            "t.merchant_line_code AS tenantLineNumber, " +
            "t.line_id AS supplyLineNumber " +
            "FROM call_record_for_manual_direct t " +
            "WHERE t.call_out_time >= :startTime " +
            "AND t.call_out_time < :endTime " +
            "AND t.call_status IS NOT NULL " +
            "AND t.waitmsec IS NOT NULL " +
            "AND t.waitmsec >= 0 " +
            "GROUP BY t.merchant_line_code, t.line_id;", nativeQuery = true)
    List<Tuple> findOneDayCallRecordsForLineStatisticHistory(String startTime, String endTime);

    @Query(value = "SELECT * " +
            "FROM call_record_for_manual_direct t " +
            "WHERE t.call_out_time >= :startTime " +
            "AND t.call_out_time < :endTime " +
            "AND t.call_status IS NOT NULL " +
            "AND t.waitmsec IS NOT NULL " +
            "AND t.waitmsec >= 0 " +
            "AND t.group_id = :groupId ", nativeQuery = true)
    List<CallRecordForManualDirect> findRecordsByTaskIds(String startTime, String endTime, String groupId);

    @Query(value = "SELECT " +
            "COUNT(1) AS callNum," +
            "t.call_duration_sec AS second " +
            "FROM call_record_for_manual_direct t " +
            "WHERE  t.call_out_time > :startTime " +
            "AND    t.call_out_time <= :endTime " +
            "AND    t.call_duration_sec <= 70 " +
            "AND    t.line_id = :supplyLineNumber " +
            "AND    t.call_status = '7' " +
            "AND    t.call_duration_sec is not null " +
            "GROUP BY t.call_duration_sec " , nativeQuery = true)
    List<Tuple> statisticSupplyLineDurationGroupBySecond(String startTime, String endTime, String supplyLineNumber);

    @Query(value = "SELECT " +
            "COUNT(1) AS callNum," +
            "t.call_duration_sec AS second " +
            "FROM call_record_for_manual_direct t " +
            "WHERE  t.call_out_time > :startTime " +
            "AND    t.call_out_time <= :endTime " +
            "AND    t.call_duration_sec <= 70 " +
            "AND    t.merchant_line_code = :tenantLineNumber " +
            "AND    t.call_status = '7' " +
            "AND    t.call_duration_sec is not null " +
            "GROUP BY t.call_duration_sec " , nativeQuery = true)
    List<Tuple> statisticTenantLineDurationGroupBySecond(String startTime, String endTime, String tenantLineNumber);

    @Query(value = "SELECT " +
            "COUNT(1) AS callNum " +
            "FROM call_record_for_manual_direct t " +
            "WHERE  t.call_out_time > :startTime " +
            "AND    t.call_out_time <= :endTime " +
            "AND    t.call_duration_sec > 70 " +
            "AND    t.line_id = :supplyLineNumber " +
            "AND    t.call_status = '7' " +
            "AND    t.call_duration_sec is not null  " , nativeQuery = true)
    int statisticSupplyLineDurationUpSecond(String startTime, String endTime, String supplyLineNumber);

    @Query(value = "SELECT " +
            "COUNT(1) AS callNum " +
            "FROM call_record_for_manual_direct t " +
            "WHERE  t.call_out_time > :startTime " +
            "AND    t.call_out_time <= :endTime " +
            "AND    t.call_duration_sec > 70 " +
            "AND    t.merchant_line_code = :tenantLineNumber " +
            "AND    t.call_status = '7' " +
            "AND    t.call_duration_sec is not null " , nativeQuery = true)
    int statisticTenantLineDurationUpSecond(String startTime, String endTime, String tenantLineNumber);

    @Query(value = "SELECT " +
            "SUM(CASE WHEN call_duration % 60000.0 >= 100 THEN ceil(call_duration / 60000.0) ELSE floor(call_duration / 60000.0) END) AS tempCount," +
            "t.line_id AS supplyLineNumber, " +
            "t.account AS account " +
            "FROM call_record_for_manual_direct t " +
            "WHERE t.call_out_time >= :startTime " +
            "AND t.call_out_time < :endTime " +
            "AND t.call_status='7' " +
            "AND t.account IN :accounts " +
            "AND t.merchant_line_code IN :tenantLineNumbers " +
            "AND t.whole_audio_file_url <> '' " +
            "AND t.waitmsec >= 0 " +
            "GROUP BY t.line_id, t.account", nativeQuery = true)
    List<Tuple> chartDataStatistic(String startTime, String endTime, List<String> accounts, List<String> tenantLineNumbers);

    @Query(value = "SELECT " +
            "SUM(CASE WHEN call_duration % 60000.0 >= 100 THEN ceil(call_duration / 60000.0) ELSE floor(call_duration / 60000.0) END) AS successCount," +
            "t.line_id AS supplyLineNumber, " +
            "t.account AS account " +
            "FROM call_record_for_manual_direct t " +
            "WHERE t.call_out_time >= :startTime " +
            "AND t.call_out_time < :endTime " +
            "AND t.call_status='7' " +
            "AND t.account IN :accounts " +
            "AND t.whole_audio_file_url <> '' " +
            "AND t.waitmsec >= 0 " +
            "GROUP BY t.line_id, t.account", nativeQuery = true)
    List<Tuple> reconciliationDataStatistic(String startTime, String endTime, List<String> accounts);
}
