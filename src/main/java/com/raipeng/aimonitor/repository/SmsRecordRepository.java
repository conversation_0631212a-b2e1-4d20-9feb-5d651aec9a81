package com.raipeng.aimonitor.repository;


import com.raipeng.aidatacommon.model.SmsRecord;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import javax.persistence.Tuple;
import java.util.List;

public interface SmsRecordRepository extends JpaRepository<SmsRecord, Long> {
    @Query(value = "SELECT " +
            "COUNT(1) AS sendCount, " +
            "t.province AS province, " +
            "t.city AS city, " +
            "t.ordinary_link_domain_original_urls AS linkDomainUrls " +
            "FROM t_sms_record t " +
            "WHERE t.receipt_status='SEND_SUCCESS' " +
            "AND t.trigger_time >= :startTime " +
            "AND t.trigger_time < :endTime " +
            "AND t.ordinary_link_domain_original_urls IS NOT NULL " +
            "AND t.ordinary_link_domain_original_urls != ''" +
            "GROUP BY " +
            "t.province, t.city, t.ordinary_link_domain_original_urls", nativeQuery = true)
    List<Tuple> findSendCountForShortLinkOrdinary(String startTime, String endTime);

    @Query(value = "SELECT " +
            "COUNT(1) AS sendCount, " +
            "t.province AS province, " +
            "t.city AS city, " +
            "t.thousand_link_domain_original_urls AS linkDomainUrls " +
            "FROM t_sms_record t " +
            "WHERE t.receipt_status='SEND_SUCCESS' " +
            "AND t.trigger_time >= :startTime " +
            "AND t.trigger_time < :endTime " +
            "AND t.thousand_link_domain_original_urls IS NOT NULL " +
            "AND t.thousand_link_domain_original_urls != '' " +
            "GROUP BY " +
            "t.province, t.city, t.thousand_link_domain_original_urls", nativeQuery = true)
    List<Tuple> findSendCountForShortLinkThousand(String startTime, String endTime);

    @Query(value = "select * from t_sms_record t  " +
            "where  \n" +
            " t.trigger_time >= :startTime and t.trigger_time < :endTime  \n" +
            "and t.group_id = :groupId and t.last_compensation ='1' and t.message_type = 'DIRECT' ", nativeQuery = true)
    List<SmsRecord> findSmsRecordByTriggerTime(String startTime, String endTime, String groupId);

    @Query(value = " select count(1) as total," +
            " count(case when t.record_type = 'AI_AUTO'  then 1 end) as aiAutoCount," +
            " count(case when t.record_type = 'AI_MANUAL'  then 1 end) as aiManualCount" +
            " from t_sms_record t  " +
            "where  \n" +
            " t.trigger_time >= :startTime and t.trigger_time < :endTime  \n" +
            "and t.group_id = :groupId and t.sms_status ='SEND_SUCCESS' ", nativeQuery = true)
     Tuple findSmsSuccessCountByTriggerTime(String startTime, String endTime, String groupId);

    @Query(value = " select count(1) as total," +
            " count(case when t.record_type = 'AI_AUTO'  then 1 end) as aiAutoCount," +
            " count(case when t.record_type = 'AI_MANUAL'  then 1 end) as aiManualCount" +
            " from t_sms_record t  " +
            "where  \n" +
            " t.trigger_time >= :startTime and t.trigger_time < :endTime  \n" +
            "and t.group_id = :groupId and t.last_compensation ='1'", nativeQuery = true)
    Tuple findSmsCountByTriggerTime(String startTime, String endTime, String groupId);



    @Query(value = "select count(distinct (t.task_id, t.phone)) from t_sms_record t \n" +
            "where  \n" +
            "t.trigger_time >= :startTime \n" +
            "and t.trigger_time < :endTime \n" +
            "and t.task_id in :taskIdList \n" +
            "and  (:provinceCode is null or t.province_code = cast(:provinceCode as text)) \n" +
            "and  (:cityCode is null or t.city_code = cast(:cityCode as text)) \n" +
            "and  (:operator is null or t.operator = cast(:operator as text)) \n" +
            "\tAND (('EMPTY' in :scriptStringIdList) OR t.script_string_id in :scriptStringIdList) \n" +

            "and t.sms_status = 'SEND_SUCCESS' \n" +
            "and t.last_compensation = '1'", nativeQuery = true)
    int findSuccessCountByTaskId(String startTime, String endTime, List<String> taskIdList, String provinceCode,String cityCode,String operator, List<String> scriptStringIdList);


    @Query(value = "select count(distinct (t.task_id, t.phone)) from t_sms_record t \n" +
            "where  \n" +
            "t.trigger_time >= :startTime \n" +
            "and t.trigger_time < :endTime \n" +
            "and t.group_id = :groupId \n" +
            "and t.sms_status = 'SEND_SUCCESS' \n" +
            "and t.last_compensation = '1'", nativeQuery = true)
    int findSuccessCountByGroupId(String startTime, String endTime, String groupId);

    @Query(value = "select count(distinct (t.task_id, t.phone)) from t_sms_record t \n" +
            "where  \n" +
            "t.trigger_time >= :startTime \n" +
            "and t.trigger_time < :endTime \n" +
            "and t.task_id in :taskIdList \n" +
            "and  (:provinceCode is null or t.province_code = cast(:provinceCode as text)) \n" +
            "and  (:cityCode is null or t.city_code = cast(:cityCode as text)) \n" +
            "and  (:operator is null or t.operator = cast(:operator as text)) \n" +
            "\tAND (('EMPTY' in :scriptStringIdList) OR t.script_string_id in :scriptStringIdList) \n" +

            "and t.last_compensation = '1'", nativeQuery = true)
    int findTriggerCountByTaskId(String startTime, String endTime, List<String> taskIdList, String provinceCode,String cityCode,String operator, List<String> scriptStringIdList);

    @Query(value = "select count(distinct (t.task_id, t.phone)) from t_sms_record t \n" +
            "where  \n" +
            "t.trigger_time >= :startTime \n" +
            "and t.trigger_time < :endTime \n" +
            "and t.group_id = :groupId \n" +
            "and t.last_compensation = '1'", nativeQuery = true)
    int findTriggerCount(String startTime, String endTime, String groupId);


    @Query(value = "SELECT COUNT(1) " +
            "FROM t_sms_record " +
            "WHERE trigger_time > :startTime " +
            "AND   trigger_time < :endTime " +
            "AND   account in :accounts " +
            "AND   industry_second_field_id = :secondIndustryId " +
            "AND   receipt_status = 'SEND_SUCCESS' " +
            "AND   sms_content not like '%[M]%' ", nativeQuery = true)
    Integer findBZCountByDate(String startTime, String endTime, List<String> accounts, String secondIndustryId);

    @Query(value = "SELECT COUNT(1) " +
            "FROM t_sms_record " +
            "WHERE trigger_time > :startTime " +
            "AND   trigger_time < :endTime " +
            "AND   account in :accounts " +
            "AND   industry_second_field_id NOT IN :secondIndustryIds " +
            "AND   receipt_status = 'SEND_SUCCESS' " +
            "AND   sms_content not like '%[M]%' ", nativeQuery = true)
    Integer findBZCountByDateAndIndustryListNotIn(String startTime, String endTime, List<String> accounts, List<String> secondIndustryIds);


    @Query(value = "SELECT task_id as taskId, COUNT(DISTINCT PHONE) AS totalCount, COUNT(DISTINCT PHONE) FILTER (WHERE sms_status = 'SEND_SUCCESS') AS successCount \n" +
            "FROM t_sms_record where group_id = :groupId and last_compensation = '1' group by task_id \n", nativeQuery = true)
    List<Tuple> findMessageRecordStatisticByGroupId(String groupId);
}