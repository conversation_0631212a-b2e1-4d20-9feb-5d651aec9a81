package com.raipeng.aimonitor.repository;

import com.raipeng.aidatacommon.model.CallRecordForHumanMachine;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import javax.persistence.Tuple;
import java.time.LocalDateTime;
import java.util.List;


public interface CallRecordForHumanMachineRepository extends JpaRepository<CallRecordForHumanMachine, String> {
    @Query(value = "select " +
            "SUM(1) AS totalCallNum, " +
            "SUM(CASE WHEN t.call_status = '7' THEN 1 ELSE 0 END) AS totalConnectNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.say_count = 0 THEN 1 ELSE 0 END) AS silenceCallNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.call_duration <= 1000 THEN 1 ELSE 0 END) AS oneSecondConnectedNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.call_duration <= 2000 THEN 1 ELSE 0 END) AS twoSecondConnectedNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.intention_labels LIKE '%沉默挂机%' THEN 1 ELSE 0 END) AS silenceHangupNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.intention_labels LIKE '%小助理%' THEN 1 ELSE 0 END) AS assistantNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.intention_labels LIKE '%运营商提示音%' THEN 1 ELSE 0 END) AS promptSoundNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.is_trans_to_call_seat = true THEN 1 ELSE 0 END) AS transCallSeatNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.intention_class = 'A' THEN 1 ELSE 0 END) AS classANum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.intention_class = 'B' THEN 1 ELSE 0 END) AS classBNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.intention_class = 'C' THEN 1 ELSE 0 END) AS classCNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.intention_class = 'D' THEN 1 ELSE 0 END) AS classDNum, " +
            "SUM(CASE WHEN t.waitmsec <= 1000 AND t.waitmsec >= 0 THEN 1 ELSE 0 END) AS callFailedNum, " +
            "SUM(t.waitmsec)/1000 AS waitSecond, " +
            "SUM(t.call_duration_sec) AS callDurationSecond, " +
            "t.merchant_line_code AS tenantLineNumber, " +
            "t.line_id AS supplyLineNumber, " +
            "t.province AS province," +
            "t.city AS city, " +
            "t.operator AS operator " +
            "from call_record_for_human_machine t " +
            "where t.update_time>=:startTime " +
            "and t.update_time<:endTime " +
            "and t.call_status is not null " +
            "and t.merchant_line_code is not null " +
            "and waitmsec is not null " +
            "and waitmsec >= 0 " +
            "group by t.merchant_line_code, t.line_id, t.province, t.city, t.operator", nativeQuery = true)
    List<Tuple> scanCallRecordsByStartAndEndTimes(LocalDateTime startTime, LocalDateTime endTime);

    @Query(value = "SELECT " +
            "COUNT(1) AS routingFailNum, " +
            "t.merchant_line_code AS tenantLineNumber " +
            "FROM call_record_for_human_machine t " +
            "WHERE t.call_status = '1' " +
            "AND t.merchant_line_code is not null " +
            "AND t.update_time>=:startTime " +
            "AND t.update_time<:endTime " +
            "GROUP BY t.merchant_line_code", nativeQuery = true)
    List<Tuple> scanCallRecordsForRoutingFailByStartAndEndTimes(LocalDateTime startTime, LocalDateTime endTime);

    @Query(value = "SELECT " +
            "SUM(1) AS totalCallNum," +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN 1 ELSE 0 END) AS totalConnectNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN t.call_duration ELSE 0 END) / 60000 AS totalConnectedMinutes, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN call_duration % 60000.0 >= 100 THEN ceil(call_duration / 60000.0) ELSE floor(call_duration / 60000.0) END) ELSE 0 END) AS calculateNumOfSixty, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN call_duration % 6000.0 >= 100 THEN ceil(call_duration / 6000.0) ELSE floor(call_duration / 6000.0) END) ELSE 0 END) AS calculateNumOfSix, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class='A' THEN 1 ELSE 0 END) AS classANum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class='B' THEN 1 ELSE 0 END) AS classBNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class='C' THEN 1 ELSE 0 END) AS classCNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class='D' THEN 1 ELSE 0 END) AS classDNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class='E' THEN 1 ELSE 0 END) AS classENum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class='F' THEN 1 ELSE 0 END) AS classFNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class='G' THEN 1 ELSE 0 END) AS classGNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class='其他' THEN 1 ELSE 0 END) AS classOtherNum, " +
            "t.account AS account " +
            "FROM call_record_for_human_machine t " +
            "WHERE t.call_out_time >= :callOutTime " +
            "AND t.call_status IS NOT NULL " +
            "AND t.waitmsec IS NOT NULL " +
            "AND t.waitmsec >= 0 " +
            "AND (t.if_test is NULL OR t.if_test != '1') " +
            "GROUP BY t.account;", nativeQuery = true)
    List<Tuple> findCallDataByAccount(String callOutTime);

    @Query(value = "SELECT " +
            "SUM(1) AS totalCallNum," +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN 1 ELSE 0 END) AS totalConnectNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN t.call_duration ELSE 0 END) / 60000 AS totalConnectedMinutes, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN call_duration % 60000.0 >= 100 THEN ceil(call_duration / 60000.0) ELSE floor(call_duration / 60000.0) END) ELSE 0 END) AS calculateNumOfSixty, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN call_duration % 6000.0 >= 100 THEN ceil(call_duration / 6000.0) ELSE floor(call_duration / 6000.0) END) ELSE 0 END) AS calculateNumOfSix, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class='A' THEN 1 ELSE 0 END) AS classANum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class='B' THEN 1 ELSE 0 END) AS classBNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class='C' THEN 1 ELSE 0 END) AS classCNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class='D' THEN 1 ELSE 0 END) AS classDNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class='E' THEN 1 ELSE 0 END) AS classENum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class='F' THEN 1 ELSE 0 END) AS classFNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class='G' THEN 1 ELSE 0 END) AS classGNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.intention_class='其他' THEN 1 ELSE 0 END) AS classOtherNum, " +
            "t.account AS account " +
            "FROM call_record_for_human_machine t " +
            "WHERE t.call_out_time >= :startTime " +
            "AND t.call_out_time < :endTime " +
            "AND t.call_status IS NOT NULL " +
            "AND t.waitmsec IS NOT NULL " +
            "AND t.waitmsec >= 0 " +
            "AND (t.if_test is NULL OR t.if_test != '1') " +
            "GROUP BY t.account;", nativeQuery = true)
    List<Tuple> findCallDataHistoryByAccount(String startTime, String endTime);

    @Query(value = "SELECT " +
            "SUM(1) AS totalCallNum," +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN 1 ELSE 0 END) AS totalConnectNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN t.call_duration ELSE 0 END) AS totalConnectedDuration, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN call_duration % 60000.0 >= 100 THEN ceil(call_duration / 60000.0) ELSE floor(call_duration / 60000.0) END) ELSE 0 END) AS calculateNumOfSixty, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN call_duration % 6000.0 >= 100 THEN ceil(call_duration / 6000.0) ELSE floor(call_duration / 6000.0) END) ELSE 0 END) AS calculateNumOfSix, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN t.call_duration <= 1000 THEN 1 ELSE 0 END) ELSE 0 END) AS oneSecondConnectedNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN (t.call_duration <= 2000 AND t.call_duration > 1000) THEN 1 ELSE 0 END) ELSE 0 END) AS twoSecondConnectedNum, " +
            "SUM(CASE WHEN t.call_status='7' AND t.say_count = 0 THEN 1 ELSE 0 END) AS silenceCallNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.intention_labels LIKE '%沉默挂机%' THEN 1 ELSE 0 END) AS silenceHangupNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.intention_labels LIKE '%小助理%' THEN 1 ELSE 0 END) AS assistantNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.intention_labels LIKE '%运营商提示音%' THEN 1 ELSE 0 END) AS promptSoundNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.is_trans_to_call_seat = true THEN 1 ELSE 0 END) AS transCallSeatNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.intention_class = 'A' THEN 1 ELSE 0 END) AS classANum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.intention_class = 'B' THEN 1 ELSE 0 END) AS classBNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.intention_class = 'C' THEN 1 ELSE 0 END) AS classCNum, " +
            "SUM(CASE WHEN t.call_status = '7' AND t.intention_class = 'D' THEN 1 ELSE 0 END) AS classDNum, " +
            "SUM(CASE WHEN (t.waitmsec <= 1000 AND t.waitmsec >= 0) THEN 1 ELSE 0 END) AS callFailedNum, " +
            "t.merchant_line_code AS tenantLineNumber, " +
            "t.line_id AS supplyLineNumber " +
            "FROM call_record_for_human_machine t " +
            "WHERE t.call_out_time >= :startTime " +
            "AND t.call_out_time < :endTime " +
            "AND t.call_status IS NOT NULL " +
            "AND t.waitmsec IS NOT NULL " +
            "AND t.waitmsec >= 0 " +
            "GROUP BY t.merchant_line_code, t.line_id;", nativeQuery = true)
    List<Tuple> findOneDayCallRecordsForLineStatistic(String startTime, String endTime);


    @Query(value = "select * from call_record_for_human_machine t " +
            "where t.call_out_time>=:startTime " +
            "and t.call_out_time<:endTime " +
            "and t.call_status is not null " +
            "and waitmsec is not null " +
            "and waitmsec >= 0", nativeQuery = true)
    List<CallRecordForHumanMachine> findCallRecordsByStartAndEndTime(String startTime, String endTime);

    @Query(value = "select " +
            "COUNT(DISTINCT t.phone) as phoneNum," +
            "COUNT(DISTINCT t.clue_id) as clueNum," +
            "STRING_AGG(DISTINCT CAST(t.call_seat_id AS TEXT), ',') AS seatIdListString, " +
            "t.task_id AS taskId," +
            "t.task_name AS taskName," +
            "t.account AS account " +
            "from call_record_for_human_machine t " +
            "where t.call_out_time >= :startTime " +
            "and t.call_out_time < :endTime " +
            "and t.call_status is not null " +
            "and t.waitmsec is not null " +
            "and t.waitmsec >= 0 " +
            "group by t.task_id, t.account, t.task_name", nativeQuery = true)
    List<Tuple> scanClueStatisticData(String startTime, String endTime);

    @Query(value = "select " +
            "SUM(1) as totalCallNum," +
            "SUM(CASE WHEN t.call_status='7' THEN 1 ELSE 0 END) AS totalConnectNum," +
            "SUM(CASE WHEN t.call_status='7' THEN (CASE WHEN call_duration % 60000.0>=100 THEN ceil(call_duration/60000.0) ELSE floor(call_duration/60000.0) END) ELSE 0 END) AS calculateOfSixty," +
            "t.task_id as taskId," +
            "t.merchant_line_code as tenantLineNumber " +
            "from call_record_for_human_machine t " +
            "where t.call_out_time >= :startTime " +
            "and t.call_out_time < :endTime " +
            "and t.call_status is not null " +
            "and t.waitmsec is not null " +
            "and t.waitmsec >= 0 " +
            "group by t.merchant_line_code, t.task_id", nativeQuery = true)
    List<Tuple> scanClueLineStatisticData(String startTime, String endTime);

    @Query(value = "SELECT " +
            "COUNT(1) AS callNum," +
            "t.call_duration_sec AS second " +
            "FROM call_record_for_human_machine t " +
            "WHERE  t.call_out_time > :startTime " +
            "AND    t.call_out_time <= :endTime " +
            "AND    t.call_duration_sec <= 70 " +
            "AND    t.line_id = :supplyLineNumber " +
            "AND    t.call_status = '7' " +
            "AND    t.call_duration_sec is not null " +
            "GROUP BY t.call_duration_sec ", nativeQuery = true)
    List<Tuple> statisticSupplyLineDurationGroupBySecond(String startTime, String endTime, String supplyLineNumber);

    @Query(value = "SELECT " +
            "COUNT(1) AS callNum," +
            "t.call_duration_sec AS second " +
            "FROM call_record_for_human_machine t " +
            "WHERE  t.call_out_time > :startTime " +
            "AND    t.call_out_time <= :endTime " +
            "AND    t.call_duration_sec <= 70 " +
            "AND    t.merchant_line_code = :tenantLineNumber " +
            "AND    t.call_status = '7' " +
            "AND    t.call_duration_sec is not null " +
            "GROUP BY t.call_duration_sec ", nativeQuery = true)
    List<Tuple> statisticTenantLineDurationGroupBySecond(String startTime, String endTime, String tenantLineNumber);

    @Query(value = "SELECT " +
            "COUNT(1) AS callNum " +
            "FROM call_record_for_human_machine t " +
            "WHERE  t.call_out_time > :startTime " +
            "AND    t.call_out_time <= :endTime " +
            "AND    t.call_duration_sec > 70 " +
            "AND    t.line_id = :supplyLineNumber " +
            "AND    t.call_status = '7' " +
            "AND    t.call_duration_sec is not null  ", nativeQuery = true)
    int statisticSupplyLineDurationUpSecond(String startTime, String endTime, String supplyLineNumber);

    @Query(value = "SELECT " +
            "COUNT(1) AS callNum " +
            "FROM call_record_for_human_machine t " +
            "WHERE  t.call_out_time > :startTime " +
            "AND    t.call_out_time <= :endTime " +
            "AND    t.call_duration_sec > 70 " +
            "AND    t.merchant_line_code = :tenantLineNumber " +
            "AND    t.call_status = '7' " +
            "AND    t.call_duration_sec is not null ", nativeQuery = true)
    Integer statisticTenantLineDurationUpSecond(String startTime, String endTime, String tenantLineNumber);

    @Query(value = "SELECT t.* " +
            "FROM call_record_for_human_machine t " +
            "WHERE t.call_out_time >=:startTime " +
            "AND   t.call_out_time <:endTime " +
            "AND   t.call_status = '7' " +
            "AND   t.whole_audio_file_url != '' " +
            "AND   (t.if_test != '1' OR t.if_test IS NULL)", nativeQuery = true)
    List<CallRecordForHumanMachine> findPutThroughRecordsByTime(String startTime, String endTime);

    @Query(value = "SELECT t.* " +
            "FROM call_record_for_human_machine t " +
            "WHERE t.call_out_time >=:startTime " +
            "AND   t.call_out_time <:endTime " +
            "AND   t.call_status = '7' " +
            "AND   t.task_id in :taskIds " +
            "AND   t.whole_audio_file_url != '' " +
            "AND   (t.if_test != '1' OR t.if_test IS NULL)", nativeQuery = true)
    List<CallRecordForHumanMachine> findPutThroughRecordsByTaskIds(String startTime, String endTime, List<String> taskIds);

    @Query(value = "select f.phone as phone, " +
            " f.intention_class as intention " +
            "from call_record_for_human_machine f " +
            "where f.call_out_time >=:startTime " +
            "and f.call_out_time <:endTime " +
            "and f.call_status = '7' " +
            "and f.task_id in :taskIds " +
            "and f.whole_audio_file_url != '' " +
            "and f.intention_class in ('A','B','C','F','G') " +
            "and (f.if_test != '1' or f.if_test is null)", nativeQuery = true)
    List<Tuple> findVolcanoRecordsByTaskIdsForAIManual(String startTime, String endTime, List<String> taskIds);

    @Query(value = "select " +
            "SUM(1) AS totalCallNum, " +
            "SUM(CASE WHEN call_status = '7' THEN 1 ELSE 0 END) AS totalConnectNum, " +
            "SUM(CASE WHEN call_status = '7' AND say_count = 0  THEN 1 ELSE 0 END) AS silenceCallNum, " +
            "SUM(CASE WHEN call_status = '7' AND call_duration <= 1000 THEN 1 ELSE 0 END) AS oneSecondConnectedNum, " +
            "SUM(CASE WHEN call_status = '7' AND call_duration > 1000 AND call_duration <= 2000 THEN 1 ELSE 0 END) AS twoSecondConnectedNum, " +
            "SUM(CASE WHEN call_status = '7' AND intention_labels LIKE '%沉默挂机%' THEN 1 ELSE 0 END) AS silenceHangupNum, " +
            "SUM(CASE WHEN call_status = '7' AND intention_labels LIKE '%小助理%' THEN 1 ELSE 0 END) AS assistantNum, " +
            "string_agg(intention_labels, ',') AS intentionLabelNames , " +
            "SUM(CASE WHEN call_status = '7' AND intention_class = 'A' THEN 1 ELSE 0 END) AS classANum, " +
            "SUM(CASE WHEN call_status = '7' AND intention_class = 'B' THEN 1 ELSE 0 END) AS classBNum, " +
            "SUM(CASE WHEN call_status = '7' AND intention_class = 'C' THEN 1 ELSE 0 END) AS classCNum, " +
            "SUM(CASE WHEN call_status = '7' AND intention_class = 'D' THEN 1 ELSE 0 END) AS classDNum, " +
            "SUM(CASE WHEN call_status = '7' AND intention_class = 'E' THEN 1 ELSE 0 END) AS classENum, " +
            "SUM(CASE WHEN call_status = '7' AND intention_class = 'F' THEN 1 ELSE 0 END) AS classFNum, " +
            "SUM(CASE WHEN call_status = '7' AND intention_class = 'G' THEN 1 ELSE 0 END) AS classGNum, " +
            "sum(call_duration) as totalCallDuration, " +
            "SUM(CASE WHEN call_status = '7' AND intention_class = 'A' THEN call_duration ELSE 0 END) AS classACallDuration, " +
            "SUM(CASE WHEN call_status = '7' AND intention_class = 'B' THEN call_duration ELSE 0 END) AS classBCallDuration, " +
            "SUM(CASE WHEN call_status = '7' AND intention_class = 'C' THEN call_duration ELSE 0 END) AS classCCallDuration, " +
            "SUM(CASE WHEN call_status = '7' AND intention_class = 'D' THEN call_duration ELSE 0 END) AS classDCallDuration, " +
            "SUM(CASE WHEN call_status = '7' AND intention_class = 'E' THEN call_duration ELSE 0 END) AS classECallDuration, " +
            "SUM(CASE WHEN call_status = '7' AND intention_class = 'F' THEN call_duration ELSE 0 END) AS classFCallDuration, " +
            "SUM(CASE WHEN call_status = '7' AND intention_class = 'G' THEN call_duration ELSE 0 END) AS classGCallDuration, " +
            "account , " +
            "task_id AS taskId, " +
            "script_string_id AS scriptStringId, " +
            "province_code AS provinceCode," +
            "max(province) AS province," +
            "city_code AS cityCode, " +
            "max(city) AS city, " +
            "operator AS operator " +
            "from call_record_for_human_machine " +
            "where call_out_time>=:startTime " +
            "and call_out_time<:endTime " +
            "and call_status is not null " +
            "and call_status != '3' \n" +
            "and waitmsec is not null \n" +
            "and waitmsec >= 0 " +
            "and (if_fast_recall is null or if_fast_recall != '1') \n" +
            "group by account, task_id, script_string_id, task_id, province_code, city_code, operator", nativeQuery = true)
    List<Tuple> scanCallRecordStatisticsByStartAndEndTimes(String startTime, String endTime);


    @Query(value = "SELECT " +
            "SUM(CASE WHEN t.call_status='7' AND t.whole_audio_file_url!='' THEN (CASE WHEN call_duration % 60000.0 >= 100 THEN ceil(call_duration / 60000.0) ELSE floor(call_duration / 60000.0) END) ELSE 0 END) AS calculateNumOfSixty, " +
            "t.task_id AS taskId " +
            "FROM call_record_for_human_machine t " +
            "WHERE t.task_id in :taskIdList " +
            "AND t.call_status IS NOT NULL " +
            "AND t.waitmsec IS NOT NULL " +
            "AND t.waitmsec >= 0 " +
            "GROUP BY t.task_id ", nativeQuery = true)
    List<Tuple> findOneDayCallRecordsForTask(List<String> taskIdList);
}
