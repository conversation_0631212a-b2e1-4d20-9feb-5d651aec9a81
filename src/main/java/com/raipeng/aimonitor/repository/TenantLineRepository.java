package com.raipeng.aimonitor.repository;

import com.raipeng.aidatacommon.model.TenantLine;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import javax.persistence.Tuple;
import java.util.List;

public interface TenantLineRepository extends JpaRepository<TenantLine, Long> {

    @Query(value = "select * from tenant_line " +
            "where line_number in " +
            "(select t.tenant_Line_number from supply_line_group t where t.supply_line_numbers @> to_jsonb(?1))",
            nativeQuery = true)
    List<TenantLine> findTenantLinesBySupplyLineNumber(List<String> supplyLineNumber);


    @Query(value = "select " +
            "t1.line_name as tenantLineName, " +
            "t2.tenant_name as tenantName, " +
            "t2.tenant_no as tenantNumber, " +
            "t2.id as tenantId, " +
            "t1.enable_status as status, " +
            "t1.concurrent_limit as concurrentLimit, " +
            "t1.update_time as updateTime, " +
            "t1.line_number as tenantLineNumber, " +
            "t1.line_type as tenantLineType, " +
            "t1.group_id as groupId " +
            "from tenant_line t1, t_ai_tenant t2 where t1.tenant_id=t2.id " +
            "and (?1 is null or t1.line_name like concat('%', ?1,'%') )" +
            "and (?2 is null or t1.line_number=cast(?2 as text))" +
            "and (?3 is null or t1.enable_status=cast(?3 as text))" +
            "and (t1.tenant_id in (select t3.id from t_ai_tenant t3 where (?4 is null or t3.tenant_name=cast(?4 as text)) and (?5 is null or t3.tenant_no=cast(?5 as text))))", nativeQuery = true)
    List<Tuple> findTenantLineResponse(String tenantLineName, String tenantLineNumber, String status, String tenantName, String tenantNumber);

    TenantLine findFirstByLineNumber(String tenantLineNumber);

    @Query(value = "select * from tenant_line t " +
            "where (:tenantLineName is null or t.line_name like concat('%', :tenantLineName,'%')) " +
            "and (:enableStatus is null or t.enable_status=cast(:enableStatus as text)) " +
            "and (:tenantName is null or t.tenant_id=(select s.id from t_ai_tenant s where s.tenant_name=cast(:tenantName as text))) " +
            "and (:tenantCode is null or t.tenant_id=(select s.id from t_ai_tenant s where s.tenant_no=cast(:tenantCode as text))) " +
            "and (:account is null or (split_part(t.group_id, '_', 3) = cast((select a.id from t_admin a where a.account=cast(:account as text)) as text)))", nativeQuery = true)
    List<TenantLine> findTenantLinesBySupplyLineMonitorDetailParam(String tenantLineName, String enableStatus, String account, String tenantName, String tenantCode);

    @Query(value = "select * from tenant_line t " +
            "where (:tenantLineName is null or t.line_name like concat('%', :tenantLineName,'%')) " +
            "and (:tenantLineNumber is null or t.line_number=cast(:tenantLineNumber as text)) " +
            "and (:tenantName is null or t.tenant_id=(select x.id from t_ai_tenant x where x.tenant_name=cast(:tenantName as text))) " +
            "and (:tenantCode is null or t.tenant_id=(select s.id from t_ai_tenant s where s.tenant_no=cast(:tenantCode as text))) ", nativeQuery = true)
    List<TenantLine> findTenantLinesByTenantLineMonitorParam(String tenantLineName, String tenantLineNumber, String tenantName, String tenantCode);

    @Query(value = "select line_number from tenant_line t where t.second_industries @> to_jsonb(?1)", nativeQuery = true)
    List<String> findTenantLineNumbersByIndustry(List<String> industry);

    @Query(value = "select line_number from tenant_line t where line_number NOT IN :otherNumbers", nativeQuery = true)
    List<String> findOtherLineNumbers(List<String> otherNumbers);

    @Query(value = "select line_number as tenantLineNum,concurrent_limit as concurrentLimit from tenant_line where enable_status = 'ENABLE' and concurrent_limit is not null ", nativeQuery = true)
    List<Tuple> findAllTenantLineLimit();
}
