package com.raipeng.aimonitor.repository;


import com.raipeng.aidatacommon.model.TenantSupplyLine;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import javax.persistence.Tuple;
import java.util.List;

public interface TenantSupplyLineRepository extends JpaRepository<TenantSupplyLine, Long> {
    List<TenantSupplyLine> findAllByTenantLineNumber(String tenantLineNumber);

    List<TenantSupplyLine> findAllBySupplyLineNumber(String supplyLineNumber);

    @Query(value = "select coalesce(sum(concurrent_limit),0) as concurrentLimit ,\n" +
            "       count(1) as totalCount ,\n" +
            "       SUM(CASE WHEN is_priority  = TRUE THEN 1 ELSE 0 END) AS priorityCount,\n" +
            "       tenant_line_number as tenantLineNum " +
            "       from tenant_supply_line " +
            " where tenant_line_number in (:tenantLineNumbers) and is_pending = false group by tenant_line_number \n",nativeQuery = true)
    List<Tuple> findStatisticByTenantLineNumber(List<String> tenantLineNumbers);


    @Query(value = "select coalesce(sum(concurrent_limit),0) as concurrentLimit ,\n" +
            "       count(1) as totalCount ,\n" +
            "       SUM(CASE WHEN is_priority  = TRUE THEN 1 ELSE 0 END) AS priorityCount,\n" +
            "       supply_line_number as supplyLineNum " +
            "       from tenant_supply_line " +
            " where supply_line_number in (:supplyLineNumbers) and is_pending = false group by supply_line_number \n",nativeQuery = true)
    List<Tuple> findStatisticBySupplyLineNumber(List<String> supplyLineNumbers);

    @Query(value = "select tenant_line_number as tenantLineNum,supply_line_number as supplyLineNum,concurrent_limit as concurrentLimit from tenant_supply_line where is_pending = false and concurrent_limit is not null", nativeQuery = true)
    List<Tuple> findAllTenantSupplyLineLimit();

}
