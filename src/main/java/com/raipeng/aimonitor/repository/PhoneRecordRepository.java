package com.raipeng.aimonitor.repository;

import com.raipeng.aidatacommon.model.PhoneRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import javax.persistence.Tuple;
import java.util.List;

public interface PhoneRecordRepository extends JpaRepository<PhoneRecord,Long> {
    PhoneRecord findPhoneRecordByRecordId(String recordId);

    @Query(value = "SELECT * \n" +
            "FROM t_phone_record \n" +
            "WHERE task_id IN (:taskIds) \n" +
            "AND (:callStatus IS NULL OR call_status = :callStatus) \n" +
            "ORDER BY random() \n" +
            "LIMIT :limitSize  ", nativeQuery = true)
    List<PhoneRecord> findBySamplingPhoneRecord(List<String> taskIds,  String callStatus , Integer limitSize);


    @Query(value = "SELECT " +
            " city," +
            " operator ," +
            " province ," +
            " count(*) as totalNum," +
            "   SUM(CASE WHEN put_through_num > 0 THEN 1 ELSE 0 END) AS putThroughCount, " +
            "  SUM(CASE WHEN called_num > 0 THEN 1 ELSE 0 END) AS calledCount, " +
            "  SUM(CASE WHEN call_status = '待呼叫' THEN 1 ELSE 0 END) AS remainCount " +
            "FROM t_phone_record \n" +
            "WHERE task_id = :taskId \n" +
            "group by operator,province,city order by operator,province,city \n", nativeQuery = true)
    List<Tuple> findStatisticByTaskId(String taskId);
}
