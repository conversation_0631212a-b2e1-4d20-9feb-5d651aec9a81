package com.raipeng.aimonitor.repository;


import com.raipeng.aidatacommon.model.SupplyLine;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import javax.persistence.Tuple;
import java.util.List;
import java.util.Set;


public interface SupplyLineRepository extends JpaRepository<SupplyLine, Long> {
    SupplyLine findSupplyLineByLineNumber(String lineNumber);

    @Query(value = "select * from supply_line t " +
            "where (:supplyLineName is null or t.line_name like  concat('%', :supplyLineName,'%'))" +
            "and (:supplyLineNumber is null or t.line_number=cast(:supplyLineNumber as text)) " +
            "and (:supplyLineType is null or t.line_type=cast(:supplyLineType as text)) " +
            "and (:status is null or t.enable_status=cast(:status as text)) " +
            "and (:masterCallNumber is null or t.master_call_number=cast(:masterCallNumber as text)) " +
            "and (:disPlayCallNumber is null or t.display_call_number=cast(:disPlayCallNumber as text)) " +
            "and (:callLineSupplierId is null or t.call_line_supplier_id=cast(cast(:callLineSupplierId as text) as bigint)) " +
            "and (:callLineSupplierName is null or t.call_line_supplier_name=cast(:callLineSupplierName as text)) " +
            "and (:callLineSupplierNumber is null or t.call_line_supplier_number=cast(:callLineSupplierNumber as text)) " +
            "and t.service_providers @> cast(:serviceProviders as jsonb) " +
            "and t.second_industries @> cast(:secondIndustries as jsonb) " +
            "and t.city_codes @> cast(:cityCodes as jsonb)", nativeQuery = true)
    List<SupplyLine> findSupplyLinesByConditions(
            String supplyLineName,
            String supplyLineNumber,
            String supplyLineType,
            String status,
            String masterCallNumber,
            String disPlayCallNumber,
            Long callLineSupplierId,
            String callLineSupplierName,
            String callLineSupplierNumber,
            String serviceProviders,
            String secondIndustries,
            String cityCodes
    );

    @Query(value = "select " +
            "t.line_number as lineNumber," +
            "t.line_name as lineName, " +
            "t.line_type as lineType, " +
            "t.call_line_supplier_id as callLineSupplierId, " +
            "t.call_line_supplier_number as callLineSupplierNumber," +
            "t.enable_status as enableStatus, " +
            "t.master_call_number as masterCallNumber," +
            "t.display_call_number as displayCallNumber," +
            "t.prefix," +
            "t.register_ip as registerIp," +
            "t.register_port as registerPort," +
            "t.concurrent_limit as concurrentLimit," +
            "t.access_type as lineAccessType," +
            "t.is_pending as isPending," +
            "t.call_line_supplier_name as callLineSupplierName " +
            "from supply_line t where t.line_number=:lineNumber", nativeQuery = true)
    Tuple findSupplyLineBySupplyLineNumber(String lineNumber);

    @Query(value = "select * from supply_line t " +
            "where (:supplyLineName is null or t.line_name like concat('%', :supplyLineName,'%')) " +
            "and (:supplyLineNumber is null or t.line_number=cast(:supplyLineNumber as text)) " +
            "and (:masterCallNumber is null or t.master_call_number=cast(:masterCallNumber as text)) " +
            "and (:supplierNumber is null or t.call_line_supplier_number=cast(:supplierNumber as text)) " +
            "and (:supplierName is null or t.call_line_supplier_name=cast(:supplierName as text))", nativeQuery = true)
    List<SupplyLine> findSupplyLinesBySupplyLineMonitorParam(String supplyLineName,
                                                               String supplyLineNumber,
                                                               String masterCallNumber,
                                                               String supplierName,
                                                               String supplierNumber);

    @Query(value = "select * from supply_line t " +
            "where (:supplyLineName is null or t.line_name like concat('%', :supplyLineName,'%')) " +
            "and (:enableStatus is null or t.enable_status=cast(:enableStatus as text)) " +
            "and (:supplierNumber is null or t.call_line_supplier_number=cast(:supplierNumber as text)) " +
            "and (:supplierName is null or t.call_line_supplier_name=cast(:supplierName as text))", nativeQuery = true)
    List<SupplyLine> findSupplyLinesByTenantLineMonitorDetailParam(String supplyLineName,
                                                                     String enableStatus,
                                                                     String supplierName,
                                                                     String supplierNumber);

    @Query(value = "select sum(t.concurrent_limit) from supply_line t where t.line_number in :supplyLineNumbers", nativeQuery = true)
    int findSupplyLinesCountSumByNumbers(List<String> supplyLineNumbers);


    @Query(value = "SELECT * " +
            "FROM supply_line t " +
            "WHERE t.enable_status = 'ENABLE' " +
            "AND t.is_pending = FALSE " +
            "AND t.line_type = 'AI_OUTBOUND_CALL'", nativeQuery = true)
    List<SupplyLine> findAISupplyLinesNotPending();

    List<SupplyLine> findAllByCallLineSupplierNumberIn(Set<String> supplierNumbers);

    @Query(value = "select t.line_number from supply_line t where t.call_line_supplier_number not in :supplierNumbers", nativeQuery = true)
    List<String> findSupplyLineNumberBySupplierNumberNotIn(Set<String> supplierNumbers);

    @Query(value = "SELECT " +
            "t.line_number " +
            "FROM supply_line t " +
            "WHERE t.call_line_supplier_id " +
            "   IN (SELECT c.id " +
            "       FROM call_line_supplier c " +
            "       WHERE c.is_callback_supply_line = TRUE)", nativeQuery = true)
    Set<String> findNumbersForCallback();

    @Query(value = "select t.line_number from supply_line t where t.call_line_supplier_number = :supplierNumber", nativeQuery = true)
    List<String> findNumbersByCallLineSupplierNumber(String supplierNumber);

    @Query(value = "select * from supply_line t where t.call_line_supplier_number = :supplierNumber", nativeQuery = true)
    List<SupplyLine> findAllByCallLineSupplierNumber(String supplierNumber);

    @Query(value = "select line_number as supplyLineNum,concurrent_limit as concurrentLimit from supply_line where enable_status = 'ENABLE' and is_pending = false and concurrent_limit is not null ", nativeQuery = true)
    List<Tuple> findAllSupplyLineLimit();
}
