package com.raipeng.aimonitor.enums;

public enum CallLineBelong {
    DXYS("得心应手"),

    XRZL("仙人指路"),

    XSCS("限时传送"),

    BZZY("白泽自有"),

    KHZY("客户自有");

    private final String label;

    CallLineBelong(String label) {
        this.label = label;
    }

    public static String getLabelByValue(String value) {
        for (CallLineBelong item : CallLineBelong.values()) {
            if (item.name().equals(value)) {
                return item.label;
            }
        }
        return "";
    }
}
