package com.raipeng.aiengine.mq.producter;

import com.raipeng.aiengine.service.CallEngineService;
import com.raipeng.aiengine.service.DingDingService;
import com.raipeng.aiengine.service.SendPhoneService;
import com.raipeng.aiengine.service.els.FsClientData;
import com.raipeng.common.entity.prioritycall.CallPhoneUnit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

@Component
@RefreshScope
@Slf4j
public class SendPhoneQueue {

    @Value("${send.phone.size:20}")
    private Integer sendPhoneSize;

    @Value("${send.phone.gap.time:1000}")
    private Long sendPhoneGapTime;

    @Value("${send.phone.sleep.time:1000}")
    private Long sendPhoneSleepTime;

    @Value("${send.phone.thread.size:5}")
    private Integer sendPhoneThreadSize;

    @Value("${send.phone.queue.size.threshold:2000}")
    private Integer sendPhoneQueueSizeThreshold;

    @Autowired
    private SendPhoneService sendPhoneService;

    public void submit(){
        for(int i=0;i<sendPhoneThreadSize;i++){
            executorService.submit(this::consumeQueue);
        }
    }

    private static ExecutorService executorService =  new ThreadPoolExecutor(5, 5,
            100L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(20000));

    private final static ConcurrentLinkedQueue<CallPhoneUnit> callPhoneQueue = new ConcurrentLinkedQueue<>();

    private static ReentrantReadWriteLock rwlock = new ReentrantReadWriteLock();
    private static Lock wlock = rwlock.writeLock();

    public String addData(List<CallPhoneUnit> dataList){
        if(getQueueSize() >= sendPhoneQueueSizeThreshold) {
            return "full";//返回满
        }
        if(dataList != null && dataList.size() > 0)
            callPhoneQueue.addAll(dataList);

        return "success";
    }

    public String isFull(){
        if(getQueueSize() >= sendPhoneQueueSizeThreshold) {
            return "full";//返回满
        }
        return "success";
    }
    private static ConcurrentLinkedQueue<FsClientData> fsClientDataQueue = new ConcurrentLinkedQueue<>();

    public Integer getQueueSize(){
        return callPhoneQueue.size();
    }

    /**
     * 循环获取可用的fs
     * @return 可用的fs，如果没有则返回null
     */
    private synchronized FsClientData getAvailableFs(){
        FsClientData fsClientData = fsClientDataQueue.poll();
        if(fsClientData != null){
            return fsClientData;
        }else{
            for (Map.Entry<String, FsClientData> entry : CallEngineService.fsMulClient.entrySet()) {
                FsClientData ele = entry.getValue();
                if (ele.getRemainCount() > 0) {
                    fsClientDataQueue.offer(ele);
                }
            }
            fsClientData = fsClientDataQueue.poll();
            if(fsClientData != null){
                return fsClientData;
            }
        }
        return null;
    }

    private void consumeQueue(){
        List<CallPhoneUnit> callPhoneUnitList = Lists.newArrayList();
        while(true){
            try{
                Long start = System.currentTimeMillis()-1000;
                boolean bool = true;
                do{
                    CallPhoneUnit unit = callPhoneQueue.poll();
                    if(unit != null){
                        callPhoneUnitList.add(unit);
                    }
                    if(callPhoneUnitList.size() >= sendPhoneSize){
                        //log.info("===>线程正在归还"+Thread.currentThread().getId()+ JSON.toJSONString(lineUnitList));
                        FsClientData availableFs = getAvailableFs();
                        if (availableFs != null) {
                            List<CallPhoneUnit> copyList = new CopyOnWriteArrayList<>(callPhoneUnitList);
                            sendPhoneService.sendPhone(copyList,availableFs);
                            callPhoneUnitList.clear();
                        }else{
                            callPhoneQueue.addAll(callPhoneUnitList);
                            callPhoneUnitList.clear();
                        }
                    }
                    if(unit == null){
                        bool = false;
                    }
                }while(bool);
                Long end = System.currentTimeMillis();
                if(callPhoneUnitList.size() > 0 && (end-start) >= sendPhoneGapTime){//大于1000ms会统一归还一下
                    //log.info("===>线程正在归还,正在归还超过1s的数据"+Thread.currentThread().getId()+ JSON.toJSONString(lineUnitList));
                    FsClientData availableFs = getAvailableFs();
                    if (availableFs != null) {
                        List<CallPhoneUnit> copyList = new CopyOnWriteArrayList<>(callPhoneUnitList);
                        sendPhoneService.sendPhone(copyList,availableFs);
                        callPhoneUnitList.clear();
                    }else{
                        callPhoneQueue.addAll(callPhoneUnitList);
                        callPhoneUnitList.clear();
                    }
                }
                Thread.sleep(sendPhoneSleepTime);
            }catch (Exception e){
                e.printStackTrace();
                if(callPhoneUnitList.size() > 0){//大于1000ms会统一归还一下
                    FsClientData availableFs = getAvailableFs();
                    if (availableFs != null) {
                        List<CallPhoneUnit> copyList = new CopyOnWriteArrayList<>(callPhoneUnitList);
                        sendPhoneService.sendPhone(copyList,availableFs);
                        callPhoneUnitList.clear();
                    }else{
                        callPhoneQueue.addAll(callPhoneUnitList);
                        callPhoneUnitList.clear();
                    }

                }
                DingDingService.dingDingSendMsgException("送呼出现异常");
            }
        }
    }



    public void backPhoneFromQueue(){
        List<CallPhoneUnit> callPhoneUnitList = Lists.newArrayList();
        try{
            boolean bool = true;
            do{
                CallPhoneUnit unit = callPhoneQueue.poll();
                if(unit != null){
                    callPhoneUnitList.add(unit);
                }
                if(callPhoneUnitList.size() >= sendPhoneSize){
                    //log.info("===>线程正在归还"+Thread.currentThread().getId()+ JSON.toJSONString(lineUnitList));
                    List<CallPhoneUnit> copyList = new CopyOnWriteArrayList<>(callPhoneUnitList);
                    sendPhoneService.backPhone(copyList);
                    callPhoneUnitList.clear();
                }
                if(unit == null){
                    bool = false;
                }
            }while(bool);

            if(callPhoneUnitList.size() > 0){
                //log.info("===>线程正在归还"+Thread.currentThread().getId()+ JSON.toJSONString(lineUnitList));
                List<CallPhoneUnit> copyList = new CopyOnWriteArrayList<>(callPhoneUnitList);
                sendPhoneService.backPhone(copyList);
                callPhoneUnitList.clear();
            }

        }catch (Exception e){
            e.printStackTrace();
        }

    }


}
