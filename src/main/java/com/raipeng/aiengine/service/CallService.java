package com.raipeng.aiengine.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Maps;
import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.GetResponse;
import com.raipeng.aicall.mq.producter.material.CallPhoneProducerMaterial;
import com.raipeng.aiengine.constant.ApplicationConstants;
import com.raipeng.aiengine.constant.RabbitMqConstants;
import com.raipeng.aicall.scriptsystem.CallStatus;
import com.raipeng.aicall.scriptsystem.ScriptStatus;
import com.raipeng.aiengine.feign.LineReturnFeign;
import com.raipeng.aiengine.model.vo.TaskData;
import com.raipeng.aiengine.mq.producter.StopToRedisReturnQueue;
import com.raipeng.aiengine.service.els.FsClientData;
import com.raipeng.aiengine.service.els.FsEslClient;
import com.raipeng.aiengine.service.help.monitor.HelpMonitor;
import com.raipeng.aiengine.service.help.monitor.HelpTask;
import com.raipeng.aiengine.utils.SnowFlakeUtil;
import com.raipeng.common.entity.linemanager.CallLineUnit;
import com.raipeng.common.util.StringUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.freeswitch.esl.client.inbound.InboundConnectionFailure;
import org.freeswitch.esl.client.transport.message.EslMessage;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.Pipeline;
import redis.clients.jedis.params.SetParams;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.Serializable;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeoutException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.raipeng.aiengine.constant.RabbitMqConstants.*;

@Slf4j
@Service
@RefreshScope
public class CallService {

    @Autowired
    @Qualifier("secondRabbitTemplate")
    private RabbitTemplate secondRabbitTemplate;

    @Value("${ai.call.engine.dialog.id.flag}")
    private Long aiCallEngineDialogIdFlag;

    /**
     * 是否网关
     */
    @Value("${is_gw:false}")
    private Boolean isGw;

    @Value("${is.direct.ip:false}")
    private Boolean isDirectIp;

    @Autowired
    private SimpleRedisService simpleRedisService;


    @Autowired
    private CallRecordService callRecordService;

    @Value("${pull.rate:8,2}")
    private String pullRate;

    @Value("${fast.pull.rate:5,5}")
    private String fastPullRate;

    @Autowired
    private CurrentServerIpService currentServerIpService;

    @Autowired
    private LineReturnFeign lineReturnFeign;

    @Value("${is.pressure.test:0}")
    private String isPressureTest;

    @Autowired
    private TaskDataService taskDataService;

    @Autowired
    private StopToRedisReturnQueue stopToRedisReturnQueue;

    static final String ringRegex = "RING\\[\\d+\\]";
    static final Pattern ringPattern = Pattern.compile(ringRegex);

    @Resource
    private JedisPool jedisPool;

    @Autowired
    private HelperService helperService;

    //els链接，并订阅所有事件
    public void eslConnect(FsClientData fsClientData) {
        try {
            fsClientData.getFsClient().connect(fsClientData.getFsConfig().getIp(), fsClientData.getFsConfig().getPort(), fsClientData.getFsConfig().getPwd(), 10);
        } catch (InboundConnectionFailure e) {
            log.error("Connect failed", e);
            DingDingService.dingDingSendMsgException("异常139，连接FS失败","ip: "+fsClientData.getFsConfig().getIp());
            return;
        }
        //FsEslAnswerListener listener1 = new FsEslAnswerListener();
        //FsEslEventListener listener1 = new FsEslEventListener(robotWavFileService,simpleRedisService,this);
        //FsEslRecordStartAndStopAndAnswerListener listener2 = new FsEslRecordStartAndStopAndAnswerListener(asrService,audioTextService,robotWavFileService,simpleRedisService,isDownloadAudioFile);
        //FsEslRecordStartAndStopAndAnswerListener2 listener2 = new FsEslRecordStartAndStopAndAnswerListener2(asrService,audioTextService,robotWavFileService,simpleRedisService,isDownloadAudioFile);
        //FsEslRecordStartAndStopAndAnswerListener3 listener3 = new FsEslRecordStartAndStopAndAnswerListener3(asrService,audioTextService,robotWavFileService,simpleRedisService,isDownloadAudioFile);
        //添加注册监听事件
        //fsClientData.getFsClient().removeAllEventListener();
        //fsClientData.getFsClient().addEventListener(listener1);
        //FsEslClient.getClient().addEventListener(listener2);
        //FsEslClient.getClient().addEventListener(listener3);
        //订阅所有事件
        //FsEslClient.getClient().setEventSubscriptions("plain", "BACKGROUND_JOB");
        //FsEslClient.getClient().setEventSubscriptions("plain", "all");
        //订阅部分事件
        //FsEslClient.getClient().setEventSubscriptions("plain", "HEARTBEAT RECV_RTCP_MESSAGE CHANNEL_DESTROY CHANNEL_CREATE CHANNEL_BRIDGE CHANNEL_ANSWER CHANNEL_HANGUP CHANNEL_HANGUP_COMPLETE RECORD_START RECORD_START RECORD_STOP PLAYBACK_START PLAYBACK_STOP");

        //fsClientData.getFsClient().setEventSubscriptions("plain", "CHANNEL_ANSWER");
        //fsClientData.getFsClient().setEventSubscriptions("plain", "all");

        //通道关闭
        //channelsCheckService.channelsCheck();
    }

    public CallPhoneProducerMaterial pullPhoneDependence(Channel channel,String callQueue){
        if(currentServerIpService.getPullPhoneSwitch().equals("stop")){
            if(LocalDateTime.now().getSecond()%10 == 0){
                log.info("engine拉取号码功能已经关闭，当前不再消费号码！");
            }
            return null;
        }
        return pullPhoneOne(channel,callQueue);
    }

    public CallPhoneProducerMaterial pullPhone(Channel channel){

        List<Double> rates = Arrays.stream(fastPullRate.split(",")).map(p->Double.parseDouble(p)).collect(Collectors.toList());
        int result = ActivityService.lottery(rates);
        List<String> queueList = Lists.newArrayList();
        queueList.add("pullPhoneOne");
        queueList.add("pullPhoneTwo");
        String pullPhone = queueList.get(result);
        CallPhoneProducerMaterial temp = null;
        if(pullPhone.equals("pullPhoneOne")){
            temp = pullPhoneOne(channel,RabbitMqConstants.FAST_CALL_PHONE_QUEUE);
            if (temp == null){
                temp = pullPhoneTwo(channel);
            }
            return temp;
        }else{
            temp = pullPhoneTwo(channel);
            if (temp == null){
                temp = pullPhoneOne(channel,RabbitMqConstants.FAST_CALL_PHONE_QUEUE);
            }
            return temp;
        }
    }

    public CallPhoneProducerMaterial pullPhoneOne(Channel channel,String callQueue){
        //创建一个不带事务的消息通道。有了通道就好办了，就可以进行确认与取消了
        try {

            GetResponse getResponse = null ;
            getResponse = channel.basicGet(callQueue, true);
            //获取标记
            if(getResponse!=null){
                //deliveryTag = getResponse.getEnvelope().getDeliveryTag();
                String body = new String(getResponse.getBody(),"UTF-8");
                CallPhoneProducerMaterial data = JSONObject.parseObject(body,CallPhoneProducerMaterial.class);

                TaskData taskData = taskDataService.getTaskData(data.getTaskId());

                // 判断任务是否不呼叫
                if(!taskData.getTaskStatus().equals("进行中") && !taskData.getTaskStatus().equals("待执行") ){
                    //log.info("该任务数据由于任务关闭,所以屏蔽："+data.getTaskName()+"归还大小："+data.getData().size());
                    for (CallPhoneProducerMaterial.PhoneData datum : data.getData()) {
                        //callPhoneStopToRedis(channel,taskData.getTaskId(),datum);
                        if(currentServerIpService.getIsUseBatchToRedis().equals("1")){
                            callPhoneStopToRedisBatch(channel,taskData.getTaskId(),datum);
                        }else{
                            callPhoneStopToRedis(channel,taskData.getTaskId(),datum);
                        }
                    }
                    return null;
                }

                if(!ifInWorkingTime(taskData.getStartTime(),taskData.getEndTime(),taskData.getTaskName(),data)){//非工作时间
                    //22点之后，清理呼叫队列中数据
                    if(!(LocalDateTime.now().getHour() == 22)){
                        backQueueNotWorktime(channel,data);
                    }
                    return null;
                }

                StringBuilder shieldMobile = new StringBuilder("");
                for (CallPhoneProducerMaterial.PhoneData datum : data.getData()) {
                    if(isShield(taskData,datum)){
                        //callPhoneStopToRedis(channel,taskData.getTaskId(),datum);
                        if(currentServerIpService.getIsUseBatchToRedis().equals("1")){
                            callPhoneStopToRedisBatch(channel,taskData.getTaskId(),datum);
                        }else{
                            callPhoneStopToRedis(channel,taskData.getTaskId(),datum);
                        }
                        shieldMobile.append(datum.getPhone()).append(",");
                    }
                }
                List<CallPhoneProducerMaterial.PhoneData> temp = data.getData().stream().filter(p->!shieldMobile.toString().contains(p.getPhone())).collect(Collectors.toList());


                if(temp == null){
                    return null;
                }else {
                    data.setData(temp);
                    //log.info("正在消费号码"+JSON.toJSONString(data));
                    return data;
                }



            }

        } catch (IOException e) {
            try {
                channel.close();
            } catch (IOException e1) {
                throw new RuntimeException(e1);
            } catch (TimeoutException e2) {
                throw new RuntimeException(e2);
            }
            channel = secondRabbitTemplate.getConnectionFactory().createConnection().createChannel(false);
            e.printStackTrace();
            DingDingService.dingDingSendMsgException("pull号码时error","sfServerId",e.getMessage());
        }
        return null;

    }

    private boolean isShield(TaskData taskData,CallPhoneProducerMaterial.PhoneData datum){
        //分运营商屏蔽
        if(org.apache.commons.lang3.StringUtils.isNotBlank(taskData.getAllRestrictCity())
                && org.apache.commons.lang3.StringUtils.isNotBlank(taskData.getAllRestrictProvince())
                && taskData.getAllRestrictCity().contains(datum.getCityCode())
                && taskData.getAllRestrictProvince().contains(datum.getProvinceCode())){
            return true;
        }

        if(org.apache.commons.lang3.StringUtils.isNotBlank(taskData.getDxRestrictCity())
                && org.apache.commons.lang3.StringUtils.isNotBlank(taskData.getDxRestrictProvince())
                && datum.getOperator().contains("电信") && taskData.getDxRestrictCity().contains(datum.getCityCode())
                && taskData.getDxRestrictProvince().contains(datum.getProvinceCode())){
            return true;
        }

        if(org.apache.commons.lang3.StringUtils.isNotBlank(taskData.getLtRestrictCity())
                && org.apache.commons.lang3.StringUtils.isNotBlank(taskData.getLtRestrictProvince())
                && datum.getOperator().contains("联通") && taskData.getLtRestrictCity().contains(datum.getCityCode())
                && taskData.getLtRestrictProvince().contains(datum.getProvinceCode())){
            return true;
        }

        if(org.apache.commons.lang3.StringUtils.isNotBlank(taskData.getYdRestrictCity())
                && org.apache.commons.lang3.StringUtils.isNotBlank(taskData.getYdRestrictProvince())
                && datum.getOperator().contains("移动") && taskData.getYdRestrictCity().contains(datum.getCityCode())
                && taskData.getYdRestrictProvince().contains(datum.getProvinceCode())){
            return true;
        }
        return false;

    }



    public CallPhoneProducerMaterial pullPhoneTwo(Channel channel){
        //创建一个不带事务的消息通道。有了通道就好办了，就可以进行确认与取消了
        try {
            //这里是获取队列的一条消息
            //参数二：表示此时即使消费者已经收到消息了，RabbitMQ 也不会立马将消息移除，而是等待消费者显式的回复确认信号后，才会将消息删除
            /**
             * 根据比率来计算从哪个队列里面去数据
             */
            List<Double> rates = Arrays.stream(pullRate.split(",")).map(p->Double.parseDouble(p)).collect(Collectors.toList());
            int result = ActivityService.lottery(rates);
            List<String> queueList = Lists.newArrayList();
            queueList.add(RabbitMqConstants.CALL_PHONE_QUEUE);
            queueList.add(RabbitMqConstants.CALL_PHONE_LINE_LACK_QUEUE);
            GetResponse getResponse = null ;
            getResponse = channel.basicGet(queueList.get(result), true);

            //log.info("===>正在消费号码："+result);

            /**
             * 从一个队列中没有获取道数据，则到另一个队列中获取
             */
            if(getResponse==null){
                if(result==0){
                    result = 1;
                }else{
                    result = 0;
                }
                getResponse = channel.basicGet(queueList.get(result), true);
            }

            //获取标记
            if(getResponse!=null){
                //deliveryTag = getResponse.getEnvelope().getDeliveryTag();
                String body = new String(getResponse.getBody(),"UTF-8");
                CallPhoneProducerMaterial data = JSONObject.parseObject(body,CallPhoneProducerMaterial.class);
                if(!ifInWorkingTime(data.getStartTime(),data.getEndTime(),data.getTaskName(),data)){//非工作时间
                    //22点之后，清理呼叫队列中数据
                    if(!(LocalDateTime.now().getHour() == 22)){
                        backQueueNotWorktime(channel,data);
                    }
                    return null;
                }
                //log.info("===>正在消费号码："+body);
                return data;
            }

        } catch (IOException e) {
            try {
                channel.close();
            } catch (IOException e1) {
                throw new RuntimeException(e1);
            } catch (TimeoutException e2) {
                throw new RuntimeException(e2);
            }
            channel = secondRabbitTemplate.getConnectionFactory().createConnection().createChannel(false);
            e.printStackTrace();
            DingDingService.dingDingSendMsgException("pull号码时error","sfServerId",e.getMessage());
        }
        return null;

    }

    public void batchToRedis(List<CallEngineService.CallEngine.SendPhoneCallable> sendThreads){
        //StopWatch stopWatch = new StopWatch();
        try(Jedis jedis = jedisPool.getResource()){
            Pipeline pipeline = jedis.pipelined();
            SetParams setParams = new SetParams();
            setParams.ex(2700);
            //stopWatch.start("缓存打包");
            genCallStatus(sendThreads,pipeline,setParams);
            //stopWatch.stop();
            //stopWatch.start("同步redis执行");
            pipeline.sync();//同步执行
            //stopWatch.stop();
            /*
            if(stopWatch.getTotalTimeMillis()> 50L){
                log.info("===> 批量缓存打包大小："+sendThreads.size()+": "+stopWatch.prettyPrint());
            }

             */
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    private void genCallStatus(List<CallEngineService.CallEngine.SendPhoneCallable> sendThreads, Pipeline pipeline, SetParams setParams){
        for (CallEngineService.CallEngine.SendPhoneCallable sendPhoneCallable : sendThreads) {
            CallPhoneProducerMaterial.PhoneDataCache phoneDataCache = sendPhoneCallable.getPhoneDataCache();
            FsClientData fsClientData = sendPhoneCallable.getFsClientData();
            CallLineUnit callLineUnit = sendPhoneCallable.getCallLineUnit();
            ScriptStatus status = new ScriptStatus();
            status.setTrainPhone(false);
            status.setScriptId(phoneDataCache.getScriptId());
            status.setVersion(phoneDataCache.getScriptVersion());
            status.setScriptLongId(phoneDataCache.getScriptLongId());
            status.setUserContent("");
            status.setCallId(sendPhoneCallable.getCallId());
            status.setPhoneDataCache(phoneDataCache);
            if(status.getCallStatus()==null){
                status.setCallStatus(new CallStatus());
                status.getCallStatus().setCallOutTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                status.getCallStatus().setCallId(sendPhoneCallable.getCallId());
            }
            status.getCallStatus().setFsIp(fsClientData.getFsConfig().getIp());
            status.getCallStatus().setAiCallIp(currentServerIpService.getIp());
            status.getCallStatus().setProvince(phoneDataCache.getProvince());
            status.getCallStatus().setCity(phoneDataCache.getCity());
            status.getCallStatus().setLineId(phoneDataCache.getLineId());
            status.getCallStatus().setLineCode(phoneDataCache.getLineCode());
            status.getCallStatus().setMerchantLineId(phoneDataCache.getMerchantLineId());
            status.getCallStatus().setMerchantLineCode(phoneDataCache.getMerchantLineCode());
            status.getCallStatus().setCallLineUnit(callLineUnit);
            status.getCallStatus().setPhoneDataCache(phoneDataCache);
            String jsonString = JSONObject.toJSONString(status, SerializerFeature.WriteClassName);
            pipeline.set(ApplicationConstants.CALL_USER_STATUS+sendPhoneCallable.getCallId(),jsonString,setParams);
        }

    }


    public boolean ifInWorkingTime(String startTime,String endTime,String taskName,CallPhoneProducerMaterial data){
        if(StringUtils.isEmpty(startTime)||StringUtils.isEmpty(endTime)){
            //log.info("===> 任务工作时间为空，任务名："+taskName+",DATA="+JSON.toJSONString(data));
            DingDingService.dingDingSendMsgException("任务工作时间为空","任务名："+taskName);
            return true;
        }

        List<String> startWorkTimeList = Arrays.asList(startTime.split(","));
        List<String> endWorkTimeList = Arrays.asList(endTime.split(","));
        if(startWorkTimeList.size() != endWorkTimeList.size()){
            return false;
        }
        LocalDateTime now = LocalDateTime.now();
        String preFix = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))+" ";
        String subFix = ":00";
        for (int i = 0; i < startWorkTimeList.size(); i++) {
            LocalDateTime sTime = LocalDateTime.parse(preFix+startWorkTimeList.get(i)+subFix, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            LocalDateTime eTime = LocalDateTime.parse(preFix+endWorkTimeList.get(i)+subFix, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            if(now.compareTo(sTime)>=0 && now.compareTo(eTime)<=0){
                return true;
            }
        }
        return false;
    }

    public void backQueueNotWorktime(Channel channel,CallPhoneProducerMaterial material){
        String jsonString = JSON.toJSONString(material);
        CallPhoneProducerMaterialTemp temp = JSONObject.parseObject(jsonString,CallPhoneProducerMaterialTemp.class);
        try {
            String pushString = JSON.toJSONString(temp);
            //log.info("===>非工作时间消费号码："+pushString);
            channel.basicPublish(CALL_PHONE_NOT_WORKING_TIME_EXCHANGE,CALL_PHONE_NOT_WORKING_TIME_ROUTING, new AMQP.BasicProperties().builder().deliveryMode(2).build(),pushString.getBytes(StandardCharsets.UTF_8));
        } catch (IOException e) {
            e.printStackTrace();
            try {
                channel.close();
            } catch (IOException ex) {
                throw new RuntimeException(ex);
            } catch (TimeoutException ex) {
                throw new RuntimeException(ex);
            }
            channel = secondRabbitTemplate.getConnectionFactory().createConnection().createChannel(false);
            DingDingService.dingDingSendMsgException("回写sql报错","sfServerId",e.getMessage());
        }
        return;

    }

    private String getPushString(String taskId,CallPhoneProducerMaterial.PhoneData phoneData){
        StringBuilder sb = new StringBuilder("");
        sb.append(taskId).append(",");
        sb.append(phoneData.getPlainPhone()).append(",");//先传密文
        sb.append(phoneData.getSpeechCallId()).append(",");
        sb.append(phoneData.getPhone()).append(",");
        sb.append(phoneData.getProvince()).append(",");
        sb.append(phoneData.getProvinceCode()).append(",");
        sb.append(phoneData.getCity()).append(",");
        sb.append(phoneData.getCityCode()).append(",");
        sb.append(phoneData.getOperator()).append(",");
        if(phoneData.getFirstCallOrReCall().contains("firstCall")){
            sb.append(phoneData.getFirstCallOrReCall());
        }else{
            sb.append(phoneData.getTargetTime()).append(",");
            sb.append(phoneData.getFirstCallOrReCall());
        }
        return sb.toString();

    }

    private void callPhoneStopToRedis(Channel channel,String taskId,CallPhoneProducerMaterial.PhoneData phoneData){
        try {
            String jsonString =  getPushString(taskId,phoneData);
            //log.info("======>归还redis的串"+jsonString);
            channel.basicPublish(CALL_PHONE_STOP_TO_REDIS_EXCHANGE,CALL_PHONE_STOP_TO_REDIS_ROUTING, new AMQP.BasicProperties().builder().deliveryMode(2).build(),jsonString.getBytes(StandardCharsets.UTF_8));
        } catch (IOException e) {
            e.printStackTrace();
            try {
                channel.close();
            } catch (IOException ex) {
                throw new RuntimeException(ex);
            } catch (TimeoutException ex) {
                throw new RuntimeException(e);
            }
            channel = secondRabbitTemplate.getConnectionFactory().createConnection().createChannel(false);
        }
        return;
    }

    private void callPhoneStopToRedisBatch(Channel channel,String taskId,CallPhoneProducerMaterial.PhoneData phoneData){
        String jsonString =  getPushString(taskId,phoneData);
        log.info("======>归还redis的串"+jsonString);
        //channel.basicPublish(CALL_PHONE_STOP_TO_REDIS_EXCHANGE,CALL_PHONE_STOP_TO_REDIS_ROUTING, new AMQP.BasicProperties().builder().deliveryMode(2).build(),jsonString.getBytes(StandardCharsets.UTF_8));
        stopToRedisReturnQueue.addData(jsonString);
    }



    public CallPhoneProducerMaterial pullPhoneTwoTest(Channel channel){
        //创建一个不带事务的消息通道。有了通道就好办了，就可以进行确认与取消了
        try {
            //这里是获取队列的一条消息
            //参数二：表示此时即使消费者已经收到消息了，RabbitMQ 也不会立马将消息移除，而是等待消费者显式的回复确认信号后，才会将消息删除
            /**
             * 根据比率来计算从哪个队列里面去数据
             */
            List<Double> rates = Arrays.stream(pullRate.split(",")).map(p->Double.parseDouble(p)).collect(Collectors.toList());
            int result = ActivityService.lottery(rates);
            List<String> queueList = Lists.newArrayList();
            queueList.add(RabbitMqConstants.CALL_PHONE_QUEUE_TEST);
            queueList.add(RabbitMqConstants.CALL_PHONE_LINE_LACK_QUEUE_TEST);
            GetResponse getResponse = null ;
            getResponse = channel.basicGet(queueList.get(result), true);

            /**
             * 从一个队列中没有获取道数据，则到另一个队列中获取
             */
            if(getResponse==null){
                if(result==0){
                    result = 1;
                }else{
                    result = 0;
                }
                getResponse = channel.basicGet(queueList.get(result), true);
            }

            //获取标记
            if(getResponse!=null){
                //deliveryTag = getResponse.getEnvelope().getDeliveryTag();
                String body = new String(getResponse.getBody(),"UTF-8");
                CallPhoneProducerMaterial data = JSONObject.parseObject(body,CallPhoneProducerMaterial.class);
                log.info("===>正在消费号码："+body);
                return data;
            }

        } catch (IOException e) {
            try {
                channel.close();
            } catch (IOException e1) {
                throw new RuntimeException(e1);
            } catch (TimeoutException e2) {
                throw new RuntimeException(e2);
            }
            channel = secondRabbitTemplate.getConnectionFactory().createConnection().createChannel(false);
            e.printStackTrace();
            DingDingService.dingDingSendMsgException("pull号码时error","sfServerId",e.getMessage());
        }
        return null;

    }

    @Data
    public static class CallPhoneProducerMaterialTemp implements Serializable {
        private LocalDate date;
        private String taskId;//任务id
        private String lineId;//线路id
        private String lineCode;//线路id
        private String lineGroupCode;//线路id
        private String taskName;//任务名
        private String scriptId;//话术id
        private Integer scriptVersion;//话术id
        private Long scriptLongId;//话术id
        private String merchantLineId;
        private String merchantLineCode;
        private String startTime;
        private String endTime;
        private List<PhoneDataTemp> data;//号码包，一个包暂定10个
        private String groupId;
        private String callTeamHandleType;
    }

    @Data
    public static class PhoneDataTemp  implements Serializable {
        private String speechCallId;//呼叫id
        private String phone;
        private String plainPhone;
        private String cityCode;
        private String operator;
        private String city;
        private String province;
        private Integer sendCallErrorRetryTimes=0;//送呼失败补偿次数-默认0
        private List<String> historyMatchSupplyNumbers;//历史匹配到的供应线路，如果首次匹配则为null
        private Map<String, List<LocalDateTime>> callingHistory;//拨打历史记录 Map<供应线路编号, 拨打时间列表>
        private Map<String, List<LocalDateTime>> dialingHistory;//拨通历史记录 Map<供应线路编号, 拨通时间列表>
        private Set<String> blackSupplyLineNumbers;
        private Set<Long> lightPhoneIds;
        private String provinceCode;
        private String firstCallOrReCall;//0 首呼，1补呼,默认是首呼队列
        private String targetTime;//补呼时间
        private Map<String,Object> extParams;
        //private Integer waitOutHangupTime = 120;//超时挂机时长,默认120s

    }

    public void backQueue(Channel channel,CallPhoneProducerMaterial material){
        //创建一个不带事务的消息通道。有了通道就好办了，就可以进行确认与取消了
        String jsonString = JSON.toJSONString(material);
        CallPhoneProducerMaterialTemp temp = JSONObject.parseObject(jsonString,CallPhoneProducerMaterialTemp.class);

        try {
            //这里是获取队列的一条消息
            //参数二：表示此时即使消费者已经收到消息了，RabbitMQ 也不会立马将消息移除，而是等待消费者显式的回复确认信号后，才会将消息删除
            String pushString = JSON.toJSONString(temp);
            channel.basicPublish(CALL_PHONE_EXCHANGE,CALL_PHONE_ROUTING, new AMQP.BasicProperties().builder().deliveryMode(2).build(),pushString.getBytes(StandardCharsets.UTF_8));

        } catch (IOException e) {
            e.printStackTrace();
            try {
                channel.close();
            } catch (IOException ex) {
                throw new RuntimeException(ex);
            } catch (TimeoutException ex) {
                throw new RuntimeException(ex);
            }
            channel = secondRabbitTemplate.getConnectionFactory().createConnection().createChannel(false);
            DingDingService.dingDingSendMsgException("回写sql报错","sfServerId",e.getMessage());
        }
        return;

    }

    public void backLineLackQueue(Channel channel,CallPhoneProducerMaterial material){
        //创建一个不带事务的消息通道。有了通道就好办了，就可以进行确认与取消了
        String jsonString = JSON.toJSONString(material);
        CallPhoneProducerMaterialTemp temp = JSONObject.parseObject(jsonString,CallPhoneProducerMaterialTemp.class);

        try {
            //这里是获取队列的一条消息
            //参数二：表示此时即使消费者已经收到消息了，RabbitMQ 也不会立马将消息移除，而是等待消费者显式的回复确认信号后，才会将消息删除
            String pushString = JSON.toJSONString(temp);
            channel.basicPublish(CALL_PHONE_LINE_LACK_EXCHANGE,CALL_PHONE_LINE_LACK_ROUTING, new AMQP.BasicProperties().builder().deliveryMode(2).build(),pushString.getBytes(StandardCharsets.UTF_8));

        } catch (IOException e) {
            e.printStackTrace();
            try {
                channel.close();
            } catch (IOException ex) {
                throw new RuntimeException(ex);
            } catch (TimeoutException ex) {
                throw new RuntimeException(ex);
            }
            channel = secondRabbitTemplate.getConnectionFactory().createConnection().createChannel(false);
            DingDingService.dingDingSendMsgException("回写sql报错","sfServerId",e.getMessage());
        }
        return;

    }

    @Autowired
    private LackLinePushToSingleService lackLinePushToSingleService;

    public void backLineLackQueue1(Channel channel,CallPhoneProducerMaterial material, CallPhoneProducerMaterial.PhoneData phoneData){
        //创建一个不带事务的消息通道。有了通道就好办了，就可以进行确认与取消了
        String jsonString = JSON.toJSONString(material);
        CallPhoneProducerMaterialTemp temp = JSONObject.parseObject(jsonString,CallPhoneProducerMaterialTemp.class);
        PhoneDataTemp tempPhoneData = new PhoneDataTemp();
        tempPhoneData.setPhone(phoneData.getPhone());
        tempPhoneData.setPlainPhone(phoneData.getPlainPhone());
        tempPhoneData.setSpeechCallId(phoneData.getSpeechCallId());
        tempPhoneData.setOperator(phoneData.getOperator());
        tempPhoneData.setCityCode(phoneData.getCityCode());
        tempPhoneData.setSendCallErrorRetryTimes(phoneData.getSendCallErrorRetryTimes());
        tempPhoneData.setHistoryMatchSupplyNumbers(phoneData.getHistoryMatchSupplyNumbers());
        tempPhoneData.setProvince(phoneData.getProvince());
        tempPhoneData.setProvinceCode(phoneData.getProvinceCode());
        tempPhoneData.setCity(phoneData.getCity());
        tempPhoneData.setCallingHistory(phoneData.getCallingHistory());
        tempPhoneData.setDialingHistory(phoneData.getDialingHistory());
        tempPhoneData.setBlackSupplyLineNumbers(phoneData.getBlackSupplyLineNumbers());
        tempPhoneData.setLightPhoneIds(phoneData.getLightPhoneIds());
        tempPhoneData.setFirstCallOrReCall(phoneData.getFirstCallOrReCall());
        tempPhoneData.setTargetTime(phoneData.getTargetTime());
        tempPhoneData.setExtParams(phoneData.getExtParams());
        //tempPhoneData.setWaitOutHangupTime(phoneData.getWaitOutHangupTime());
        temp.getData().clear();
        temp.getData().add(tempPhoneData);
        try {
            //这里是获取队列的一条消息
            //参数二：表示此时即使消费者已经收到消息了，RabbitMQ 也不会立马将消息移除，而是等待消费者显式的回复确认信号后，才会将消息删除
            String pushString = JSON.toJSONString(temp);
            CallPhoneProducerMaterial cc = new CallPhoneProducerMaterial();
            cc = JSONObject.parseObject(pushString,CallPhoneProducerMaterial.class);
            //channel.basicPublish(CALL_PHONE_LINE_LACK_SINGLE_EXCHANGE,CALL_PHONE_LINE_LACK_SINGLE_ROUTING, new AMQP.BasicProperties().builder().deliveryMode(2).build(),pushString.getBytes(StandardCharsets.UTF_8));
            lackLinePushToSingleService.add(cc);
        } catch (Exception e) {
            e.printStackTrace();
            try {
                channel.close();
            } catch (IOException ex) {
                throw new RuntimeException(ex);
            } catch (TimeoutException ex) {
                throw new RuntimeException(ex);
            }
            channel = currentServerIpService.getMainRabbitTemplate().getConnectionFactory().createConnection().createChannel(false);
            DingDingService.dingDingSendMsgException("回写sql报错","sfServerId",e.getMessage());
        }
        return;

    }


    /**
     * 归还线路
     */
    public void lineReturn(CallLineUnit callLineUnit){
        URI uri = null;
        try{
            uri = new URI("http://"+callLineUnit.getUnitAddress());
        }catch (Exception e){
            e.printStackTrace();
        }
        if(uri!=null){
            try{
                lineReturnFeign.returnOneLine(uri,callLineUnit);
            }catch (Exception e){
                try{
                    lineReturnFeign.returnOneLine(uri,callLineUnit);
                }catch (Exception e1){
                    try{
                        lineReturnFeign.returnOneLine(uri,callLineUnit);
                    }catch (Exception e2){
                        e2.printStackTrace();
                        DingDingService.dingDingSendMsgException("归还线路线路异常","aiCallIp"+currentServerIpService.getIp(),e2.getMessage());
                    }
                }
            }

        }
    }




    public Boolean callPhone(CallPhoneProducerMaterial.PhoneDataCache phoneDataCache, FsClientData fsClientData, CallLineUnit callLineUnit){
        Integer waitOutHangupTime = 120;//超时挂机时长

        if(phoneDataCache.getExtParams() != null && phoneDataCache.getExtParams().containsKey("waitOutHangupTime"))
            waitOutHangupTime = (Integer)phoneDataCache.getExtParams().get("waitOutHangupTime");

        //Long startTime = System.currentTimeMillis();
       // StopWatch stopWatch = new StopWatch();
        //stopWatch.start("打包");
        if (fsClientData.getFsClient().canSend(this,fsClientData)) {//检查网络抖动
            String callChannelUniqueId = null;
            if(isPressureTest.equals("1")){
                callChannelUniqueId = getUUIDTest(fsClientData.getFsClient());
            }else{
                callChannelUniqueId = getCallIdBySnow();//getUUID(fsClientData.getFsClient());
                if(StringUtils.isNotEmpty(phoneDataCache.getCallTeamHandleType()) && phoneDataCache.getCallTeamHandleType().equals("take_over")){
                    callChannelUniqueId = callChannelUniqueId + "_take_over";
                }else if(StringUtils.isNotEmpty(phoneDataCache.getCallTeamHandleType()) && phoneDataCache.getCallTeamHandleType().equals("listen_in")){
                    callChannelUniqueId = callChannelUniqueId + "_listen_in";
                }

            }
            callChannelUniqueId = "engine_"+callChannelUniqueId;//engine呼的都改成线路缺失的call_id
            String customerId = String.format("%s#%s#%s#%s",fsClientData.getFsConfig().getIp(),callChannelUniqueId,fsClientData.getFsConfig().getEngineIp(),fsClientData.getFsConfig().getEnginePort());
            String args;
            if(isGw){//网关
                if(isDirectIp){//如果是直接ip，则使用ip字段
                    if(isPressureTest.equals("1")){
                        args = "{origination_uuid="+callChannelUniqueId+",origination_caller_id_number="+callLineUnit.getMasterCallNumber()+",origination_caller_id_name="+callLineUnit.getMasterCallNumber()+",originate_timeout=120,ignore_early_media=true,speech_call_id="+phoneDataCache.getSpeechCallId()+"}"+"sofia/external/"+callLineUnit.getPrefix()+phoneDataCache.getPlainPhone()+"@"+callLineUnit.getRegisterIp()+":"+callLineUnit.getRegisterPort()+" 50007 XML default";
                    }else{
                        if(fsClientData.getFsConfig().getIsKml() == 1){
                            if(phoneDataCache.getTaskName().contains("volcano:")){
                                String packageId = phoneDataCache.getTaskName().split("volcano:")[1];
                                args = "{customerId="+customerId+",origination_uuid="+callChannelUniqueId+",ignore_early_media=false,origination_caller_id_number="+callLineUnit.getMasterCallNumber()+",origination_caller_id_name="+callLineUnit.getMasterCallNumber()+",originate_timeout="+120+",speech_call_id="+phoneDataCache.getSpeechCallId()+",sip_h_X-C-USER-DATA="+packageId+",sip_sticky_contact=true}"+"sofia/external/"+callLineUnit.getRegisterIp()+".."+callLineUnit.getRegisterPort()+"..."+callLineUnit.getPrefix()+phoneDataCache.getPlainPhone()+"@"+fsClientData.getFsConfig().getKmlAddress()+" 50005 XML default";
                            }else if(phoneDataCache.getTaskName().startsWith("ANT_")){
                                Integer SecretCall = 1;
                                String uniTenantId = "DJISOFJG";
                                String uniChannelMediaId = "3923";
                                if(phoneDataCache.getExtParams() != null){
                                    uniTenantId = (String) phoneDataCache.getExtParams().getOrDefault("uniTenantId","123");//客户ID
                                    uniChannelMediaId = (String) phoneDataCache.getExtParams().getOrDefault("uniChannelMediaId","111");//机器人厂商ID/标识
                                }
                                args = "{customerId="+customerId+",origination_uuid="+callChannelUniqueId+",ignore_early_media=false,origination_caller_id_number="+callLineUnit.getMasterCallNumber()+",origination_caller_id_name="+callLineUnit.getMasterCallNumber()+",originate_timeout="+120+",speech_call_id="+phoneDataCache.getSpeechCallId()
                                        +",sip_h_SecretCall="+SecretCall+",sip_h_uniTenantId="+uniTenantId+",sip_h_uniChannelMediaId="+uniChannelMediaId+
                                        ",sip_sticky_contact=true}"+"sofia/external/"+callLineUnit.getRegisterIp()+".."+callLineUnit.getRegisterPort()+"..."+callLineUnit.getPrefix()+phoneDataCache.getPlainPhone()+"@"+fsClientData.getFsConfig().getKmlAddress()+" 50005 XML default";
                            }else if(phoneDataCache.getTaskName().contains("RING")) {
                                Matcher matcher = ringPattern.matcher(phoneDataCache.getTaskName());
                                String ring;
                                if(matcher.find()) {
                                    //System.out.println("Found: " + matcher.group());
                                    ring = matcher.group();
                                    try{
                                        waitOutHangupTime = Integer.valueOf(ring.replace("RING[","").replace("]",""));
                                    }catch (Exception e1){
                                        //传参数格式有问题，请排查
                                        DingDingService.dingDingSendMsgException("该任务传参格式有问题，请排查！","任务名：" + phoneDataCache.getTaskName());
                                    }
                                }
                                args = "{customerId="+customerId+",origination_uuid="+callChannelUniqueId+",ignore_early_media=true,origination_caller_id_number="+callLineUnit.getMasterCallNumber()+",origination_caller_id_name="+callLineUnit.getMasterCallNumber()+",originate_timeout="+120+",speech_call_id="+phoneDataCache.getSpeechCallId()+",sip_sticky_contact=true}"+"sofia/external/"+callLineUnit.getRegisterIp()+".."+callLineUnit.getRegisterPort()+"..."+callLineUnit.getPrefix()+phoneDataCache.getPlainPhone()+"@"+fsClientData.getFsConfig().getKmlAddress()+" 50005 XML default";

                            }else{
                                args = "{customerId="+customerId+",origination_uuid="+callChannelUniqueId+",ignore_early_media=false,origination_caller_id_number="+callLineUnit.getMasterCallNumber()+",origination_caller_id_name="+callLineUnit.getMasterCallNumber()+",originate_timeout="+120+",speech_call_id="+phoneDataCache.getSpeechCallId()+",sip_sticky_contact=true}"+"sofia/external/"+callLineUnit.getRegisterIp()+".."+callLineUnit.getRegisterPort()+"..."+callLineUnit.getPrefix()+phoneDataCache.getPlainPhone()+"@"+fsClientData.getFsConfig().getKmlAddress()+" 50005 XML default";
                            }
                        }else{
                            if(phoneDataCache.getTaskName().contains("volcano:")){
                                String packageId = phoneDataCache.getTaskName().split("volcano:")[1];
                                args = "{customerId="+customerId+",origination_uuid="+callChannelUniqueId+",ignore_early_media=false,origination_caller_id_number="+callLineUnit.getMasterCallNumber()+",origination_caller_id_name="+callLineUnit.getMasterCallNumber()+",originate_timeout=120,speech_call_id="+phoneDataCache.getSpeechCallId()+",sip_h_X-C-USER-DATA="+packageId+"}"+"sofia/external/"+callLineUnit.getPrefix()+phoneDataCache.getPlainPhone()+"@"+callLineUnit.getRegisterIp()+":"+callLineUnit.getRegisterPort()+" 50005 XML default";
                            }else{
                                args = "{customerId="+customerId+",origination_uuid="+callChannelUniqueId+",ignore_early_media=false,origination_caller_id_number="+callLineUnit.getMasterCallNumber()+",origination_caller_id_name="+callLineUnit.getMasterCallNumber()+",originate_timeout=120,speech_call_id="+phoneDataCache.getSpeechCallId()+"}"+"sofia/external/"+callLineUnit.getPrefix()+phoneDataCache.getPlainPhone()+"@"+callLineUnit.getRegisterIp()+":"+callLineUnit.getRegisterPort()+" 50005 XML default";
                            }
                        }
                        //log.info("呼叫命令："+args);
                    }
                }else{
                    args = "{origination_uuid="+callChannelUniqueId+",origination_caller_id_number="+777777+",originate_timeout=120,ignore_early_media=true,speech_call_id="+phoneDataCache.getSpeechCallId()+"}"+"sofia/gateway/"+phoneDataCache.getLineCode()+"/"+phoneDataCache.getPlainPhone()+" 50005 XML default";
                }
            }else{
                args = "{origination_uuid="+callChannelUniqueId+",origination_caller_id_number="+777777+",originate_timeout=120,ignore_early_media=true,speech_call_id="+phoneDataCache.getSpeechCallId()+"}"+"user/"+phoneDataCache.getPlainPhone()+" 50005 XML default";
            }
            //log.info("===>originate："+fsClientData.getFsConfig().getIp()+","+phoneDataCache.getPlainPhone()+","+phoneDataCache.getPhone()+","+args);

            if(callChannelUniqueId.startsWith("lll")){
                //TODO 将数据推送到新的队列
                CallPhoneProducerMaterialTemp temp = new CallPhoneProducerMaterialTemp();
                BeanUtils.copyProperties(phoneDataCache,temp);
                PhoneDataTemp phoneDataTemp = new PhoneDataTemp();
                BeanUtils.copyProperties(phoneDataCache,phoneDataTemp);
                List<PhoneDataTemp> listData = Lists.newArrayList();

                if(phoneDataTemp.getSendCallErrorRetryTimes() != null && phoneDataTemp.getSendCallErrorRetryTimes() >= 0){
                    phoneDataTemp.setSendCallErrorRetryTimes(phoneDataTemp.getSendCallErrorRetryTimes()-1);
                }
                if(phoneDataTemp.getHistoryMatchSupplyNumbers() != null && phoneDataTemp.getHistoryMatchSupplyNumbers().size() > 0){
                    phoneDataTemp.getHistoryMatchSupplyNumbers().removeIf(p->p.equals(callLineUnit.getSupplyLineNumber()));
                }
                listData.add(phoneDataTemp);
                temp.setData(listData);
                try {
                    //这里是获取队列的一条消息
                    //参数二：表示此时即使消费者已经收到消息了，RabbitMQ 也不会立马将消息移除，而是等待消费者显式的回复确认信号后，才会将消息删除
                    String pushString = JSON.toJSONString(temp);
                    ChannelManagerService.getOneChannel().basicPublish(CALL_PHONE_NO_UUID_EXCHANGE,CALL_PHONE_NO_UUID_ROUTING, new AMQP.BasicProperties().builder().deliveryMode(2).build(),pushString.getBytes(StandardCharsets.UTF_8));
                    //归还线路
                    lineReturn(callLineUnit);
                } catch (IOException e) {
                    e.printStackTrace();
                    DingDingService.dingDingSendMsgException("推送失败","sfServerId",e.getMessage());
                }
                return false;
            }


            //log.info("===>jobApiId："+jobApiId);
            ScriptStatus status = new ScriptStatus();
            status.setTrainPhone(false);
            status.setScriptId(phoneDataCache.getScriptId());
            status.setVersion(phoneDataCache.getScriptVersion());
            status.setScriptLongId(phoneDataCache.getScriptLongId());
            status.setUserContent("");
            status.setCallId(callChannelUniqueId);
            status.setPhoneDataCache(phoneDataCache);
            if(status.getCallStatus()==null){
                status.setCallStatus(new CallStatus());
                status.getCallStatus().setCallOutTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                status.getCallStatus().setCallId(callChannelUniqueId);
            }
            status.getCallStatus().setFsIp(fsClientData.getFsConfig().getIp());
            status.getCallStatus().setAiCallIp(currentServerIpService.getIp());
            status.getCallStatus().setProvince(phoneDataCache.getProvince());
            status.getCallStatus().setCity(phoneDataCache.getCity());
            status.getCallStatus().setLineId(phoneDataCache.getLineId());
            status.getCallStatus().setLineCode(phoneDataCache.getLineCode());
            status.getCallStatus().setMerchantLineId(phoneDataCache.getMerchantLineId());
            status.getCallStatus().setMerchantLineCode(phoneDataCache.getMerchantLineCode());
            status.getCallStatus().setCallLineUnit(callLineUnit);
            status.getCallStatus().setPhoneDataCache(phoneDataCache);
            String jsonString = JSONObject.toJSONString(status, SerializerFeature.WriteClassName);
            //stopWatch.stop();
            //stopWatch.start("单条入redis");

            /**
             *
             */
            //log.info("单次拼接耗时"+(System.currentTimeMillis()-startTime)+"ms");
            //Long startTime1 = System.currentTimeMillis();
            try{
                simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+callChannelUniqueId, jsonString,ApplicationConstants.CALL_USER_STATUS_EXPIRE);
            }catch (Exception e){
                try{
                    simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+callChannelUniqueId, jsonString,ApplicationConstants.CALL_USER_STATUS_EXPIRE);
                }catch (Exception e1){
                }
            }
            /*
            if(!simpleRedisService.hasKey(ApplicationConstants.CALL_USER_STATUS+callChannelUniqueId)){
                try{
                    simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+callChannelUniqueId, jsonString,ApplicationConstants.CALL_USER_STATUS_EXPIRE);
                }catch (Exception e2){
                    e2.printStackTrace();
                }
                if(!simpleRedisService.hasKey(ApplicationConstants.CALL_USER_STATUS+callChannelUniqueId)){
                    log.error("===>callId: "+callChannelUniqueId+"呼叫的时候，保存用户状态出错");
                    DingDingService.dingDingSendMsgException("===>callId: "+callChannelUniqueId,"往FS推送数据时候，保存用户状态没有成功");
                }
            }
             */
            //log.info("单次redis耗时"+(System.currentTimeMillis()-startTime1)+"ms");
            //startTime1 = System.currentTimeMillis();
            //log.info("呼叫号码："+args);
            //stopWatch.stop();
            //stopWatch.start("送呼");

            if(waitOutHangupTime < 120){//低于120s说明主动设置了挂机等待时长
                HelpTask task = new HelpTask(callChannelUniqueId,fsClientData.getFsConfig().getIp(),waitOutHangupTime,helperService);
                HelpMonitor.addCall(task);
            }

            String jobApiId;
            try{
                jobApiId = fsClientData.getFsClient().sendAsyncApiCommand("originate", args);
            }catch (Exception e){
                try{
                    jobApiId = fsClientData.getFsClient().sendAsyncApiCommand("originate", args);
                }catch (Exception e1){
                    jobApiId = fsClientData.getFsClient().sendAsyncApiCommand("originate", args);
                }
            }
            /*
            stopWatch.stop();
            if(stopWatch.getTotalTimeMillis()>50L){
                log.info("===>callId: "+callChannelUniqueId+"送呼耗时："+stopWatch.prettyPrint());
            }

             */
            //log.info("单次送FS耗时"+(System.currentTimeMillis()-startTime1)+"ms");
            return true;
        }else{
            CallStatus callStatus = new CallStatus();
            callStatus.setCallStatus(-1);
            callStatus.setProvince(phoneDataCache.getProvince());
            callStatus.setCity(phoneDataCache.getCity());
            callStatus.setFsIp(fsClientData.getFsConfig().getIp());
            callStatus.setAiCallIp(currentServerIpService.getIp());
            callStatus.setLineId(phoneDataCache.getLineId());
            callStatus.setLineCode(phoneDataCache.getLineCode());
            callStatus.setMerchantLineId(phoneDataCache.getMerchantLineId());
            callStatus.setMerchantLineCode(phoneDataCache.getMerchantLineCode());
            callStatus.setCallLineUnit(callLineUnit);
            callStatus.setWhoHangup(1);
            callStatus.setPhoneDataCache(phoneDataCache);
            DingDingService.dingDingSendMsgException("送呼异常","fsServerIp："+fsClientData.getFsConfig().getIp(),"当前号码："+phoneDataCache.getPhone(),"lineId："+phoneDataCache.getLineId(),"taskId："+phoneDataCache.getTaskId(),"taskName："+phoneDataCache.getTaskName(),"scriptId："+phoneDataCache.getScriptId(),"lineCode："+phoneDataCache.getLineCode());
            callRecordService.toMQCdr(phoneDataCache.getTaskId(),phoneDataCache.getTaskName(),phoneDataCache.getPhone(),phoneDataCache.getLineId(),phoneDataCache.getScriptId(),null,phoneDataCache.getSpeechCallId(),"呼叫号码出错(可能是因为网络抖动)",null,phoneDataCache.getLineCode(),"","",phoneDataCache.getScriptVersion(),callStatus,phoneDataCache.getScriptLongId(),"");
            return false;
        }
    }


    public Boolean callPhone2(CallPhoneProducerMaterial.PhoneDataCache phoneDataCache, FsClientData fsClientData, CallLineUnit callLineUnit,String callId){
        Integer waitOutHangupTime = 120;//超时挂机时长

        if(phoneDataCache.getExtParams() != null && phoneDataCache.getExtParams().containsKey("waitOutHangupTime"))
            waitOutHangupTime = (Integer)phoneDataCache.getExtParams().get("waitOutHangupTime");

        //Long startTime = System.currentTimeMillis();
        if (fsClientData.getFsClient().canSend(this,fsClientData)) {//检查网络抖动
            String customerId = String.format("%s#%s#%s#%s",fsClientData.getFsConfig().getIp(),callId,fsClientData.getFsConfig().getEngineIp(),fsClientData.getFsConfig().getEnginePort());
            String args;
            if(phoneDataCache.getTaskName().contains("volcano:")){
                String packageId = phoneDataCache.getTaskName().split("volcano:")[1];
                args = "{customerId="+customerId+",origination_uuid="+callId+",ignore_early_media=false,origination_caller_id_number="+callLineUnit.getMasterCallNumber()+",origination_caller_id_name="+callLineUnit.getMasterCallNumber()+",originate_timeout="+120+",speech_call_id="+phoneDataCache.getSpeechCallId()+",sip_h_X-C-USER-DATA="+packageId+",sip_sticky_contact=true}"+"sofia/external/"+callLineUnit.getRegisterIp()+".."+callLineUnit.getRegisterPort()+"..."+callLineUnit.getPrefix()+phoneDataCache.getPlainPhone()+"@"+fsClientData.getFsConfig().getKmlAddress()+" 50005 XML default";
            }else if(phoneDataCache.getTaskName().startsWith("ANT_")){
                Integer SecretCall = 1;
                String uniTenantId = "DJISOFJG";
                String uniChannelMediaId = "3923";
                if(phoneDataCache.getExtParams() != null){
                    uniTenantId = (String) phoneDataCache.getExtParams().getOrDefault("uniTenantId","123");//客户ID
                    uniChannelMediaId = (String) phoneDataCache.getExtParams().getOrDefault("uniChannelMediaId","111");//机器人厂商ID/标识
                }
                args = "{customerId="+customerId+",origination_uuid="+callId+",ignore_early_media=false,origination_caller_id_number="+callLineUnit.getMasterCallNumber()+",origination_caller_id_name="+callLineUnit.getMasterCallNumber()+",originate_timeout="+120+",speech_call_id="+phoneDataCache.getSpeechCallId()
                        +",sip_h_SecretCall="+SecretCall+",sip_h_uniTenantId="+uniTenantId+",sip_h_uniChannelMediaId="+uniChannelMediaId+
                        ",sip_sticky_contact=true}"+"sofia/external/"+callLineUnit.getRegisterIp()+".."+callLineUnit.getRegisterPort()+"..."+callLineUnit.getPrefix()+phoneDataCache.getPlainPhone()+"@"+fsClientData.getFsConfig().getKmlAddress()+" 50005 XML default";
            }else if(phoneDataCache.getTaskName().contains("RING")) {
                Matcher matcher = ringPattern.matcher(phoneDataCache.getTaskName());
                String ring;
                if(matcher.find()) {
                    //System.out.println("Found: " + matcher.group());
                    ring = matcher.group();
                    try{
                        waitOutHangupTime = Integer.valueOf(ring.replace("RING[","").replace("]",""));
                    }catch (Exception e1){
                        //传参数格式有问题，请排查
                        DingDingService.dingDingSendMsgException("该任务传参格式有问题，请排查！","任务名：" + phoneDataCache.getTaskName());
                    }
                }
                args = "{customerId="+customerId+",origination_uuid="+callId+",ignore_early_media=true,origination_caller_id_number="+callLineUnit.getMasterCallNumber()+",origination_caller_id_name="+callLineUnit.getMasterCallNumber()+",originate_timeout="+120+",speech_call_id="+phoneDataCache.getSpeechCallId()+",sip_sticky_contact=true}"+"sofia/external/"+callLineUnit.getRegisterIp()+".."+callLineUnit.getRegisterPort()+"..."+callLineUnit.getPrefix()+phoneDataCache.getPlainPhone()+"@"+fsClientData.getFsConfig().getKmlAddress()+" 50005 XML default";

            }else{
                args = "{customerId="+customerId+",origination_uuid="+callId+",ignore_early_media=false,origination_caller_id_number="+callLineUnit.getMasterCallNumber()+",origination_caller_id_name="+callLineUnit.getMasterCallNumber()+",originate_timeout="+120+",speech_call_id="+phoneDataCache.getSpeechCallId()+",sip_sticky_contact=true}"+"sofia/external/"+callLineUnit.getRegisterIp()+".."+callLineUnit.getRegisterPort()+"..."+callLineUnit.getPrefix()+phoneDataCache.getPlainPhone()+"@"+fsClientData.getFsConfig().getKmlAddress()+" 50005 XML default";
            }
            //log.info("呼叫命令："+args);

            if(waitOutHangupTime < 120){//低于120s说明主动设置了挂机等待时长
                HelpTask task = new HelpTask(callId,fsClientData.getFsConfig().getIp(),waitOutHangupTime,helperService);
                HelpMonitor.addCall(task);
            }
            String jobApiId;
            try{
                jobApiId = fsClientData.getFsClient().sendAsyncApiCommand("originate", args);
            }catch (Exception e){
                try{
                    jobApiId = fsClientData.getFsClient().sendAsyncApiCommand("originate", args);
                }catch (Exception e1){
                    jobApiId = fsClientData.getFsClient().sendAsyncApiCommand("originate", args);
                }
            }
            //log.info("单次送FS耗时"+(System.currentTimeMillis()-startTime1)+"ms");
            return true;
        }else{
            CallStatus callStatus = new CallStatus();
            callStatus.setCallStatus(-1);
            callStatus.setProvince(phoneDataCache.getProvince());
            callStatus.setCity(phoneDataCache.getCity());
            callStatus.setFsIp(fsClientData.getFsConfig().getIp());
            callStatus.setAiCallIp(currentServerIpService.getIp());
            callStatus.setLineId(phoneDataCache.getLineId());
            callStatus.setLineCode(phoneDataCache.getLineCode());
            callStatus.setMerchantLineId(phoneDataCache.getMerchantLineId());
            callStatus.setMerchantLineCode(phoneDataCache.getMerchantLineCode());
            callStatus.setCallLineUnit(callLineUnit);
            callStatus.setWhoHangup(1);
            callStatus.setPhoneDataCache(phoneDataCache);
            DingDingService.dingDingSendMsgException("送呼异常","fsServerIp："+fsClientData.getFsConfig().getIp(),"当前号码："+phoneDataCache.getPhone(),"lineId："+phoneDataCache.getLineId(),"taskId："+phoneDataCache.getTaskId(),"taskName："+phoneDataCache.getTaskName(),"scriptId："+phoneDataCache.getScriptId(),"lineCode："+phoneDataCache.getLineCode());
            callRecordService.toMQCdr(phoneDataCache.getTaskId(),phoneDataCache.getTaskName(),phoneDataCache.getPhone(),phoneDataCache.getLineId(),phoneDataCache.getScriptId(),null,phoneDataCache.getSpeechCallId(),"呼叫号码出错(可能是因为网络抖动)",null,phoneDataCache.getLineCode(),"","",phoneDataCache.getScriptVersion(),callStatus,phoneDataCache.getScriptLongId(),"");
            return false;
        }
    }

    public static void main(String[] args) {
        Map<String,String> map = Maps.newHashMap();
        String s = map.getOrDefault("111","123");
        System.out.println(s);
    }

    public static void main223(String[] args) {
        List<String> list = Lists.newArrayList();
        list.add("RING[14]20250424小赢卡贷_白泽移动db");
        list.add("RING[14]20250424duibi_XRydRG");
        list.add("RING[14]20250424小赢卡贷_白泽");
        Integer waitOutHangupTime = 120;//超时挂机时长
        for (String result : list) {
            Matcher matcher = ringPattern.matcher(result);
            String ring;
            if(matcher.find()) {
                //System.out.println("Found: " + matcher.group());
                ring = matcher.group();
                try{
                    waitOutHangupTime = Integer.valueOf(ring.replace("RING[","").replace("]",""));
                }catch (Exception e1){
                    //传参数格式有问题，请排查
                    DingDingService.dingDingSendMsgException("该任务传参格式有问题，请排查！","任务名：" + result);
                }
            }
            String sss = "{customerId="+123+",origination_uuid="+123+",ignore_early_media=false,origination_caller_id_number="+123+",origination_caller_id_name="+123+",originate_timeout="+waitOutHangupTime+",speech_call_id="+123+",sip_sticky_contact=true}"+"sofia/external/"+123+".."+123+"..."+123+123+"@"+123+" 50005 XML default";
            System.out.println(sss);
        }

    }

    public Boolean callPhoneTest(CallPhoneProducerMaterial.PhoneDataCache phoneDataCache, FsClientData fsClientData, CallLineUnit callLineUnit){
        if (fsClientData.getFsClient().canSend(this,fsClientData)) {//检查网络抖动
            String callChannelUniqueId = getUUIDTest(fsClientData.getFsClient());
            log.info("===>当前呼叫的id"+callChannelUniqueId);
            String args;
            if(isGw){//网关
                if(isDirectIp){//如果是直接ip，则使用ip字段
                    args = "{origination_uuid="+callChannelUniqueId+",origination_caller_id_number="+callLineUnit.getMasterCallNumber()+",origination_caller_id_name="+callLineUnit.getMasterCallNumber()+",originate_timeout=120,ignore_early_media=true,speech_call_id="+phoneDataCache.getSpeechCallId()+"}"+"sofia/external/"+callLineUnit.getPrefix()+phoneDataCache.getPlainPhone()+"@"+callLineUnit.getRegisterIp()+":"+callLineUnit.getRegisterPort()+" 50005 XML default";
                }else{
                    args = "{origination_uuid="+callChannelUniqueId+",origination_caller_id_number="+777777+",originate_timeout=120,ignore_early_media=true,speech_call_id="+phoneDataCache.getSpeechCallId()+"}"+"sofia/gateway/"+phoneDataCache.getLineCode()+"/"+phoneDataCache.getPlainPhone()+" 50005 XML default";
                }
            }else{
                args = "{origination_uuid="+callChannelUniqueId+",origination_caller_id_number="+777777+",originate_timeout=120,ignore_early_media=true,speech_call_id="+phoneDataCache.getSpeechCallId()+"}"+"user/"+phoneDataCache.getPlainPhone()+" 50005 XML default";
            }
            log.info("===>originate："+args);
            String jobApiId;
            try{
                jobApiId = fsClientData.getFsClient().sendAsyncApiCommand("originate", args);
            }catch (Exception e){
                try{
                    jobApiId = fsClientData.getFsClient().sendAsyncApiCommand("originate", args);
                }catch (Exception e1){
                    jobApiId = fsClientData.getFsClient().sendAsyncApiCommand("originate", args);
                }
            }
            log.info("===>jobApiId："+jobApiId);
            ScriptStatus status = new ScriptStatus();
            status.setTrainPhone(false);
            status.setScriptId(phoneDataCache.getScriptId());
            status.setVersion(phoneDataCache.getScriptVersion());
            status.setScriptLongId(phoneDataCache.getScriptLongId());
            status.setUserContent("");
            status.setCallId(callChannelUniqueId);
            status.setPhoneDataCache(phoneDataCache);
            if(status.getCallStatus()==null){
                status.setCallStatus(new CallStatus());
                status.getCallStatus().setCallOutTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                status.getCallStatus().setCallId(callChannelUniqueId);
            }
            status.getCallStatus().setFsIp(fsClientData.getFsConfig().getIp());
            status.getCallStatus().setAiCallIp(currentServerIpService.getIp());
            status.getCallStatus().setProvince(phoneDataCache.getProvince());
            status.getCallStatus().setCity(phoneDataCache.getCity());
            status.getCallStatus().setLineId(phoneDataCache.getLineId());
            status.getCallStatus().setLineCode(phoneDataCache.getLineCode());
            status.getCallStatus().setMerchantLineId(phoneDataCache.getMerchantLineId());
            status.getCallStatus().setMerchantLineCode(phoneDataCache.getMerchantLineCode());
            status.getCallStatus().setCallLineUnit(callLineUnit);
            String jsonString = JSONObject.toJSONString(status, SerializerFeature.WriteClassName);
            simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+callChannelUniqueId, jsonString,ApplicationConstants.CALL_USER_STATUS_EXPIRE);
            return true;
        }else {
            return false;
        }
    }

    public void noScript(CallPhoneProducerMaterial.PhoneDataCache phoneDataCache){
        CallStatus callStatus = new CallStatus();
        callStatus.setCallStatus(-1);
        callStatus.setProvince(phoneDataCache.getProvince());
        callStatus.setCity(phoneDataCache.getCity());
        callStatus.setFsIp(null);
        callStatus.setAiCallIp(currentServerIpService.getIp());
        callStatus.setLineId(phoneDataCache.getLineId());
        callStatus.setLineCode(phoneDataCache.getLineCode());
        callStatus.setMerchantLineId(phoneDataCache.getMerchantLineId());
        callStatus.setMerchantLineCode(phoneDataCache.getMerchantLineCode());
        callStatus.setCallLineUnit(null);
        callStatus.setWhoHangup(1);
        callStatus.setPhoneDataCache(phoneDataCache);
        DingDingService.dingDingSendMsgException("送呼异常-没有话术","当前号码："+phoneDataCache.getPhone(),"lineId："+phoneDataCache.getLineId(),"taskId："+phoneDataCache.getTaskId(),"taskName："+phoneDataCache.getTaskName(),"scriptId："+phoneDataCache.getScriptId(),"lineCode："+phoneDataCache.getLineCode());
        callRecordService.toMQCdr(phoneDataCache.getTaskId(),phoneDataCache.getTaskName(),phoneDataCache.getPhone(),phoneDataCache.getLineId(),phoneDataCache.getScriptId(),null,phoneDataCache.getSpeechCallId(),"没有话术",null,phoneDataCache.getLineCode(),"","没有话术",phoneDataCache.getScriptVersion(),callStatus,phoneDataCache.getScriptLongId(),"");


    }


    public Boolean callPhoneTrainPhone(CallPhoneProducerMaterial.PhoneDataCache phoneDataCache, FsClientData fsClientData, CallLineUnit callLineUnit){
        if (fsClientData.getFsClient().canSend(this,fsClientData)) {//检查网络抖动
            //String callChannelUniqueId = getUUID(fsClientData.getFsClient());
            String callChannelUniqueId = getCallIdBySnow();
            String args = "{origination_uuid="+callChannelUniqueId+",origination_caller_id_number="+777777+",originate_timeout=120,ignore_early_media=true,speech_call_id="+phoneDataCache.getSpeechCallId()+"}"+"user/"+phoneDataCache.getPlainPhone()+" 50005 XML default";
            log.info("===>originate："+args);
            String jobApiId;
            try{
                jobApiId = fsClientData.getFsClient().sendAsyncApiCommand("originate", args);
            }catch (Exception e){
                try{
                    jobApiId = fsClientData.getFsClient().sendAsyncApiCommand("originate", args);
                }catch (Exception e1){
                    jobApiId = fsClientData.getFsClient().sendAsyncApiCommand("originate", args);
                }
            }
            log.info("===>jobApiId："+jobApiId);
            ScriptStatus status = new ScriptStatus();
            status.setTrainPhone(true);//是话术训练
            status.setScriptId(phoneDataCache.getScriptId());
            status.setVersion(phoneDataCache.getScriptVersion());
            status.setScriptLongId(phoneDataCache.getScriptLongId());
            status.setUserContent("");
            status.setCallId(callChannelUniqueId);
            status.setPhoneDataCache(phoneDataCache);
            if(status.getCallStatus()==null){
                status.setCallStatus(new CallStatus());
                status.getCallStatus().setCallOutTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                status.getCallStatus().setCallId(callChannelUniqueId);
            }
            status.getCallStatus().setFsIp(fsClientData.getFsConfig().getIp());
            status.getCallStatus().setAiCallIp(currentServerIpService.getIp());
            status.getCallStatus().setProvince(phoneDataCache.getProvince());
            status.getCallStatus().setCity(phoneDataCache.getCity());
            status.getCallStatus().setLineId(phoneDataCache.getLineId());
            status.getCallStatus().setLineCode(phoneDataCache.getLineCode());
            status.getCallStatus().setMerchantLineId(phoneDataCache.getMerchantLineId());
            status.getCallStatus().setMerchantLineCode(phoneDataCache.getMerchantLineCode());
            status.getCallStatus().setCallLineUnit(callLineUnit);
            status.getCallStatus().setPhoneDataCache(phoneDataCache);
            String jsonString = JSONObject.toJSONString(status, SerializerFeature.WriteClassName);
            simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+callChannelUniqueId, jsonString,ApplicationConstants.CALL_USER_STATUS_EXPIRE);
            return true;
        }else{
            CallStatus callStatus = new CallStatus();
            callStatus.setCallStatus(-1);
            callStatus.setProvince(phoneDataCache.getProvince());
            callStatus.setCity(phoneDataCache.getCity());
            callStatus.setFsIp(fsClientData.getFsConfig().getIp());
            callStatus.setAiCallIp(currentServerIpService.getIp());
            callStatus.setLineId(phoneDataCache.getLineId());
            callStatus.setLineCode(phoneDataCache.getLineCode());
            callStatus.setMerchantLineId(phoneDataCache.getMerchantLineId());
            callStatus.setMerchantLineCode(phoneDataCache.getMerchantLineCode());
            callStatus.setCallLineUnit(callLineUnit);
            callStatus.setWhoHangup(1);
            callStatus.setPhoneDataCache(phoneDataCache);
            DingDingService.dingDingSendMsgException("送呼异常","fsServerIp："+fsClientData.getFsConfig().getIp(),"当前号码："+phoneDataCache.getPhone(),"lineId："+phoneDataCache.getLineId(),"taskId："+phoneDataCache.getTaskId(),"taskName："+phoneDataCache.getTaskName(),"scriptId："+phoneDataCache.getScriptId(),"lineCode："+phoneDataCache.getLineCode());
            callRecordService.toMQCdr(phoneDataCache.getTaskId(),phoneDataCache.getTaskName(),phoneDataCache.getPhone(),phoneDataCache.getLineId(),phoneDataCache.getScriptId(),null,phoneDataCache.getSpeechCallId(),"呼叫号码出错(可能是因为网络抖动)",null,phoneDataCache.getLineCode(),"","",phoneDataCache.getScriptVersion(),callStatus,phoneDataCache.getScriptLongId(),"");
            return false;
        }
    }


    public void lineNoCity(CallPhoneProducerMaterial.PhoneDataCache phoneDataCache,FsClientData fsClientData,String cause){
        CallStatus callStatus = new CallStatus();
        callStatus.setCallStatus(-1);
        callStatus.setProvince(phoneDataCache.getProvince());
        callStatus.setCallId(phoneDataCache.getCity());
        callStatus.setFsIp(fsClientData.getFsConfig().getIp());
        callStatus.setAiCallIp(currentServerIpService.getIp());
        callStatus.setLineId(phoneDataCache.getLineId());
        callStatus.setLineCode(phoneDataCache.getLineCode());
        callStatus.setMerchantLineId(phoneDataCache.getMerchantLineId());
        callStatus.setMerchantLineCode(phoneDataCache.getMerchantLineCode());
        callStatus.setIsNewRecord("0");
        callStatus.setWhoHangup(1);
        callStatus.setPhoneDataCache(phoneDataCache);
        //DingDingService.dingDingSendMsg("送呼异常","fsServerIp："+fsClientData.getFsConfig().getIp(),"当前号码："+phoneDataCache.getPhone(),"lineId："+phoneDataCache.getLineId(),"taskId："+phoneDataCache.getTaskId(),"taskName："+phoneDataCache.getTaskName(),"scriptId："+phoneDataCache.getScriptId(),"lineCode："+phoneDataCache.getLineCode());
        callRecordService.toMQCdr(phoneDataCache.getTaskId(),phoneDataCache.getTaskName(),phoneDataCache.getPhone(),phoneDataCache.getLineId(),phoneDataCache.getScriptId(),null,phoneDataCache.getSpeechCallId(),"呼叫号码出错(可能是因为网络抖动)",null,phoneDataCache.getLineCode(),"",cause,phoneDataCache.getScriptVersion(),callStatus,phoneDataCache.getScriptLongId(),"");
    }

    public void noTenantLine(CallPhoneProducerMaterial.PhoneDataCache phoneDataCache,FsClientData fsClientData,String cause){
        CallStatus callStatus = new CallStatus();
        callStatus.setCallStatus(-1);
        callStatus.setProvince(phoneDataCache.getProvince());
        callStatus.setCallId(phoneDataCache.getCity());
        callStatus.setFsIp(fsClientData.getFsConfig().getIp());
        callStatus.setAiCallIp(currentServerIpService.getIp());
        callStatus.setLineId(phoneDataCache.getLineId());
        callStatus.setLineCode(phoneDataCache.getLineCode());
        callStatus.setMerchantLineId(phoneDataCache.getMerchantLineId());
        callStatus.setMerchantLineCode(phoneDataCache.getMerchantLineCode());
        callStatus.setIsNewRecord("0");
        callStatus.setWhoHangup(1);
        callStatus.setPhoneDataCache(phoneDataCache);
        //DingDingService.dingDingSendMsg("送呼异常","fsServerIp："+fsClientData.getFsConfig().getIp(),"当前号码："+phoneDataCache.getPhone(),"lineId："+phoneDataCache.getLineId(),"taskId："+phoneDataCache.getTaskId(),"taskName："+phoneDataCache.getTaskName(),"scriptId："+phoneDataCache.getScriptId(),"lineCode："+phoneDataCache.getLineCode());
        callRecordService.toMQCdr(phoneDataCache.getTaskId(),phoneDataCache.getTaskName(),phoneDataCache.getPhone(),phoneDataCache.getLineId(),phoneDataCache.getScriptId(),null,phoneDataCache.getSpeechCallId(),"呼叫号码出错(可能是因为网络抖动)",null,phoneDataCache.getLineCode(),"",cause,phoneDataCache.getScriptVersion(),callStatus,phoneDataCache.getScriptLongId(),"");
    }

    /*
    public void call(String phone){
        if (FsEslClient.getClient().canSend(this)) {//检查网络抖动
            String callChannelUniqueId = getUUID();
            String args = "{origination_uuid=" + callChannelUniqueId + ",origination_caller_id_number=" + 777777 + "}" + "user/" + phone + " 5558 XML default";
            String jobApiId = FsEslClient.getClient().sendAsyncApiCommand("originate", args);
            System.out.println(jobApiId);
        }
    }
     */
    private String getUUID(FsEslClient fsClient){
        List<String> uuids = null;
        EslMessage eslMessage = null;
        try{
            eslMessage = fsClient.sendSyncApiCommand("create_uuid","");
            uuids = eslMessage.getBodyLines();
        }catch (Exception e){
            try{
                eslMessage = fsClient.sendSyncApiCommand("create_uuid","");
                uuids = eslMessage.getBodyLines();
            }catch (Exception e1){
                e1.printStackTrace();
            }
        }

        if(uuids != null && uuids.size() >=1 && StringUtils.isNotEmpty(uuids.get(0))){
            return uuids.get(0);
        }else{
            return "lll"+ UUID.randomUUID().toString().replace("-","");
        }

    }

    public String genCallId(CallPhoneProducerMaterial.PhoneDataCache phoneDataCache) {
        String callId = getCallIdBySnow();
        if(StringUtils.isNotEmpty(phoneDataCache.getCallTeamHandleType()) && phoneDataCache.getCallTeamHandleType().equals("take_over")){
            callId = callId + "_take_over";
        }else if(StringUtils.isNotEmpty(phoneDataCache.getCallTeamHandleType()) && phoneDataCache.getCallTeamHandleType().equals("listen_in")){
            callId = callId + "_listen_in";
        }
        callId = "engine_"+callId;
        return callId;
    }

    private String getCallIdBySnow(){
        return aiCallEngineDialogIdFlag+"_"+String.valueOf(SnowFlakeUtil.getSnowFlakeIdTwo());
    }

    private String getUUIDTest(FsEslClient fsClient){

        //return "L-"+ UUID.randomUUID().toString().replace("-","");
        return "L-" + getCallIdBySnow();
    }

    public static void main22(String[] args) {
        String uniqueId = "1";
        String s =  UUID.nameUUIDFromBytes(uniqueId.getBytes()).toString();
    }


    public void pushTestDataToMq(Integer num){
        for(int i =0 ; i < num ; i++){
            CallPhoneProducerMaterialTemp temp = new CallPhoneProducerMaterialTemp();
            temp.setDate(LocalDate.now());
            temp.setMerchantLineCode("XLEEBF0592C072");
            temp.setMerchantLineId("95");
            temp.setScriptId("46e6fbb4-b33b-41dc-8462-281a84f38277");
            temp.setScriptLongId(827L);
            temp.setScriptVersion(11);
            temp.setTaskId("550");
            temp.setTaskName("0809-测试任务");
            temp.setData(Lists.newArrayList());
            for(int j = 0 ; j < 10 ; j++){
                PhoneDataTemp tempPhoneData = new PhoneDataTemp();
                tempPhoneData.setPhone("H1_CDD520B0DCD2129FD98987C04F82F2B"+j);
                tempPhoneData.setPlainPhone("1552555256"+j);
                tempPhoneData.setSpeechCallId("05f99b8a-1b42-4c26-8bd9-093419cae4a5");
                tempPhoneData.setOperator("联通");
                tempPhoneData.setCityCode("140200");
                tempPhoneData.setSendCallErrorRetryTimes(0);
                tempPhoneData.setHistoryMatchSupplyNumbers(null);
                temp.getData().add(tempPhoneData);
            }

            try {
                //这里是获取队列的一条消息
                //参数二：表示此时即使消费者已经收到消息了，RabbitMQ 也不会立马将消息移除，而是等待消费者显式的回复确认信号后，才会将消息删除
                String pushString = JSON.toJSONString(temp);
                ChannelManagerService.getOneChannel().basicPublish(CALL_PHONE_EXCHANGE_TEST,CALL_PHONE_ROUTING_TEST, new AMQP.BasicProperties().builder().deliveryMode(2).build(),pushString.getBytes(StandardCharsets.UTF_8));

            }catch (Exception e){
                e.printStackTrace();
            }

        }
        return;
    }

    public void noLine(Integer num){
        for(int i =0 ; i < num ; i++){
            CallPhoneProducerMaterialTemp temp = new CallPhoneProducerMaterialTemp();
            temp.setDate(LocalDate.now());
            temp.setMerchantLineCode("XLEEBF0592C072");
            temp.setMerchantLineId("95");
            temp.setScriptId("46e6fbb4-b33b-41dc-8462-281a84f38277");
            temp.setScriptLongId(827L);
            temp.setScriptVersion(11);
            temp.setTaskId("550");
            temp.setTaskName("0809-测试任务");
            temp.setData(Lists.newArrayList());
            for(int j = 0 ; j < 1 ; j++){
                PhoneDataTemp tempPhoneData = new PhoneDataTemp();
                tempPhoneData.setPhone("H1_CDD520B0DCD2129FD98987C04F82F2B"+j);
                tempPhoneData.setPlainPhone("1552555256"+j);
                tempPhoneData.setSpeechCallId("05f99b8a-1b42-4c26-8bd9-093419cae4a5");
                tempPhoneData.setOperator("联通");
                tempPhoneData.setCityCode("140200");
                tempPhoneData.setSendCallErrorRetryTimes(0);
                tempPhoneData.setHistoryMatchSupplyNumbers(null);
                temp.getData().add(tempPhoneData);
            }

            try {
                //这里是获取队列的一条消息
                //参数二：表示此时即使消费者已经收到消息了，RabbitMQ 也不会立马将消息移除，而是等待消费者显式的回复确认信号后，才会将消息删除
                String pushString = JSON.toJSONString(temp);
                ChannelManagerService.getOneChannel().basicPublish(CALL_PHONE_LINE_LACK_SINGLE_EXCHANGE,CALL_PHONE_LINE_LACK_SINGLE_ROUTING, new AMQP.BasicProperties().builder().deliveryMode(2).build(),pushString.getBytes(StandardCharsets.UTF_8));

            }catch (Exception e){
                e.printStackTrace();
            }
            return;
        }
    }

    public void noUuid(Integer num){
        for(int i =0 ; i < num ; i++){
            CallPhoneProducerMaterialTemp temp = new CallPhoneProducerMaterialTemp();
            temp.setDate(LocalDate.now());
            temp.setMerchantLineCode("XLEEBF0592C072");
            temp.setMerchantLineId("95");
            temp.setScriptId("46e6fbb4-b33b-41dc-8462-281a84f38277");
            temp.setScriptLongId(827L);
            temp.setScriptVersion(11);
            temp.setTaskId("550");
            temp.setTaskName("0809-测试任务");
            temp.setData(Lists.newArrayList());
            for(int j = 0 ; j < 1 ; j++){
                PhoneDataTemp tempPhoneData = new PhoneDataTemp();
                tempPhoneData.setPhone("H1_CDD520B0DCD2129FD98987C04F82F2B"+j);
                tempPhoneData.setPlainPhone("1552555256"+j);
                tempPhoneData.setSpeechCallId("05f99b8a-1b42-4c26-8bd9-093419cae4a5");
                tempPhoneData.setOperator("联通");
                tempPhoneData.setCityCode("140200");
                tempPhoneData.setSendCallErrorRetryTimes(0);
                tempPhoneData.setHistoryMatchSupplyNumbers(null);
                temp.getData().add(tempPhoneData);
            }

            try {
                //这里是获取队列的一条消息
                //参数二：表示此时即使消费者已经收到消息了，RabbitMQ 也不会立马将消息移除，而是等待消费者显式的回复确认信号后，才会将消息删除
                String pushString = JSON.toJSONString(temp);
                log.info("");
                ChannelManagerService.getOneChannel().basicPublish(CALL_PHONE_NO_UUID_EXCHANGE,CALL_PHONE_NO_UUID_ROUTING, new AMQP.BasicProperties().builder().deliveryMode(2).build(),pushString.getBytes(StandardCharsets.UTF_8));

            }catch (Exception e){
                e.printStackTrace();
            }
            return;
        }
    }


    private String getUUIDPhoneMonitor(FsEslClient fsClient){
        return "M-"+ UUID.randomUUID().toString().replace("-","");
    }

    private String getUUIDPhoneMonitorBySnow(FsEslClient fsClient){
        return "M-"+ getCallIdBySnow();
    }


    /**
     * 电话告警
     * @param phoneDataCache
     * @param fsClientData
     * @param callLineUnit
     * @return
     */
    public Boolean phoneMonitor(CallPhoneProducerMaterial.PhoneDataCache phoneDataCache, FsClientData fsClientData, CallLineUnit callLineUnit,String ttsContent){
        if (fsClientData.getFsClient().canSend(this,fsClientData)) {//检查网络抖动
            String callChannelUniqueId = getUUIDPhoneMonitorBySnow(fsClientData.getFsClient());
            String args = "{origination_uuid="+callChannelUniqueId+",origination_caller_id_number="+callLineUnit.getMasterCallNumber()+",origination_caller_id_name="+callLineUnit.getMasterCallNumber()+",originate_timeout=120,ignore_early_media=true,speech_call_id="+phoneDataCache.getSpeechCallId()+"}"+"sofia/external/"+callLineUnit.getPrefix()+phoneDataCache.getPlainPhone()+"@"+callLineUnit.getRegisterIp()+":"+callLineUnit.getRegisterPort()+" 50005 XML default";
            log.info("===>originate："+args);
            String jobApiId;
            try{
                jobApiId = fsClientData.getFsClient().sendAsyncApiCommand("originate", args);
            }catch (Exception e){
                try{
                    jobApiId = fsClientData.getFsClient().sendAsyncApiCommand("originate", args);
                }catch (Exception e1){
                    jobApiId = fsClientData.getFsClient().sendAsyncApiCommand("originate", args);
                }
            }
            log.info("===>jobApiId："+jobApiId);
            ScriptStatus status = new ScriptStatus();
            status.setTrainPhone(true);//是话术训练
            status.setScriptId(phoneDataCache.getScriptId());
            status.setVersion(phoneDataCache.getScriptVersion());
            status.setScriptLongId(phoneDataCache.getScriptLongId());
            status.setUserContent("");
            status.setCallId(callChannelUniqueId);
            status.setPhoneDataCache(phoneDataCache);
            if(status.getCallStatus()==null){
                status.setCallStatus(new CallStatus());
                status.getCallStatus().setCallOutTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                status.getCallStatus().setCallId(callChannelUniqueId);
            }
            status.getCallStatus().setFsIp(fsClientData.getFsConfig().getIp());
            status.getCallStatus().setAiCallIp(currentServerIpService.getIp());
            status.getCallStatus().setProvince(phoneDataCache.getProvince());
            status.getCallStatus().setCity(phoneDataCache.getCity());
            status.getCallStatus().setLineId(phoneDataCache.getLineId());
            status.getCallStatus().setLineCode(phoneDataCache.getLineCode());
            status.getCallStatus().setMerchantLineId(phoneDataCache.getMerchantLineId());
            status.getCallStatus().setMerchantLineCode(phoneDataCache.getMerchantLineCode());
            status.getCallStatus().setCallLineUnit(callLineUnit);
            status.getCallStatus().setUserFullAnswerContent(new StringBuilder(ttsContent));
            String jsonString = JSONObject.toJSONString(status, SerializerFeature.WriteClassName);
            simpleRedisService.setValueWithExpire(ApplicationConstants.CALL_USER_STATUS+callChannelUniqueId, jsonString,ApplicationConstants.CALL_USER_STATUS_EXPIRE);
            return true;
        }else{
            CallStatus callStatus = new CallStatus();
            callStatus.setCallStatus(-1);
            callStatus.setProvince(phoneDataCache.getProvince());
            callStatus.setCity(phoneDataCache.getCity());
            callStatus.setFsIp(fsClientData.getFsConfig().getIp());
            callStatus.setAiCallIp(currentServerIpService.getIp());
            callStatus.setLineId(phoneDataCache.getLineId());
            callStatus.setLineCode(phoneDataCache.getLineCode());
            callStatus.setMerchantLineId(phoneDataCache.getMerchantLineId());
            callStatus.setMerchantLineCode(phoneDataCache.getMerchantLineCode());
            callStatus.setCallLineUnit(callLineUnit);
            callStatus.setWhoHangup(1);
            DingDingService.dingDingSendMsgException("电话告警异常，请注意","fsServerIp："+fsClientData.getFsConfig().getIp(),"当前号码："+phoneDataCache.getPhone(),"lineId："+phoneDataCache.getLineId(),"taskId："+phoneDataCache.getTaskId(),"taskName："+phoneDataCache.getTaskName(),"scriptId："+phoneDataCache.getScriptId(),"lineCode："+phoneDataCache.getLineCode());
            return false;
        }
    }



}

