package com.raipeng.aiengine.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.raipeng.aicall.mq.producter.material.CallPhoneProducerMaterial;
import com.raipeng.aicall.scriptsystem.CallStatus;
import com.raipeng.aicall.scriptsystem.ScriptStatus;
import com.raipeng.aiengine.constant.ApplicationConstants;
import com.raipeng.aiengine.feign.FeignClientUtils;
import com.raipeng.aiengine.feign.IAISendFeign;
import com.raipeng.aiengine.mq.producter.SendPhoneQueue;
import com.raipeng.aiengine.service.els.FsClientData;
import com.raipeng.aiengine.utils.SnowFlakeUtil;
import com.raipeng.common.entity.linemanager.CallLineUnit;
import com.raipeng.common.entity.prioritycall.CallPhoneUnit;
import com.raipeng.common.response.AiEngineSendRequest;
import com.raipeng.common.response.AiEngineSendResponse;
import com.raipeng.aiengine.controller.response.AiEngineDetailedConcurrentResponse;
import com.raipeng.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.Pipeline;
import redis.clients.jedis.params.SetParams;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SendPhoneService {

    @Autowired
    private SendPhoneQueue sendPhoneQueue;

    @Autowired
    private CurrentServerIpService currentServerIpService;

    @Autowired
    private EngineConcurrentStatistics engineConcurrentStatistics;

    @Value("${ai.call.engine.dialog.id.flag}")
    private Long aiCallEngineDialogIdFlag;

    @Autowired
    private CallService callService;

    @Resource
    private JedisPool jedisPool;

    @Autowired
    private FeignClientManager feignClientManager;
/*
    private final static ExecutorService sendPhonePool = new ThreadPoolExecutor(10, 100,
            300L, TimeUnit.SECONDS,
            new SynchronousQueue<Runnable>());

 */

    private static ExecutorService sendPhonePool =  new ThreadPoolExecutor(20, 100,
            100L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(200000));


    public void backPhoneFromQueue(){
        sendPhoneQueue.backPhoneFromQueue();
    }


    public void backPhone(List<CallPhoneUnit> callPhoneUnitList) {
        //判断当前engine是否可用，threshold是否<=0,如果都小于0
        //数据回传缓存
        for (Map.Entry<String, IAISendFeign> stringIAISendFeignEntry : feignClientManager.aiSendFeignMap.entrySet()) {
            IAISendFeign feign =  stringIAISendFeignEntry.getValue();
            try{
                int tryTimes = 3;
                while(tryTimes > 0) {
                    try{
                        feign.receiveBackToRedisData(callPhoneUnitList);
                        log.info("回传redis"+JSON.toJSONString(callPhoneUnitList));
                        --tryTimes;
                        break;
                    }catch (Exception e){
                        e.printStackTrace();
                    }
                }
                if(tryTimes <= 0){
                    //移除改客户端
                    Iterator<Map.Entry<String,IAISendFeign>> iterator = feignClientManager.aiSendFeignMap.entrySet().iterator();
                    while(iterator.hasNext()){
                        IAISendFeign feign1 = iterator.next().getValue();
                        if(feign1.equals(feign)){
                            iterator.remove();
                        }
                    }
                }
                break;
            }catch (Exception e){
                e.printStackTrace();
            }
        }

    }

    public AiEngineSendResponse receivePhone(AiEngineSendRequest request) {
        List<CallPhoneUnit> callPhoneUnitList = request.getCallPhoneUnits();
        //初始化
        if(!feignClientManager.aiSendFeignMap.containsKey(request.getAiSendIp()+":"+request.getAiSendPort())){
            feignClientManager.aiSendFeignMap.put(request.getAiSendIp()+":"+request.getAiSendPort(),
                    FeignClientUtils.build(request.getAiSendIp()+":"+request.getAiSendPort(), IAISendFeign.class));
        }

        //1、判断当前engine是否属于停止状态，如果始于停止状态，则返回不可用，返回号码
        AiEngineSendResponse response = new AiEngineSendResponse();
        if (currentServerIpService.getPullPhoneSwitch().equals("stop")) {
            response.setEnable(false);
            response.setCallPhoneUnits(callPhoneUnitList);
            response.setAvailableConcurrent(0);
            response.setCurrentConcurrent(0);
            response.setMaxConcurrent(0);
            log.info("engine被停止了"+JSON.toJSONString(callPhoneUnitList));
            return response;
        }
        //2、非暂停状态，需要判断是否有可用并发，如果没可用并发，则返回号码，且返回当前并发，最大并发，剩余并发
        if (engineConcurrentStatistics.getAvailableConcurrent() <= 0) {
            response.setEnable(true);
            response.setCallPhoneUnits(callPhoneUnitList);
            response.setAvailableConcurrent(0);
            response.setCurrentConcurrent(engineConcurrentStatistics.getCurrentConcurrent());
            response.setMaxConcurrent(engineConcurrentStatistics.getMaxConcurrent());
            log.info("没有可用并发了"+JSON.toJSONString(callPhoneUnitList));
            return response;
        }
        //3、 本地接收缓存，如果本地接收缓存超过一定的阈值，说明该engine消费能力已经不足
        String result = sendPhoneQueue.addData(callPhoneUnitList);
        if (result.equals("full")) {//缓存已满
            response.setEnable(true);
            response.setCallPhoneUnits(callPhoneUnitList);
            response.setAvailableConcurrent(0);
            response.setCurrentConcurrent(engineConcurrentStatistics.getCurrentConcurrent());
            response.setMaxConcurrent(engineConcurrentStatistics.getMaxConcurrent());
            log.info("缓存已满"+JSON.toJSONString(callPhoneUnitList));
            return response;
        }
        //4、非暂停状态，消费号码，且返回当前并发，最大并发，剩余并发
        response.setEnable(true);
        response.setCallPhoneUnits(null);
        response.setAvailableConcurrent(engineConcurrentStatistics.getAvailableConcurrent());
        response.setCurrentConcurrent(engineConcurrentStatistics.getCurrentConcurrent());
        response.setMaxConcurrent(engineConcurrentStatistics.getMaxConcurrent());
        //log.info("正常消费"+JSON.toJSONString(callPhoneUnitList));
        return response;
    }

    //获取当前并发
    public AiEngineSendResponse obtainConcurrent() {
        //1、判断当前engine是否属于停止状态，如果始于停止状态，则返回不可用，返回号码
        AiEngineSendResponse response = new AiEngineSendResponse();
        if (currentServerIpService.getPullPhoneSwitch().equals("stop")) {
            response.setEnable(false);
            response.setCallPhoneUnits(null);
            response.setAvailableConcurrent(0);
            response.setCurrentConcurrent(0);
            response.setMaxConcurrent(0);
            return response;
        }
        //2、非暂停状态，需要判断是否有可用并发，如果没可用并发，则返回号码，且返回当前并发，最大并发，剩余并发
        if (engineConcurrentStatistics.getAvailableConcurrent() <= 0) {
            response.setEnable(true);
            response.setCallPhoneUnits(null);
            response.setAvailableConcurrent(0);
            response.setCurrentConcurrent(engineConcurrentStatistics.getCurrentConcurrent());
            response.setMaxConcurrent(engineConcurrentStatistics.getMaxConcurrent());
            return response;
        }
        //3、 本地接收缓存，如果本地接收缓存超过一定的阈值，说明该engine消费能力已经不足
        String result = sendPhoneQueue.isFull();
        if (result.equals("full")) {//缓存已满
            response.setEnable(true);
            response.setCallPhoneUnits(null);
            response.setAvailableConcurrent(0);
            response.setCurrentConcurrent(engineConcurrentStatistics.getCurrentConcurrent());
            response.setMaxConcurrent(engineConcurrentStatistics.getMaxConcurrent());
            return response;
        }
        //4、非暂停状态，消费号码，且返回当前并发，最大并发，剩余并发
        response.setEnable(true);
        response.setCallPhoneUnits(null);
        response.setAvailableConcurrent(engineConcurrentStatistics.getAvailableConcurrent());
        response.setCurrentConcurrent(engineConcurrentStatistics.getCurrentConcurrent());
        response.setMaxConcurrent(engineConcurrentStatistics.getMaxConcurrent());
        return response;
    }

    /**
     * 获取详细并发信息（包含每个FS的并发信息）
     * @return 详细并发响应
     */
    public AiEngineDetailedConcurrentResponse obtainDetailedConcurrent() {
        // 先获取基础并发信息
        AiEngineSendResponse basicResponse = obtainConcurrent();
        AiEngineDetailedConcurrentResponse detailedResponse = AiEngineDetailedConcurrentResponse.fromBasicResponse(basicResponse);

        // 获取FS详细信息
        detailedResponse.setFsDetail(engineConcurrentStatistics.getFsDetail());
        return detailedResponse;
    }

    //生成call_id
    private String getCallIdBySnow(){
        return aiCallEngineDialogIdFlag+"_"+SnowFlakeUtil.getSnowFlakeIdTwo();
    }

    //送呼
    public void sendPhone(List<CallPhoneUnit> callPhoneUnitList,FsClientData fsClientData) {

        //判断当前engine是否可用，threshold是否<=0,如果都小于0
        boolean continueGo = true;
        if (currentServerIpService.getPullPhoneSwitch().equals("stop")
            || engineConcurrentStatistics.getAvailableConcurrent() <= 0){
            //数据回传缓存
            for (Map.Entry<String, IAISendFeign> stringIAISendFeignEntry : feignClientManager.aiSendFeignMap.entrySet()) {
                IAISendFeign feign =  stringIAISendFeignEntry.getValue();
                try{
                    int tryTimes = 3;
                    while(tryTimes > 0) {
                        try{
                            feign.receiveBackToRedisData(callPhoneUnitList);
                            log.info("回传redis"+JSON.toJSONString(callPhoneUnitList));
                            --tryTimes;
                            continueGo = false;
                            break;
                        }catch (Exception e){
                            e.printStackTrace();
                        }

                    }
                    if(tryTimes <= 0){
                        //移除改客户端
                       Iterator<Map.Entry<String,IAISendFeign>> iterator = feignClientManager.aiSendFeignMap.entrySet().iterator();
                       while(iterator.hasNext()){
                           IAISendFeign feign1 = iterator.next().getValue();
                           if(feign1.equals(feign)){
                               iterator.remove();
                           }
                       }
                    }else{
                        break;
                    }
                }catch (Exception e){
                    e.printStackTrace();
                }
            }

        }
        if(!continueGo){
            return;
        }

        List<Future> futures = Lists.newArrayList();
        //找到一个可用的fs
        /*
        Set<Map.Entry<String, FsClientData>> set = CallEngineService.fsMulClient.entrySet();
        List<FsClientData> fsList = set.stream().map(Map.Entry::getValue).collect(Collectors.toList());

        List<Double> rates = fsList.stream().map(p -> Double.parseDouble(String.valueOf(p.getRemainCount()))).collect(Collectors.toList());
        int result = ActivityService.lottery(rates);
        FsClientData fsClientData = fsList.get(result);
         */
        //log.info("可用fs"+JSON.toJSONString(fsClientData));
        //批量送fs
        List<CallEngineService.CallEngine.SendPhoneCallable> sendThreads = Lists.newArrayList();
        for (CallPhoneUnit unit : callPhoneUnitList) {
            CallPhoneProducerMaterial.PhoneDataCache phoneDataCache = getPhoneDataCache(unit);
            //核减后置-实际打出去之后，才核减
            fsClientData.subRemainCount(1);
            sendThreads.add(new CallEngineService.CallEngine.SendPhoneCallable(callService, phoneDataCache, fsClientData, unit.getCallLineUnit(),genCallId(phoneDataCache)));
        }
        //log.info("批量送fs"+JSON.toJSONString(sendThreads));
        //统一入缓存
        try(Jedis jedis = jedisPool.getResource()){
            Pipeline pipeline = jedis.pipelined();
            SetParams setParams = new SetParams();
            setParams.ex(2700);
            genCallStatus(sendThreads,pipeline,setParams);
            pipeline.sync();//同步执行
        }catch (Exception e){
            e.printStackTrace();
        }

        for (CallEngineService.CallEngine.SendPhoneCallable sendPhoneCallable : sendThreads) {
            futures.add(sendPhonePool.submit(sendPhoneCallable));
        }

        for (Future<String> future : futures) {
            try {
                future.get(currentServerIpService.getSendNotFullTimeout(), TimeUnit.MILLISECONDS);//设置阻塞的超时时间-200ml
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            } catch (ExecutionException e) {
                throw new RuntimeException(e);
            } catch (TimeoutException e) {
                throw new RuntimeException(e);
            }
        }
        //log.info("批量送fs结束"+JSON.toJSONString(sendThreads));
        futures.clear();//执行完成之后，清空
    }

    private String genCallId(CallPhoneProducerMaterial.PhoneDataCache phoneDataCache) {
        String callId = getCallIdBySnow();
        if(StringUtils.isNotEmpty(phoneDataCache.getCallTeamHandleType()) && phoneDataCache.getCallTeamHandleType().equals("take_over")){
            callId = callId + "_take_over";
        }else if(StringUtils.isNotEmpty(phoneDataCache.getCallTeamHandleType()) && phoneDataCache.getCallTeamHandleType().equals("listen_in")){
            callId = callId + "_listen_in";
        }
        callId = "engine_"+callId;
        return callId;
    }

    private void genCallStatus(List<CallEngineService.CallEngine.SendPhoneCallable> sendPhoneCallableList,Pipeline pipeline,SetParams setParams){
        for (CallEngineService.CallEngine.SendPhoneCallable sendPhoneCallable : sendPhoneCallableList) {
            CallPhoneProducerMaterial.PhoneDataCache phoneDataCache = sendPhoneCallable.getPhoneDataCache();
            FsClientData fsClientData = sendPhoneCallable.getFsClientData();
            CallLineUnit callLineUnit = sendPhoneCallable.getCallLineUnit();
            ScriptStatus status = new ScriptStatus();
            status.setTrainPhone(false);
            status.setScriptId(phoneDataCache.getScriptId());
            status.setVersion(phoneDataCache.getScriptVersion());
            status.setScriptLongId(phoneDataCache.getScriptLongId());
            status.setUserContent("");
            status.setCallId(sendPhoneCallable.getCallId());
            status.setPhoneDataCache(phoneDataCache);
            if(status.getCallStatus()==null){
                status.setCallStatus(new CallStatus());
                status.getCallStatus().setCallOutTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                status.getCallStatus().setCallId(sendPhoneCallable.getCallId());
            }
            status.getCallStatus().setFsIp(fsClientData.getFsConfig().getIp());
            status.getCallStatus().setAiCallIp(currentServerIpService.getIp());
            status.getCallStatus().setProvince(phoneDataCache.getProvince());
            status.getCallStatus().setCity(phoneDataCache.getCity());
            status.getCallStatus().setLineId(phoneDataCache.getLineId());
            status.getCallStatus().setLineCode(phoneDataCache.getLineCode());
            status.getCallStatus().setMerchantLineId(phoneDataCache.getMerchantLineId());
            status.getCallStatus().setMerchantLineCode(phoneDataCache.getMerchantLineCode());
            status.getCallStatus().setCallLineUnit(callLineUnit);
            status.getCallStatus().setPhoneDataCache(phoneDataCache);
            String jsonString = JSONObject.toJSONString(status, SerializerFeature.WriteClassName);
            pipeline.set(ApplicationConstants.CALL_USER_STATUS+sendPhoneCallable.getCallId(),jsonString,setParams);
        }

    }

    private CallPhoneProducerMaterial.PhoneDataCache getPhoneDataCache(CallPhoneUnit unit) {
        CallPhoneProducerMaterial data = new CallPhoneProducerMaterial();
        data.setDate(LocalDate.now());
        data.setTaskId(unit.getTaskId());
        data.setTaskName(unit.getTaskName());
        data.setScriptId(unit.getScriptId());
        data.setScriptVersion(unit.getScriptVersion());
        data.setScriptLongId(unit.getScriptLongId());
        data.setGroupId(unit.getGroupId());
        data.setMerchantLineCode(unit.getMerchantLineCode());
        data.setMerchantLineId(unit.getMerchantLineId());
        data.setCallTeamHandleType(unit.getCallTeamHandleType());

        List<CallPhoneProducerMaterial.PhoneData> phoneDataList = Lists.newArrayList();
        CallPhoneProducerMaterial.PhoneData phoneData = new CallPhoneProducerMaterial.PhoneData();
        phoneDataList.add(phoneData);
        data.setData(phoneDataList);
        phoneData.setSpeechCallId(unit.getSpeechCallId());
        phoneData.setPhone(unit.getPhone());
        phoneData.setPlainPhone(unit.getPlainPhone());
        phoneData.setProvince(unit.getProvince());
        phoneData.setCity(unit.getCityCode());
        phoneData.setCityCode(unit.getCityCode());
        phoneData.setOperator(unit.getOperator());
        phoneData.setSendCallErrorRetryTimes(0);
        phoneData.setHistoryMatchSupplyNumbers(null);
        phoneData.setCallingHistory(unit.getCallingHistory());
        phoneData.setDialingHistory(unit.getDialingHistory());
        phoneData.setProvinceCode(unit.getProvinceCode());
        phoneData.setFirstCallOrReCall(unit.getFirstCallOrReCall());
        phoneData.setTargetTime(unit.getTargetTime());
        phoneData.setBlackSupplyLineNumbers(unit.getBlackSupplyLineNumbers());
        phoneData.setLightPhoneIds(unit.getLightPhoneIds());
        CallPhoneProducerMaterial.PhoneDataCache phoneDataCache = CallPhoneProducerMaterial.PhoneDataCache.getPhoneDataCache(data, phoneData);
        phoneDataCache.setLineCode(unit.getCallLineUnit().getSupplyLineName());
        phoneDataCache.setLineId(unit.getCallLineUnit().getSupplyLineNumber());
        phoneDataCache.setSendCallErrorRetryTimes(phoneDataCache.getSendCallErrorRetryTimes() + 1);
        if (phoneDataCache.getHistoryMatchSupplyNumbers() == null) {
            phoneDataCache.setHistoryMatchSupplyNumbers(Lists.newArrayList());
        }
        phoneDataCache.getHistoryMatchSupplyNumbers().add(unit.getCallLineUnit().getSupplyLineNumber());
        phoneDataCache.setExtParams(unit.getExtParams());
        return phoneDataCache;
    }

}
