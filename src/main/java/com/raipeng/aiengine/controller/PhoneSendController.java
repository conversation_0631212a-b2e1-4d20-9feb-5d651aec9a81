package com.raipeng.aiengine.controller;

import com.raipeng.aiengine.service.SendPhoneService;
import com.raipeng.common.response.AiEngineSendRequest;
import com.raipeng.common.response.AiEngineSendResponse;
import com.raipeng.aiengine.controller.response.AiEngineDetailedConcurrentResponse;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/engine")
@Api(value = "/engine", tags = {"engine监控"})
@RefreshScope
@Slf4j
public class PhoneSendController {

    @Autowired
    private SendPhoneService sendPhoneService;

    /**
     * 送号
     * @return
     */
    @PostMapping("/send-phone")
    public AiEngineSendResponse sendPhone(@RequestBody AiEngineSendRequest request) {
        log.info("===>送号"+request.getCallPhoneUnits().size());
        return sendPhoneService.receivePhone(request);
    }

    @GetMapping("/back-phone")
    public String backPhone() {
        log.info("===>backPhone");
        sendPhoneService.backPhoneFromQueue();
        return "success";
    }

    /**
     * 获取当前并发
     * @return
     */
    @PostMapping("/obtain-concurrent")
    public AiEngineSendResponse obtainConcurrent() {
        //log.info("===>获取当前并发");
        return sendPhoneService.obtainConcurrent();
    }

    /**
     * 获取详细并发信息（包含每个FS的并发信息）
     * @return 详细并发响应，包含整体并发信息和每个FS服务器的详细信息
     */
    @PostMapping("/obtain-detailed-concurrent")
    public AiEngineDetailedConcurrentResponse obtainDetailedConcurrent() {
        log.info("===>获取详细并发信息");
        return sendPhoneService.obtainDetailedConcurrent();
    }

}
