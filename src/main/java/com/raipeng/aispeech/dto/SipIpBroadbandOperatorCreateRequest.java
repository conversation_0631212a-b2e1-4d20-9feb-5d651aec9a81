package com.raipeng.aispeech.dto;

import com.raipeng.aispeech.enums.BroadbandOperatorType;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 创建线路宽带商信息请求对象
 */
@Data
public class SipIpBroadbandOperatorCreateRequest {

    /**
     * 线路号码
     */
    @NotBlank(message = "线路号码不能为空")
    private String lineNumber;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 宽带网络商（接收中文名称，如：移动、电信、联通）
     */
    private String broadbandOperator;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 获取宽带运营商枚举类型
     *
     * @return 宽带运营商枚举，如果broadbandOperator为空则返回null
     */
    public BroadbandOperatorType getBroadbandOperatorType() {
        if (broadbandOperator == null || broadbandOperator.trim().isEmpty()) {
            return null;
        }
        return BroadbandOperatorType.fromName(broadbandOperator.trim());
    }
}
