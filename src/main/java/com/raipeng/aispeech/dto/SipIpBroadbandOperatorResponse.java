package com.raipeng.aispeech.dto;

import com.raipeng.aispeech.enums.BroadbandOperatorType;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 线路宽带商信息响应对象
 */
@Data
public class SipIpBroadbandOperatorResponse {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 线路号码
     */
    private String lineNumber;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * IP查询宽带商
     */
    private String operator;

    /**
     * 宽带网络商（返回中文名称给前端显示）
     */
    private String broadbandOperator;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * ISP信息
     */
    private String ispInfo;

    /**
     * 是否已查询IP接口
     */
    private Boolean ipQueried;

    /**
     * IP查询时间
     */
    private LocalDateTime ipQueryTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 设置宽带运营商（从枚举类型转换为中文名称）
     *
     * @param operatorType 宽带运营商枚举类型
     */
    public void setBroadbandOperatorFromType(BroadbandOperatorType operatorType) {
        if (operatorType != null) {
            this.broadbandOperator = operatorType.getName();
        } else {
            this.broadbandOperator = null;
        }
    }
}
