package com.raipeng.aispeech.service;

import com.raipeng.aispeech.config.HotConfig;
import com.raipeng.aispeech.context.AIContext;
import com.raipeng.aispeech.controller.response.AIOutboundTaskTemplateResponse;
import com.raipeng.aispeech.entity.HangUpSmsTriggerPojo;
import com.raipeng.aispeech.entity.ScriptSmsTriggerPojo;
import com.raipeng.aispeech.entity.VariableSmsPojo;
import com.raipeng.aispeech.model.AIOutboundTaskTemplate;
import com.raipeng.aispeech.model.AITenant;
import com.raipeng.aispeech.model.Admin;
import com.raipeng.aispeech.model.TenantSmsTemplate;
import com.raipeng.aispeech.model.callteam.CallTeam;
import com.raipeng.aispeech.repository.AIOutboundTaskTemplateRepository;
import com.raipeng.aispeech.repository.AITenantRepository;
import com.raipeng.aispeech.repository.AdminRepository;
import com.raipeng.aispeech.repository.CallTeamRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.raipeng.common.enums.AIOutboundTaskType.AI_AUTO;

@Slf4j
@Service
public class AIOutboundTaskTemplateService {
    @Autowired
    private AIOutboundTaskTemplateRepository aiOutboundTaskTemplateRepository;

    @Autowired
    private AITenantRepository aiTenantRepository;

    @Autowired
    private CallTeamRepository callTeamRepository;

    @Autowired
    private AdminRepository adminRepository;

    @Autowired
    private TenantSmsTemplateService tenantSmsTemplateService;

    @Autowired
    private HotConfig hotConfig;

    public List<AIOutboundTaskTemplate> findList(String name, String taskType) {
        List<AIOutboundTaskTemplate> res = aiOutboundTaskTemplateRepository.findListByName(name, AIContext.getGroupId(), taskType);
        return res.stream().peek(a -> {
            a.setStartWorkTimeList(Arrays.asList(a.getStartWorkTimes().split(",")));
            a.setEndWorkTimeList(Arrays.asList(a.getEndWorkTimes().split(",")));
        }).collect(Collectors.toList());
    }

    public List<AIOutboundTaskTemplate> operatorFindByName(String name, String taskType, String groupId) {
        String operatorGroupId = AIContext.getGroupId();
        if (operatorGroupId.startsWith("0_")) {
            List<AIOutboundTaskTemplate> res = aiOutboundTaskTemplateRepository.findListByName(name, groupId, taskType);
            return res.stream().peek(a -> {
                a.setStartWorkTimeList(Arrays.asList(a.getStartWorkTimes().split(",")));
                a.setEndWorkTimeList(Arrays.asList(a.getEndWorkTimes().split(",")));
            }).collect(Collectors.toList());
        } else {
            return new ArrayList<>();
        }
    }

    public List<AIOutboundTaskTemplate> findAiAutoList(String name) {
        List<AIOutboundTaskTemplate> res = aiOutboundTaskTemplateRepository.findAiAutoListByName(name, AIContext.getGroupId());
        return res.stream().peek(a -> {
            a.setStartWorkTimeList(Arrays.asList(a.getStartWorkTimes().split(",")));
            a.setEndWorkTimeList(Arrays.asList(a.getEndWorkTimes().split(",")));
        }).collect(Collectors.toList());
    }

    public List<AIOutboundTaskTemplate> findAiManualList(String name) {
        List<AIOutboundTaskTemplate> res = aiOutboundTaskTemplateRepository.findAiManualListByName(name, AIContext.getGroupId());
        return res.stream().peek(a -> {
            a.setStartWorkTimeList(Arrays.asList(a.getStartWorkTimes().split(",")));
            a.setEndWorkTimeList(Arrays.asList(a.getEndWorkTimes().split(",")));
        }).collect(Collectors.toList());
    }

    public AIOutboundTaskTemplate save(AIOutboundTaskTemplate aiOutboundTaskTemplate) {
        String groupId = AIContext.getGroupId();
        log.info("保存编辑模板 {} : {} ", groupId, aiOutboundTaskTemplate);
        if (AI_AUTO.equals(aiOutboundTaskTemplate.getTaskType())) {
            aiOutboundTaskTemplate.setCallTeamIds(null);
            aiOutboundTaskTemplate.setCallTeamPushType(null);
            aiOutboundTaskTemplate.setLineRatio(null);
            aiOutboundTaskTemplate.setVirtualSeatRatio(null);
        }

        aiOutboundTaskTemplate.setStartWorkTimes(String.join(",", aiOutboundTaskTemplate.getStartWorkTimeList()));
        aiOutboundTaskTemplate.setEndWorkTimes(String.join(",", aiOutboundTaskTemplate.getEndWorkTimeList()));
        if (groupId.startsWith("0_")) {
            groupId = aiOutboundTaskTemplate.getGroupId();
        }
        aiOutboundTaskTemplate.setGroupId(groupId);
        AITenant aiTenant = aiTenantRepository.findById(Long.valueOf(groupId.split("_")[1])).get();
        aiOutboundTaskTemplate.setTenantName(aiTenant.getTenantName());
        //话术短信触发和挂机触发找到使用的变量
        List<TenantSmsTemplate> smsTemplates = findSmsTemplateByScriptSmsHangUpSms(aiOutboundTaskTemplate.getScriptSms(), aiOutboundTaskTemplate.getHangUpSms());
        Map<String, VariableSmsPojo> usedVariables = findVariableBySmsTemplates(smsTemplates);
        if (!usedVariables.isEmpty()) {
            aiOutboundTaskTemplate.setVariableSms(new ArrayList<>(usedVariables.values()));
        } else {
            aiOutboundTaskTemplate.setVariableSms(null);
        }

        return aiOutboundTaskTemplateRepository.save(aiOutboundTaskTemplate);
    }

    public void deleteById(Long id) {
        Long trainTaskTemplateId = hotConfig.getTrainTaskTemplateId();
        if (trainTaskTemplateId.equals(id)) {
            throw new RuntimeException("此模板绑定了话术训练,请勿删除");
        }
        if (aiOutboundTaskTemplateRepository.findById(id).isPresent()) {
            aiOutboundTaskTemplateRepository.deleteById(id);
        }
    }

    public List<CallTeam> findAllCallTeamsByGroupId(String groupId) {
        return callTeamRepository.findCallTeamsByGroupId(groupId);
    }

    public String findTemplateByScript(String scriptStringId) {
        List<AIOutboundTaskTemplate> templates = aiOutboundTaskTemplateRepository.findAIOutboundTaskTemplatesByScriptStringId(scriptStringId);
        String res = "";
        if (templates != null && !templates.isEmpty()) {
            String groupId = templates.get(0).getGroupId();
            Admin account = adminRepository.findFirstByGroupIdAndIsTenantManager(groupId, true);
            if (account != null) {
                res = "[" + account.getAccount() + "]" + templates.get(0).getTemplateName() + "等" + templates.size() + "个外呼模板";
            }
            return res;
        }
        return res;
    }


    public Map<String, VariableSmsPojo> findVariableBySmsTemplates(List<TenantSmsTemplate> allSmsTemplate) {
        Map<String, VariableSmsPojo> variableName = new HashMap<>();
        if (!allSmsTemplate.isEmpty()) {
            allSmsTemplate.forEach(template -> {
                if (template.getVariableUsed() != null) {
                    template.getVariableUsed().forEach(variable -> {
                        variableName.put(variable.getVariableName(), variable);
                    });
                }
            });
        }
        return variableName;
    }

    public List<TenantSmsTemplate> findSmsTemplateByScriptSmsHangUpSms(List<ScriptSmsTriggerPojo> scriptSms,
                                                                       List<HangUpSmsTriggerPojo> hangUpSms) {
        List<Long> smsTemplateId = new ArrayList<>();
        if (scriptSms != null) {
            scriptSms.forEach(template -> {
                smsTemplateId.add(template.getSmsTemplateId());
            });
        }
        if (hangUpSms != null) {
            hangUpSms.forEach(template -> {
                smsTemplateId.add(template.getSmsTemplateId());
            });
        }
        return tenantSmsTemplateService.findSmsTemplatesByIdIn(smsTemplateId);
    }

    public List<AIOutboundTaskTemplate> findVolcanoTaskTemplates() {
        List<Admin> admins = adminRepository.findAdminsByTenantIdAndIsTenantManager(14L, true);
        List<String> groupIds = admins.stream().map(Admin::getGroupId).collect(Collectors.toList());
        return aiOutboundTaskTemplateRepository.findVolcanoTaskTemplates(groupIds);
    }

    public void updateTemplateStatus(AIOutboundTaskTemplate template) {
        String updateStatus = template.getTemplateStatus();

        Optional<AIOutboundTaskTemplate> templateById = aiOutboundTaskTemplateRepository.findById(template.getId());
        if (templateById.isPresent()) {
            AIOutboundTaskTemplate aiOutboundTaskTemplate = templateById.get();
            aiOutboundTaskTemplate.setTemplateStatus(updateStatus);
            aiOutboundTaskTemplateRepository.save(aiOutboundTaskTemplate);
            log.info("更新模板 {} {} 状态为 {}", aiOutboundTaskTemplate.getId(), aiOutboundTaskTemplate.getTemplateName(), updateStatus);
        }

    }


    public AIOutboundTaskTemplateResponse findTemplatePositionById(Long id) {
        AIOutboundTaskTemplateResponse aiOutboundTaskTemplateResponse = new AIOutboundTaskTemplateResponse();
        Optional<AIOutboundTaskTemplate> templateById = aiOutboundTaskTemplateRepository.findById(id);
        if (templateById.isPresent()) {
            AIOutboundTaskTemplate aiOutboundTaskTemplate = templateById.get();
            String groupId = aiOutboundTaskTemplate.getGroupId();
            Admin admin = adminRepository.findFirstById(Long.parseLong(groupId.split("_")[2]));
            BeanUtils.copyProperties(aiOutboundTaskTemplate, aiOutboundTaskTemplateResponse);
            aiOutboundTaskTemplateResponse.setStartWorkTimeList(Arrays.asList(aiOutboundTaskTemplateResponse.getStartWorkTimes().split(",")));
            aiOutboundTaskTemplateResponse.setEndWorkTimeList(Arrays.asList(aiOutboundTaskTemplateResponse.getEndWorkTimes().split(",")));
            aiOutboundTaskTemplateResponse.setAccount(admin.getAccount());
        }
        return aiOutboundTaskTemplateResponse;
    }

}
