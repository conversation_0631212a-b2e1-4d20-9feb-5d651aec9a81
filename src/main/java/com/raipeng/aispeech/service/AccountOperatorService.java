package com.raipeng.aispeech.service;


import com.raipeng.aispeech.context.AIContext;
import com.raipeng.aispeech.controller.request.AccountOperatorParam;
import com.raipeng.aispeech.controller.response.AccountOperatorResponse;
import com.raipeng.aispeech.exceptionadvice.exception.AdminCheckException;
import com.raipeng.aispeech.feign.FilterTenantBlackFeign;
import com.raipeng.aispeech.model.AIOutboundTask;
import com.raipeng.aispeech.model.TenantSecrets;
import com.raipeng.aispeech.repository.AIOutboundTaskRepository;
import com.raipeng.aispeech.repository.AdminRepository;
import com.raipeng.aispeech.repository.AdminRoleRepository;
import com.raipeng.aispeech.repository.TenantSecretsRepository;
import com.raipeng.common.constant.AdminConstants;
import com.raipeng.common.enums.AccountOperatorType;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.List;

import static com.raipeng.common.constant.AIRedisCacheKeyConstant.*;

@Service
public class AccountOperatorService {
    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private AdminRepository adminRepository;

    @Autowired
    private AIOutboundTaskRepository aiOutboundTaskRepository;

    @Autowired
    private TenantSecretsRepository tenantSecretsRepository;

    @Autowired
    private AdminRoleRepository adminRoleRepository;

    @Autowired
    private FilterTenantBlackFeign filterTenantBlackFeign;

    @Transactional
    public void updateAccountOperatorParam(AccountOperatorParam param) {
        Long userId = AIContext.getUserId();
        adminRepository.findById(userId).flatMap(user -> adminRoleRepository.findById(user.getRoleId())).ifPresent(adminRole -> {
            if ("超级管理员".equals(adminRole.getRoleName())) {
                Long accountId = param.getAccountId();
                adminRepository.findById(accountId).ifPresent(admin -> {
                    List<AIOutboundTask> tasks = aiOutboundTaskRepository.findRunningTaskByGroupId(
                            LocalDateTime.of(LocalDate.now(), LocalTime.MIN), admin.getGroupId());
                    if (!tasks.isEmpty()) {
                        throw new AdminCheckException("当前账户有任务运行，无法修改，请先暂停任务或联系管理员");
                    }
                    TenantSecrets tenantSecrets = tenantSecretsRepository.findFirstByTenantId(String.valueOf(admin.getTenantId()));
                    if (tenantSecrets == null) {
                        throw new AdminCheckException("当前账户所在商户没有相关配置，请联系管理员");
                    }
                    admin.setCallBackRange(AccountOperatorType.getStringList(param.getCallBackRange()));
                    admin.setDataStatisticRange(AccountOperatorType.getStringList(param.getDataStatisticRange()));
                    admin.setCallbackUrl(param.getCallBackUrl());
                    admin.setSmsCallbackUrl(param.getSmsCallbackUrl());
                    admin.setTaskCallbackUrl(param.getTaskCallbackUrl());
                    admin.setCallSmsCallBackUrl(param.getCallSmsCallbackUrl());
                    admin.setCallDataCallBackUrl(param.getCallDataCallbackUrl());
                    admin.setCallUpdateCallBackUrl(param.getCallUpdateCallbackUrl());
                    admin.setCallMCallBackUrl(param.getCallMCallbackUrl());
                    admin.setCallbackStatusConfig(param.getCallbackStatusConfig());
                    admin.setCallbackFieldConfig(param.getCallbackFieldConfig());
                    admin.setSmsMoCallbackUrl(param.getSmsMoCallbackUrl());
                    List<String> whiteIps = param.getWhiteIps();
                    tenantSecrets.setWhiteList(String.join(",", whiteIps));
                    adminRepository.save(admin);
                    tenantSecretsRepository.save(tenantSecrets);
                    deleteCache(CACHE_FOR_ADMIN);
                    deleteCache(CACHE_FOR_ANT_ADMIN);
                    deleteCache(CACHE_FOR_TENANT_SECRETS);
                    deleteCache(CACHE_FOR_ANTS_GROUP_IDS);
                    deleteCache(CACHE_FOR_ADMIN_CALLBACK_ENTITY);
                    deleteCache(CACHE_FOR_CALLBACK_TYPE_MAP);
                    filterTenantBlackFeign.clearAdminCache();
                });
            }
        });
    }

    public AccountOperatorResponse findAccountOperatorParamByAccountId(Long accountId) {
        Long userId = AIContext.getUserId();
        AccountOperatorResponse response = new AccountOperatorResponse();

        if(!AIContext.getAccountType().equals(AdminConstants.OPERATOR_END)){
            return response;
        }
        adminRepository.findById(userId)
                .flatMap(user -> adminRoleRepository.findById(user.getRoleId()))
                .ifPresent(userAdminRole -> {
//            if ("超级管理员".equals(userAdminRole.getRoleName()) || "研发-内部".equals(userAdminRole.getRoleName())) {
                adminRepository.findById(accountId).ifPresent(admin -> {
                    response.setAccountId(accountId);
                    response.setCallBackRange(AccountOperatorType.getEnumsList(admin.getCallBackRange()));
                    response.setDataStatisticRange(AccountOperatorType.getEnumsList(admin.getDataStatisticRange()));
                    response.setCallBackUrl(admin.getCallbackUrl());
                    response.setTaskCallbackUrl(admin.getTaskCallbackUrl());
                    response.setSmsCallbackUrl(admin.getSmsCallbackUrl());
                    response.setCallDataCallbackUrl(admin.getCallDataCallBackUrl());
                    response.setCallSmsCallbackUrl(admin.getCallSmsCallBackUrl());
                    response.setCallUpdateCallbackUrl(admin.getCallUpdateCallBackUrl());
                    response.setCallMCallbackUrl(admin.getCallMCallBackUrl());
                    response.setCallbackStatusConfig(admin.getCallbackStatusConfig());
                    response.setCallbackFieldConfig(admin.getCallbackFieldConfig());
                    response.setSmsMoCallbackUrl(admin.getSmsMoCallbackUrl());
                    TenantSecrets tenantSecrets = tenantSecretsRepository.findFirstByTenantId(String.valueOf(admin.getTenantId()));
                    if (tenantSecrets != null) {
                        response.setSalt(tenantSecrets.getSalt());
                        response.setAes(tenantSecrets.getAesKey());
                        if (tenantSecrets.getWhiteList() != null) {
                            String whiteList = tenantSecrets.getWhiteList();
                            String[] split = whiteList.split(",");
                            response.setWhiteIps(Arrays.asList(split));
                        }
                    }
                });
//            }
        });
        return response;
    }

    public void deleteCache(String key) {
        RBucket<Object> bucket = redissonClient.getBucket(key);
        bucket.delete();
    }
}
