package com.raipeng.aispeech.service;

import com.raipeng.aispeech.dto.SipIpBroadbandOperatorCreateRequest;
import com.raipeng.aispeech.dto.SipIpBroadbandOperatorResponse;
import com.raipeng.aispeech.dto.SipIpBroadbandOperatorUpdateRequest;
import com.raipeng.aispeech.enums.BroadbandOperatorType;
import com.raipeng.aispeech.model.SipIpBroadbandOperator;
import com.raipeng.aispeech.repository.SipIpBroadbandOperatorRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 线路宽带商数据服务
 */
@Slf4j
@Service
public class SipIpBroadbandOperatorService {

    @Autowired
    private SipIpBroadbandOperatorRepository sipIpBroadbandOperatorRepository;

    /**
     * 创建线路宽带商信息
     *
     * @param request 创建请求
     * @return 创建结果
     */
    @Transactional
    public SipIpBroadbandOperatorResponse createLineOperator(SipIpBroadbandOperatorCreateRequest request) {
        log.info("创建线路宽带商信息，线路号码: {}", request.getLineNumber());
        
        // 检查线路号码是否已存在
        Optional<SipIpBroadbandOperator> existingOperator = sipIpBroadbandOperatorRepository.findByLineNumber(request.getLineNumber());
        SipIpBroadbandOperator operator = new SipIpBroadbandOperator();
        if (existingOperator.isPresent()) {
            BeanUtils.copyProperties(existingOperator.get(),operator);
        }else{
            operator.setIpQueried(false);
        }
        // 创建新实体
        if(!request.getIpAddress().equals(operator.getIpAddress())){
            operator.setIpQueried(false);
            operator.setIspInfo(null);
            operator.setIpQueryTime(null);
            operator.setOperator(null);
        }
        operator.setIpAddress(request.getIpAddress());
        // 将前端传入的中文名称转换为英文代码存储到数据库
        BroadbandOperatorType operatorType = request.getBroadbandOperatorType();
        if (operatorType != null) {
            operator.setBroadbandOperator(operatorType.getCode());
        } else {
            operator.setBroadbandOperator(request.getBroadbandOperator());
        }
        operator.setRemark(request.getRemark());
        operator.setLineNumber(request.getLineNumber());
        // 保存到数据库
        SipIpBroadbandOperator savedOperator = sipIpBroadbandOperatorRepository.save(operator);
        
        log.info("线路宽带商信息创建成功，ID: {}, 线路号码: {}", savedOperator.getId(), savedOperator.getLineNumber());
        
        return convertToResponse(savedOperator);
    }

    /**
     * 根据线路号码查询
     *
     * @param lineNumber 线路号码
     * @return 查询结果
     */
    public SipIpBroadbandOperatorResponse getByLineNumber(String lineNumber) {
        log.info("根据线路号码查询，线路号码: {}", lineNumber);
        
        if (!StringUtils.hasText(lineNumber)) {
            log.error("线路号码不能为空");
            return null;
        }

        Optional<SipIpBroadbandOperator> operatorOptional = sipIpBroadbandOperatorRepository.findByLineNumber(lineNumber);
        if (!operatorOptional.isPresent()) {
            log.error("线路号码不能为空：{}",lineNumber);
            return null;
        }
        
        return convertToResponse(operatorOptional.get());
    }

    /**
     * 根据线路号码更新
     *
     * @param lineNumber 线路号码
     * @param request    更新请求
     * @return 更新结果
     */
    @Transactional
    public SipIpBroadbandOperatorResponse updateByLineNumber(String lineNumber, SipIpBroadbandOperatorUpdateRequest request) {
        log.info("根据线路号码更新，线路号码: {}", lineNumber);
        
        if (!StringUtils.hasText(lineNumber)) {
            throw new RuntimeException("线路号码不能为空");
        }
        
        Optional<SipIpBroadbandOperator> operatorOptional = sipIpBroadbandOperatorRepository.findByLineNumber(lineNumber);
        if (!operatorOptional.isPresent()) {
            throw new RuntimeException("线路号码不存在: " + lineNumber);
        }
        
        SipIpBroadbandOperator operator = operatorOptional.get();
        
        // 只更新非空字段
        if (StringUtils.hasText(request.getIpAddress())) {
            operator.setIpAddress(request.getIpAddress());
        }
        if (StringUtils.hasText(request.getBroadbandOperator())) {
            // 将前端传入的中文名称转换为英文代码存储到数据库
            try {
                BroadbandOperatorType operatorType = request.getBroadbandOperatorType();
                operator.setBroadbandOperator(operatorType.getCode());
            } catch (IllegalArgumentException e) {
                // 如果不是有效的枚举值，直接存储原始值
                operator.setBroadbandOperator(request.getBroadbandOperator());
            }
        }
        if (request.getRemark() != null) {
            operator.setRemark(request.getRemark());
        }
        
        // 保存更新
        SipIpBroadbandOperator savedOperator = sipIpBroadbandOperatorRepository.save(operator);
        
        log.info("线路宽带商信息更新成功，线路号码: {}", lineNumber);
        
        return convertToResponse(savedOperator);
    }

    /**
     * 根据线路号码删除
     *
     * @param lineNumber 线路号码
     * @return 删除结果
     */
    @Transactional
    public boolean deleteByLineNumber(String lineNumber) {
        log.info("根据线路号码删除，线路号码: {}", lineNumber);

        
        Optional<SipIpBroadbandOperator> operatorOptional = sipIpBroadbandOperatorRepository.findByLineNumber(lineNumber);
        if (operatorOptional.isPresent()) {
            sipIpBroadbandOperatorRepository.delete(operatorOptional.get());
        }


        log.info("线路宽带商信息删除成功，线路号码: {}", lineNumber);
        
        return true;
    }

    /**
     * 查询所有记录
     *
     * @return 所有记录
     */
    public List<SipIpBroadbandOperatorResponse> getAllLineOperators() {
        log.info("查询所有线路宽带商信息");
        
        List<SipIpBroadbandOperator> operators = sipIpBroadbandOperatorRepository.findAllOrderByUpdateTime();
        
        return operators.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    /**
     * 转换实体为响应对象
     *
     * @param operator 实体对象
     * @return 响应对象
     */
    private SipIpBroadbandOperatorResponse convertToResponse(SipIpBroadbandOperator operator) {
        SipIpBroadbandOperatorResponse response = new SipIpBroadbandOperatorResponse();
        BeanUtils.copyProperties(operator, response);

        // 将数据库中的英文代码转换为中文名称返回给前端
        if (StringUtils.hasText(operator.getBroadbandOperator())) {
            try {
                BroadbandOperatorType operatorType = BroadbandOperatorType.fromCode(operator.getBroadbandOperator());
                response.setBroadbandOperatorFromType(operatorType);
            } catch (IllegalArgumentException e) {
                // 如果不是有效的枚举代码，直接返回原始值
                response.setBroadbandOperator(operator.getBroadbandOperator());
            }
        }

        return response;
    }
}
