package com.raipeng.aispeech.service;


import com.raipeng.aispeech.context.AIContext;
import com.raipeng.aispeech.controller.request.AudioListRequestDTO;
import com.raipeng.aispeech.controller.request.CorpusContentDTO;
import com.raipeng.aispeech.controller.response.*;
import com.raipeng.aispeech.entity.AsrProcessPojo;
import com.raipeng.aispeech.enums.PriorType;
import com.raipeng.aispeech.exceptionadvice.exception.ScriptCheckException;
import com.raipeng.aispeech.model.dto.CanvasCorpusData;
import com.raipeng.aispeech.model.script.*;
import com.raipeng.aispeech.repository.*;
import com.raipeng.aispeech.utils.IpAddressUtil;
import com.raipeng.aispeech.utils.ScriptServiceUtil;
import com.raipeng.common.enums.ScriptCorpusTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static com.raipeng.common.enums.ScriptCorpusTypeEnum.KNOWLEDGE_BASE_QA;
import static com.raipeng.common.enums.ScriptCorpusTypeEnum.KNOWLEDGE_ORDINARY;

@Slf4j
@Service
@RefreshScope
public class ScriptCorpusService {
    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ScriptRepository scriptRepository;

    @Autowired
    private ScriptBranchRepository scriptBranchRepository;

    @Autowired
    private ScriptCorpusRepository scriptCorpusRepository;

    @Autowired
    private ScriptCanvasRepository scriptCanvasRepository;

    @Autowired
    private ScriptMultiContentService scriptMultiContentService;

    @Autowired
    private ScriptUnitContentRepository scriptUnitContentRepository;

    @Autowired
    private AsrProcessService asrProcessService;

    @Autowired
    private ScriptMultiContentRepository scriptMultiContentRepository;

    @Autowired
    private ScriptPriorGroupRepository scriptPriorGroupRepository;
    @Autowired
    private  KnowledgeGroupRepository knowledgeGroupRepository;
    @Autowired
    private  AdvancedRulesService advancedRulesService;

    @Value("${script.audio.upload.path:/static/file/ai/audio_record}")
    private String fileNewUploadPath;

    @Value("${script.audio.download.path:https://uat.bountech.com/marketfront/file/ai-speech/ai/audio_record}")
    private String fileNewDownloadPath;

    private final Pattern pattern = Pattern.compile("_(\\d+)\\.");

    /**
     * 单独修改某个语料的文字内容(需要添加多段语句块内容)
     *
     * @param corpusContentDTO 内容参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateOneCorpusContent(CorpusContentDTO corpusContentDTO) {
        //语料文字内容支持多语句后，此处id传的是contentEntityId
        ScriptServiceUtil.checkScriptCanBeEdit(scriptRepository, corpusContentDTO.getScriptId());
        scriptCorpusRepository.findById(corpusContentDTO.getCorpusId()).ifPresent(corpus -> {
            Long scriptId = corpus.getScriptId();
            List<ScriptCanvas> canvasList = scriptCanvasRepository.findAllByScriptId(scriptId);
            for (ScriptCanvas canvas : canvasList) {
                Map<Long, CanvasCorpusData> canvasCorpusDataMap = canvas.getCanvasCorpusDataMap();
                if (canvasCorpusDataMap == null || canvasCorpusDataMap.isEmpty()) {
                    continue;
                }
                if (canvasCorpusDataMap.containsKey(corpusContentDTO.getCorpusId())) {
                    //画布中语料的语句预览中把第一个语句块的多个语句拼接存储进行展示
                    ScriptMultiContent multiContent = scriptMultiContentRepository.findFirstByCorpusId(corpus.getId());
                    Map<Long, ScriptUnitContent> unitContentMap = scriptUnitContentRepository.findAllByMultiContentIdAndIsDeletedOrderByOrders(multiContent.getId(), false).stream().collect(Collectors.toMap(ScriptUnitContent::getId, t->t));
                    if (unitContentMap.containsKey(corpusContentDTO.getContentId())) {
                        unitContentMap.get(corpusContentDTO.getContentId()).setContent(corpusContentDTO.getContent());
                        List<ScriptUnitContent> unitContents = unitContentMap.values().stream().collect(Collectors.toList());
                        unitContents.sort(Comparator.comparing(ScriptUnitContent::getOrders));
                        String contents = unitContents.stream().map(ScriptUnitContent::getContent).collect(Collectors.joining(""));
                        canvasCorpusDataMap.get(corpusContentDTO.getCorpusId()).setContent(contents);
                        canvas.setCanvasCorpusDataMap(canvasCorpusDataMap);
                    }
                    scriptCanvasRepository.saveAndFlush(canvas);
                    break;
                }
            }
            //单独更新某个语句内容
            ScriptUnitContent scriptUnitContent = scriptUnitContentRepository.findFirstById(corpusContentDTO.getContentId());
            scriptUnitContent.setContent(corpusContentDTO.getContent());
            scriptUnitContent.setIsPlayed(false);
            scriptUnitContent.setAudioStatus("0");
            scriptUnitContent.setAudioTag(null);
            scriptUnitContentRepository.saveAndFlush(scriptUnitContent);
            asrProcessService.processReCalculate(scriptUnitContent);
        });
    }

    /**
     * 拷贝一个语料
     *
     * @param scriptId 话术ID
     * @param corpusId 语料ID
     * @return 语料
     */
    @Transactional(rollbackFor = Exception.class)
    public ScriptCorpus copyOneScriptCorpusByCorpusId(Long scriptId, Long corpusId, boolean isHead) {
        ScriptServiceUtil.checkScriptCanBeEdit(scriptRepository, scriptId);
        Optional<ScriptCorpus> corpusOptional = scriptCorpusRepository.findById(corpusId);
        if (!corpusOptional.isPresent()) {
            throw new ScriptCheckException("你要复制的语料不存在");
        }
        ScriptCorpus scriptCorpus = corpusOptional.get();
        ScriptCorpusTypeEnum corpusType = scriptCorpus.getCorpusType();
        switch (corpusType) {
            case MASTER_ORDINARY:
            case MASTER_CONNECT:
            case KNOWLEDGE_ORDINARY:
            case KNOWLEDGE_CONNECT:
                return copyCorpus(scriptCorpus, isHead);
            default:
                throw new ScriptCheckException("不能复制该话术");
        }
    }

    /**
     * 物理删除某个语料
     *
     * @param id 语料ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteOneScriptCorpusById(Long id) {
        Optional<ScriptCorpus> corpusOptional = scriptCorpusRepository.findById(id);
        corpusOptional.ifPresent(corpus -> {
            ScriptServiceUtil.checkScriptCanBeEdit(scriptRepository, corpus.getScriptId());
            // 如果有分支，处理分支
            List<ScriptBranch> branchList = corpus.getBranchList();
            if (branchList != null && branchList.size() != 0) {
                for (ScriptBranch scriptBranch : branchList) {
                    scriptBranchRepository.deleteById(scriptBranch.getId());
                }
            }
            // 如有需要再增加高级规则和最终意向的同步删除功能 删除基本问答/重复预料
            advancedRulesService.deleteAdvanceRulesConditionByCorpusId(corpus.getScriptId(), corpus.getId());
            scriptCorpusRepository.deleteById(id);
            scriptMultiContentService.deleteMultiContentByCorpusList(Collections.singletonList(corpus));
            scriptPriorGroupRepository.deleteAllByCorpusId(id);
        });
    }

    public ResponseEntity<byte[]> downloadAudioFiles(List<Long> contentIds, HttpServletRequest request) {
        List<ScriptUnitContent> scriptUnitContents = scriptUnitContentRepository.findAllById(contentIds)
                .stream()
                .filter(corpus -> corpus.getAudioPath() != null)
                .collect(Collectors.toList());

        String ip = IpAddressUtil.getIpAddress(request);
        String account = AIContext.getAccount();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

        if (scriptUnitContents.size() == 1) {
            String audioName = getAudioName(scriptUnitContents.get(0));
            if (audioName != null) {
                ResponseEntity<byte[]> response = restTemplate.getForEntity(scriptUnitContents.get(0).getAudioPath(), byte[].class);
                if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                    headers.setContentDispositionFormData("attachment", audioName);
                    headers.setContentLength(response.getBody().length);
                    log.info("账号：{}，ip：{} 下载音频文件：{} 成功", account, ip, contentIds);
                    return ResponseEntity.status(HttpStatus.OK).headers(headers).body(response.getBody());
                }
            }
        } else if (scriptUnitContents.size() > 1) {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            try (ZipOutputStream zipOut = new ZipOutputStream(baos)) {
                for (ScriptUnitContent contentEntity : scriptUnitContents) {
                    String audioName = getAudioName(contentEntity);
                    if (audioName != null) {
                        ResponseEntity<byte[]> response = restTemplate.getForEntity(contentEntity.getAudioPath(), byte[].class);
                        if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                            ZipEntry zipEntry = new ZipEntry(audioName);
                            zipOut.putNextEntry(zipEntry);
                            zipOut.write(response.getBody());
                            zipOut.closeEntry();
                        }
                    }
                }
                zipOut.finish();
                zipOut.close();
                headers.setContentDispositionFormData("attachment", "audios.zip");
                headers.setContentLength(baos.toByteArray().length);
                log.info("账号：{}，ip：{} 下载音频文件：{} 成功", account, ip, contentIds);
                return ResponseEntity.status(HttpStatus.OK).headers(headers).body(baos.toByteArray());
            } catch (IOException e) {
                log.info("账号：{}，ip：{} 下载音频文件：{} 失败", account, ip, contentIds);
                return ResponseEntity.status(HttpStatus.NO_CONTENT).headers(headers).body(new byte[0]);
            }
        }
        log.info("账号：{}，ip：{} 下载音频文件：{} 失败", account, ip, contentIds);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).headers(headers).body(new byte[0]);
    }

    /**
     * 查找一个语料
     *
     * @param id 语料id
     * @return 语料
     */
    public Optional<ScriptCorpus> findOneScriptCorpusById(Long id) {
        Optional<ScriptCorpus> scriptCorpusOptional = scriptCorpusRepository.findById(id);
        scriptCorpusOptional.ifPresent(corpus -> {
            corpus.setScriptMultiContents(scriptMultiContentService.getMultiContentsByCorpus(corpus));
            PriorGroup priorGroup = scriptPriorGroupRepository.findFirstByCorpusId(corpus.getId());
            if (Objects.nonNull(priorGroup)) {
                corpus.setPriorGroup(priorGroup);
            }
        });
        return scriptCorpusOptional;
    }


    /**
     * 根据传入的id列表给语料重新排序
     *
     * @param corpusIdList 语料id列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void sortCorpusList(List<Long> corpusIdList, Long scriptId) {
        ScriptServiceUtil.checkScriptCanBeEdit(scriptRepository, scriptId);
        int weight = 0;
        for (Long corpusId : corpusIdList) {
            Optional<ScriptCorpus> cropusOptional = scriptCorpusRepository.findById(corpusId);
            weight = weight + 1;
            int finalWeight = weight;
            cropusOptional.ifPresent(corpus -> {
                corpus.setWeight(finalWeight);
                scriptCorpusRepository.save(corpus);
            });
        }
    }

    public ScriptAudioResponse audioList(AudioListRequestDTO dto) {
        Long id = dto.getScriptId();
        String name = dto.getUnitContentName();
        String content = dto.getContent();
        List<String> typeList = dto.getTypeList();
        String uploadStatus = dto.getUploadStatus();
        String canvasName = dto.getCanvasName();
        Boolean isPlayed = dto.getIsPlayed();
        String audioStatus = dto.getAudioStatus();

        List<Long> multiContentIds = scriptMultiContentRepository.findAllByScriptId(id).stream().map(ScriptMultiContent::getId).collect(Collectors.toList());
        List<ScriptUnitContent> unitContents;
        List<ScriptUnitContentForAudio> unitContentForAudios = new ArrayList<>();

        List<ScriptCanvas> canvasList = scriptCanvasRepository.findAllByScriptId(id);

        Map<Long, String> corpusIdNameMap = canvasList.stream()
                .filter(canvas -> {
                    Map<Long, CanvasCorpusData> canvasCorpusDataMap = canvas.getCanvasCorpusDataMap();
                    return canvasCorpusDataMap != null && !canvasCorpusDataMap.isEmpty();
                })
                .flatMap(canvas ->
                        canvas.getCanvasCorpusDataMap().entrySet().stream()
                                .map(entry -> new HashMap.SimpleEntry<>(entry.getKey(), canvas.getName()))
                )
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (existing, replacement) -> existing // 处理重复键：保留先出现的值
                ));

        unitContents = scriptUnitContentRepository.findAllByContentNameLikeAndMultiContentIdInAndIsDeleted(name, multiContentIds, content,false);
        int count = 0;
        unitContents = unitContents.stream().filter(a -> {
            if (StringUtils.isEmpty(a.getContent())) {
                return false;
            }
            if (CollectionUtils.isNotEmpty(typeList)){
                if (Objects.isNull(a.getCorpusType())) {
                    return false;
                }
                if (!typeList.contains(a.getCorpusType().toString())) {
                    return false;
                }
            }
            if (StringUtils.isNotBlank(uploadStatus)) {
                if ("未上传".equals(uploadStatus)) {
                    if ("已上传".equals(a.getUploadStatus())) {
                        return false;
                    }
                } else {
                    if (!uploadStatus.equals(a.getUploadStatus())) {
                        return false;
                    }
                }
            }
//            if (Objects.nonNull(isPlayed)) {
//                if (isPlayed) {
//                    if (!isPlayed.equals(a.getIsPlayed())) {
//                        return false;
//                    }
//                }  else {
//                    if (Objects.nonNull(a.getIsPlayed()) && a.getIsPlayed()) {
//                        return false;
//                    }
//                }
//            }

            if (StringUtils.isNotBlank(audioStatus)) {
                if (!audioStatus.equals(a.getAudioStatus())) {
                    return false;
                }
            }

            return true;
        }).collect(Collectors.toList());
        for (ScriptUnitContent scriptUnitContent : unitContents) {
            if ("已上传".equals(scriptUnitContent.getUploadStatus())) {
                count++;
            }
            ScriptUnitContentForAudio scriptUnitContentForAudio = new ScriptUnitContentForAudio();
            BeanUtils.copyProperties(scriptUnitContent, scriptUnitContentForAudio);

            scriptUnitContentForAudio.setCanvasName(corpusIdNameMap.get(scriptUnitContent.getCorpusId()));

            unitContentForAudios.add(scriptUnitContentForAudio);
        }
        unitContentForAudios = unitContentForAudios.stream().filter(unit -> {
            if (StringUtils.isNotBlank(canvasName)) {
                if (StringUtils.isBlank(unit.getCanvasName())) {
                    return false;
                } else if (!unit.getCanvasName().contains(canvasName)) {
                    return false;
                }
            }
            return true;
        }).collect(Collectors.toList());
        unitContentForAudios.sort((o1, o2) -> {
            String s1 = o1.getUploadStatus();
            String s2 = o2.getUploadStatus();
            s1 = StringUtils.isEmpty(s1) ? "未上传" : s1;
            s2 = StringUtils.isEmpty(s2) ? "未上传" : s2;
            if (s1.equals(s2)) {
                return o2.getUpdateTime().compareTo(o1.getUpdateTime());
            } else {
                if ("未上传".equals(s1)) {
                    return -1;
                } else {
                    return 1;
                }
            }
        });
        ScriptAudioResponse scriptAudioResponse = new ScriptAudioResponse();
        scriptAudioResponse.setScriptUnitContents(unitContentForAudios);
        scriptAudioResponse.setTotalNums(unitContents.size());
        scriptAudioResponse.setUploadNums(count);
        return scriptAudioResponse;
    }

    public List<ScriptUnitContent> audioListByUpdate(Long scriptId, String judge) {
        List<Long> multiContentIds = scriptMultiContentRepository.findAllByScriptId(scriptId).stream().map(ScriptMultiContent::getId).collect(Collectors.toList());
        List<ScriptUnitContent> unitContents = scriptUnitContentRepository.findAllByMultiContentIdInAndIsDeleted(multiContentIds, false);
        if ("1".equals(judge)) {
            return unitContents.stream().filter(a -> ("已修改".equals(a.getUpdateStatus()))).collect(Collectors.toList());
        } else {
            return unitContents;
        }
    }

    public String importAudioFiles(MultipartFile file, Long contentId) throws IOException {
        Optional<ScriptUnitContent> contentEntityOptional = scriptUnitContentRepository.findById(contentId);
        if (!contentEntityOptional.isPresent()) {
            return "上传失败，未获取到语句";
        }
        ScriptUnitContent unitContent = contentEntityOptional.get();
        Optional<ScriptMultiContent> multiContentOptional = scriptMultiContentRepository.findById(unitContent.getMultiContentId());
        if (!multiContentOptional.isPresent()) {
            return "上传失败，未获取到多语句";
        }
        ScriptMultiContent multiContent = multiContentOptional.get();
        Long scriptId = multiContent.getScriptId();

        ScriptServiceUtil.checkScriptCanBeEdit(scriptRepository, scriptId);
        if (!file.isEmpty()) {
            String fileName = file.getOriginalFilename();
            String filePrefix = fileName.split("\\.")[0];
            if (filePrefix.contains("_")) {
                String[] filePrefixSplit = filePrefix.split("_");
                if (filePrefixSplit.length != 3) {
                    return "上传失败，不符合规范的文件名：" + filePrefix;
                }
                filePrefix = filePrefixSplit[1];
            }

            Script script = scriptRepository.getOne(scriptId);
            unitContent.setUploadStatus("已上传");
            unitContent.setIsPlayed(false);//未播放
            unitContent.setAudioStatus("0");//未播放
            unitContent.setAudioTag(null);//未播放
            unitContent.setUpdateStatus("已修改");
            unitContent.setUploadTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            String audioPath = unitContent.getAudioPath();

            String uploadPath;
            String downloadPath;
            if (StringUtils.isEmpty(audioPath)) {
                uploadPath = fileNewUploadPath + "/" + script.getScriptStringId() + "/" + script.getScriptStringId() + "_" + filePrefix + "_0." + fileName.split("\\.")[1];
                downloadPath = fileNewDownloadPath + "/" + script.getScriptStringId() + "/" + script.getScriptStringId() + "_" + filePrefix + "_0." + fileName.split("\\.")[1];
            } else {
                Matcher matcher = pattern.matcher(audioPath);
                if (!matcher.find()) {
                    return "上传失,语料音频名称有误";
                }
                int version = Integer.parseInt(matcher.group(1)) + 1;
                uploadPath = fileNewUploadPath + "/" + script.getScriptStringId() + "/" + script.getScriptStringId() + "_" + filePrefix + "_" + version + "." + fileName.split("\\.")[1];
                downloadPath = fileNewDownloadPath + "/" + script.getScriptStringId() + "/" + script.getScriptStringId() + "_" + filePrefix + "_" + version + "." + fileName.split("\\.")[1];
            }
            unitContent.setAudioPath(downloadPath);
            scriptUnitContentRepository.save(unitContent);
            AsrProcessPojo asrProcessPojo = new AsrProcessPojo();
            asrProcessPojo.setFile(file);
            asrProcessPojo.setUnitContent(unitContent);
            asrProcessService.processAsrResult(Collections.singletonList(asrProcessPojo));
            File tmpFile = new File(uploadPath);
            try {
                if (!tmpFile.exists()) {
                    tmpFile.getParentFile().mkdirs();
                    tmpFile.createNewFile();
                }
                file.transferTo(tmpFile);
            } catch (IOException e) {
                e.printStackTrace();
                return "上传失败，" + e.getMessage();
            }
        }
        return "上传成功";
    }

    public void deleteAudio(Long scriptId) {
        ScriptServiceUtil.checkScriptCanBeEdit(scriptRepository, scriptId);
        List<ScriptCorpus> allByScriptId = scriptCorpusRepository.findAllByScriptId(scriptId);
        for (ScriptCorpus scriptCorpus : allByScriptId) {
            scriptCorpus.setAudioPath(null);
            scriptCorpus.setUploadStatus("未上传");
            scriptCorpus.setUploadTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        List<ScriptUnitContent> unitContents = scriptUnitContentRepository.findAllByScriptId(scriptId);
        for (ScriptUnitContent unitContent : unitContents) {
            unitContent.setAudioPath(null);
            unitContent.setUploadStatus("未上传");
            unitContent.setAsrTxt(null);
            unitContent.setAsrResult(null);
            unitContent.setAsrStatus(null);
            unitContent.setIsPlayed(false);
            unitContent.setAudioStatus("0");
            unitContent.setAudioTag(null);
        }
        scriptCorpusRepository.saveAll(allByScriptId);
        scriptUnitContentRepository.saveAll(unitContents);
    }

    private String getAudioName(ScriptUnitContent scriptUnitContent) {
        String name = scriptUnitContent.getContentName();
        if (StringUtils.isNotEmpty(name)) {
            return name + ".wav";
        }
        return null;
    }

    public String updatePlayedAudio(Long id) {
        String audioStatus = "0";
        Optional<ScriptUnitContent> byId = scriptUnitContentRepository.findById(id);
        if (!byId.isPresent()) {
            return audioStatus;
        }
        ScriptUnitContent scriptUnitContent = byId.get();
        if (StringUtils.isNotBlank(scriptUnitContent.getAudioTag())) {
            audioStatus = "2";
            scriptUnitContentRepository.updateUnitContent(id, audioStatus);
        } else {
            audioStatus = "1";
            scriptUnitContentRepository.updateUnitContent(id, audioStatus);
        }
        return audioStatus;
    }

    public List<ScriptCorpusSimpleDTO> findKnowledgeBaseAndPriorQAByScriptId(Long id) {
        List<ScriptCorpus> allByScriptIdAndIsDeleted = scriptCorpusRepository.findAllByScriptIdAndIsDeleted(id, false);
        List<ScriptCorpusSimpleDTO> scriptCorpusSimpleDTOList = new ArrayList<>();
        for (ScriptCorpus scriptCorpus : allByScriptIdAndIsDeleted) {
            boolean b = KNOWLEDGE_BASE_QA.equals(scriptCorpus.getCorpusType()) || ScriptCorpusTypeEnum.FUNC_PRIOR_QA.equals(scriptCorpus.getCorpusType())
                    || (KNOWLEDGE_ORDINARY.equals(scriptCorpus.getCorpusType()) && scriptCorpus.getIsKnowledgeBase() != null && scriptCorpus.getIsKnowledgeBase());
            if (b) {
                ScriptCorpusSimpleDTO scriptCorpusSimpleDTO = new ScriptCorpusSimpleDTO();
                BeanUtils.copyProperties(scriptCorpus, scriptCorpusSimpleDTO);
                scriptCorpusSimpleDTOList.add(scriptCorpusSimpleDTO);
            }
        }
        return scriptCorpusSimpleDTOList;
    }

    public List<KnowledgeScriptCorpusSimpleDTO> findKnowledgeOrdinaryByScriptId(Long id) {

        List<ScriptCorpus> allByScriptIdAndIsDeleted = scriptCorpusRepository.findAllByScriptIdAndIsDeleted(id, false);
        List<KnowledgeGroup> byScriptIdOrderByPriorityAsc = knowledgeGroupRepository.findByScriptIdOrderByPriorityAsc(id);
        Map<Long, String> collect = byScriptIdOrderByPriorityAsc.stream().collect(Collectors.toMap(KnowledgeGroup::getId, KnowledgeGroup::getGroupName));
        List<KnowledgeScriptCorpusSimpleDTO> scriptCorpusSimpleDTOList = new ArrayList<>();
        for (ScriptCorpus scriptCorpus : allByScriptIdAndIsDeleted) {
            boolean b = KNOWLEDGE_ORDINARY.equals(scriptCorpus.getCorpusType()) && scriptCorpus.getIsKnowledgeBase() != null && scriptCorpus.getIsKnowledgeBase();
            if (b) {
                KnowledgeScriptCorpusSimpleDTO scriptCorpusSimpleDTO = new KnowledgeScriptCorpusSimpleDTO();
                BeanUtils.copyProperties(scriptCorpus, scriptCorpusSimpleDTO);
                Long knowledgeGroupId = scriptCorpusSimpleDTO.getKnowledgeGroupId();
                scriptCorpusSimpleDTO.setGroupName(collect.get(knowledgeGroupId));
                scriptCorpusSimpleDTOList.add(scriptCorpusSimpleDTO);
            }
        }
        return scriptCorpusSimpleDTOList;
    }

    public ScriptCorpus copyCorpus(ScriptCorpus scriptCorpus, boolean isHead) {
        List<ScriptBranch> branchList = scriptCorpus.getBranchList();

        ScriptCorpus newCorpus = new ScriptCorpus();
        BeanUtils.copyProperties(scriptCorpus, newCorpus);
        newCorpus.setId(null);
        newCorpus.setBranchList(null);
        newCorpus.setIsTopHead(false);
        newCorpus.setName(newCorpus.getName() + "-复制");
        newCorpus.setIsHead(isHead);
        ScriptCorpus newScriptCorpus = scriptCorpusRepository.save(newCorpus);

        List<ScriptMultiContent> oldScriptMultiContents = scriptMultiContentRepository.findByCorpusId(scriptCorpus.getId());
        List<ScriptMultiContent> newScriptMultiContents = new ArrayList<>();
        for (ScriptMultiContent oldScriptMultiContent : oldScriptMultiContents) {
            ScriptMultiContent scriptMultiContent = new ScriptMultiContent();
            BeanUtils.copyProperties(oldScriptMultiContent, scriptMultiContent);
            scriptMultiContent.setId(null);
            scriptMultiContent.setCorpusId(newScriptCorpus.getId());
            ScriptMultiContent newScriptMultiContent = scriptMultiContentRepository.save(scriptMultiContent);
            List<ScriptUnitContent> scriptUnitContents = scriptUnitContentRepository.findAllByMultiContentIdAndIsDeletedOrderByOrders(oldScriptMultiContent.getId(), false);
            List<ScriptUnitContent> newScriptUnitContents = new ArrayList<>();
            for (ScriptUnitContent scriptUnitContent : scriptUnitContents) {
                ScriptUnitContent unitContent = new ScriptUnitContent();
                BeanUtils.copyProperties(scriptUnitContent, unitContent);
                String contentName = scriptUnitContent.getContentName();
                String newContentName = contentName.replace(scriptCorpus.getName(), scriptCorpus.getName() + "-复制");
                unitContent.setId(null);
                unitContent.setAudioPath(null);
                unitContent.setContentName(newContentName);
                unitContent.setMultiContentId(newScriptMultiContent.getId());
                unitContent.setUploadStatus(null);
                unitContent.setUploadTime(null);
                unitContent.setCorpusId(newScriptCorpus.getId());
                unitContent.setAsrResult(null);
                unitContent.setAsrTxt(null);
                unitContent.setAsrStatus(null);
                unitContent.setIsPlayed(false);
                unitContent.setAudioStatus("0");
                unitContent.setAudioTag(null);
                newScriptUnitContents.add(unitContent);
            }
            List<ScriptUnitContent> scriptUnitContentsSave = scriptUnitContentRepository.saveAll(newScriptUnitContents);
            newScriptMultiContent.setScriptUnitContents(scriptUnitContentsSave);
            newScriptMultiContents.add(newScriptMultiContent);
        }
        newScriptCorpus.setScriptMultiContents(newScriptMultiContents);

        Map<Long, ScriptBranch> branchMap = new HashMap<>();
        if (branchList != null && !branchList.isEmpty()) {
            List<ScriptBranch> newBranchList = new ArrayList<>();
            for (ScriptBranch scriptBranch : branchList) {
                ScriptBranch newBranch = new ScriptBranch();
                BeanUtils.copyProperties(scriptBranch, newBranch);
                newBranch.setId(null);
                newBranch.setPreCorpusId(newScriptCorpus.getId());
                ScriptBranch scriptBranchSave = scriptBranchRepository.save(newBranch);
                branchMap.put(scriptBranch.getId(), scriptBranchSave);
                newBranchList.add(scriptBranchSave);
            }
            newScriptCorpus.setBranchList(newBranchList);
        }


        PriorGroup priorGroup = scriptPriorGroupRepository.findFirstByCorpusId(scriptCorpus.getId());
        if (priorGroup != null) {
            List<PriorPojo> priorList = priorGroup.getPriorList();
            List<PriorPojo> newPriorList = new ArrayList<>();
            if (priorList != null) {
                for (PriorPojo priorPojo : priorList) {
                    PriorPojo newPriorPojo = new PriorPojo();
                    if (priorPojo.getType().equals(PriorType.KNOWLEDGE_GROUPS)) {
                        BeanUtils.copyProperties(priorPojo, newPriorPojo);
                        newPriorList.add(newPriorPojo);
                    } else{
                        BeanUtils.copyProperties(priorPojo, newPriorPojo);
                        newPriorPojo.setId(branchMap.get(priorPojo.getId()).getId());
                        newPriorList.add(newPriorPojo);
                    }
                }
            }
            PriorGroup newPriorGroup = new PriorGroup();
            newPriorGroup.setId(null);
            newPriorGroup.setPriorList(newPriorList);
            newPriorGroup.setCorpusId(newScriptCorpus.getId());
            newPriorGroup.setScriptId(scriptCorpus.getScriptId());
            PriorGroup priorGroupSave = scriptPriorGroupRepository.save(newPriorGroup);
            newScriptCorpus.setPriorGroup(priorGroupSave);
        }
        return scriptCorpusRepository.save(newScriptCorpus);
    }

    public RecordShowCorpusDTO findTriggerCorpusByScriptId(Long scriptId) {
        List<ScriptCorpus> allByScriptId = scriptCorpusRepository.findAllByScriptId(scriptId);
        List<String> triggerHumanList = new ArrayList<>();
        List<String> triggerSmsList = new ArrayList<>();
        for (ScriptCorpus scriptCorpus : allByScriptId) {
            if (scriptCorpus.isListenInOrTakeOver()) {
                triggerHumanList.add(scriptCorpus.getName());
            }
            if (StringUtils.isNotBlank(scriptCorpus.getSmsTriggerName())) {
                triggerSmsList.add(scriptCorpus.getName());
            }
        }

        RecordShowCorpusDTO recordShowCorpusDTO = new RecordShowCorpusDTO();
        recordShowCorpusDTO.setTriggerHumanList(triggerHumanList);
        recordShowCorpusDTO.setTriggerSmsList(triggerSmsList);
        return recordShowCorpusDTO;
    }
}
