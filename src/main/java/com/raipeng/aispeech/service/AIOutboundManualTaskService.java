package com.raipeng.aispeech.service;

import com.raipeng.aispeech.exceptionadvice.exception.AdminCheckException;
import com.raipeng.aispeech.exceptionadvice.exception.ClueCheckException;
import com.raipeng.aispeech.model.AIOutboundTask;
import com.raipeng.aispeech.model.callteam.CallSeat;
import com.raipeng.aispeech.model.callteam.CallTeam;
import com.raipeng.aispeech.model.dto.AIOutboundQueryDto;
import com.raipeng.aispeech.model.dto.CallSeatRestDetailsDto;
import com.raipeng.aispeech.model.dto.CallSeatRestDetailsDtoImpl;
import com.raipeng.aispeech.repository.AIOutboundManualTaskRepository;
import com.raipeng.aispeech.repository.AIOutboundTaskRepository;
import com.raipeng.aispeech.repository.CallSeatRepository;
import com.raipeng.aispeech.repository.CallTeamRepository;
import com.raipeng.common.enums.AIOutboundTaskType;
import com.raipeng.common.util.JpaResultUtils;
import com.raipeng.common.util.StringUtils;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RMap;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Tuple;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.raipeng.aispeech.constant.CommonConstant.REDIS_TASK_TMP;

@Service
@RefreshScope
@Slf4j
public class AIOutboundManualTaskService {

    @Autowired
    private AIOutboundManualTaskRepository aiOutboundManualTaskRepository;

    @Autowired
    private AiTaskWriteService aiTaskWriteService;

    @Autowired
    private AIOutboundTaskRepository aiOutboundTaskRepository;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private PhoneRecordDailyHistoryService phoneRecordDailyHistoryService;

    @Autowired
    private RedisGetPhoneService redisGetPhoneService;

    @Autowired
    private CallTeamRepository callTeamRepository;

    @Autowired
    private CallSeatRepository callSeatRepository;

    @Autowired
    private AIOutboundTaskService aiOutboundTaskService;

    public void updateLineRatio(Long taskId, String groupId, Double lineRatio) {
        Optional<AIOutboundTask> taskOptional = aiOutboundManualTaskRepository.findById(taskId);
        if (taskOptional.isPresent()) {
            AIOutboundTask saveDto = taskOptional.get();
            deleteTaskFromRedis(saveDto.getTaskName(), saveDto.getId().toString(), saveDto.getPhoneNum());
            aiTaskWriteService.updateLineRatio(taskId, groupId, lineRatio);
        }else{
            log.info("updateLineRatio Task id {} 不存在", taskId);
        }
    }

    public void updateOccupyRate(Long taskId, String groupId, Integer occupyRate) {
        Optional<AIOutboundTask> taskOptional = aiOutboundManualTaskRepository.findById(taskId);
        if (taskOptional.isPresent()) {
            AIOutboundTask saveDto = taskOptional.get();
            deleteTaskFromRedis(saveDto.getTaskName(), saveDto.getId().toString(), saveDto.getPhoneNum());
            aiTaskWriteService.updateOccupyRate(taskId, groupId, occupyRate);
        }else{
            log.info("updateOccupyRate Task id {} 不存在", taskId);
        }
    }

    public void updateVirtualSeatRatio(Long taskId, String groupId, Double virtualSeatRatio) {
        Optional<AIOutboundTask> taskOptional = aiOutboundManualTaskRepository.findById(taskId);
        if (taskOptional.isPresent()) {
            AIOutboundTask saveDto = taskOptional.get();
            deleteTaskFromRedis(saveDto.getTaskName(), saveDto.getId().toString(), saveDto.getPhoneNum());
            aiTaskWriteService.updateVirtualSeatRatio(taskId, groupId, virtualSeatRatio);
        }else{
            log.info("updateVirtualSeatRatio Task id {} 不存在", taskId);
        }
    }

    public List<CallSeatRestDetailsDto> findSignedSeats(Long taskId, String groupId, List<Long> teams) {
        List<Tuple> tuples = aiOutboundManualTaskRepository.findSignedSeats(taskId, groupId, teams);
        List<CallSeatRestDetailsDto> callExtra = JpaResultUtils.processResult(tuples, CallSeatRestDetailsDto.class);
        return callExtra;
    }

    public List<CallSeatRestDetailsDtoImpl> findAllSignedSeats(Set<Long> taskIds, String groupId, List<Long> teams) {
        List<CallSeat> tuples = callSeatRepository.findAllSignedSeats(groupId, teams);
//        List<CallSeatRestDetailsDtoImpl> callExtra = JpaResultUtils.processResult(tuples, CallSeatRestDetailsDtoImpl.class);
        List<CallSeatRestDetailsDtoImpl> callExtra = tuples.stream().map(t -> {
            CallSeatRestDetailsDtoImpl callSeatRestDetailsDtoImpl = new CallSeatRestDetailsDtoImpl();
            callSeatRestDetailsDtoImpl.setCallSeatId(BigInteger.valueOf(t.getId()));
            callSeatRestDetailsDtoImpl.setTaskIds(t.getTaskIds());
            callSeatRestDetailsDtoImpl.setCallSeatRestTimeStart(t.getUpdateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            callSeatRestDetailsDtoImpl.setAccount(t.getAccount());
            callSeatRestDetailsDtoImpl.setName(t.getName());
            callSeatRestDetailsDtoImpl.setCallSeatStatus(t.getCallSeatStatus().toString());
            return callSeatRestDetailsDtoImpl;
        }).collect(Collectors.toList());
        return callExtra.stream().filter(t -> t.getTaskIds() != null && t.getTaskIds().stream()
                .anyMatch(taskIds::contains)).collect(Collectors.toList());
    }

    public void lockTask(AIOutboundQueryDto aiOutboundQueryDto) {
        String[] taskIdList = aiOutboundQueryDto.getTaskIds().split(",");
        for (String taskId : taskIdList) {
            Optional<AIOutboundTask> taskOptional = aiOutboundManualTaskRepository.findById(Long.valueOf(taskId));
            if (taskOptional.isPresent()) {
                AIOutboundTask saveDto = taskOptional.get();
                deleteTaskFromRedis(saveDto.getTaskName(), saveDto.getId().toString(), saveDto.getPhoneNum());
                saveDto.setIfLock(1);
                aiTaskWriteService.lockTaskSave(saveDto);
            }
        }
    }

    public void unLockTask(AIOutboundQueryDto aiOutboundQueryDto) {
        String[] taskIdList = aiOutboundQueryDto.getTaskIds().split(",");
        for (String taskId : taskIdList) {
            Optional<AIOutboundTask> taskOptional = aiOutboundManualTaskRepository.findById(Long.valueOf(taskId));
            if (taskOptional.isPresent()) {
                AIOutboundTask saveDto = taskOptional.get();
                deleteTaskFromRedis(saveDto.getTaskName(), saveDto.getId().toString(), saveDto.getPhoneNum());
                saveDto.setIfLock(0);
                aiTaskWriteService.unLockTaskSave(saveDto);
            }
        }
    }

    public BigDecimal findTransferRate(String taskId) {

        LocalDateTime startTime = LocalDateTime.now().minusMinutes(10);
        RMap<String, String> redisRes = redissonClient.getMap("TASK_ID_" + taskId + "_transferRate");
        if (redisRes.isExists()) {
            return new BigDecimal(redisRes.get("TASK_ID_" + taskId + "_transferRate"));
        }

        Tuple allCalls = aiOutboundManualTaskRepository.findTransferCall(taskId, startTime);
        if (((BigInteger) allCalls.get(0)).equals(BigInteger.ZERO)) {
            return BigDecimal.valueOf(0);
        }
        BigDecimal total = new BigDecimal((BigInteger) allCalls.get(0));
        BigDecimal transfer = new BigDecimal((BigInteger) allCalls.get(1));
        BigDecimal dbRes = transfer.divide(total, 5, RoundingMode.HALF_UP);
        redisRes.put("TASK_ID_" + taskId + "_transferRate", dbRes.toString());
        redisRes.expire(60, TimeUnit.SECONDS);
        return dbRes;
    }

    public BigDecimal findPutThroughRate(String taskId) {

        LocalDateTime startTime = LocalDateTime.now().minusMinutes(10);
        RMap<String, String> redisRes = redissonClient.getMap("TASK_ID_" + taskId + "_throughRate");
        if (redisRes.isExists()) {
            return new BigDecimal(redisRes.get("TASK_ID_" + taskId + "_throughRate"));
        }

        Tuple allCalls = aiOutboundManualTaskRepository.findLast10MinutesCall(taskId, startTime);
        if (((BigInteger) allCalls.get(0)).equals(BigInteger.ZERO)) {
            return BigDecimal.valueOf(0);
        }
        BigDecimal total = new BigDecimal((BigInteger) allCalls.get(0));
        BigDecimal putThrough = new BigDecimal((BigInteger) allCalls.get(1));
        BigDecimal dbRes = putThrough.divide(total, 5, RoundingMode.HALF_UP);

        redisRes.put("TASK_ID_" + taskId + "_throughRate", dbRes.toString());
        redisRes.expire(60, TimeUnit.SECONDS);
        return dbRes;
    }


    public Integer findPrincipleConcurrency(AIOutboundTask task, String groupId, List<Long> teams) {
        long startTime = System.currentTimeMillis();
        Long taskId = task.getId();

        List<CallSeat> callSeats = callSeatRepository.findCallSeatsByStatus(taskId, groupId,teams);
//        XxlJobLogger.log("First task read findPrincipleConcurrency:  "+ (System.currentTimeMillis() - startTime) +  " ms");
        if (callSeats == null) {
            return 0;
        }
        boolean emptyCallSeat = false;
        List<CallSeat> idleSeats = callSeats.stream()
                .filter(seat -> "HUMAN_MACHINE_IDLE".equals(seat.getCallSeatStatus().toString())
                        ||"MANUAL_DIRECT_IDLE".equals(seat.getCallSeatStatus().toString()))
                .collect(Collectors.toList());

        if(!callSeats.isEmpty() && idleSeats.isEmpty() && task.getVirtualSeatRatio()!=null && task.getLineRatio() != null){
            idleSeats = callSeats;
            emptyCallSeat = true;
            XxlJobLogger.log("empty seat, total seats : " + idleSeats.size());
        }


        startTime = System.currentTimeMillis();
        Map<Long, Integer> occupyRateSum = new HashMap<>();
        Map<Long, Double> occupyRateTarget = new HashMap<>();
        for (CallSeat seat : idleSeats) {
            Long seatId = seat.getId();
            Set<Long> seatTaskIds = seat.getTaskIds();
            if(seatTaskIds == null) continue;
            for(Long seatTaskId : seatTaskIds){
                Optional<AIOutboundTask> seatTask = aiOutboundManualTaskRepository.findById(seatTaskId);
                if(seatTask.isPresent() && seatTask.get().getCallStatus() != null && seatTask.get().getCallStatus().equals("进行中")){
                    Integer rate = seatTask.get().getOccupyRate();
                    Double lineRatio = seatTask.get().getLineRatio();
                    occupyRateSum.put(seatId, occupyRateSum.getOrDefault(seatId, 0) + rate);
                    if (seatTaskId.equals(taskId)) {
                        occupyRateTarget.put(seatId, lineRatio * rate);
                    }
                }
            }
        }

//        XxlJobLogger.log("Second task read findPrincipleConcurrency:  "+ (System.currentTimeMillis() - startTime) +  " ms");

        double res = 0.0;
        for (Map.Entry<Long, Integer> occupy : occupyRateSum.entrySet()) {
            if (occupy.getValue() != 0 && occupyRateTarget.get(occupy.getKey()) != null) {
                res = ( res + occupyRateTarget.get(occupy.getKey())/occupy.getValue() );
            }
        }
        if(emptyCallSeat){
            XxlJobLogger.log("res: " + res + " virtualRatio: " + task.getVirtualSeatRatio());
            return (int) Math.floor(res  * task.getVirtualSeatRatio() * 100) / 100;
        }
        return (int) Math.floor(res * 100) / 100;
    }


    public void findPrincipleConcurrencyBatch(List<AIOutboundTask> tasks) {
        Map<Long, AIOutboundTask> taskMap = new HashMap<>();
        for(AIOutboundTask task : tasks){
            taskMap.put(task.getId(), task);
        }
        List<CallSeat> allCallSeats = callSeatRepository.findAllCallSeatsByStatus();
        Map<Long,List<CallSeat>> callSeatMap = allCallSeats.stream()
                .filter(seat -> seat.getCallTeamId() != null)
                .collect(Collectors.groupingBy(CallSeat::getCallTeamId));

        for (AIOutboundTask task : tasks) {
            long startTime = System.currentTimeMillis();
            Long taskId = task.getId();
            String groupId = task.getGroupId();
            List<Long> teams = task.getCallTeamIds();
            if(StringUtils.isEmpty(groupId) || teams == null || teams.isEmpty()){
                continue;
            }

            List<CallSeat> callSeats = teams.stream().map(callSeatMap::get)
                    .filter(Objects::nonNull)
                    .flatMap(Collection::stream)
                    .filter(seat -> seat.getTaskIds() != null && seat.getTaskIds().contains(taskId))
                    .collect(Collectors.toList());

            if (callSeats.isEmpty()) {
                aiTaskWriteService.updateAiAnswerNum(0, taskId);
                continue;
            }
            boolean emptyCallSeat = false;
            List<CallSeat> idleSeats = callSeats.stream()
                    .filter(seat -> "HUMAN_MACHINE_IDLE".equals(seat.getCallSeatStatus().toString())
                            || "MANUAL_DIRECT_IDLE".equals(seat.getCallSeatStatus().toString()))
                    .collect(Collectors.toList());

            if (idleSeats.isEmpty() && task.getVirtualSeatRatio() != null && task.getLineRatio() != null) {
                idleSeats = callSeats;
                emptyCallSeat = true;
                XxlJobLogger.log("empty seat, total seats : " + idleSeats.size());
            }

            startTime = System.currentTimeMillis();
            Map<Long, Integer> occupyRateSum = new HashMap<>();
            Map<Long, Double> occupyRateTarget = new HashMap<>();
            for (CallSeat seat : idleSeats) {
                Long seatId = seat.getId();
                Set<Long> seatTaskIds = seat.getTaskIds();
                if (seatTaskIds == null) continue;
                for (Long seatTaskId : seatTaskIds) {
                    AIOutboundTask seatTask = taskMap.get(seatTaskId);
                    if (seatTask != null && seatTask.getCallStatus() != null && seatTask.getCallStatus().equals("进行中")) {
                        Integer rate = seatTask.getOccupyRate();
                        Double lineRatio = seatTask.getLineRatio();
                        occupyRateSum.put(seatId, occupyRateSum.getOrDefault(seatId, 0) + rate);
                        if (seatTaskId.equals(taskId)) {
                            occupyRateTarget.put(seatId, lineRatio * rate);
                        }
                    }
                }
            }
            double res = 0.0;
            for (Map.Entry<Long, Integer> occupy : occupyRateSum.entrySet()) {
                if (occupy.getValue() != 0 && occupyRateTarget.get(occupy.getKey()) != null) {
                    res = (res + occupyRateTarget.get(occupy.getKey()) / occupy.getValue());
                }
            }
            if (emptyCallSeat) {
                Integer virtualAiNum = (int) Math.ceil(res * task.getVirtualSeatRatio() * 100 / 100 );
                XxlJobLogger.log("TaskId:" + taskId + " PrincipleConcurrency " + virtualAiNum + "res: " + res + " virtualRatio: " + task.getVirtualSeatRatio() );
                aiTaskWriteService.updateAiAnswerNum( virtualAiNum, taskId);
                continue;
            }

            Integer updateAiNum = (int) Math.ceil(res * 100 / 100);
            aiTaskWriteService.updateAiAnswerNum(updateAiNum, taskId);
            XxlJobLogger.log("TaskId:" + taskId + " PrincipleConcurrency = " + updateAiNum  + " " + res);
        }
    }

    public void findPrincipleConcurrencyBatchBackUp(List<AIOutboundTask> tasks) {
        Map<Long, AIOutboundTask> taskMap = new HashMap<>();
        for(AIOutboundTask task : tasks){
            taskMap.put(task.getId(), task);
        }
        for (AIOutboundTask task : tasks) {
            long startTime = System.currentTimeMillis();
            Long taskId = task.getId();
            String groupId = task.getGroupId();
            List<Long> teams = task.getCallTeamIds();
            if(StringUtils.isEmpty(groupId) || teams == null || teams.isEmpty()){
                continue;
            }

            List<CallSeat> callSeats = callSeatRepository.findCallSeatsByStatus(taskId, groupId, teams);
//        XxlJobLogger.log("First task read findPrincipleConcurrency:  "+ (System.currentTimeMillis() - startTime) +  " ms");
            if (callSeats == null) {
                aiTaskWriteService.updateAiAnswerNum(0, taskId);
                continue;
            }
            boolean emptyCallSeat = false;
            List<CallSeat> idleSeats = callSeats.stream()
                    .filter(seat -> "HUMAN_MACHINE_IDLE".equals(seat.getCallSeatStatus().toString())
                            || "MANUAL_DIRECT_IDLE".equals(seat.getCallSeatStatus().toString()))
                    .collect(Collectors.toList());

            if (!callSeats.isEmpty() && idleSeats.isEmpty() && task.getVirtualSeatRatio() != null && task.getLineRatio() != null) {
                idleSeats = callSeats;
                emptyCallSeat = true;
                XxlJobLogger.log("empty seat, total seats : " + idleSeats.size());
            }

            startTime = System.currentTimeMillis();
            Map<Long, Integer> occupyRateSum = new HashMap<>();
            Map<Long, Double> occupyRateTarget = new HashMap<>();
            for (CallSeat seat : idleSeats) {
                Long seatId = seat.getId();
                Set<Long> seatTaskIds = seat.getTaskIds();
                if (seatTaskIds == null) continue;
                for (Long seatTaskId : seatTaskIds) {
//                    Optional<AIOutboundTask> seatTask = aiOutboundManualTaskRepository.findById(seatTaskId);
                    AIOutboundTask seatTask = taskMap.get(seatTaskId);
                    if (seatTask != null && seatTask.getCallStatus() != null && seatTask.getCallStatus().equals("进行中")) {
                        Integer rate = seatTask.getOccupyRate();
                        Double lineRatio = seatTask.getLineRatio();
                        occupyRateSum.put(seatId, occupyRateSum.getOrDefault(seatId, 0) + rate);
                        if (seatTaskId.equals(taskId)) {
                            occupyRateTarget.put(seatId, lineRatio * rate);
                        }
                    }
                }
            }
            double res = 0.0;
            for (Map.Entry<Long, Integer> occupy : occupyRateSum.entrySet()) {
                if (occupy.getValue() != 0 && occupyRateTarget.get(occupy.getKey()) != null) {
                    res = (res + occupyRateTarget.get(occupy.getKey()) / occupy.getValue());
                }
            }
            if (emptyCallSeat) {
                Integer virtualAiNum = (int) Math.ceil(res * task.getVirtualSeatRatio() * 100 / 100 );
                XxlJobLogger.log("res: " + res + " virtualRatio: " + task.getVirtualSeatRatio() + " PrincipleConcurrency " + virtualAiNum);
                aiTaskWriteService.updateAiAnswerNum( virtualAiNum, taskId);
                continue;
            }

            Integer updateAiNum = (int) Math.ceil(res * 100 / 100);
            aiTaskWriteService.updateAiAnswerNum(updateAiNum, taskId);
            XxlJobLogger.log("TaskId:" + taskId + " PrincipleConcurrency = " + updateAiNum  + " " + res);
        }
    }



    public boolean switchAiManualTask(AIOutboundQueryDto aiOutboundQueryDto) {
        AIOutboundTask aiOutboundTask = aiOutboundTaskRepository.findById(aiOutboundQueryDto.getTaskId()).get();
        if (aiOutboundTask.getIfLock() != null && aiOutboundTask.getIfLock() == 1) {
            return false;
        }
        if (!"进行中".equals(aiOutboundTask.getCallStatus()) && !redisGetPhoneService.ifQueueEmpty(aiOutboundTask.getId())) {
            if(!aiOutboundTaskService.verifyVariableAndTemplatedStartTask(aiOutboundTask)){
                return false;
            }
            if (!aiOutboundTaskService.checkTriggerNames(aiOutboundTask)) {
                return false;
            }
            deleteTaskFromRedis(aiOutboundTask.getTaskName(), aiOutboundTask.getId().toString(), aiOutboundTask.getPhoneNum());
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            aiOutboundTask.setTaskStartTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            aiOutboundTask.setCallStatus("进行中");
            aiOutboundTask.setAiAnswerNum(aiOutboundQueryDto.getConcurrent());
            aiOutboundTask.setLineId(aiOutboundQueryDto.getLineId());
            aiOutboundTask.setLineCode(aiOutboundQueryDto.getLineCode());
            aiOutboundTask.setLineName(aiOutboundQueryDto.getLineName());
            initCallTeamsWhenStartTask(aiOutboundTask);
        } else if ("进行中".equals(aiOutboundTask.getCallStatus())) {
            aiOutboundTask.setTaskEndTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            Integer totalNum = phoneRecordDailyHistoryService.findPhoneNums(0, 0, null, aiOutboundTask);
            Integer finishedNum = phoneRecordDailyHistoryService.findPhoneNums(0, 0, "呼叫完成", aiOutboundTask);
            Integer shieldedNum = phoneRecordDailyHistoryService.findPhoneNums(0, 0, "已屏蔽", aiOutboundTask);
            if (finishedNum + shieldedNum >= totalNum) {
                aiOutboundTask.setCallStatus("已停止");
            } else {
                aiOutboundTask.setCallStatus("未完成");
            }
            initCallTeamsWhenStopTask(aiOutboundTask);
        }
        aiOutboundTask.setAiAnswerNum(0);
        aiTaskWriteService.switchAiManualTaskSave(aiOutboundTask);
        aiOutboundTaskService.sendTaskStatus(String.valueOf(aiOutboundTask.getId()),aiOutboundTask.getTaskName(),aiOutboundTask.getCallStatus(),aiOutboundTask.getGroupId(),"switchAiManualTask");
        return true;
    }

    public void deleteTaskFromRedis(String taskName, String taskId, int phoneNum) {
        RBucket<Object> bucketTaskIdBody = redissonClient.getBucket("REDIS_TASK_TMP::" + taskId + "_body");
        if (!bucketTaskIdBody.isExists()) {
            return;
        }
        long taskPhoneNum = redissonClient.getAtomicLong("REDIS_TASK_TMP::" + taskId + "_phoneNum").get();
        long taskSyncPhoneNum = redissonClient.getAtomicLong("REDIS_TASK_TMP::" + taskId + "_syncPhoneNum").get();
        long taskSyncRecordNum = redissonClient.getAtomicLong("REDIS_TASK_TMP::" + taskId + "_syncRecordNum").get();
        if (phoneNum != taskSyncPhoneNum || taskPhoneNum != taskSyncPhoneNum || phoneNum != taskSyncRecordNum) {
            log.info("deleteTaskFromRedis taskName: {} taskId: {} phoneNum: {} taskPhoneNum : {} taskSyncPhoneNum: {} taskSyncRecordNum: {}",
                    taskName, taskId, phoneNum, taskPhoneNum, taskSyncPhoneNum, taskSyncRecordNum);
            throw new AdminCheckException("任务" + taskName + "还在传输数据，不具备操作条件");
        }

        String templateId = (String) redissonClient.getBucket(REDIS_TASK_TMP + taskId + "_templateId").get();
        RBucket<Object> bucketTaskName = redissonClient.getBucket("REDIS_TASK_TMP::" + taskName + "_" + templateId + "_id");
        RSet<String> taskNameKeyList = redissonClient.getSet(REDIS_TASK_TMP + "taskNameKeyList");
        taskNameKeyList.remove(REDIS_TASK_TMP + taskName + "_" + templateId + "_id");
        taskNameKeyList.remove(REDIS_TASK_TMP + taskId + "_body");
        bucketTaskName.delete();
        bucketTaskIdBody.delete();
        log.info("deleteTaskFromRedis {} {} {}", taskName, templateId, taskId);
    }


    public List<AIOutboundTask> startAiManualTaskBatch(AIOutboundQueryDto aiOutboundQueryDto) {
        String[] taskIdList = aiOutboundQueryDto.getTaskIds().split(",");
        List<AIOutboundTask> res = new ArrayList<>();
        for (String taskId : taskIdList) {
            aiOutboundQueryDto.setTaskId(Long.parseLong(taskId));
            res.add(startAiManualTask(aiOutboundQueryDto));
        }
        return res;
    }

    public AIOutboundTask startAiManualTask(AIOutboundQueryDto aiOutboundQueryDto) {
        AIOutboundTask aiOutboundTask = aiOutboundTaskRepository.findById(aiOutboundQueryDto.getTaskId()).get();
        if (aiOutboundTask.getIfLock() != null && aiOutboundTask.getIfLock() == 1) {
            aiOutboundTask.setBatchStatus("执行失败");
            return aiOutboundTask;
        }
        if (aiOutboundTask.getIsAutoStop() != null && aiOutboundTask.getIsAutoStop() == 1 && aiOutboundQueryDto.getIncludeAutoStop() == null) {
            aiOutboundTask.setBatchStatus("执行失败");
            return aiOutboundTask;
        }
        if(!aiOutboundTaskService.verifyVariableAndTemplatedStartTask(aiOutboundTask)){
            aiOutboundTask.setBatchStatus("执行失败");
            return aiOutboundTask;
        }
        if (!aiOutboundTaskService.checkTriggerNames(aiOutboundTask)) {
            aiOutboundTask.setBatchStatus("执行失败");
            return aiOutboundTask;
        }
        aiOutboundTask.setTaskStartTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        if (!"进行中".equals(aiOutboundTask.getCallStatus()) && !redisGetPhoneService.ifQueueEmpty(aiOutboundTask.getId())) {
            try {
                deleteTaskFromRedis(aiOutboundTask.getTaskName(), aiOutboundTask.getId().toString(), aiOutboundTask.getPhoneNum());
            } catch (AdminCheckException e) {
                aiOutboundTask.setBatchStatus("执行失败");
                return aiOutboundTask;
            }
            aiOutboundTask.setCallStatus("进行中");
            aiOutboundTask.setAiAnswerNum(0);
            aiOutboundTask.setLineId(aiOutboundQueryDto.getLineId());
            aiOutboundTask.setLineCode(aiOutboundQueryDto.getLineCode());
            aiOutboundTask.setLineName(aiOutboundQueryDto.getLineName());
            aiOutboundTask.setBatchStatus("执行成功");
            aiOutboundTaskService.sendTaskStatus(String.valueOf(aiOutboundTask.getId()),aiOutboundTask.getTaskName(),aiOutboundTask.getCallStatus(),aiOutboundTask.getGroupId(),"startAiManualTask");
        }
        aiTaskWriteService.startAiManualTaskSave(aiOutboundTask);
        initCallTeamsWhenStartTask(aiOutboundTask);
        return aiOutboundTask;
    }

    public List<AIOutboundTask> stopAiManualTaskBatch(AIOutboundQueryDto aiOutboundQueryDto) {
        String[] taskIdList = aiOutboundQueryDto.getTaskIds().split(",");
        List<AIOutboundTask> res = new ArrayList<>();
        for (String taskId : taskIdList) {
            aiOutboundQueryDto.setTaskId(Long.parseLong(taskId));
            res.add(stopAiManualTask(aiOutboundQueryDto, false));
        }
        return res;
    }

    public void stopAiManualTaskBatchForChannel(List<Long> taskIds) {
        for (Long taskId : taskIds) {
            stopAiManualTaskBatchForChannelPending(taskId);
        }
    }

    public void stopAiManualTaskBatchForChannelPending(Long taskId) {
        Optional<AIOutboundTask> taskOptional = aiOutboundTaskRepository.findById(taskId);
        if (!taskOptional.isPresent()) {
            log.info("Exception=>任务:{}停止失败,任务未找到", taskId);
            return;
        }
        AIOutboundTask aiOutboundTask = taskOptional.get();
        if (aiOutboundTask.getIfLock() != null && aiOutboundTask.getIfLock() == 1) {
            aiOutboundTask.setBatchStatus("执行失败");
            log.info("Exception=>任务:{}停止失败,任务已锁住", taskId);
            return;
        }
        try {
            if ("进行中".equals(aiOutboundTask.getCallStatus())) {

                Integer totalNum = phoneRecordDailyHistoryService.findPhoneNums(0, 0, null, aiOutboundTask);
                Integer finishedNum = phoneRecordDailyHistoryService.findPhoneNums(0, 0, "呼叫完成", aiOutboundTask);
                Integer shieldNum = phoneRecordDailyHistoryService.findPhoneNums(0, 0, "已屏蔽", aiOutboundTask);
                if (finishedNum + shieldNum >= totalNum) {
                    aiOutboundTask.setCallStatus("已停止");
                } else {
                    aiOutboundTask.setCallStatus("未完成");
                }

                aiOutboundTask.setTaskEndTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
            aiTaskWriteService.updateAiManualStatusForChannelPending(
                    aiOutboundTask.getId(),
                    aiOutboundTask.getCallStatus(),
                    aiOutboundTask.getTaskEndTime());
            initCallTeamsWhenStopTask(aiOutboundTask);
            aiOutboundTaskService.sendTaskStatus(String.valueOf(aiOutboundTask.getId()),aiOutboundTask.getTaskName(),aiOutboundTask.getCallStatus(),aiOutboundTask.getGroupId(),"stopAiManualTaskBatchForChannelPending");
        } catch (Exception e) {
            log.info("Exception=>任务:{}停止失败:{}", taskId, e.getMessage());
        }
    }

    @Transactional
    public AIOutboundTask stopAiManualTask(AIOutboundQueryDto aiOutboundQueryDto, boolean ifForce) {
        AIOutboundTask aiOutboundTask = aiOutboundTaskRepository.findById(aiOutboundQueryDto.getTaskId()).get();
        if (aiOutboundTask.getIfLock() != null && aiOutboundTask.getIfLock() == 1) {
            aiOutboundTask.setBatchStatus("执行失败");
            return aiOutboundTask;
        }
        try {
            if ("进行中".equals(aiOutboundTask.getCallStatus())) {
                if (ifForce) {
                    aiOutboundTask.setCallStatus("已停止");
                } else {
                    Integer totalNum = phoneRecordDailyHistoryService.findPhoneNums(0, 0, null, aiOutboundTask);
                    Integer finishedNum = phoneRecordDailyHistoryService.findPhoneNums(0, 0, "呼叫完成", aiOutboundTask);
                    Integer shieldNum = phoneRecordDailyHistoryService.findPhoneNums(0, 0, "已屏蔽", aiOutboundTask);
                    if (finishedNum + shieldNum >= totalNum) {
                        aiOutboundTask.setCallStatus("已停止");
                    } else {
                        aiOutboundTask.setCallStatus("未完成");
                    }
                }
                aiOutboundTask.setTaskEndTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            } else {
                if (ifForce) {
                    aiOutboundTask.setCallStatus("已停止");
                }
            }
            aiTaskWriteService.updateAiManualStatus(aiOutboundTask.getId(), aiOutboundTask.getCallStatus(), aiOutboundTask.getTaskEndTime());
            initCallTeamsWhenStopTask(aiOutboundTask);
            aiOutboundTaskService.sendTaskStatus(String.valueOf(aiOutboundTask.getId()),aiOutboundTask.getTaskName(),aiOutboundTask.getCallStatus(),aiOutboundTask.getGroupId(),"stopAiManualTask");
            aiOutboundTask.setBatchStatus("执行成功");
        } catch (Exception e) {
            aiOutboundTask.setBatchStatus("执行失败");
        }
        return aiOutboundTask;
    }

    private void initCallTeamsWhenStartTask(AIOutboundTask aiOutboundTask) {
        Long taskId = aiOutboundTask.getId();
        if (AIOutboundTaskType.AI_MANUAL.equals(aiOutboundTask.getTaskType())) {
            List<Long> callTeamIds = aiOutboundTask.getCallTeamIds();
            if (callTeamIds == null) {
                throw new ClueCheckException("人机协同任务需要配置坐席组");
            }
            List<CallTeam> callTeams = callTeamRepository.findAllById(callTeamIds);
            for (CallTeam callTeam : callTeams) {
                // 1. 坐席组和任务关联
                Set<Long> taskIds = callTeam.getTaskIds() == null ? new HashSet<>() : callTeam.getTaskIds();
                taskIds.add(aiOutboundTask.getId());
                callTeam.setTaskIds(taskIds);

                // 2. 初始化可签入坐席
                Map<Long, List<Long>> taskIdActiveCallSeatIdMap = callTeam.getTaskIdActiveCallSeatIdMap();
                taskIdActiveCallSeatIdMap = taskIdActiveCallSeatIdMap == null ? new HashMap<>() : taskIdActiveCallSeatIdMap;
                taskIdActiveCallSeatIdMap.put(taskId, callTeam.getCallSeatIds());
                callTeam.setTaskIdActiveCallSeatIdMap(taskIdActiveCallSeatIdMap);
            }
            callTeamRepository.saveAll(callTeams);
        }
    }

    private void initCallTeamsWhenStopTask(AIOutboundTask aiOutboundTask) {
        Long taskId = aiOutboundTask.getId();
        if (AIOutboundTaskType.AI_MANUAL.equals(aiOutboundTask.getTaskType())) {
            List<Long> callTeamIds = aiOutboundTask.getCallTeamIds();
            if (callTeamIds == null) {
                throw new ClueCheckException("人机协同任务需要配置坐席组");
            }
            List<CallTeam> callTeams = callTeamRepository.findAllById(callTeamIds);
            for (CallTeam callTeam : callTeams) {
                Set<Long> taskIds = callTeam.getTaskIds() == null ? new HashSet<>() : callTeam.getTaskIds();
                taskIds.remove(taskId);
                callTeam.setTaskIds(taskIds);

                Map<Long, List<Long>> taskIdActiveCallSeatIdMap = callTeam.getTaskIdActiveCallSeatIdMap();
                taskIdActiveCallSeatIdMap = taskIdActiveCallSeatIdMap == null ? new HashMap<>() : taskIdActiveCallSeatIdMap;
                taskIdActiveCallSeatIdMap.remove(taskId);
                callTeam.setTaskIdActiveCallSeatIdMap(taskIdActiveCallSeatIdMap);
            }
            callTeamRepository.saveAll(callTeams);
        }
    }
}