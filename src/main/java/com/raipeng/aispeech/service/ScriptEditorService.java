package com.raipeng.aispeech.service;


import com.raipeng.aispeech.context.AIContext;
import com.raipeng.aispeech.controller.request.scriptedit.ScriptDTO;
import com.raipeng.aispeech.exceptionadvice.exception.AdminCheckException;
import com.raipeng.aispeech.exceptionadvice.exception.ScriptCheckException;
import com.raipeng.aispeech.exceptionadvice.exception.ScriptCopyException;
import com.raipeng.aispeech.model.script.Script;
import com.raipeng.aispeech.model.script.ScriptEditor;
import com.raipeng.aispeech.model.script.ScriptExtend;
import com.raipeng.aispeech.model.script.ScriptOperationLog;
import com.raipeng.aispeech.repository.*;
import com.raipeng.common.enums.ScriptStatusEnum;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ScriptEditorService {
    @Autowired
    private ScriptRepository scriptRepository;

    @Autowired
    private ScriptEditorRepository scriptEditorRepository;

    @Autowired
    private ScriptService scriptService;

    @Autowired
    private ScriptStatusChangeService scriptStatusChangeService;

    @Autowired
    private ScriptOperationLogRepository scriptOperationLogRepository;

    @Autowired
    private ScriptExtendRepository scriptExtendRepository;

    public List<ScriptEditor> findAllScriptEditor() {
        return scriptEditorRepository.findAll();
    }

    public void editScriptEditorAttribute(Long scriptEditorId, String account, int maxScriptNum, boolean isSupperAccount) {
        Optional<ScriptEditor> scriptEditorOptional = scriptEditorRepository.findById(scriptEditorId);
        if (!scriptEditorOptional.isPresent()) {
            throw new AdminCheckException("当前话术师ID不存在,请检查");
        }
        ScriptEditor otherEditor = scriptEditorRepository.findOtherEditor(scriptEditorId, account);
        if (otherEditor != null) {
            throw new AdminCheckException("当前话术师已存在,请检查");
        }
        ScriptEditor scriptEditor = scriptEditorOptional.get();
        scriptEditor.setAccount(account);
        scriptEditor.setMaxScriptNum(maxScriptNum);
        scriptEditor.setIsSupperAccount(isSupperAccount);
        scriptEditorRepository.save(scriptEditor);
    }

    public void addOneScriptEditor(String account, int maxScriptNum, boolean isSupperAccount) {
        ScriptEditor scriptEditor = scriptEditorRepository.findByAccount(account);
        if (scriptEditor != null) {
            throw new AdminCheckException("当前话术师已存在");
        }
        scriptEditor = new ScriptEditor();
        scriptEditor.setAccount(account);
        scriptEditor.setMaxScriptNum(maxScriptNum);
        scriptEditor.setIsSupperAccount(isSupperAccount);
        scriptEditorRepository.save(scriptEditor);
    }

    public List<String> findAllScriptEditorAccounts() {
        return scriptEditorRepository.findAll()
                .stream()
                .filter(scriptEditor -> scriptEditor.getIsSupperAccount() ==null || !scriptEditor.getIsSupperAccount())
                .map(ScriptEditor::getAccount)
                .collect(Collectors.toList());
    }

    public List<Script> findAllScriptInPermission(ScriptStatusEnum status, Integer days) {
        ScriptEditor scriptEditor = findCurrentEditor();
        if (scriptEditor.getIsSupperAccount()) {
            List<Script> scripts = scriptRepository.findScriptsByIsDeletedAndStatusOrderByUpdateTimeDesc(false, status);
            List<ScriptExtend> scriptExtends = scriptExtendRepository.findAllByScriptStringIdIn(scripts.stream().map(Script::getScriptStringId).collect(Collectors.toList()));
            Map<String, ScriptExtend> scriptExtendMap = scriptExtends.stream().collect(Collectors.toMap(ScriptExtend::getScriptStringId, Function.identity()));
            List<Script> scriptList = new ArrayList<>();
            scripts.forEach(script -> {
                script.setEditPermission(true);
                ScriptExtend scriptExtend = scriptExtendMap.get(script.getScriptStringId());
                if (scriptExtend != null) {
                    script.setRemark(scriptExtend.getRemark());
                    script.setLastUsingDate(scriptExtend.getLastUsingDate());
                }
                if (scriptExtend == null || scriptExtend.getLastUsingDate() == null || days == null) {
                    scriptList.add(script);
                } else {
                    if (LocalDate.parse(scriptExtend.getLastUsingDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")).plusDays(days - 1).isBefore(LocalDate.now())) {
                        scriptList.add(script);
                    }
                }
            });
            return scriptList;
        }
        List<Script> scripts = scriptRepository.findScriptsForEditor(status.toString(), scriptEditor.getAccount());
        List<ScriptExtend> scriptExtends = scriptExtendRepository.findAllByScriptStringIdIn(scripts.stream().map(Script::getScriptStringId).collect(Collectors.toList()));
        Map<String, ScriptExtend> scriptExtendMap = scriptExtends.stream().collect(Collectors.toMap(ScriptExtend::getScriptStringId, Function.identity()));
        List<Script> scriptList = new ArrayList<>();
        scripts.forEach(script ->{
            script.setEditPermission(scriptEditor.getAccount().equals(script.getOwnerAccount()));
            ScriptExtend scriptExtend = scriptExtendMap.get(script.getScriptStringId());
            if (scriptExtend != null) {
                script.setRemark(scriptExtend.getRemark());
                script.setLastUsingDate(scriptExtend.getLastUsingDate());
            }
            if (scriptExtend == null || scriptExtend.getLastUsingDate() == null || days == null) {
                scriptList.add(script);
            } else {
                if (LocalDate.parse(scriptExtend.getLastUsingDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")).plusDays(days - 1).isBefore(LocalDate.now())) {
                    scriptList.add(script);
                }
            }
        });
        return scriptList;
    }

    @Transactional
    public ScriptExtend remarkOneScript(Long scriptId, String remark) {
        ScriptEditor scriptEditor = findCurrentEditor();
        Optional<Script> scriptOptional = scriptRepository.findById(scriptId);
        if (!scriptOptional.isPresent()) {
            throw new AdminCheckException("当前话术不存在");
        }
        Script script = scriptOptional.get();
        if (!scriptEditor.getIsSupperAccount() && !scriptEditor.getAccount().equals(script.getOwnerAccount())) {
            throw new AdminCheckException("没有添加备注的权限");
        }

        String scriptStringId = script.getScriptStringId();
        ScriptExtend scriptExtend = scriptExtendRepository.findScriptExtendByScriptStringId(scriptStringId);
        if (scriptExtend == null) {
            scriptExtend = new ScriptExtend();
        }
        scriptExtend.setScriptStringId(scriptStringId);
        scriptExtend.setRemark(remark);
        return scriptExtendRepository.save(scriptExtend);
    }

    public List<Script> findAllScriptInPermissionByIndustryId(ScriptStatusEnum status, Long secondaryIndustryId) {
        ScriptEditor scriptEditor = findCurrentEditor();
        if (scriptEditor.getIsSupperAccount()) {
            return scriptRepository.findScriptsByIsDeletedAndStatusAndSecondaryIndustryIdOrderByUpdateTimeDesc(false, status, secondaryIndustryId);
        }
        return scriptRepository.findScriptsByIndustryForEditor(status.toString(), secondaryIndustryId, scriptEditor.getAccount());
    }

    @Transactional
    public Script createOneScript(ScriptDTO param) {
        ScriptEditor scriptEditor = findCurrentEditor();
        int scriptNum = scriptRepository.findEditorScriptNumByAccount(scriptEditor.getAccount());
        Integer maxScriptNum = scriptEditor.getMaxScriptNum();
        if (scriptNum >= maxScriptNum) {
            throw new ScriptCheckException("当前账户最多生效"+maxScriptNum+"个话术,请先停用一些话术");
        }
        Script script = scriptService.addOneScript(param, scriptEditor.getAccount());
        ScriptOperationLog scriptOperationLog = new ScriptOperationLog();
        scriptOperationLog.setScriptStringId(script.getScriptStringId());
        scriptOperationLog.setOperateType("新建");
        scriptOperationLog.setOperateContent("新建话术");
        scriptOperationLog.setUserAccount(scriptEditor.getAccount());
        scriptOperationLogRepository.save(scriptOperationLog);
        return script;
    }

    @Transactional
    public Script copyOneScript(Long scriptId) {
        long now = System.currentTimeMillis();
        ScriptEditor scriptEditor = findCurrentEditor();
        Script script = getOneScriptById(scriptId);
        ScriptOperationLog scriptOperationLog = new ScriptOperationLog();
        int scriptNum = scriptRepository.findEditorScriptNumByAccount(scriptEditor.getAccount());
        Integer maxScriptNum = scriptEditor.getMaxScriptNum();
        if (scriptNum >= maxScriptNum) {
            throw new ScriptCheckException("当前账户最多生效"+maxScriptNum+"个话术,请先停用一些话术");
        }
        if (!ScriptStatusEnum.ACTIVE.equals(script.getStatus()) && !ScriptStatusEnum.STOP.equals(script.getStatus())) {
            throw new ScriptCopyException("当前话术状态不正确,不可复制");
        }
        List<String> watchAccounts = script.getWatchAccounts();
        if (!scriptEditor.getIsSupperAccount() && (watchAccounts == null || !watchAccounts.contains(scriptEditor.getAccount())) && !script.getOwnerAccount().equals(scriptEditor.getAccount())) {
            throw new AdminCheckException("当前账号没有此话术复制权限");
        }
        Script newScript = scriptService.copyOneScriptTotallyById(script, scriptEditor.getAccount());
        //不需要复制备注
        scriptOperationLog.setScriptStringId(newScript.getScriptStringId());
        scriptOperationLog.setOperateType("复制");
        scriptOperationLog.setUserAccount(scriptEditor.getAccount());
        scriptOperationLog.setOperateContent("复制自:" + script.getScriptName() + "|id:" + script.getId());
        scriptOperationLogRepository.save(scriptOperationLog);
        log.info("复制一个话术 原{} 耗时:{}", scriptId, System.currentTimeMillis() - now);
        return newScript;
    }

    @Transactional
    public Script updateOneScriptVersion(Long scriptId) {
        ScriptEditor scriptEditor = findCurrentEditor();
        Script script = getOneScriptById(scriptId);
        if (!ScriptStatusEnum.ACTIVE.equals(script.getStatus()) ) {
            throw new ScriptCopyException("当前话术状态不正确,不可以升级");
        }
        ScriptExtend scriptExtend = findScriptExtendByScriptStringId(script.getScriptStringId());
        if (scriptEditor.getIsSupperAccount()) {
            Script scriptResult = scriptService.updateOneScriptVersionById(script);
            if(scriptExtend != null){
                scriptResult.setRemark(scriptExtend.getRemark());
            }
            return scriptResult;
        }
        String ownerAccount = script.getOwnerAccount();
        if (!scriptEditor.getAccount().equals(ownerAccount)) {
            throw new AdminCheckException("当前账号没有此话术升级权限");
        }
        //设置备注 备注不用升级
        Script scriptResult = scriptService.updateOneScriptVersionById(script);
        if (scriptExtend != null) {
            scriptResult.setRemark(scriptExtend.getRemark());
        }
        return scriptResult;
    }

    @Transactional
    public void deleteOneScriptLogic(Long scriptId) {
        ScriptEditor scriptEditor = findCurrentEditor();
        Script script = getOneScriptById(scriptId);
        if (!ScriptStatusEnum.EDIT.equals(script.getStatus()) && !ScriptStatusEnum.REJECT.equals(script.getStatus())) {
            throw new ScriptCopyException("当前话术状态不正确,不可删除");
        }
        if (scriptEditor.getIsSupperAccount()) {
            scriptService.setScriptDeleteStatusById(scriptId);
            return;
        }
        if (!scriptEditor.getAccount().equals(script.getOwnerAccount())) {
            throw new AdminCheckException("当前账号没有此话术的删除权限");
        }
        scriptService.setScriptDeleteStatusById(scriptId);
    }

    public void stopOneScript(Long scriptId) {
        ScriptEditor scriptEditor = findCurrentEditor();
        if (scriptEditor.getIsSupperAccount()) {
            scriptStatusChangeService.stopOneScript(scriptId, null);
            return;
        }
        scriptStatusChangeService.stopOneScript(scriptId, scriptEditor.getAccount());
    }

    public void activeOneScript(Long scriptId) {
        ScriptEditor scriptEditor = findCurrentEditor();
        Script script = getOneScriptById(scriptId);
        if (!scriptEditor.getIsSupperAccount() && !scriptEditor.getAccount().equals(script.getOwnerAccount())) {
            throw new ScriptCheckException("你无权激活该话术");
        }
        String ownerAccount = script.getOwnerAccount();
        if (ownerAccount == null) {
            throw new ScriptCheckException("该话术没有所属账号");
        }
        int scriptNum = scriptRepository.findEditorScriptNumByAccount(ownerAccount);
        ScriptEditor scriptEditorOwnThisScript = scriptEditorRepository.findByAccount(ownerAccount);
        if (scriptEditorOwnThisScript == null) {
            throw new ScriptCheckException("该话术所属账号不是话术师,请联系管理员");
        }
        if (scriptNum >= scriptEditorOwnThisScript.getMaxScriptNum()) {
            throw new ScriptCheckException("该话术所属账户名下话术过多,请停用一些");
        }
        scriptStatusChangeService.activeOneScript(scriptId);
    }

    public void lockOneScript(Long scriptId) {
        ScriptEditor scriptEditor = findCurrentEditor();
        Script script = getOneScriptById(scriptId);
        if (!scriptEditor.getIsSupperAccount() && !scriptEditor.getAccount().equals(script.getOwnerAccount())) {
            throw new ScriptCheckException("你无权编辑这个话术");
        }
        scriptStatusChangeService.lockOneScript(scriptId);
    }

    public void unLockOneScript(Long scriptId) {
        ScriptEditor scriptEditor = findCurrentEditor();
        Script script = getOneScriptById(scriptId);
        if (!scriptEditor.getIsSupperAccount() && !scriptEditor.getAccount().equals(script.getOwnerAccount())) {
            throw new ScriptCheckException("你无权编辑这个话术");
        }
        scriptStatusChangeService.unLockOneScript(scriptId);
    }

    @Transactional
    public void editScriptWatchPermissionForEditor(Long scriptId, List<String> watchAccounts) {
        ScriptEditor scriptEditor = findCurrentEditor();
        Script script = getOneScriptById(scriptId);
        List<Script> scripts = scriptRepository.findScriptsByScriptStringIdAndIsDeleted(script.getScriptStringId(), false);
        if (!scriptEditor.getIsSupperAccount()) {
            throw new ScriptCheckException("这不是你能操作的");
        }
        if (watchAccounts == null) {
            throw new ScriptCheckException("请输入可见账号");
        }
        if (watchAccounts.contains(script.getOwnerAccount())) {
            throw new ScriptCheckException("账号:"+script.getOwnerAccount()+"为本话术的所属账号,无需设置可见");
        }
        scripts.forEach(oneScript -> {oneScript.setWatchAccounts(watchAccounts);});
        scriptRepository.saveAll(scripts);
    }

    private ScriptEditor findCurrentEditor() {
        String account = AIContext.getAccount();
        ScriptEditor scriptEditor = scriptEditorRepository.findByAccount(account);
        if (scriptEditor == null) {
            throw new ScriptCheckException("当前账户没有创建话术权限,请联系管理员");
        }
        return scriptEditor;
    }

    private Script getOneScriptById(Long scriptId) {
        Optional<Script> scriptOptional = scriptRepository.findById(scriptId);
        if (!scriptOptional.isPresent()) {
            throw new ScriptCopyException("原话术不存在");
        }
        Script script = scriptOptional.get();
        if (script.getIsDeleted()) {
            throw new ScriptCopyException("当前话术已被删除,请刷新页面");
        }        return script;
    }

    public ScriptExtend findScriptExtendByScriptStringId(String scriptStringId) {
        return scriptExtendRepository.findScriptExtendByScriptStringId(scriptStringId);
    }
}
