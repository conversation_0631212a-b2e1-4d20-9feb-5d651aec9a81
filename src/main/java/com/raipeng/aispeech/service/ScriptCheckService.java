package com.raipeng.aispeech.service;

import com.raipeng.aispeech.config.HotConfig;
import com.raipeng.aispeech.context.AIContext;
import com.raipeng.aispeech.controller.request.ScriptCheckRequest;
import com.raipeng.aispeech.entity.ScriptSmsTriggerPojo;
import com.raipeng.aispeech.exceptionadvice.exception.ScriptCheckException;
import com.raipeng.aispeech.feign.AiCallSubFeign;
import com.raipeng.aispeech.model.AIOutboundTaskTemplate;
import com.raipeng.aispeech.model.script.*;
import com.raipeng.aispeech.repository.*;
import com.raipeng.aispeech.utils.ScriptServiceUtil;
import com.raipeng.common.enums.ScriptStatusEnum;
import com.raipeng.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ScriptCheckService {
    @Autowired
    private HotConfig hotConfig;

    @Autowired
    private ScriptCheckRecordRepository scriptCheckRecordRepository;

    @Autowired
    private ScriptRepository scriptRepository;

    @Autowired
    private ScriptService scriptService;

    @Autowired
    private ScriptCorpusRepository scriptCorpusRepository;

    @Autowired
    private ScriptOperationLogRepository scriptOperationLogRepository;

    @Autowired
    private AIOutboundTaskTemplateRepository aiOutboundTaskTemplateRepository;

    @Autowired
    private AiCallSubFeign aiCallSubFeign;

    @Autowired
    private ScriptUnitContentRepository scriptUnitContentRepository;

    public void checkPreReleaseScripts() {
        List<Script> scripts = scriptRepository.findNoDeleteAndPreReleaseScripts();
        aiCallSubFeign.checkAudiosSynced(scripts.stream().map(Script::getId).collect(Collectors.toList()));

        List<String> warnScripts = new ArrayList<>();
        List<Script> scriptList = scriptRepository.findAllById(scripts.stream().map(Script::getId).collect(Collectors.toList()));
        for (Script script : scriptList) {
            ScriptStatusEnum status = script.getStatus();
            LocalDateTime updateTime = script.getUpdateTime();
            if (!ScriptStatusEnum.ACTIVE.equals(status)) {
                if (updateTime.plusMinutes(30).isBefore(LocalDateTime.now())) {
                    warnScripts.add(script.getScriptName()+":"+script.getId());
                }
            }
        }
        if (warnScripts.size() > 0) {
            log.warn("Exception=>话术长时间未生效,请检查"+String.join(";", warnScripts));
        }
    }

    public List<ScriptCheckRecord> getList(ScriptCheckRequest scriptCheckRequest){
        return scriptCheckRecordRepository.findListByCondition(scriptCheckRequest.getScriptName(),
                scriptCheckRequest.getUserAccount(),
                scriptCheckRequest.getCommitType(),
                scriptCheckRequest.getCheckRes(),
                scriptCheckRequest.getStartCommitTime(),
                scriptCheckRequest.getEndCommitTime(),
        scriptCheckRequest.getStartCheckTime(),
        scriptCheckRequest.getEndCheckTime())
                ;
    }

    public List<ScriptCheckRecord> getRecordForProcess(){
        return scriptCheckRecordRepository.findListForProcess();
    }

    @Transactional
    public ScriptCheckRecord check(Long id, String checkRes, String opinion) throws ScriptCheckException{
        ScriptCheckRecord scriptCheckRecord = scriptCheckRecordRepository.findById(id).get();
        scriptCheckRecord.setCheckRes(checkRes);
        scriptCheckRecord.setOpinion(opinion);
        Optional<Script> byId = scriptRepository.findById(scriptCheckRecord.getScriptId());
        if (!byId.isPresent()){
            System.out.println("话术审核出错："+scriptCheckRecord+","+id+","+checkRes+","+opinion);
            throw new ScriptCheckException("话术审核出错："+scriptCheckRecord+","+id+","+checkRes+","+opinion);
        }
        Script script = byId.get();
        ScriptServiceUtil.checkScriptCanBeCheck(scriptRepository, script.getId());
        ScriptOperationLog scriptOperationLog = new ScriptOperationLog();
        scriptOperationLog.setOperateType("审核");
        scriptOperationLog.setOperateContent(checkRes);
        scriptOperationLog.setOpinion(opinion);
        scriptOperationLog.setScriptStringId(script.getScriptStringId());
        scriptOperationLog.setUserAccount(AIContext.getAccount());
        scriptOperationLogRepository.save(scriptOperationLog);
        if("通过".equals(checkRes)){
            scriptService.pushAiScriptToAICall(scriptCheckRecord.getScriptStringId());
            List<ScriptCorpus> allByScriptId = scriptCorpusRepository.findAllByScriptId(scriptCheckRecord.getScriptId());
            List<ScriptUnitContent> allByCorpusId = scriptUnitContentRepository.findByCorpusIdIn(allByScriptId.stream().map(ScriptCorpus::getId).collect(Collectors.toList()));
            Set<String> smsTriggerNames = new HashSet<>();
            allByScriptId.forEach(a->{
                a.setUpdateStatus("已提交");
                if (a.getSmsTriggerName() != null) {
                    smsTriggerNames.add(a.getSmsTriggerName());
                }
            });
            allByCorpusId.forEach(a -> {
                a.setUpdateStatus("已提交");
            });
            if (hotConfig.isNeedPreview()) {
                script.setStatus(ScriptStatusEnum.PREVIEW);
                script.setLockAccount(null);
            } else {
                script.setStatus(ScriptStatusEnum.ACTIVE);
                script.setLockAccount(null);
                scriptService.afterPublishSetDeleteStatus(scriptCheckRecord.getScriptStringId());
                List<AIOutboundTaskTemplate> templates = aiOutboundTaskTemplateRepository
                        .findAIOutboundTaskTemplatesByScriptStringId(script.getScriptStringId());
                for (AIOutboundTaskTemplate template : templates) {
                    template.setSpeechCraftId(script.getId());
                    template.setSpeechCraftName(script.getScriptName());
                    template.setScriptSms(getScriptSmsTriggerPojo(template.getScriptSms(), smsTriggerNames));
                }
                aiOutboundTaskTemplateRepository.saveAll(templates);
            }
            scriptCorpusRepository.saveAll(allByScriptId);
            scriptService.checkRunningTasks(script, smsTriggerNames);
        }else{
            script.setStatus(ScriptStatusEnum.REJECT);

        }
        scriptRepository.save(script);
        scriptCheckRecordRepository.save(scriptCheckRecord);
        return scriptCheckRecord;
    }

    @Transactional
    public ScriptCheckRecord commit(ScriptCheckRecord scriptCheckRecord){
        ScriptServiceUtil.checkScriptCanBeEdit(scriptRepository, scriptCheckRecord.getScriptId());
        scriptService.checkOneScript(scriptCheckRecord.getScriptId());
        Script one = scriptRepository.getOne(scriptCheckRecord.getScriptId());
        ScriptCheckRecord firstByScriptId = scriptCheckRecordRepository.findFirstByScriptStringId(one.getScriptStringId());
        List<ScriptCorpus> scriptCorpusList = scriptCorpusRepository.findAllByScriptId(one.getId());
        ScriptOperationLog scriptOperationLog = new ScriptOperationLog();
        scriptOperationLog.setOperateType("提交");
        scriptOperationLog.setScriptStringId(one.getScriptStringId());
        one.setStatus(ScriptStatusEnum.VERIFY);
        if(firstByScriptId == null){
            firstByScriptId = scriptCheckRecord;
            firstByScriptId.setCommitType("新建");
            firstByScriptId.setCheckRes("审核中");
            scriptOperationLog.setOperateContent("新话术提交");
            firstByScriptId.setCheckRes("审核中");
        }else{
            firstByScriptId.setScriptId(scriptCheckRecord.getScriptId());
            firstByScriptId.setScriptName(scriptCheckRecord.getScriptName());
            firstByScriptId.setCommitType("修改");
            firstByScriptId.setCheckRes("审核中");
            List<String> operationCxt = new ArrayList<>();
            for(ScriptCorpus scriptCorpus : scriptCorpusList){
                if("已修改".equals(scriptCorpus.getUploadStatus())){
                    operationCxt.add(scriptCorpus.getName());
                }
            }
            if(CollectionUtils.isNotEmpty(operationCxt)){
                scriptOperationLog.setOperateContent("修改语料："+String.join(",", operationCxt));
            }
        }
        firstByScriptId.setScriptStringId(one.getScriptStringId());
        firstByScriptId.setScriptName(one.getScriptName());
        firstByScriptId.setScriptVersion(one.getVersion());
        scriptOperationLog.setUserAccount(AIContext.getAccount());
        scriptOperationLogRepository.save(scriptOperationLog);
        scriptRepository.save(one);
        return scriptCheckRecordRepository.save(firstByScriptId);
    }

    private List<ScriptSmsTriggerPojo> getScriptSmsTriggerPojo(List<ScriptSmsTriggerPojo> entities, Set<String> triggerNames) {
        if (triggerNames == null || triggerNames.size() == 0) {
            return new ArrayList<>();
        }
        if (entities == null || entities.size() == 0) {
            entities = new ArrayList<>();
            for (String triggerName : triggerNames) {
                entities.add(ScriptSmsTriggerPojo.builder().triggerName(triggerName).build());
            }
        }
        List<String> oldTriggerNames = entities.stream().map(ScriptSmsTriggerPojo::getTriggerName).collect(Collectors.toList());
        for (String triggerName : triggerNames) {
            if (!oldTriggerNames.contains(triggerName)) {
                entities.add(ScriptSmsTriggerPojo.builder().triggerName(triggerName).build());
            }
        }
        for (String oldTriggerName : oldTriggerNames) {
            if (!triggerNames.contains(oldTriggerName)) {
                entities.removeIf(entity -> entity.getTriggerName().equals(oldTriggerName));
            }
        }
        return entities;
    }

    public void updateAudioTag(Long id, String audioTag) {
        // 增加话术状态校验
        scriptUnitContentRepository.findById(id).ifPresent(unitContent -> {
            scriptRepository.findById(unitContent.getScriptId() ).ifPresent(script -> {
                if (!script.getStatus().equals(ScriptStatusEnum.EDIT) && !script.getStatus().equals(ScriptStatusEnum.REJECT)) {
                    throw new ScriptCheckException("话术非编辑状态，无法修改验听标签");
                }
            });
            if (StringUtils.isEmpty(audioTag)) {
                scriptUnitContentRepository.updateAudioTag(id, "1", null);
            } else {
                scriptUnitContentRepository.updateAudioTag(id, "2", audioTag);
            }
        });
    }
}
