package com.raipeng.aispeech.service;


import com.raipeng.aispeech.exceptionadvice.exception.ScriptCheckException;
import com.raipeng.aispeech.model.script.ScriptCorpus;
import com.raipeng.aispeech.model.script.ScriptMultiContent;
import com.raipeng.aispeech.model.script.ScriptUnitContent;
import com.raipeng.aispeech.repository.ScriptMultiContentRepository;
import com.raipeng.aispeech.repository.ScriptUnitContentRepository;
import com.raipeng.common.enums.ConnectTypeEnum;
import com.raipeng.common.enums.InterruptType;
import com.raipeng.common.enums.ScriptCorpusTypeEnum;
import com.raipeng.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.IOException;
import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 多语句相关操作
 */
@Slf4j
@Service
public class ScriptMultiContentService {
    @Autowired
    private ScriptMultiContentRepository scriptMultiContentRepository;

    @Autowired
    private ScriptUnitContentRepository scriptUnitContentRepository;

    @Autowired
    private ScriptCorpusService scriptCorpusService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private AsrProcessService asrProcessService;

    private final String splitChars = "~~";

    /**
     * 添加多段语句
     *
     * @param multiContents 多段语句参数
     * @param corpus        语料
     * @return 多段语句集合
     */
    public List<ScriptMultiContent> addMultiContents(List<ScriptMultiContent> multiContents, ScriptCorpus corpus) {
        ScriptCorpusTypeEnum corpusType = corpus.getCorpusType();
        // 1. 连接语料的连接类型不是挂机时不需要进行语句的操作
        if ((ScriptCorpusTypeEnum.KNOWLEDGE_CONNECT.equals(corpusType) || ScriptCorpusTypeEnum.MASTER_CONNECT.equals(corpusType)) && !ConnectTypeEnum.HANG_UP.equals(corpus.getConnectType())) {
            return null;
        }
        if (CollectionUtils.isEmpty(multiContents)) {
            throw new ScriptCheckException("没有多段文本输入");
        }
        if (ScriptCorpusTypeEnum.PRE_INTERRUPT.equals(corpusType) || ScriptCorpusTypeEnum.PRE_CONTINUE.equals(corpusType) || ScriptCorpusTypeEnum.PRE_UNDERTAKE.equals(corpusType)) {
            if (multiContents.size() > 1 || multiContents.get(0).getScriptUnitContents().size() > 1) {
                throw new ScriptCheckException("预置语料只允许一条语句");
            }
        }
        List<ScriptMultiContent> multiContentSaveList = new ArrayList<>();
        multiContents.forEach(multiContent -> {
            List<ScriptUnitContent> unitContents = multiContent.getScriptUnitContents();
            if (CollectionUtils.isEmpty(unitContents)) {
                throw new ScriptCheckException("语句内容为空");
            }
            int size = unitContents.size();
            multiContent.setMaxNameSuffix(size);
            multiContent.setCorpusId(corpus.getId());

            ScriptMultiContent multiContentSave = scriptMultiContentRepository.save(multiContent);

            int nameSuffix = 1;
            for (ScriptUnitContent unitContent : unitContents) {
                if (StringUtils.isEmpty(unitContent.getContent())) {
                    throw new ScriptCheckException("语句文本为空");
                }
                unitContent.setCorpusId(corpus.getId());
                unitContent.setMultiContentId(multiContentSave.getId());
                unitContent.setScriptId(corpus.getScriptId());
                unitContent.setInterruptType(InterruptType.CAN_NOT_BE_INTERRUPTED);
                unitContent.setCorpusType(corpusType);
                unitContent.setContentName(generateNextName(corpus.getName(), nameSuffix));
                nameSuffix += 1;
            }
            List<ScriptUnitContent> unitContentListSave = scriptUnitContentRepository.saveAll(unitContents);
            if (CollectionUtils.isNotEmpty(unitContentListSave)) {
                unitContentListSave.sort(Comparator.comparing(ScriptUnitContent::getOrders));
            }
            multiContentSave.setScriptUnitContents(unitContentListSave);
            multiContentSaveList.add(multiContentSave);
        });
        return multiContentSaveList;
    }

    private String generateNextName(String baseName, int nameSuffix) {
        return nameSuffix == 1 ? baseName : baseName + splitChars + nameSuffix;
    }

    /**
     * 更新多段语句
     *
     * @param multiContents  多段语句参数
     * @param corpus         语料
     * @param needChangeName 是否更名
     * @return 多段语句集合
     */
    public List<ScriptMultiContent> updateMultiContents(List<ScriptMultiContent> multiContents, ScriptCorpus corpus, boolean needChangeName, String changeName) {
        ScriptCorpusTypeEnum corpusType = corpus.getCorpusType();
        // 连接语料的连接类型不是挂机时不需要进行语句的操作
        if ((ScriptCorpusTypeEnum.KNOWLEDGE_CONNECT.equals(corpusType) || ScriptCorpusTypeEnum.MASTER_CONNECT.equals(corpusType)) && !ConnectTypeEnum.HANG_UP.equals(corpus.getConnectType())) {
            return null;
        }
        //语料名称变化则批量修改语句名称
        List<ScriptUnitContent> unitContents = new ArrayList<>();
        if (needChangeName) {
            unitContents = batchUpdateBaseName(corpus.getId(), changeName);
        }

        // 查找所有非删除状态的ScriptMultiContent
        Map<Long, ScriptMultiContent> multiContentMap = scriptMultiContentRepository.findAllByCorpusIdAndIsDeletedOrderByOrders(corpus.getId(), false).stream().collect(Collectors.toMap(ScriptMultiContent::getId, e -> e));

        List<ScriptMultiContent> scriptMultiContents = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(multiContents)) {
            for (ScriptMultiContent multiContent : multiContents) {
                ScriptMultiContent multiContentToSave = multiContentMap.get(multiContent.getId());
                if (multiContentToSave == null) {
                    //连接语料挂机类型有语句，其他连接类型没有，当编辑时修改为挂机类型时语句属于新增，没有id
                    if ((corpusType.equals(ScriptCorpusTypeEnum.KNOWLEDGE_CONNECT) || corpusType.equals(ScriptCorpusTypeEnum.MASTER_CONNECT)) && ConnectTypeEnum.HANG_UP.equals(corpus.getConnectType())) {
                        //保存该部分并重新获取
                        addMultiContents(multiContents, corpus);
                        multiContentToSave = scriptMultiContentRepository.findFirstById(multiContent.getId());
                    } else {
                        throw new ScriptCheckException("多文本对象不存在， id：" + multiContent.getId());
                    }
                }

                //直接删除
                multiContentToSave.setDeleted(multiContent.isDeleted());
                if (multiContent.isDeleted()) {
                    deleteMultiContent(multiContent);
                    continue;
                }
                // 更新多文本框属性
                multiContentToSave.setOrders(multiContent.getOrders());

                // 处理内容
                ScriptMultiContent scriptMultiContent = processContents(multiContentToSave, multiContent, unitContents, corpusType, changeName);
                scriptMultiContents.add(scriptMultiContent);
            }
        }
        return scriptMultiContents;
    }

    private void deleteMultiContent(ScriptMultiContent multiContent) {
        scriptMultiContentRepository.deleteById(multiContent.getId());
        scriptUnitContentRepository.deleteByMultiContentId(multiContent.getId());
    }

    private ScriptMultiContent processContents(ScriptMultiContent multiContentToSave, ScriptMultiContent multiContent, List<ScriptUnitContent> unitContents, ScriptCorpusTypeEnum corpusType, String changeName) {
        Map<Long, ScriptUnitContent> updateMap = multiContent.getScriptUnitContents().stream().filter(content -> Objects.nonNull(content.getId())).collect(Collectors.toMap(ScriptUnitContent::getId, e -> e));  // 只保留ID不为空的对象

        if (CollectionUtils.isEmpty(unitContents)) {
            unitContents = scriptUnitContentRepository.findAllById(multiContent.getScriptUnitContents().stream().map(ScriptUnitContent::getId).filter(Objects::nonNull).collect(Collectors.toList()));
        }

        List<ScriptUnitContent> saveContentEntities = new ArrayList<>();
        List<ScriptUnitContent> deleteUnitContents = new ArrayList<>();
        for (ScriptUnitContent unitContent : unitContents) {
            ScriptUnitContent unitContentDTO = updateMap.get(unitContent.getId());
            if (unitContentDTO != null) {
                if (unitContentDTO.isDeleted()) {
                    deleteUnitContents.add(unitContent);
                    continue;
                }
                unitContent.setContentType(unitContentDTO.getContentType());
                unitContent.setOrders(unitContentDTO.getOrders());
                //比较数据库中和要更新中的content是否变化，如果变化，那么需要更新验听状态且重新识别一次
                if (!unitContentDTO.getContent().equals(unitContent.getContent())) {
                    unitContent.setIsPlayed(false);
                    unitContent.setAudioStatus("0");
                    unitContent.setAudioTag(null);
                    unitContent.setContent(unitContentDTO.getContent());
                    asrProcessService.processReCalculate(unitContent);
                }
            }
            saveContentEntities.add(unitContent);
        }
        //如果有要删除的应该优先删除，否则影响排序结果
        if (CollectionUtils.isNotEmpty(deleteUnitContents)) {
            scriptUnitContentRepository.deleteAll(deleteUnitContents);
        }
        scriptUnitContentRepository.saveAll(saveContentEntities);

        int maxSuffix = multiContentToSave.getMaxNameSuffix();

        //找出 ID 为空的对象，进行插入操作
        List<ScriptUnitContent> newContentEntities = new ArrayList<>();
        for (ScriptUnitContent content : multiContent.getScriptUnitContents()) {
            if (content.getId() == null) {
                content.setCorpusId(multiContent.getCorpusId());
                content.setMultiContentId(multiContent.getId());
                content.setCorpusType(corpusType);
                maxSuffix += 1;
                content.setContentName(generateNextName(changeName, maxSuffix));
                content.setInterruptType(InterruptType.CAN_NOT_BE_INTERRUPTED);
                content.setScriptId(multiContent.getScriptId());
                newContentEntities.add(content);  // 添加需要插入的对象
            }
        }
        scriptUnitContentRepository.saveAll(newContentEntities);

        multiContentToSave.setMaxNameSuffix(maxSuffix);
        scriptMultiContentRepository.saveAndFlush(multiContentToSave);

        ScriptMultiContent scriptMultiContent = new ScriptMultiContent();
        BeanUtils.copyProperties(multiContentToSave, scriptMultiContent);
        List<ScriptUnitContent> units = Stream.concat(saveContentEntities.stream(), newContentEntities.stream()).collect(Collectors.toList());
        units.sort(Comparator.comparing(ScriptUnitContent::getOrders));
        scriptMultiContent.setScriptUnitContents(units);
        return scriptMultiContent;
    }

    public List<ScriptUnitContent> batchUpdateBaseName(Long corpusId, String newBaseName) {
        List<ScriptUnitContent> contents = scriptUnitContentRepository.findByCorpusId(corpusId);

        // 新名称列表
        List<ScriptUnitContent> newNames = new ArrayList<>();
        for (ScriptUnitContent content : contents) {
            // 获取当前内容的基础名称
            String currentBaseName = content.getContentName();
            String newName = generateUpdatedName(currentBaseName, newBaseName); // 生成新名称

            //处理音频文件
            MultipartFile file = getFile(content, newName);
            try {
                if (file != null) {
                    scriptCorpusService.importAudioFiles(file, content.getId());
                }
            } catch (IOException e) {
                throw new ScriptCheckException("更新语料名时上传音频文件失败: " + content.getContentName());
            }
            content.setContentName(newName);
            newNames.add(content); // 保存新名称
        }
        return newNames;
    }

    private MultipartFile getFile(ScriptUnitContent content, String newName) {
        String audioName = getAudioName(newName);
        if (audioName != null && StringUtils.isNotEmpty(content.getAudioPath())) {
            ResponseEntity<byte[]> response = restTemplate.getForEntity(content.getAudioPath(), byte[].class);
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                // 调用转换方法
                MultipartFile multipartFile = bytesToMultipartFile(
                        response.getBody(),
                        audioName,
                        "wav"
                );
                return multipartFile;
            }
        }
        return null;
    }

    private static MultipartFile bytesToMultipartFile(byte[] bytes, String fileName, String contentType) {
        DiskFileItemFactory factory = new DiskFileItemFactory();
        FileItem fileItem = factory.createItem("file", contentType, false, fileName);
        try (OutputStream os = fileItem.getOutputStream()) {
            os.write(bytes); // 直接将字节数组写入输出流
        } catch (IOException e) {
            log.error("字节流转换为MultipartFile失败", e);
            return null;
        }
        return new CommonsMultipartFile(fileItem);
    }

    private String getAudioName(String newName) {
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(newName)) {
            return newName + ".wav";
        }
        return null;
    }

    private String generateUpdatedName(String currentName, String newBaseName) {
        // 如果当前名称以数字后缀结束，查找后缀
        if (currentName.contains(splitChars)) {
            // obtain the index to split the base name and the suffix
            int index = currentName.indexOf(splitChars);
            return newBaseName + currentName.substring(index);
        } else {
            return newBaseName; // 否则返回新的基础名称
        }
    }

    /**
     * 获取语料的多段语句
     *
     * @param corpus 语料
     * @return 语料的多段语句集合
     */
    public List<ScriptMultiContent> getMultiContentsByCorpus(ScriptCorpus corpus) {
        List<ScriptMultiContent> multiContents = scriptMultiContentRepository.findByCorpusId(corpus.getId());
        List<Long> multiContentIds = multiContents.stream().map(ScriptMultiContent::getId).collect(Collectors.toList());
        List<ScriptUnitContent> scriptUnitContentList = scriptUnitContentRepository.findAllByMultiContentIdInAndIsDeleted(multiContentIds, false);
        Map<Long, List<ScriptUnitContent>> multiContentIdToUnitsMap = scriptUnitContentList.stream().collect(Collectors.groupingBy(ScriptUnitContent::getMultiContentId, Collectors.toList()));
        multiContents.forEach(multiContent -> {
            List<ScriptUnitContent> unitContents = multiContentIdToUnitsMap.get(multiContent.getId());
            if (CollectionUtils.isNotEmpty(unitContents)) {
                unitContents.sort(Comparator.comparing(ScriptUnitContent::getOrders));
            }
            multiContent.setScriptUnitContents(unitContents);
        });
        return multiContents;
    }

    /**
     * 获取语料集合的多段语句map
     *
     * @param corpusList 语料集合
     * @return key:语料ID, value:该语料的多段语句集合
     */
    public Map<Long, List<ScriptMultiContent>> getMultiContentMapByCorpusList(List<ScriptCorpus> corpusList) {
        List<Long> corpusIds = corpusList.stream().map(ScriptCorpus::getId).collect(Collectors.toList());
        List<ScriptMultiContent> multiContents = scriptMultiContentRepository.findAllByCorpusIdInAndIsDeletedOrderByOrders(corpusIds, false);
        return multiContents.stream().collect(Collectors.groupingBy(ScriptMultiContent::getCorpusId));
    }

    /**
     * 根据语料获取带多段语句的语料
     *
     * @param corpus 语料
     * @return 带多段语句的语料
     */
    public ScriptCorpus getCorpusWithContent(ScriptCorpus corpus) {
        corpus.setScriptMultiContents(getMultiContentsByCorpus(corpus));
        return corpus;
    }

    /**
     * 根据语料集合获取带多段语句的语料集合
     *
     * @param corpusList 语料集合
     * @return 带多段语句的语料集合
     */
    public List<ScriptCorpus> getCorpusListWithContent(List<ScriptCorpus> corpusList) {
        long startTime = System.currentTimeMillis();
        Map<Long, ScriptCorpus> corpusMap = corpusList.stream()
                .collect(Collectors.toMap(ScriptCorpus::getId, corpus -> corpus, (existing, replacement) -> existing, LinkedHashMap::new));
        List<Long> corpusIds = new ArrayList<>(corpusMap.keySet());
        List<ScriptMultiContent> multiContents = scriptMultiContentRepository.findAllByCorpusIdInAndIsDeleted(corpusIds, false);
        List<ScriptUnitContent> scriptUnitContentList = scriptUnitContentRepository.findAllByCorpusIdInAndIsDeletedOrderByOrders(corpusIds, false);
        Map<Long, List<ScriptUnitContent>> multiContentIdToUnitsMap = scriptUnitContentList.stream().collect(Collectors.groupingBy(ScriptUnitContent::getMultiContentId, LinkedHashMap::new, Collectors.toList()));
        // 对 multiContents 进行处理并添加到对应的 ScriptCorpus 中
        for (ScriptMultiContent multiContent : multiContents) {
            List<ScriptUnitContent> unitContents = multiContentIdToUnitsMap.get(multiContent.getId());
            multiContent.setScriptUnitContents(unitContents);
            // 获取scriptCorpus后处理，确保scriptMultiContents初始化
            ScriptCorpus scriptCorpus = corpusMap.get(multiContent.getCorpusId());
            if (scriptCorpus.getScriptMultiContents() == null) {
                scriptCorpus.setScriptMultiContents(new ArrayList<>());
            }
            scriptCorpus.getScriptMultiContents().add(multiContent);
        }
        log.info("修改后方法getCorpusListWithContent耗时:" + (System.currentTimeMillis() - startTime));
        // 直接返回 LinkedHashMap 的值，保持原始顺序
        return new ArrayList<>(corpusMap.values());
    }

    /**
     * 物理删除多段语句
     *
     * @param corpusList 语料集合
     */
    public void deleteMultiContentByCorpusList(List<ScriptCorpus> corpusList) {
        List<Long> corpusIds = corpusList.stream().map(ScriptCorpus::getId).collect(Collectors.toList());
        scriptMultiContentRepository.deleteAllByCorpusIdIn(corpusIds);
        scriptUnitContentRepository.deleteAllByCorpusIdIn(corpusIds);
    }
}
