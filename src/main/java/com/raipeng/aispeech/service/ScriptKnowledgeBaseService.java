package com.raipeng.aispeech.service;

import com.raipeng.aispeech.controller.request.scriptedit.KnowledgeBaseQACorpusBatchUpdateDTO;
import com.raipeng.aispeech.controller.request.scriptedit.KnowledgeBaseQACorpusDTO;
import com.raipeng.aispeech.controller.request.scriptedit.KnowledgeConnectCorpusDTO;
import com.raipeng.aispeech.controller.request.scriptedit.KnowledgeOrdinaryCorpusDTO;
import com.raipeng.aispeech.controller.response.Response;
import com.raipeng.aispeech.exceptionadvice.exception.ScriptCheckException;
import com.raipeng.aispeech.model.AIIntentionType;
import com.raipeng.aispeech.model.AILabel;
import com.raipeng.aispeech.model.AdvancedRules;
import com.raipeng.aispeech.model.script.*;
import com.raipeng.aispeech.repository.*;
import com.raipeng.aispeech.utils.BeanUtils;
import com.raipeng.aispeech.utils.ScriptSaveUtil;
import com.raipeng.aispeech.utils.ScriptServiceUtil;
import com.raipeng.aispeech.utils.ScriptUtil;
import com.raipeng.common.enums.QueryType;
import com.raipeng.common.enums.ScriptCorpusTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.raipeng.common.enums.ConnectTypeEnum.SELECT_MASTER_PROCESS;

@Slf4j
@Service
@RefreshScope
public class ScriptKnowledgeBaseService {
    @Autowired
    private ScriptCorpusRepository scriptCorpusRepository;

    @Autowired
    private ScriptBranchRepository scriptBranchRepository;

    @Autowired
    private ScriptSaveUtil scriptSaveUtil;

    @Autowired
    private AdvancedRulesRepository advancedRulesRepository;

    @Autowired
    private AdvancedRulesService advancedRulesService;

    @Autowired
    private ScriptRepository scriptRepository;

    @Autowired
    private ScriptMultiContentService scriptMultiContentService;

    @Autowired
    private ScriptMultiContentRepository scriptMultiContentRepository;

    @Autowired
    private ScriptUnitContentRepository scriptUnitContentRepository;

    @Autowired
    private AILabelRepository aiLabelRepository;

    @Autowired
    private ScriptPreCorpusService scriptPreCorpusService;

    @Autowired
    private AIIntentionTypeRepository aiIntentionTypeRepository;

    @Autowired
    private KnowledgeGroupRepository knowledgeGroupRepository;

    @Autowired
    private EventTriggerForScriptRepository eventTriggerForScriptRepository;

    @Value("${script.audio.upload.path:/static/file/ai/audio_record}")
    private String fileNewUploadPath;

    @Value("${script.audio.download.path:https://uat.bountech.com/marketfront/file/ai-speech/ai/audio_record}")
    private String fileNewDownloadPath;

    @Transactional(rollbackFor = Exception.class)
    public ScriptCorpus saveKnowledgeBaseQA(KnowledgeBaseQACorpusDTO corpusDTO) {
        ScriptServiceUtil.checkScriptCanBeEdit(scriptRepository, corpusDTO.getScriptId());
        if (corpusDTO.getId() == null) {
            if (null == corpusDTO.getQueryType()) {
                corpusDTO.setQueryType(QueryType.INDUSTRY_QUERY);
            }
            return scriptSaveUtil.addFuncOrConnectOrKnowledgeQACorpus(corpusDTO, ScriptCorpusTypeEnum.KNOWLEDGE_BASE_QA);
        } else {
            return scriptSaveUtil.updateFuncOrConnectOrKnowledgeQACorpus(corpusDTO);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public ScriptCorpus saveKnowledgeOrdinaryCorpus(KnowledgeOrdinaryCorpusDTO corpusDTO) {
        ScriptServiceUtil.checkScriptCanBeEdit(scriptRepository, corpusDTO.getScriptId());
        if (corpusDTO.getId() == null) {
            return addKnowledgeOrdinaryCorpus(corpusDTO);
        } else {
            return scriptSaveUtil.updateOrdinaryCorpus(corpusDTO, ScriptCorpusTypeEnum.KNOWLEDGE_ORDINARY);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public ScriptCorpus saveKnowledgeConnectCorpus(KnowledgeConnectCorpusDTO corpusDTO) {
        ScriptServiceUtil.checkScriptCanBeEdit(scriptRepository, corpusDTO.getScriptId());
        if (corpusDTO.getId() == null) {
            return scriptSaveUtil.addFuncOrConnectOrKnowledgeQACorpus(corpusDTO, ScriptCorpusTypeEnum.KNOWLEDGE_CONNECT);
        } else {
            return scriptSaveUtil.updateFuncOrConnectOrKnowledgeQACorpus(corpusDTO);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateKnowledgeBaseQA(KnowledgeBaseQACorpusBatchUpdateDTO batchUpdateDTO) {
        if(batchUpdateDTO.getCorpusIds() == null || batchUpdateDTO.getScriptId() == null){
            throw new IllegalArgumentException("语料ID 或 话术ID 为空");
        }
        ScriptServiceUtil.checkScriptCanBeEdit(scriptRepository, batchUpdateDTO.getScriptId());
        log.info("知识库批量设置 {} ", batchUpdateDTO);
        for(Long corpusId : batchUpdateDTO.getCorpusIds()){
            Optional<ScriptCorpus> corpusOptional = scriptCorpusRepository.findById(corpusId);
            if (!corpusOptional.isPresent()) {
                throw new ScriptCheckException("语料不存在");
            }
            ScriptCorpus corpus = corpusOptional.get();
            // 更新语境类型相关字段（三者联动）
            if(batchUpdateDTO.getIsOpenContext() != null ||  batchUpdateDTO.getOpenScopeType() != null
                    || batchUpdateDTO.getGroupOpenScope() != null){
                corpus.setIsOpenContext(batchUpdateDTO.getIsOpenContext());
                corpus.setOpenScopeType(batchUpdateDTO.getOpenScopeType());
                corpus.setGroupOpenScope(batchUpdateDTO.getGroupOpenScope());
            }
            // 更新触发转人工
            if(batchUpdateDTO.getListenInOrTakeOver() != null){
                corpus.setListenInOrTakeOver(batchUpdateDTO.getListenInOrTakeOver());
            }
            // 更新触发短信
            if(batchUpdateDTO.getSmsTriggerName() != null){
                corpus.setSmsTriggerName(batchUpdateDTO.getSmsTriggerName());
            }
            // 更新最长等待时间
            if(batchUpdateDTO.getMaxWaitingTime() != null){
                corpus.setMaxWaitingTime(batchUpdateDTO.getMaxWaitingTime());
            }
            // 更新意向标签
            if(batchUpdateDTO.getAiLabels() != null) {
                if(batchUpdateDTO.getAiLabels().isEmpty()){
                    corpus.setAiLabels(null);
                }else{
                corpus.setAiLabels(batchUpdateDTO.getAiLabels());
            }
            }
            // 更新意向分类
            if(batchUpdateDTO.getAiIntentionType() != null){
                if(batchUpdateDTO.getAiIntentionType().getId() == null){
                    corpus.setAiIntentionType(null);
                }else {
                    corpus.setAiIntentionType(batchUpdateDTO.getAiIntentionType());
                }
            }
            scriptCorpusRepository.save(corpus);
        }
        return batchUpdateDTO.getCorpusIds().size();
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteKnowledgeQAScriptCorpusByIds(List<Long> knowledgeQAIds, Long scriptId) {
        ScriptServiceUtil.checkScriptCanBeEdit(scriptRepository, scriptId);
        // 高级规则和最终意向的同步删除功能
        advancedRulesService.deleteAdvanceRulesConditionByCorpusIds(scriptId, knowledgeQAIds);
        List<ScriptCorpus> corpusList = scriptCorpusRepository.findAllByIdInAndScriptId(knowledgeQAIds, scriptId);
        scriptMultiContentService.deleteMultiContentByCorpusList(corpusList);
        scriptCorpusRepository.deleteAll(corpusList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteAllKnowledgeQAByScriptId(Long scriptId) {
        ScriptServiceUtil.checkScriptCanBeEdit(scriptRepository, scriptId);
        List<ScriptCorpus> corpusList = scriptCorpusRepository.findAllByCorpusTypeAndScriptId(ScriptCorpusTypeEnum.KNOWLEDGE_BASE_QA, scriptId);
        // 高级规则和最终意向的同步删除功能
        advancedRulesService.deleteAdvanceRulesConditionByCorpusIds(scriptId, corpusList.stream().map(ScriptCorpus::getId).collect(Collectors.toList()));
        scriptMultiContentService.deleteMultiContentByCorpusList(corpusList);
        scriptCorpusRepository.deleteAll(corpusList);
    }

    private ScriptCorpus addKnowledgeOrdinaryCorpus(KnowledgeOrdinaryCorpusDTO corpusDTO) {
        ScriptCorpus scriptCorpus = new ScriptCorpus();

        // 1. 检测重名
        ScriptServiceUtil.checkRepeatCorpusName(scriptCorpusRepository, corpusDTO);

        // 2. 判断是头节点
        if (corpusDTO.getIsHead()) {
            scriptCorpus.setWeight(findMaxKnowledgeBaseWeightOfGroup(corpusDTO.getKnowledgeGroupId()) + 1);
            scriptCorpus.setIsKnowledgeBase(true);
        }

        // 3. 保存分支
        List<ScriptBranch> branchList = corpusDTO.getBranchList();

        // 4. 检测分支重复
        ScriptUtil.checkBranchListContentRepeat(branchList);

        // 5. 保存分支
        if (branchList != null && !branchList.isEmpty()) {
            for (ScriptBranch scriptBranch : branchList) {
                if (scriptBranch.getId() == null) {
                    scriptBranch.setIsPublished(false);
                    scriptBranch.setScriptId(corpusDTO.getScriptId());
                    ScriptBranch newBranch = scriptBranchRepository.save(scriptBranch);
                    scriptBranch.setId(newBranch.getId());
                }
            }
        }

        // 6. 保存语料
        BeanUtils.copyProperties(corpusDTO, scriptCorpus);
        scriptCorpus.setCorpusType(ScriptCorpusTypeEnum.KNOWLEDGE_ORDINARY);
        scriptCorpus.setReturnPlayDefault(true);
        scriptSaveUtil.updateCorpusSemanticIdsInUse(corpusDTO, scriptCorpus);
        ScriptCorpus corpusSave = scriptCorpusRepository.save(scriptCorpus);

        // 7. 记录分支头结点
        if (branchList != null && branchList.size() > 0) {
            for (ScriptBranch scriptBranch : branchList) {
                scriptBranch.setPreCorpusId(corpusSave.getId());
                scriptSaveUtil.updateBranchSemanticIdsInUse(scriptBranch);
                scriptBranchRepository.save(scriptBranch);
            }
        }
        corpusSave.setScriptMultiContents(scriptMultiContentService.addMultiContents(corpusDTO.getScriptMultiContents(), corpusSave));
        return corpusSave;
    }

    private void deleteAdvanceRulesByCorpusIds(Long scriptId, List<Long> knowledgeQAIds) {
        List<AdvancedRules> advancedRules = advancedRulesRepository.findAllByScriptId(scriptId);
        if (advancedRules != null && advancedRules.size() > 0) {
            advancedRules.forEach(advancedRule -> {
                String hitAnswerIds = advancedRule.getHitAnswerIds();
                if (StringUtils.isNotEmpty(hitAnswerIds)) {
                    List<String> ids = new ArrayList<>(Arrays.asList(hitAnswerIds.split(",")));
                    ids.removeAll(knowledgeQAIds.stream().map(String::valueOf).collect(Collectors.toList()));
                    if (ids.size() == 0) {
                        advancedRule.setHitAnswerIds(null);
                    } else {
                        advancedRule.setHitAnswerIds(String.join(",", ids));
                    }
                    advancedRulesRepository.save(advancedRule);
                }
            });
        }
    }

    //<editor-fold desc="知识库分组相关">
    public List<ScriptCorpus> findAllKnowledgeBaseByScriptId(Long id) {
        return scriptMultiContentService.getCorpusListWithContent(findAllKnowledgeBaseByScriptIdNoContent(id));
    }

    private List<ScriptCorpus> findAllKnowledgeBaseByScriptIdNoContent(Long id) {
        return scriptCorpusRepository.findAllByScriptIdAndIsKnowledgeBaseAndIsDeletedOrderByWeightAsc(id, true, false);
    }

    // 组内独立判断权重
    public int findMaxKnowledgeBaseWeightOfGroup(Long id) {
        List<ScriptCorpus> allKnowledgeBaseList = findAllKnowledgeBaseByGroupIdNoContent(id, false);
        if (allKnowledgeBaseList != null && !allKnowledgeBaseList.isEmpty()) {
            return  allKnowledgeBaseList.stream().map(ScriptCorpus::getWeight).max(Comparator.naturalOrder()).orElse(0);
        } else {
            return 0;
        }
    }

    public List<ScriptCorpus> batchMove(Long gid, List<Long> ids) {
        List<ScriptCorpus> allByIds = findAllByIds(ids);
        AtomicInteger max = new AtomicInteger(findMaxKnowledgeBaseWeightOfGroup(gid));
        allByIds.forEach(
                item -> {
                    item.setKnowledgeGroupId(gid);
                    item.setWeight(max.incrementAndGet());
                }
        );
        return scriptCorpusRepository.saveAll(allByIds);
    }

    private static String getNewPath(String path, String oldScriptStringId, String newScriptStringId, boolean isCopy, String newName) {
        String newPath = path.replace(oldScriptStringId, newScriptStringId);
        String[] split = newPath.split("_");
        if (split.length > 0 && split[split.length - 1].endsWith(".wav")) {
            if (isCopy) {
                newPath.replace(split[split.length - 1], "0.wav");
                return newPath.replace(split[split.length - 2], newName);
            } else {
                return newPath.replace(split[split.length - 1], "0.wav");
            }
        } else {
            throw new ScriptCheckException("批量导入基本问答中音频格式有误，请联系管理员");
        }
    }

    private void checkNameUnique(Long targetGid, Set<String> newName) {
        KnowledgeGroup group = knowledgeGroupRepository.findById(targetGid).orElseThrow(() -> new IllegalArgumentException("该分组不存在"));
        Set<String> allName = scriptCorpusRepository.findAllByScriptId(group.getScriptId())
                .stream()
                .map(ScriptCorpus::getName)
                .collect(Collectors.toSet());
        Assert.isTrue(Collections.disjoint(newName, allName), "存在名称重复");
    }

    public List<ScriptCorpus> batchCopy(Long targetGid, Map<Long, String> pairs) {
        Set<String> newName = new HashSet<>(pairs.values());
        Assert.isTrue(newName.size() == pairs.size(), "重命名时存在名称重复");
        checkNameUnique(targetGid, newName);
        final String[] scriptStringId = {null};
        Map<Long, ScriptCorpus> oldIdNewCorpusMap = new LinkedHashMap<>();
        AtomicInteger max = new AtomicInteger(findMaxKnowledgeBaseWeightOfGroup(targetGid));
        pairs.forEach((sourceCorpusId, value) -> {
            ScriptCorpus oldItem = findById(sourceCorpusId);
            ScriptCorpus newItem = SerializationUtils.clone(oldItem);
            newItem.setId(null);
            newItem.setKnowledgeGroupId(targetGid);
            newItem.setName(value);
            newItem.setWeight(max.incrementAndGet());
            oldIdNewCorpusMap.put(sourceCorpusId, newItem);
            if (scriptStringId[0] == null) {
                scriptStringId[0] = scriptRepository.findFirstById(oldItem.getScriptId()).getScriptStringId();
            }
        });

        List<ScriptCorpus> savedCorpuses = scriptCorpusRepository.saveAll(oldIdNewCorpusMap.values());
        //batchCopyKnowledgeQA
        int[] index = {0};
        oldIdNewCorpusMap.forEach((k, v) -> {
            oldIdNewCorpusMap.computeIfPresent(k, (key, value) -> savedCorpuses.get(index[0]++));
        });

        copyMultiContents(savedCorpuses, oldIdNewCorpusMap, pairs, scriptStringId[0], scriptStringId[0], null, null);

        return savedCorpuses;
    }

    private void copyMultiContents(List<ScriptCorpus> savedCorpuses, Map<Long, ScriptCorpus> oldIdNewCorpusMap, Map<Long, String> pairs, String oldScriptStringId, String newScriptStringId,
                                   Map<String, Long> interruptBaseNameMap, Map<String, Long> continueBaseNameMap) {
        //多语句copy
        if (CollectionUtils.isEmpty(savedCorpuses)) {
            return;
        }
        Script script = scriptRepository.getOne(savedCorpuses.get(0).getScriptId());
        Map<Long, ScriptMultiContent> multiContentMap = ScriptServiceUtil.copyMultiContentReturnMultiMap(script.getId(), oldIdNewCorpusMap, scriptMultiContentRepository);
        copyUnitContentReturnContentMap(script.getId(), oldIdNewCorpusMap, multiContentMap, pairs, oldScriptStringId, newScriptStringId, interruptBaseNameMap, continueBaseNameMap);
    }

    private void copyUnitContentReturnContentMap(Long newScriptId, Map<Long, ScriptCorpus> oldIdNewCorpusMap, Map<Long, ScriptMultiContent> multiContentMap, Map<Long, String> pairs,
                                                 String oldScriptStringId, String newScriptStringId, Map<String, Long> interruptBaseNameMap, Map<String, Long> continueBaseNameMap) {
        List<Long> multiContentIds = new ArrayList<>(multiContentMap.keySet());
        List<ScriptUnitContent> unitContents = scriptUnitContentRepository.findAllByMultiContentIdInAndIsDeleted(multiContentIds, false);

        List<ScriptUnitContent> newUnitContents = new ArrayList<>();
        unitContents.forEach(unitContent -> {
            boolean isCopy = false;
            ScriptUnitContent newScriptUnitContent = new ScriptUnitContent();
            org.springframework.beans.BeanUtils.copyProperties(unitContent, newScriptUnitContent);
            newScriptUnitContent.setId(null);
            if (pairs != null) {
                isCopy = true;
                String splitChars = "~~";
                if (unitContent.getContentName().contains(splitChars)) {
                    String newName = newScriptUnitContent.getContentName().replaceFirst(".*(?=~~)", pairs.get(unitContent.getCorpusId()));
                    newScriptUnitContent.setContentName(newName);
                } else {
                    newScriptUnitContent.setContentName(pairs.get(unitContent.getCorpusId()));
                }
            }
            if (unitContent.getAudioPath() != null) {
                String oldAudioPath = unitContent.getAudioPath();
                String newDownloadPath = getNewPath(oldAudioPath, oldScriptStringId, newScriptStringId, isCopy, newScriptUnitContent.getContentName());
                String oldUploadPath = oldAudioPath.replace(fileNewDownloadPath, fileNewUploadPath);
                String newUploadPath = getNewPath(oldUploadPath, oldScriptStringId, newScriptStringId, isCopy, newScriptUnitContent.getContentName());
                newScriptUnitContent.setAudioPath(newDownloadPath);
                File tmpFile = new File(newUploadPath);
                try {
                    if (!tmpFile.getParentFile().exists()) {
                        tmpFile.getParentFile().mkdirs();
                    }
                    Files.copy(new File(oldUploadPath).toPath(), new File(newUploadPath).toPath());
                } catch (IOException e) {
                    log.info("语句：{} 音频文件复制失败:{}", newScriptUnitContent.getContentName(), e.getMessage());
                }
            } else {
                newScriptUnitContent.setAudioPath(null);
                newScriptUnitContent.setIsPlayed(false);
                newScriptUnitContent.setAudioStatus("0");
                newScriptUnitContent.setAudioTag(null);
                newScriptUnitContent.setUploadStatus(null);
            }
            newScriptUnitContent.setMultiContentId(multiContentMap.get(unitContent.getMultiContentId()).getId());
            newScriptUnitContent.setCorpusId(multiContentMap.get(unitContent.getMultiContentId()).getCorpusId());
            newScriptUnitContent.setScriptId(newScriptId);
            newUnitContents.add(newScriptUnitContent);
        });

        //批量导入基本问答时更新打断垫句和续播垫句id
        List<ScriptCorpus> corpusList = scriptMultiContentService.getCorpusListWithContent(scriptCorpusRepository.findAllByIdIn(new ArrayList<>(oldIdNewCorpusMap.keySet())));
        List<Long> interruptIds = corpusList.stream().flatMap(corpus -> corpus.getScriptMultiContents().stream()).flatMap(multiContent -> multiContent.getScriptUnitContents().stream())
                .map(ScriptUnitContent::getPreInterruptCorpusId).collect(Collectors.toList());
        Map<Long, String> interruptStringMap = scriptCorpusRepository.findAllByIdIn(interruptIds).stream().filter(Objects::nonNull).collect(Collectors.toMap(ScriptCorpus::getId, ScriptCorpus::getName));
        List<Long> continueIds = corpusList.stream().flatMap(corpus -> corpus.getScriptMultiContents().stream()).flatMap(multiContent -> multiContent.getScriptUnitContents().stream())
                .map(ScriptUnitContent::getPreContinueCorpusIdForInterrupt).collect(Collectors.toList());
        Map<Long, String> continueStringMap = scriptCorpusRepository.findAllByIdIn(continueIds).stream().filter(Objects::nonNull).collect(Collectors.toMap(ScriptCorpus::getId, ScriptCorpus::getName));
        if (pairs == null) {
            newUnitContents.forEach(unitContent -> {
                Long oldInterruptId = unitContent.getPreInterruptCorpusId();
                Long newInterruptId = interruptBaseNameMap.get(interruptStringMap.get(oldInterruptId));
                if (newInterruptId != null) {
                    unitContent.setPreInterruptCorpusId(newInterruptId);
                }
                Long oldContinueId = unitContent.getPreContinueCorpusIdForInterrupt();
                Long newContinueId = continueBaseNameMap.get(continueStringMap.get(oldContinueId));
                if (newContinueId != null) {
                    unitContent.setPreContinueCorpusIdForInterrupt(newContinueId);
                }
            });
        }
        List<ScriptUnitContent> savedUnitContents = scriptUnitContentRepository.saveAll(newUnitContents);
    }

    public ScriptCorpus findKnowledgeBaseQAByGroupIdAndName(Long gid, String name) {
        return findAllKnowledgeBaseQAByGroupId(gid)
                .stream()
                .filter(item -> item.getName().equals(name))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("该语料不存在")
                );
    }

    public List<ScriptCorpus> findAllKnowledgeBaseByGroupId(Long gid) {
        return scriptMultiContentService.getCorpusListWithContent(findAllKnowledgeBaseByGroupIdNoContent(gid, false));
    }

    public List<ScriptCorpus> findDeletedKnowledgeBaseByGroupId(Long gid) {
        return scriptMultiContentService.getCorpusListWithContent(findAllKnowledgeBaseByGroupIdNoContent(gid, true));
    }

    // 现有逻辑中似乎不需要考虑删除状态
    public List<ScriptCorpus> findAllKnowledgeBaseQAByGroupId(Long gid) {
        return scriptMultiContentService.getCorpusListWithContent(
                findAllKnowledgeBaseByGroupIdAndTypeNoContent(gid, ScriptCorpusTypeEnum.KNOWLEDGE_BASE_QA, null)
        );
    }

    private ScriptCorpus findById(Long id) {
        return scriptCorpusRepository.findById(id).orElseThrow(() -> new IllegalArgumentException("该id无对应语料"));
    }


    private List<ScriptCorpus> findAllByIds(List<Long> ids) {
        return scriptCorpusRepository.findAllById(ids);
    }

    // 根据分组id查询分组下的所有，根据del为null t f 执行不同的过滤
    private List<ScriptCorpus> findAllKnowledgeBaseByGroupIdNoContent(Long groupId, Boolean del) {
        return scriptCorpusRepository.findAllByIsKnowledgeBaseAndKnowledgeGroupId(true, groupId).stream()
                .filter(corpus -> del == null || (del == corpus.isDeleted()))
                .collect(Collectors.toList());
    }

    // 仅针对知识库使用
    private List<ScriptCorpus> findAllKnowledgeBaseByGroupIdAndTypeNoContent(Long groupId, ScriptCorpusTypeEnum type, Boolean del) {
        if (!(type == ScriptCorpusTypeEnum.KNOWLEDGE_BASE_QA
                || type == ScriptCorpusTypeEnum.KNOWLEDGE_ORDINARY
                || type == ScriptCorpusTypeEnum.KNOWLEDGE_CONNECT)) {
            throw new IllegalArgumentException("输入语料类型非知识库");
        }
        return scriptCorpusRepository.findAllByIsKnowledgeBaseAndKnowledgeGroupIdAndCorpusTypeOrderByWeightAsc(true, groupId, type)
                .stream()
                .filter(corpus -> del == null || (del == corpus.isDeleted()))
                .collect(Collectors.toList());
    }

    public Response<List<ScriptCorpus>> batchImportBaseQA(Long targetGid, Long scriptId, Map<Long, List<Long>> baseQACorpusPairs) {
        int[] count = {0, 0};
        List<AILabel> labels = aiLabelRepository.findAllByScriptId(scriptId);
        List<String> aiLabelNameList = labels.stream().map(AILabel::getLabelName).collect(Collectors.toList());
        List<AIIntentionType> intentionTypeList = aiIntentionTypeRepository.findAllByScriptId(scriptId);
        //打断垫句名称
        Map<String, Long> interruptBaseNameMap = scriptPreCorpusService.findPreCorpusListByCorpusType(scriptId, ScriptCorpusTypeEnum.PRE_INTERRUPT)
                .stream().collect(Collectors.toMap(ScriptCorpus::getName, ScriptCorpus::getId, (v1, v2) -> v1));
        //续播垫句名称
        Map<String, Long> continueBaseNameMap = scriptPreCorpusService.findPreCorpusListByCorpusType(scriptId, ScriptCorpusTypeEnum.PRE_CONTINUE)
                .stream().collect(Collectors.toMap(ScriptCorpus::getName, ScriptCorpus::getId));

        List<Long> groupListIds = knowledgeGroupRepository.findByScriptId(scriptId).stream().map(KnowledgeGroup::getId).collect(Collectors.toList());
        List<String> corpusName = scriptCorpusRepository.findAllByScriptId(scriptId).stream().map(ScriptCorpus::getName).filter(Objects::nonNull).collect(Collectors.toList());

        String newScriptStringId = scriptRepository.findFirstById(scriptId).getScriptStringId();
        final String[] oldScriptStringId = {null};

        Map<Long, ScriptCorpus> oldIdNewCorpusMap = new LinkedHashMap<>();
        baseQACorpusPairs.forEach((groupId, corpursIdList) -> {
            //校验待导入的语料是否符合当前话术的要求
            List<ScriptCorpus> corpusList = scriptMultiContentService.getCorpusListWithContent(scriptCorpusRepository.findAllByIdIn(corpursIdList));

            for (ScriptCorpus scriptCorpus : corpusList) {
                //当前话术中不包含导入语料的标签,则导入失败
                if (validateLabels(scriptCorpus, aiLabelNameList) || validateSentences(scriptCorpus, interruptBaseNameMap.keySet(), continueBaseNameMap.keySet())
                        || validateIntentionType(scriptCorpus, intentionTypeList)) {
                    //失败
                    count[0]++;
                    continue;
                }
                if (corpusName.contains(scriptCorpus.getName())) {
                    //失败
                    count[0]++;
                    continue;
                }
                if (oldScriptStringId[0] == null) {
                    oldScriptStringId[0] = scriptRepository.findFirstById(scriptCorpus.getScriptId()).getScriptStringId();
                }
                ScriptCorpus newCorpus = new ScriptCorpus();
                BeanUtils.copyProperties(scriptCorpus, newCorpus);
                newCorpus.setId(null);
                newCorpus.setScriptId(scriptId);
                newCorpus.setKnowledgeGroupId(targetGid);
                if (scriptCorpus.getAiIntentionType() != null) {
                    intentionTypeList.forEach(intentionType -> {
                        if (scriptCorpus.getAiIntentionType().getIntentionName().equals(intentionType.getIntentionName())) {
                            newCorpus.setAiIntentionType(intentionType);
                        }
                    });
                } else {
                    newCorpus.setAiIntentionType(null);
                }

                if (CollectionUtils.isNotEmpty(scriptCorpus.getAiLabels())) {
                    List<String> labelNames = scriptCorpus.getAiLabels().stream().map(AILabel::getLabelName).collect(Collectors.toList());
                    List<AILabel> labelIds = new ArrayList<>();
                    labels.forEach(label -> {
                        if (labelNames.contains(label.getLabelName())) {
                            labelIds.add(label);
                        }
                    });
                    newCorpus.setAiLabels(labelIds);
                } else {
                    newCorpus.setAiLabels(null);
                }

                //开放语境
                if (Objects.equals(true, scriptCorpus.getIsOpenContext())) {
                    switch (newCorpus.getOpenScopeType()) {
                        case ALL:
                            newCorpus.setGroupOpenScope(groupListIds);
                            break;
                        case SUCCEED_CONTEXT:
                            break;
                        case CUSTOM:
                            newCorpus.setGroupOpenScope(null);
                            break;
                        default:
                            break;
                    }
                }
                handleConnectCorpus(scriptCorpus, scriptId, newCorpus);
                validateEventTriggerValueId(scriptCorpus, newCorpus);
                oldIdNewCorpusMap.put(scriptCorpus.getId(), newCorpus);
                count[1]++;
            }
        });
        List<ScriptCorpus> savedCorpuses = scriptCorpusRepository.saveAll(oldIdNewCorpusMap.values());
        //替换为存储后的语料对象
        int[] index = {0};
        oldIdNewCorpusMap.forEach((k, v) -> {
            oldIdNewCorpusMap.computeIfPresent(k, (key, value) -> savedCorpuses.get(index[0]++));
        });

        copyMultiContents(savedCorpuses, oldIdNewCorpusMap, null, oldScriptStringId[0], newScriptStringId, interruptBaseNameMap, continueBaseNameMap);

        Response<List<ScriptCorpus>> response = new Response<>();
        response.setData(savedCorpuses);
        response.setResponseSuccess();
        response.setMsg("失败" + count[0] + "条，" + "成功" + count[1] + "条");
        return response;
    }

    private boolean validateLabels(ScriptCorpus scriptCorpus, List<String> aiLabelNameList) {
        if (CollectionUtils.isNotEmpty(scriptCorpus.getAiLabels())) {
            return scriptCorpus.getAiLabels().stream().map(AILabel::getLabelName).anyMatch(s -> !aiLabelNameList.contains(s));
        }
        //如果为空也允许导入
        return false;
    }

    private boolean validateSentences(ScriptCorpus scriptCorpus, Set<String> interruptBaseNameSet, Set<String> continueBaseNameSet) {
        //打断垫句续播垫句校验
        List<Long> interruptIds = scriptCorpus.getScriptMultiContents().stream().flatMap(multiContent -> multiContent.getScriptUnitContents().stream())
                .map(ScriptUnitContent::getPreInterruptCorpusId).collect(Collectors.toList());
        List<Long> continueIds = scriptCorpus.getScriptMultiContents().stream().flatMap(multiContent -> multiContent.getScriptUnitContents().stream())
                .map(ScriptUnitContent::getPreContinueCorpusIdForInterrupt).collect(Collectors.toList());
        List<String> interruptString = scriptCorpusRepository.findAllByIdIn(interruptIds).stream().map(ScriptCorpus::getName).filter(Objects::nonNull).collect(Collectors.toList());
        List<String> continueString = scriptCorpusRepository.findAllByIdIn(continueIds).stream().map(ScriptCorpus::getName).filter(Objects::nonNull).collect(Collectors.toList());
        //校验语料中的打断垫句和续播垫句，存在任一个不在当前话术中的则导入失败
        return interruptString.stream().anyMatch(name -> !interruptBaseNameSet.contains(name)) || continueString.stream().anyMatch(name -> !continueBaseNameSet.contains(name));
    }

    private boolean validateIntentionType(ScriptCorpus corpus, List<AIIntentionType> intentionTypeList) {
        AIIntentionType intentionType = corpus.getAiIntentionType();
        if (Objects.nonNull(intentionType)) {
            if (intentionTypeList.stream().map(AIIntentionType::getIntentionName).noneMatch(s -> s.equals(intentionType.getIntentionName()))) {
                return true;
            }
        }
        return false;
    }

    private void handleConnectCorpus(ScriptCorpus corpus, Long scriptId, ScriptCorpus newCorpus) {
        if (Objects.equals(corpus.getConnectType(), SELECT_MASTER_PROCESS)) {
            ScriptCorpus connectCorpus = scriptCorpusRepository.getOne(corpus.getConnectCorpusId());
            ScriptCorpus tarCorpus = scriptCorpusRepository.findFirstByNameAndScriptId(connectCorpus.getName(), scriptId);
            if (tarCorpus != null) {
                newCorpus.setConnectCorpusId(tarCorpus.getId());
            } else {
                newCorpus.setConnectCorpusId(null);
            }
        }
    }

    private void validateEventTriggerValueId(ScriptCorpus corpus, ScriptCorpus newCorpus) {
        if (CollectionUtils.isNotEmpty(corpus.getEventTriggerValueIds())) {
            ScriptServiceUtil.copyEventTriggerKey(corpus.getScriptId(), newCorpus.getScriptId(), eventTriggerForScriptRepository);
        }
    }

    //</editor-fold>

}
