package com.raipeng.aispeech.service;


import com.google.common.collect.Lists;
import com.raipeng.aispeech.entity.AsrProcessPojo;
import com.raipeng.aispeech.exceptionadvice.exception.ScriptCheckException;
import com.raipeng.aispeech.model.TenantSmsTemplate;
import com.raipeng.aispeech.model.script.Script;
import com.raipeng.aispeech.model.script.ScriptUnitContent;
import com.raipeng.aispeech.repository.AdminRepository;
import com.raipeng.aispeech.repository.ScriptRepository;
import com.raipeng.aispeech.repository.ScriptUnitContentRepository;
import com.raipeng.aispeech.repository.TenantSmsTemplateRepository;
import com.raipeng.aispeech.utils.ThreadManagerUtil;
import com.raipeng.common.enums.ScriptStatusEnum;
import com.raipeng.common.enums.SmsTemplateType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RefreshScope
public class ScriptConvertService {
    @Autowired
    private ScriptService scriptService;

    @Autowired
    private ScriptRepository scriptRepository;

    @Autowired
    private ScriptUnitContentRepository scriptUnitContentRepository;

    @Autowired
    private AsrProcessService asrProcessService;

    @Autowired
    private TenantSmsTemplateRepository tenantSmsTemplateRepository;

    @Value("${script.audio.upload.path:/static/file/ai/audio_record}")
    private String fileNewUploadPath;

    @Value("${script.audio.size:30}")
    private Integer audioSize;

    @Value("${script.audio.download.path:https://uat.bountech.com/marketfront/file/ai-speech/ai/audio_record}")
    private String fileNewDownloadPath;

    public void setOwnerAccount() {
        List<Script> scripts = scriptRepository.findScriptsByIsDeleted(false);
        for (Script script : scripts) {
            ScriptStatusEnum status = script.getStatus();
            if (ScriptStatusEnum.EDIT.equals(status) || ScriptStatusEnum.REJECT.equals(status)) {
                String lockAccount = script.getLockAccount();
                if (lockAccount == null) {
                    lockAccount = "luoqijia110";
                }
                script.setOwnerAccount(lockAccount);
            }
            if (ScriptStatusEnum.STOP.equals(status) || ScriptStatusEnum.ACTIVE.equals(status)) {
                String lockAccount = script.getLockAccount();
                if (lockAccount == null) {
                    lockAccount = "luoqijia110";
                }
                script.setOwnerAccount(lockAccount);
                script.setLockAccount(null);
            }
        }
        scriptRepository.saveAll(scripts);
    }

    /**
     * 预生成所有生效中的话术缓存(不用管停用中的话术)
     */
    public void reGenerateCacheForScripts(List<Long> excludeScriptIds) {
        List<Long> activeScriptIds = scriptService.findActiveScriptIds();
        for (Long activeScriptId : activeScriptIds) {
            if (excludeScriptIds.contains(activeScriptId)) {
                continue;
            }
            ThreadManagerUtil.generateCachePool.execute(()->{
                try {
                    scriptService.findLatestActiveScriptByScriptId(activeScriptId);
                    log.info("话术ID:{}预生成", activeScriptId);
                } catch (Exception e) {
                    log.error("话术ID:{}预生成失败:{}", activeScriptId, e.getMessage());
                }

            });
        }
        log.info("所有话术缓存预生成成功!!!");
    }

    public static MultipartFile fileToMultipartFile(File file) {
        FileItem fileItem = null;
        try {
            DiskFileItemFactory factory = new DiskFileItemFactory(16, null);
            fileItem = factory.createItem("fileTest", Files.probeContentType(file.toPath()), false, file.getName());
            try (InputStream input = new FileInputStream(file); OutputStream os = fileItem.getOutputStream()) {
                IOUtils.copy(input, os);
            }
        } catch (Exception e) {
            if (fileItem != null) {
                fileItem.delete();
            }
            log.info("音频文件asr识别失败:{}", e.getMessage());
            return null;
        }
        return new CommonsMultipartFile(fileItem);
    }

    private static String getNewPath(String path, String oldScriptStringId, String newScriptStringId) {
        String newPath = path.replace(oldScriptStringId, newScriptStringId);
        String[] split = newPath.split("_");
        if (split.length > 0 && split[split.length - 1].endsWith(".wav")) {
            return newPath.replace(split[split.length - 1], "0.wav");
        } else {
            throw  new ScriptCheckException("handleAllUnhandledAudio音频格式有误");

        }
    }

    public void handleAllUnhandledAudio(ScriptStatusEnum status) {
        log.info("开始处理所有：{}历史话术的音频识别", status);
        List<Script> scripts = scriptRepository.findScriptsByIsDeletedAndStatusOrderByUpdateTimeDesc(false, status);
        for (Script script : scripts) {
            handleOneUnhandledAudio(script.getId());
        }
        log.info("处理所有所有：{}历史话术的音频识别结束", status);
    }

    public void handleOneUnhandledAudio(Long scriptId) {
        log.info("开始处理话术:{}的音频识别", scriptId);
        Script script = scriptRepository.findById(scriptId).get();
        String oldScriptStringId = script.getScriptStringId();
        String newScriptStringId = script.getScriptStringId();
        List<ScriptUnitContent> unitContents = scriptUnitContentRepository.findAllByScriptId(scriptId);
        List<AsrProcessPojo> pojos = new ArrayList<>();
        unitContents.forEach(unitContent -> {
            if ("1".equals( unitContent.getAudioStatus())) {
                Double asrResult = unitContent.getAsrResult();
                if (asrResult == null || asrResult.doubleValue() == 0.0) {
                    String oldAudioPath = unitContent.getAudioPath();
                    if (oldAudioPath == null) {
                        return;
                    }
                    String newDownloadPath = getNewPath(oldAudioPath, oldScriptStringId, newScriptStringId);
                    String oldUploadPath = oldAudioPath.replace(fileNewDownloadPath, fileNewUploadPath);
                    String newUploadPath = getNewPath(oldUploadPath, oldScriptStringId, newScriptStringId);
                    File tmpFile = new File(newUploadPath);

//            log.info("========识别:{}", unitContent.getContentName());
                    MultipartFile multipartFile = fileToMultipartFile(tmpFile);
                    AsrProcessPojo pojo = new AsrProcessPojo();
                    pojo.setUnitContent(unitContent);
                    pojo.setFile(multipartFile);
                    pojos.add(pojo);
                }
            }
        });
        log.info("处理大小:{}", pojos.size());
        List<List<AsrProcessPojo>> batches = Lists.partition(pojos, audioSize);
        batches.forEach(batch -> {
            asrProcessService.processAsrResult(batch);
        });
        log.info("结束处理话术:{}的音频识别", scriptId);
    }

    public void convertSmsTemplateType(List<String> groupIds) {
        List<TenantSmsTemplate> tenantSmsTemplates = tenantSmsTemplateRepository.findAll();
        for (TenantSmsTemplate tenantSmsTemplate : tenantSmsTemplates) {
            String groupId = tenantSmsTemplate.getGroupId();
            if (groupIds.contains(groupId)) {
                String messageContent = tenantSmsTemplate.getMessageContent();
                String[] split = messageContent.split("\\|");
                tenantSmsTemplate.setVolcanoAccountId(split[0]);
                tenantSmsTemplate.setVolcanoSmsTemplateId(split[1]);
                tenantSmsTemplate.setSmsTemplateType(SmsTemplateType.V_MESSAGE);
            } else if (tenantSmsTemplate.getTemplateName().contains("M")) {
                tenantSmsTemplate.setSmsTemplateType(SmsTemplateType.M_MESSAGE);
            } else {
                tenantSmsTemplate.setSmsTemplateType(SmsTemplateType.BZ_MESSAGE);
            }
        }
        tenantSmsTemplateRepository.saveAll(tenantSmsTemplates);
    }
}
