package com.raipeng.aispeech.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.raipeng.aispeech.annotation.RedisLock;
import com.raipeng.aispeech.constant.RedisCacheConstant;
import com.raipeng.aispeech.context.AIContext;
import com.raipeng.aispeech.controller.wrapper.MobileBelongResultWrapper;
import com.raipeng.aispeech.entity.ScriptSmsTriggerPojo;
import com.raipeng.aispeech.entity.VariableSmsPojo;
import com.raipeng.aispeech.exceptionadvice.exception.AdminCheckException;
import com.raipeng.aispeech.exceptionadvice.exception.ClueCheckException;
import com.raipeng.aispeech.exceptionadvice.exception.ShowOnPageRemindException;
import com.raipeng.aispeech.model.*;
import com.raipeng.aispeech.model.callteam.CallTeam;
import com.raipeng.aispeech.model.dto.*;
import com.raipeng.aispeech.model.script.Script;
import com.raipeng.aispeech.model.vo.PriorityTaskData;
import com.raipeng.aispeech.model.vo.TaskData;
import com.raipeng.aispeech.model.vo.TaskDataVO;
import com.raipeng.aispeech.model.vo.TaskTenantData;
import com.raipeng.aispeech.repository.*;
import com.raipeng.aispeech.repositorymulti.phone.PhoneRecordHistoryRepository;
import com.raipeng.aispeech.service.clickHouse.CallRecordClickHouseService;
import com.raipeng.aispeech.service.clickHouse.CallRecordUnitSearchClickHouseService;
import com.raipeng.aispeech.service.mq.TaskStatusNoticeProducer;
import com.raipeng.aispeech.utils.RaiYiEncryptionUtil;
import com.raipeng.aispeech.utils.TenantLineUtils;
import com.raipeng.common.constant.VariableConstants;
import com.raipeng.common.enums.AIOutboundTaskType;
import com.raipeng.common.model.dto.TaskStatusNoticeDTO;
import com.raipeng.common.util.JpaResultUtils;
import com.raipeng.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.*;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.persistence.Tuple;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.raipeng.aispeech.constant.CommonConstant.REDIS_TASK_TMP;

@Service
@RefreshScope
@Slf4j
public class AIOutboundTaskService {
    @Autowired
    private AIOutboundTaskRepository aiOutboundTaskRepository;

    @Autowired
    private AiTaskWriteService aiTaskWriteService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private AIOutboundTaskService aiOutboundTaskService;

    @Autowired
    private OutboundCallRecordStatisticRepository outboundCallRecordStatisticRepository;

    @Autowired
    private CallRecordUnitSearchClickHouseService callRecordUnitSearchClickHouseService;

    @Autowired
    private SmsRecordRepository SMSRecordRepository;

    @Autowired
    private SmsRecordHistoryRepository SMSRecordHistoryRepository;

    @Autowired
    private CallRecordDailyHistoryService callRecordDailyHistoryService;

    @Autowired
    private PhoneRecordDailyHistoryService phoneRecordDailyHistoryService;

    @Autowired
    private CallRecordClickHouseService callRecordClickHouseService;

    @Autowired
    private PhoneRecordRepository phoneRecordRepository;

    @Autowired
    private PhoneRecordHistoryRepository phoneRecordHistoryRepository;

    @Autowired
    private AIOutboundTaskTemplateRepository aiOutboundTaskTemplateRepository;

    @Value("${concurrent.phonenum.ratio:1.5}")
    private Double concurrentPhoneNumRatio;

    @Autowired
    private MobileBelongRepository mobileBelongRepository;
    @Autowired
    private AIManagerLineConfigRepository aiManagerLineConfig;

    @Autowired
    private RedisGetPhoneService redisGetPhoneService;

    @Autowired
    RabbitTemplate rabbitTemplate;

    @Autowired
    private AITenantRepository aiTenantRepository;

    @Autowired
    private AdminRepository adminRepository;

    @Autowired
    private TenantLineUtils tenantLineUtils;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private TenantProgramAdminRepository tenantProgramAdminRepository;

    @Autowired
    private BatchService batchService;

    @Autowired
    private ScriptRepository scriptRepository;

    @Autowired
    private CallTeamRepository callTeamRepository;
    @Value("${history.use.clickhouse.switch:N}")
    private String historyUseClickHouseSwitch;

    @Autowired
    private AIOutboundTaskTemplateService aiOutboundTaskTemplateService;

    @Autowired
    private SmsRecordRepository smsRecordRepository;

    @Autowired
    private SmsRecordHistoryRepository smsRecordHistoryRepository;

    @Autowired
    private TaskStatusNoticeProducer taskStatusNoticeProducer;

    @Autowired
    private AIPermanentTaskAdminRepository aiPermanentTaskAdminRepository;

    public List<AIOutboundTask> findTasksByTaskIds(List<Long> taskIds) {
        return aiOutboundTaskRepository.findAllById(taskIds);
    }

    public List<AIOutboundTask> findList(AIOutboundQueryDto aiOutboundQueryDto) {
        List<AIOutboundTask> aiOutboundTasks = null;
        if (StringUtils.isNotBlank(aiOutboundQueryDto.getIds())) {
            String[] taskIds = aiOutboundQueryDto.getIds().split(",");
            List<Long> ids = new ArrayList<>();
            for (String id : taskIds) {
                ids.add(Long.valueOf(id));
            }
            aiOutboundTasks = aiOutboundTaskRepository.findAllByIdInAndGroupId(ids, AIContext.getGroupId());
        } else {
            aiOutboundTasks = aiOutboundTaskRepository.findTaskListByQueryDto(aiOutboundQueryDto.getStartTime(),
                    aiOutboundQueryDto.getEndTime(),
                    aiOutboundQueryDto.getTaskName(),
                    aiOutboundQueryDto.getCallStatus(),
                    aiOutboundQueryDto.getSpeechCraftName(),
                    aiOutboundQueryDto.getLineName(), "1".equals(aiOutboundQueryDto.getIfFindAll()) ? null : AIContext.getGroupId());
        }

        if ("1".equals(aiOutboundQueryDto.getIfFindAll())) {
            List<String> groupIdList = aiOutboundTasks.stream().map(AIOutboundTask::getGroupId).distinct().collect(Collectors.toList());
            List<Long> tenantIdList = groupIdList.stream().map(a -> Long.valueOf(a.split("_")[1])).collect(Collectors.toList());
            List<Long> accountIdList = groupIdList.stream().map(a -> Long.valueOf(a.split("_")[2])).collect(Collectors.toList());
            List<AITenant> tenantList = aiTenantRepository.findAllByIdIn(tenantIdList);
            Map<Long, AITenant> tenantMap = tenantList.stream().collect(Collectors.toMap(AITenant::getId, Function.identity()));
            List<Admin> accountList = adminRepository.findAllByIdIn(accountIdList);
            Map<Long, Admin> accountMap = accountList.stream().collect(Collectors.toMap(Admin::getId, Function.identity()));

            for (AIOutboundTask aiOutboundTask : aiOutboundTasks) {
                Admin admin = accountMap.getOrDefault(Long.valueOf(aiOutboundTask.getGroupId().split("_")[2]), null);
                AITenant aiTenant = tenantMap.getOrDefault(Long.valueOf(aiOutboundTask.getGroupId().split("_")[1]), null);
                aiOutboundTask.setAccount(admin == null ? null : admin.getAccount());
                aiOutboundTask.setTenantCode(aiTenant == null ? null : aiTenant.getTenantNo());
                aiOutboundTask.setTenantName(aiTenant == null ? null : aiTenant.getTenantName());
                Integer calledNum = aiOutboundTask.getCalledPhoneNum() == null ? 0 : aiOutboundTask.getCalledPhoneNum();
                Integer putThroughNum = aiOutboundTask.getPutThroughPhoneNum() == null ? 0 : aiOutboundTask.getPutThroughPhoneNum();
                aiOutboundTask.setPutThroughPhoneRate(calledNum == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(putThroughNum).divide(BigDecimal.valueOf(calledNum), 5, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
            }
            if (StringUtils.isNotBlank(aiOutboundQueryDto.getAccount())) {
                aiOutboundTasks = aiOutboundTasks.stream().filter(a -> a.getAccount().equals(aiOutboundQueryDto.getAccount())).collect(Collectors.toList());
            }

            if (StringUtils.isNotBlank(aiOutboundQueryDto.getTenantName())) {
                aiOutboundTasks = aiOutboundTasks.stream().filter(a -> a.getTenantName().contains(aiOutboundQueryDto.getTenantName())).collect(Collectors.toList());
            }
        }
        if (StringUtils.isNotBlank(aiOutboundQueryDto.getTaskType())) {
            if (AIOutboundTaskType.AI_AUTO.toString().equals(aiOutboundQueryDto.getTaskType())) {
                aiOutboundTasks = aiOutboundTasks.stream().filter(task -> AIOutboundTaskType.AI_AUTO.equals(task.getTaskType())).collect(Collectors.toList());
            }
            if (AIOutboundTaskType.AI_MANUAL.toString().equals(aiOutboundQueryDto.getTaskType())) {
                aiOutboundTasks = aiOutboundTasks.stream().filter(task -> AIOutboundTaskType.AI_MANUAL.equals(task.getTaskType())).collect(Collectors.toList());
            }
        }

        aiOutboundTasks.forEach(a -> {
            a.setStartWorkTimeList(Arrays.asList(a.getStartWorkTimes().split(",")));
            a.setEndWorkTimeList(Arrays.asList(a.getEndWorkTimes().split(",")));
        });
        Map<String, AIOutboundTask> taskMap = aiOutboundTasks.stream()
                .collect(Collectors.toMap(task -> String.valueOf(task.getId()), task -> task));


        LocalDate today = LocalDate.now();
        if (!aiOutboundTasks.isEmpty()) {
            LocalDateTime inputStartTime = aiOutboundTasks.get(0).getCreateTime();
            LocalDate inputDate = inputStartTime.toLocalDate();
            if (inputDate.equals(today)) {
                List<Tuple> messageRecordStatisticByGroupId = smsRecordRepository.findMessageRecordStatisticByGroupId(AIContext.getGroupId());
                for (Tuple record : messageRecordStatisticByGroupId) {
                    BigInteger totalCountBigInt = (BigInteger) record.get("totalCount");
                    Integer totalCount = totalCountBigInt != null ? totalCountBigInt.intValue() : 0;
                    BigInteger successCountBigInt = (BigInteger) record.get("successCount");
                    Integer successCount = successCountBigInt != null ? successCountBigInt.intValue() : 0;
                    String key = (String) record.get("taskId");
                    AIOutboundTask aiOutboundTask = taskMap.get(key);
                    if (aiOutboundTask != null) {
                        aiOutboundTask.setSendSmsNumber(successCount);
                        aiOutboundTask.setTriggerSmsNumber(totalCount);
                        taskMap.put(key, aiOutboundTask);
                    }
                }
            } else if (inputDate.isBefore(today)) {
                String startTime = inputDate.atStartOfDay().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                String endTime = inputDate.atStartOfDay().plusDays(1L).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                List<Tuple> messageRecordStatisticByGroupId = smsRecordHistoryRepository.findMessageRecordStatisticByGroupId(startTime, endTime, AIContext.getGroupId());
                for (Tuple record : messageRecordStatisticByGroupId) {
                    BigInteger totalCountBigInt = (BigInteger) record.get("totalCount");
                    Integer totalCount = totalCountBigInt != null ? totalCountBigInt.intValue() : 0;
                    BigInteger successCountBigInt = (BigInteger) record.get("successCount");
                    Integer successCount = successCountBigInt != null ? successCountBigInt.intValue() : 0;
                    String key = (String) record.get("taskId");
                    AIOutboundTask aiOutboundTask = taskMap.get(key);
                    if (aiOutboundTask != null) {
                        aiOutboundTask.setSendSmsNumber(successCount);
                        aiOutboundTask.setTriggerSmsNumber(totalCount);
                        taskMap.put(key, aiOutboundTask);
                    }
                }
            }
        }

        return new ArrayList<>(taskMap.values());
    }

    public List<AIOutboundTask> findTaskAdditionalList(AIOutboundQueryDto aiOutboundQueryDto) {
        List<AIOutboundTask> aiOutboundTasks = null;

        aiOutboundTasks = aiOutboundTaskRepository.findTaskAdditionalList(aiOutboundQueryDto.getStartTime(),
                aiOutboundQueryDto.getEndTime(),
                aiOutboundQueryDto.getTaskName(),
                aiOutboundQueryDto.getCallStatus(),
                aiOutboundQueryDto.getSpeechCraftName(),
                CollectionUtils.isEmpty(aiOutboundQueryDto.getLineCodes()) ? "1" : "0",
                CollectionUtils.isEmpty(aiOutboundQueryDto.getLineCodes()) ? new ArrayList<>() : aiOutboundQueryDto.getLineCodes(),
                AIContext.getGroupId());

        if (StringUtils.isNotBlank(aiOutboundQueryDto.getTaskType())) {
            if (AIOutboundTaskType.AI_AUTO.toString().equals(aiOutboundQueryDto.getTaskType())) {
                aiOutboundTasks = aiOutboundTasks.stream().filter(task -> AIOutboundTaskType.AI_AUTO.equals(task.getTaskType())).collect(Collectors.toList());
            }
            if (AIOutboundTaskType.AI_MANUAL.toString().equals(aiOutboundQueryDto.getTaskType())) {
                aiOutboundTasks = aiOutboundTasks.stream().filter(task -> AIOutboundTaskType.AI_MANUAL.equals(task.getTaskType())).collect(Collectors.toList());
            }
        }
        aiOutboundTasks.forEach(a -> {
            a.setStartWorkTimeList(Arrays.asList(a.getStartWorkTimes().split(",")));
            a.setEndWorkTimeList(Arrays.asList(a.getEndWorkTimes().split(",")));
        });
        Map<String, AIOutboundTask> taskMap = aiOutboundTasks.stream()
                .collect(Collectors.toMap(task -> String.valueOf(task.getId()), task -> task));
        if (taskMap.isEmpty()) {
            return new ArrayList<>();
        }

        List<Tuple> messageRecordStatisticByGroupId = smsRecordRepository.findMessageRecordStatisticByGroupId(AIContext.getGroupId());
        for (Tuple record : messageRecordStatisticByGroupId) {
            BigInteger totalCountBigInt = (BigInteger) record.get("totalCount");
            Integer totalCount = totalCountBigInt != null ? totalCountBigInt.intValue() : 0;
            BigInteger successCountBigInt = (BigInteger) record.get("successCount");
            Integer successCount = successCountBigInt != null ? successCountBigInt.intValue() : 0;
            String key = (String) record.get("taskId");
            AIOutboundTask aiOutboundTask = taskMap.get(key);
            if (aiOutboundTask != null) {
                aiOutboundTask.setSendSmsNumber(successCount);
                aiOutboundTask.setTriggerSmsNumber(totalCount);
                taskMap.put(key, aiOutboundTask);
            }
        }
        return new ArrayList<>(taskMap.values());
    }

    public List<AIOutboundTask> findTaskListByAITool(AIOutboundToolDto aiOutboundToolDto) {
        List<AIOutboundTask> aiOutboundTasks = null;
        log.info("findTaskListByAITool请求参数 {}", aiOutboundToolDto);
        aiOutboundTasks = aiOutboundTaskRepository.findTaskListByAITool(aiOutboundToolDto.getStartTime(),
                aiOutboundToolDto.getEndTime(),
                aiOutboundToolDto.getExpectedStartTime(),
                aiOutboundToolDto.getExpectedFinishTime(),
                aiOutboundToolDto.getTaskType(),
                CollectionUtils.isEmpty(aiOutboundToolDto.getTaskIds()) ? "1" : "0",
                CollectionUtils.isEmpty(aiOutboundToolDto.getTaskIds()) ? new ArrayList<>() : aiOutboundToolDto.getTaskIds(),
                aiOutboundToolDto.getIfAutoRecall(),
                aiOutboundToolDto.getIfAutoStop(),
                aiOutboundToolDto.getSmsTemplateAbnormal(),
                aiOutboundToolDto.getConcurrentMin(),
                aiOutboundToolDto.getConcurrentMax(),
                aiOutboundToolDto.getPhoneNumMin(),
                aiOutboundToolDto.getPhoneNumMax(),
                aiOutboundToolDto.getRemainingNumMin(),
                aiOutboundToolDto.getRemainingNumMax(),
                aiOutboundToolDto.getCallingPhoneNumMin(),
                aiOutboundToolDto.getCallingPhoneNumMax(),
                aiOutboundToolDto.getRecallingPhoneNumMin(),
                aiOutboundToolDto.getRecallingPhoneNumMax(),
                AIContext.getGroupId()
        );
//        log.info("findTaskListByAITool返回数据 {}", aiOutboundTasks);
        Set<String> callStatusSet = (aiOutboundToolDto.getCallStatus() != null) ? new HashSet<>(aiOutboundToolDto.getCallStatus()) : null;
        Set<String> scriptStringIdSet = (aiOutboundToolDto.getScriptStringIds() != null) ? new HashSet<>(aiOutboundToolDto.getScriptStringIds()) : null;
        Set<String> lineCodeSet = (aiOutboundToolDto.getLineCodes() != null) ? new HashSet<>(aiOutboundToolDto.getLineCodes()) : null;
        aiOutboundTasks = aiOutboundTasks.stream()
                .filter(task -> (callStatusSet == null || callStatusSet.contains(task.getCallStatus())))
                .filter(task -> (scriptStringIdSet == null || scriptStringIdSet.contains(task.getScriptStringId())))
                .filter(task -> (lineCodeSet == null || lineCodeSet.contains(task.getLineCode())))
                .collect(Collectors.toList());
        return aiOutboundTasks;
    }


    public String saveList(List<AIOutboundTask> aiOutboundTaskList) {

        aiOutboundTaskList.forEach(a -> {
            a.setCallStatus("待执行");
            a.setGroupId(AIContext.getGroupId());
            RAtomicLong max_task_id = redissonClient.getAtomicLong("MAX_TASK_ID");
            long taskId = max_task_id.addAndGet(1);
            a.setId(taskId);
            AITenant aiTenant = aiTenantRepository.findById(Long.valueOf(AIContext.getGroupId().split("_")[1])).get();
            a.setTenantName(aiTenant.getTenantName());
        });
        aiTaskWriteService.initTasks(aiOutboundTaskList);
        return "新建成功";
    }

    public AIOutboundTask setRestrictArea(AIOutboundTask aiOutboundTask) {
        Optional<AIOutboundTask> optional = aiOutboundTaskRepository.findById(aiOutboundTask.getId());
        if (!optional.isPresent()) {
            AIOutboundTask aiOutboundTask1 = new AIOutboundTask();
            aiOutboundTask1.setBatchStatus("执行失败");
            return aiOutboundTask1;
        }
        AIOutboundTask aiOutboundTask1 = optional.get();
        deleteTaskFromRedis(aiOutboundTask1.getTaskName(), String.valueOf(aiOutboundTask1.getId()), aiOutboundTask1.getPhoneNum());
        aiTaskWriteService.updateRestrictAreaTaskById(aiOutboundTask.getId(), aiOutboundTask.getAllRestrictCity(), aiOutboundTask.getAllRestrictProvince(),
                aiOutboundTask.getVirtualRestrictCity(), aiOutboundTask.getVirtualRestrictCity(),
                aiOutboundTask.getUnknownRestrictCity(), aiOutboundTask.getUnknownRestrictProvince(),
                aiOutboundTask.getDxRestrictCity(), aiOutboundTask.getDxRestrictProvince(),
                aiOutboundTask.getLtRestrictCity(), aiOutboundTask.getLtRestrictProvince(),
                aiOutboundTask.getYdRestrictCity(), aiOutboundTask.getYdRestrictProvince(), "执行成功");
        return aiOutboundTask1;

    }

    public List<AIOutboundTask> setRestrictAreaBatch(AIOutboundTask aiOutboundTask) {
        String[] taskIdList = aiOutboundTask.getTaskIds().split(",");
        List<AIOutboundTask> res = new ArrayList<>();
        for (String taskId : taskIdList) {
            aiOutboundTask.setId(Long.parseLong(taskId));
            res.add(setRestrictArea(aiOutboundTask));
        }
        return res;
    }

    public List<AIOutboundTask> setRestrictAreaAppendBatch(AIOutboundTask aiOutboundTask) {
        String[] taskIdList = aiOutboundTask.getTaskIds().split(",");
        List<AIOutboundTask> res = new ArrayList<>();
        for (String taskId : taskIdList) {
            aiOutboundTask.setId(Long.parseLong(taskId));
            res.add(setRestrictAreaAppend(aiOutboundTask));
        }
        return res;
    }

    public AIOutboundTask setRestrictAreaAppend(AIOutboundTask aiOutboundTask) {
        Optional<AIOutboundTask> optional = aiOutboundTaskRepository.findById(aiOutboundTask.getId());
        if (!optional.isPresent()) {
            AIOutboundTask aiOutboundTask1 = new AIOutboundTask();
            aiOutboundTask1.setBatchStatus("执行失败");
            return aiOutboundTask1;
        }
        AIOutboundTask taskSaveDTO = optional.get();
        deleteTaskFromRedis(taskSaveDTO.getTaskName(), String.valueOf(taskSaveDTO.getId()), taskSaveDTO.getPhoneNum());

        //全部
        if (StringUtils.isNotBlank(aiOutboundTask.getAllRestrictCity()) && StringUtils.isNotBlank(aiOutboundTask.getAllRestrictProvince())) {
            if (StringUtils.isEmpty(taskSaveDTO.getAllRestrictCity())) {
                taskSaveDTO.setAllRestrictCity(aiOutboundTask.getAllRestrictCity());
            } else {
                String allRestrictCity = taskSaveDTO.getAllRestrictCity();
                taskSaveDTO.setAllRestrictCity(getUniqueCityProvince(allRestrictCity, aiOutboundTask.getAllRestrictCity()));
            }
            if (StringUtils.isEmpty(taskSaveDTO.getAllRestrictProvince())) {
                taskSaveDTO.setAllRestrictProvince(aiOutboundTask.getAllRestrictProvince());
            } else {
                String allRestrictProvince = taskSaveDTO.getAllRestrictProvince();
                taskSaveDTO.setAllRestrictProvince(getUniqueCityProvince(allRestrictProvince, aiOutboundTask.getAllRestrictProvince()));
            }
        }

        //电信
        if (StringUtils.isNotBlank(aiOutboundTask.getDxRestrictCity()) && StringUtils.isNotBlank(aiOutboundTask.getDxRestrictProvince())) {
            if (StringUtils.isEmpty(taskSaveDTO.getDxRestrictCity())) {
                taskSaveDTO.setDxRestrictCity(aiOutboundTask.getDxRestrictCity());
            } else {
                String dxRestrictCity = taskSaveDTO.getDxRestrictCity();
                taskSaveDTO.setDxRestrictCity(getUniqueCityProvince(dxRestrictCity, aiOutboundTask.getDxRestrictCity()));
            }

            if (StringUtils.isEmpty(taskSaveDTO.getDxRestrictProvince())) {
                taskSaveDTO.setDxRestrictProvince(aiOutboundTask.getDxRestrictProvince());
            } else {
                String dxRestrictProvince = taskSaveDTO.getDxRestrictProvince();
                taskSaveDTO.setDxRestrictProvince(getUniqueCityProvince(dxRestrictProvince, aiOutboundTask.getDxRestrictProvince()));
            }
        }

        //联通
        if (StringUtils.isNotBlank(aiOutboundTask.getLtRestrictCity()) && StringUtils.isNotBlank(aiOutboundTask.getLtRestrictProvince())) {
            if (StringUtils.isEmpty(taskSaveDTO.getLtRestrictCity())) {
                taskSaveDTO.setLtRestrictCity(aiOutboundTask.getLtRestrictCity());
            } else {
                String ltRestrictCity = taskSaveDTO.getLtRestrictCity();
                taskSaveDTO.setLtRestrictCity(getUniqueCityProvince(ltRestrictCity, aiOutboundTask.getLtRestrictCity()));
            }

            if (StringUtils.isEmpty(taskSaveDTO.getLtRestrictProvince())) {
                taskSaveDTO.setLtRestrictProvince(aiOutboundTask.getLtRestrictProvince());
            } else {
                String ltRestrictProvince = taskSaveDTO.getLtRestrictProvince();
                taskSaveDTO.setLtRestrictProvince(getUniqueCityProvince(ltRestrictProvince, aiOutboundTask.getLtRestrictProvince()));
            }
        }

        //移动
        if (StringUtils.isNotBlank(aiOutboundTask.getYdRestrictCity()) && StringUtils.isNotBlank(aiOutboundTask.getYdRestrictProvince())) {
            if (StringUtils.isEmpty(taskSaveDTO.getYdRestrictCity())) {
                taskSaveDTO.setYdRestrictCity(aiOutboundTask.getYdRestrictCity());
            } else {
                String ydRestrictCity = taskSaveDTO.getYdRestrictCity();
                taskSaveDTO.setYdRestrictCity(getUniqueCityProvince(ydRestrictCity, aiOutboundTask.getYdRestrictCity()));
            }

            if (StringUtils.isEmpty(taskSaveDTO.getYdRestrictProvince())) {
                taskSaveDTO.setYdRestrictProvince(aiOutboundTask.getYdRestrictProvince());
            } else {
                String ydRestrictProvince = taskSaveDTO.getYdRestrictProvince();
                taskSaveDTO.setYdRestrictProvince(getUniqueCityProvince(ydRestrictProvince, aiOutboundTask.getYdRestrictProvince()));
            }
        }

        //未知
        if (StringUtils.isNotBlank(aiOutboundTask.getUnknownRestrictCity()) && StringUtils.isNotBlank(aiOutboundTask.getUnknownRestrictProvince())) {
            if (StringUtils.isEmpty(taskSaveDTO.getUnknownRestrictCity())) {
                taskSaveDTO.setUnknownRestrictCity(aiOutboundTask.getUnknownRestrictCity());
            } else {
                String unknownRestrictCity = taskSaveDTO.getUnknownRestrictCity();
                taskSaveDTO.setUnknownRestrictCity(getUniqueCityProvince(unknownRestrictCity, aiOutboundTask.getUnknownRestrictCity()));
            }

            if (StringUtils.isEmpty(taskSaveDTO.getUnknownRestrictProvince())) {
                taskSaveDTO.setUnknownRestrictProvince(aiOutboundTask.getUnknownRestrictProvince());
            } else {
                String unknownRestrictProvince = taskSaveDTO.getUnknownRestrictProvince();
                taskSaveDTO.setUnknownRestrictProvince(getUniqueCityProvince(unknownRestrictProvince, aiOutboundTask.getUnknownRestrictProvince()));
            }
        }

        aiTaskWriteService.updateRestrictAreaTaskById(taskSaveDTO.getId(), taskSaveDTO.getAllRestrictCity(), taskSaveDTO.getAllRestrictProvince(),
                taskSaveDTO.getVirtualRestrictCity(), taskSaveDTO.getVirtualRestrictCity(),
                taskSaveDTO.getUnknownRestrictCity(), taskSaveDTO.getUnknownRestrictProvince(),
                taskSaveDTO.getDxRestrictCity(), taskSaveDTO.getDxRestrictProvince(),
                taskSaveDTO.getLtRestrictCity(), taskSaveDTO.getLtRestrictProvince(),
                taskSaveDTO.getYdRestrictCity(), taskSaveDTO.getYdRestrictProvince(), "执行成功");
        return taskSaveDTO;
    }

    public String getUniqueCityProvince(String areaOld, String areaAppend) {
        String[] splitArea = areaOld.split(",");
        Set<String> areaSet = new LinkedHashSet<>(Arrays.asList(splitArea));
        String[] splitAreaAppend = areaAppend.split(",");
        areaSet.addAll(Arrays.asList(splitAreaAppend));
        return String.join(",", areaSet);
    }

    public String getReduceCity(String areaOld, String areaReduce) {
        String[] splitArea = areaOld.split(",");
        Set<String> areaSet = new LinkedHashSet<>(Arrays.asList(splitArea));
        String[] splitAreaReduce = areaReduce.split(",");
        Set<String> reduceSet = new HashSet<>(Arrays.asList(splitAreaReduce));
        areaSet.removeAll(reduceSet);
        if (areaSet.isEmpty()) {
            return null;
        }
        return String.join(",", areaSet);
    }

    public String getProvince(String cityCode) {
        String[] splitArea = cityCode.split(",");
        Set<String> areaSet = new LinkedHashSet<>();
        for (String area : splitArea) {
            areaSet.add(area.substring(0, 2) + "0000");
        }
        return String.join(",", areaSet);
    }

    public List<AIOutboundTask> setRestrictAreaReduceBatch(AIOutboundTask aiOutboundTask) {
        String[] taskIdList = aiOutboundTask.getTaskIds().split(",");
        List<AIOutboundTask> res = new ArrayList<>();
        for (String taskId : taskIdList) {
            aiOutboundTask.setId(Long.parseLong(taskId));
            res.add(setRestrictAreaReduce(aiOutboundTask));
        }
        return res;
    }


    public AIOutboundTask setRestrictAreaReduce(AIOutboundTask aiOutboundTask) {
        Optional<AIOutboundTask> optional = aiOutboundTaskRepository.findById(aiOutboundTask.getId());
        if (!optional.isPresent()) {
            AIOutboundTask aiOutboundTask1 = new AIOutboundTask();
            aiOutboundTask1.setBatchStatus("执行失败");
            return aiOutboundTask1;
        }
        AIOutboundTask taskSaveDTO = optional.get();
        deleteTaskFromRedis(taskSaveDTO.getTaskName(), String.valueOf(taskSaveDTO.getId()), taskSaveDTO.getPhoneNum());

        //全部
        if (StringUtils.isNotBlank(aiOutboundTask.getAllRestrictCity()) && StringUtils.isNotBlank(aiOutboundTask.getAllRestrictProvince())) {
            if (StringUtils.isNotEmpty(taskSaveDTO.getAllRestrictCity())) {
                String allRestrictCity = taskSaveDTO.getAllRestrictCity();
                taskSaveDTO.setAllRestrictCity(getReduceCity(allRestrictCity, aiOutboundTask.getAllRestrictCity()));
            }
            if (StringUtils.isEmpty(taskSaveDTO.getAllRestrictCity())) {
                taskSaveDTO.setAllRestrictProvince(null);
            } else {
                String province = getProvince(taskSaveDTO.getAllRestrictCity());
                taskSaveDTO.setAllRestrictProvince(province);
            }
        }

        //电信
        if (StringUtils.isNotBlank(aiOutboundTask.getDxRestrictCity()) && StringUtils.isNotBlank(aiOutboundTask.getDxRestrictProvince())) {
            if (StringUtils.isNotEmpty(taskSaveDTO.getDxRestrictCity())) {
                String dxRestrictCity = taskSaveDTO.getDxRestrictCity();
                taskSaveDTO.setDxRestrictCity(getReduceCity(dxRestrictCity, aiOutboundTask.getDxRestrictCity()));
            }
            if (StringUtils.isEmpty(taskSaveDTO.getDxRestrictCity())) {
                taskSaveDTO.setDxRestrictProvince(null);
            } else {
                String province = getProvince(taskSaveDTO.getDxRestrictCity());
                taskSaveDTO.setDxRestrictProvince(province);
            }
        }

        //联通
        if (StringUtils.isNotBlank(aiOutboundTask.getLtRestrictCity()) && StringUtils.isNotBlank(aiOutboundTask.getLtRestrictProvince())) {
            if (StringUtils.isNotEmpty(taskSaveDTO.getLtRestrictCity())) {
                String ltRestrictCity = taskSaveDTO.getLtRestrictCity();
                taskSaveDTO.setLtRestrictCity(getReduceCity(ltRestrictCity, aiOutboundTask.getLtRestrictCity()));
            }
            if (StringUtils.isEmpty(taskSaveDTO.getLtRestrictCity())) {
                taskSaveDTO.setLtRestrictProvince(null);
            } else {
                String province = getProvince(taskSaveDTO.getLtRestrictCity());
                taskSaveDTO.setLtRestrictProvince(province);
            }
        }

        //移动
        if (StringUtils.isNotBlank(aiOutboundTask.getYdRestrictCity()) && StringUtils.isNotBlank(aiOutboundTask.getYdRestrictProvince())) {
            if (StringUtils.isNotEmpty(taskSaveDTO.getYdRestrictCity())) {
                String ydRestrictCity = taskSaveDTO.getYdRestrictCity();
                taskSaveDTO.setYdRestrictCity(getReduceCity(ydRestrictCity, aiOutboundTask.getYdRestrictCity()));
            }
            if (StringUtils.isEmpty(taskSaveDTO.getYdRestrictCity())) {
                taskSaveDTO.setYdRestrictProvince(null);
            } else {
                String province = getProvince(taskSaveDTO.getYdRestrictCity());
                taskSaveDTO.setYdRestrictProvince(province);
            }
        }

        //未知
        if (StringUtils.isNotBlank(aiOutboundTask.getUnknownRestrictCity()) && StringUtils.isNotBlank(aiOutboundTask.getUnknownRestrictProvince())) {
            if (StringUtils.isNotEmpty(taskSaveDTO.getUnknownRestrictCity())) {
                String unknownRestrictCity = taskSaveDTO.getUnknownRestrictCity();
                taskSaveDTO.setUnknownRestrictCity(getReduceCity(unknownRestrictCity, aiOutboundTask.getUnknownRestrictCity()));
            }
            if (StringUtils.isEmpty(taskSaveDTO.getUnknownRestrictCity())) {
                taskSaveDTO.setUnknownRestrictProvince(null);
            } else {
                String province = getProvince(taskSaveDTO.getUnknownRestrictCity());
                taskSaveDTO.setUnknownRestrictProvince(province);
            }
        }

        aiTaskWriteService.updateRestrictAreaTaskById(taskSaveDTO.getId(), taskSaveDTO.getAllRestrictCity(), taskSaveDTO.getAllRestrictProvince(),
                taskSaveDTO.getVirtualRestrictCity(), taskSaveDTO.getVirtualRestrictCity(),
                taskSaveDTO.getUnknownRestrictCity(), taskSaveDTO.getUnknownRestrictProvince(),
                taskSaveDTO.getDxRestrictCity(), taskSaveDTO.getDxRestrictProvince(),
                taskSaveDTO.getLtRestrictCity(), taskSaveDTO.getLtRestrictProvince(),
                taskSaveDTO.getYdRestrictCity(), taskSaveDTO.getYdRestrictProvince(), "执行成功");
        return taskSaveDTO;
    }


    public void deleteById(Long id) {
        Optional<AIOutboundTask> byId = aiOutboundTaskRepository.findById(id);
        if (byId.isPresent()) {
            AIOutboundTask delTask = byId.get();
            long startTime = System.currentTimeMillis();
            log.info("删除任务开始：{} {} {}", id ,delTask.getTaskName(), delTask.getGroupId());
            aiOutboundTaskRepository.deleteById(id);
            if (delTask.getNextDayCall() != null && delTask.getNextDayCall() == 1) {
                redissonClient.getBucket(REDIS_TASK_TMP + delTask + "_permTask");
                aiPermanentTaskAdminRepository.deleteByTaskId(delTask.getId());
            }
            List<PhoneRecord> phones = phoneRecordRepository.findAllByTaskId(id.toString());
            for (PhoneRecord p : phones) {
                p.setCallStatus("已删除");
            }
            deleteTaskFromRedis(delTask.getTaskName(), String.valueOf(delTask.getId()), delTask.getPhoneNum());
            phoneRecordRepository.saveAll(phones);
            log.info("删除任务完成：{} {} {} 耗时:{}", id ,delTask.getTaskName(), delTask.getGroupId(), System.currentTimeMillis() - startTime);
        }
    }

    public AIOutboundTask createTaskOnPage(AIOutboundTask aiOutboundTask) {
        RAtomicLong max_task_id = redissonClient.getAtomicLong("MAX_TASK_ID");
        long taskId = max_task_id.addAndGet(1);
        aiOutboundTask.setId(taskId);
        aiOutboundTask.setStartWorkTimes(String.join(",", aiOutboundTask.getStartWorkTimeList()));
        aiOutboundTask.setEndWorkTimes(String.join(",", aiOutboundTask.getEndWorkTimeList()));
        aiOutboundTask.setCallStatus("待执行");
        aiOutboundTask.setBatchStatus("执行成功");
        aiOutboundTask.setAiAnswerNum(1);
        aiOutboundTask.setPhoneNum(0);
        aiOutboundTask.setCalledPhoneNum(0);
        aiOutboundTask.setPutThroughPhoneNum(0);
        aiOutboundTask.setFinishedPhoneNum(0);
        aiOutboundTask.setPhoneIntentionNum(0);
        aiOutboundTask.setAClassNum(0);

        aiOutboundTask.setCallRecordNum(0);
        aiOutboundTask.setGroupId(AIContext.getGroupId());
        initCallTeamsWhenCreateTask(aiOutboundTask);
        TenantProgramAdmin tenantProgramAdmin = tenantProgramAdminRepository.findFirstByGroupId(AIContext.getGroupId());
        if (tenantProgramAdmin != null) {
            aiOutboundTask.setProductId(tenantProgramAdmin.getProductId());
            aiOutboundTask.setIndustrySecondFieldId(tenantProgramAdmin.getSecondIndustryId());
            aiOutboundTask.setProgramId(tenantProgramAdmin.getId().toString());
        }
        //设置variableSms
        List<TenantSmsTemplate> smsTemplates = aiOutboundTaskTemplateService.findSmsTemplateByScriptSmsHangUpSms(aiOutboundTask.getScriptSms(), aiOutboundTask.getHangUpSms());
        Map<String, VariableSmsPojo> usedVariables = aiOutboundTaskTemplateService.findVariableBySmsTemplates(smsTemplates);
        if (!usedVariables.isEmpty()) {
            aiOutboundTask.setVariableSms(new ArrayList<>(usedVariables.values()));
        }

        String groupId = aiOutboundTask.getGroupId();
        String[] parts = groupId.split("_");
        if (parts.length == 3 && "23".equals(parts[1])) {
            aiOutboundTask.setTaskName("ANT_" + aiOutboundTask.getTaskName());
        }
        AIOutboundTask saveTask = aiTaskWriteService.initTask(aiOutboundTask);

        if (aiOutboundTask.getNextDayCall() != null && aiOutboundTask.getNextDayCall() == 1) {
            AIPermanentTaskAdmin taskAdmin = new AIPermanentTaskAdmin();
            taskAdmin.setTaskStatus(2);
            taskAdmin.setGroupId(aiOutboundTask.getGroupId());
            taskAdmin.setTaskId(aiOutboundTask.getId());
            taskAdmin.setTaskName(aiOutboundTask.getTaskName());
            aiPermanentTaskAdminRepository.save(taskAdmin);
        }

        return saveTask;
    }

    public void editOnPage(AIOutboundTask aiOutboundTask) {
        Optional<AIOutboundTask> byId = aiOutboundTaskRepository.findById(aiOutboundTask.getId());
        if (byId.isPresent()) {
            AIOutboundTask saveDto = byId.get();
            deleteTaskFromRedis(saveDto.getTaskName(), saveDto.getId().toString(), saveDto.getPhoneNum());
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            if (!verifyVariableAndTemplatedEditTask(saveDto, aiOutboundTask)) {
                if (phoneRecordRepository.findPhoneRecordCountByTaskIdNoChangeSource(String.valueOf(saveDto.getId())) > 0) {
                    log.info("变量不匹配");
                    throw new ShowOnPageRemindException("变量不匹配,无法修改任务");
                }
            }
            edit(aiOutboundTask);
            //固定任务
            if ((aiOutboundTask.getNextDayCall() != null && aiOutboundTask.getNextDayCall() == 1) ||
                    (saveDto.getNextDayCall() != null && saveDto.getNextDayCall() == 1)) {
                AIPermanentTaskAdmin permanentTask = aiPermanentTaskAdminRepository.findByTaskId(aiOutboundTask.getId());
                if (permanentTask == null) {
                    AIPermanentTaskAdmin taskAdmin = new AIPermanentTaskAdmin();
                    taskAdmin.setTaskStatus(2);
                    taskAdmin.setGroupId(saveDto.getGroupId());
                    taskAdmin.setTaskId(saveDto.getId());
                    taskAdmin.setTaskName(aiOutboundTask.getTaskName() == null ? saveDto.getTaskName() : aiOutboundTask.getTaskName());
                    aiPermanentTaskAdminRepository.save(taskAdmin);
                } else {
                    permanentTask.setTaskStatus(2);
                    permanentTask.setTaskName(aiOutboundTask.getTaskName() == null ? saveDto.getTaskName() : aiOutboundTask.getTaskName());
                    aiPermanentTaskAdminRepository.save(permanentTask);
                }
                redissonClient.getBucket(REDIS_TASK_TMP + aiOutboundTask.getId() + "_permTask").delete();
            }

        } else {
            log.info("Task Id not exists: " + aiOutboundTask.getId());
        }
    }

    public void edit(AIOutboundTask aiOutboundTask) {
        if (aiOutboundTask.getId() == null) {
            log.info("Task id is null");
            return;
        }
        StringBuilder updateContent = new StringBuilder();
        if (CollectionUtils.isNotEmpty(aiOutboundTask.getStartWorkTimeList())) {
            updateContent.append("start_work_times = '").append(String.join(",", aiOutboundTask.getStartWorkTimeList())).append("', ");
            updateContent.append("end_work_times = '").append(String.join(",", aiOutboundTask.getEndWorkTimeList())).append("', ");
        }
        if (aiOutboundTask.getAutoReCall() != null) {
            if (aiOutboundTask.getAutoReCall() == 1) {
                updateContent.append("auto_re_call = ").append(aiOutboundTask.getAutoReCall()).append(", ");
                updateContent.append("call_ratio_type = ").append(aiOutboundTask.getCallRatioType()).append(", ");
                updateContent.append("first_recall_time = ").append(aiOutboundTask.getFirstRecallTime()).append(", ");
                updateContent.append("second_recall_time = ").append(aiOutboundTask.getSecondRecallTime()).append(", ");
            } else {
                updateContent.append("auto_re_call = ").append(aiOutboundTask.getAutoReCall()).append(", ");
                updateContent.append("call_ratio_type = null ,");
                updateContent.append("first_recall_time = null ,");
                updateContent.append("second_recall_time = null ,");
            }
        }
        if (StringUtils.isNotBlank(aiOutboundTask.getScriptStringId())) {
            updateContent.append("script_string_id = '").append(aiOutboundTask.getScriptStringId()).append("', ");
            updateContent.append("speech_craft_id = ").append(aiOutboundTask.getSpeechCraftId()).append(", ");
            updateContent.append("speech_craft_name = '").append(aiOutboundTask.getSpeechCraftName()).append("', ");
            updateContent.append("version = ").append(aiOutboundTask.getVersion()).append(", ");

            if (aiOutboundTask.getScriptSms() == null) {
                updateContent.append("script_sms = null ,");
            } else {
                updateContent.append("script_sms = '").append(JSON.toJSONString(aiOutboundTask.getScriptSms())).append("', ");
            }
            if (aiOutboundTask.getHangUpSms() == null) {
                updateContent.append("hang_up_sms = null ,");
            } else {
                updateContent.append("hang_up_sms = '").append(JSON.toJSONString(aiOutboundTask.getHangUpSms())).append("', ");
            }
            if (aiOutboundTask.getHangUpExcluded() == null) {
                updateContent.append("hang_up_excluded = null ,");
            } else {
                updateContent.append("hang_up_excluded = '").append(JSON.toJSONString(aiOutboundTask.getHangUpExcluded())).append("', ");
            }

            List<TenantSmsTemplate> smsTemplates = aiOutboundTaskTemplateService.findSmsTemplateByScriptSmsHangUpSms(aiOutboundTask.getScriptSms(), aiOutboundTask.getHangUpSms());
            Map<String, VariableSmsPojo> variableBySmsTemplates = aiOutboundTaskTemplateService.findVariableBySmsTemplates(smsTemplates);
            if (!variableBySmsTemplates.isEmpty()) {
                aiOutboundTask.setVariableSms(new ArrayList<>(variableBySmsTemplates.values()));
                updateContent.append("variable_sms = '").append(JSON.toJSONString(aiOutboundTask.getVariableSms())).append("', ");
            } else {
                updateContent.append("variable_sms = null ,");
            }
        }
        if (StringUtils.isNotBlank(aiOutboundTask.getTaskName())) {
            updateContent.append("task_name = '").append(aiOutboundTask.getTaskName()).append("', ");
        }
        if (aiOutboundTask.getIfLock() != null) {
            updateContent.append("if_lock = ").append(aiOutboundTask.getIfLock()).append(", ");
        }
        if (aiOutboundTask.getTenantBlackList() != null) {
            if (aiOutboundTask.getTenantBlackList().isEmpty()) {
                updateContent.append("tenant_black_list = null ,");
            } else {
                updateContent.append("tenant_black_list = '").append(JSON.toJSONString(aiOutboundTask.getTenantBlackList())).append("', ");
            }
        }
        if (AIOutboundTaskType.AI_MANUAL.equals(aiOutboundTask.getTaskType())) {
            if (aiOutboundTask.getCallTeamIds() != null) {
                changeCallTeamsWhenEditTask(aiOutboundTask.getId(), aiOutboundTask);
                updateContent.append("call_team_ids = '").append(aiOutboundTask.getCallTeamIds()).append("', ");
            }
            if (aiOutboundTask.getLineRatio() != null) {
                updateContent.append("line_ratio = ").append(aiOutboundTask.getLineRatio()).append(", ");
            }
            if (aiOutboundTask.getCallTeamHandleType() != null) {
                updateContent.append("call_team_handle_type = '").append(aiOutboundTask.getCallTeamHandleType()).append("', ");
            }
            if (aiOutboundTask.getOccupyRate() != null) {
                updateContent.append("occupy_rate = ").append(aiOutboundTask.getOccupyRate()).append(", ");
            }
            if (aiOutboundTask.getVirtualSeatRatio() != null) {
                updateContent.append("virtual_seat_ratio = ").append(aiOutboundTask.getVirtualSeatRatio()).append(", ");
            }
        }
        if (updateContent.length() > 0) {
            updateContent.setLength(updateContent.length() - 2);
            aiTaskWriteService.taskEditBatchSave(updateContent.toString(), aiOutboundTask.getId());
            log.info("编辑任务 {}", aiOutboundTask);
        }
    }

    public List<AIOutboundTask> editBatch(AIOutboundTask aiOutboundTask) {
        String[] taskIdList = aiOutboundTask.getTaskIds().split(",");
        log.info("批量编辑任务：" + Arrays.toString(taskIdList));
        List<AIOutboundTask> res = new ArrayList<>();
        List<AIOutboundTask> validRes = new ArrayList<>();
        List<Long> permanentTaskIds = new ArrayList<>();
        for (String taskId : taskIdList) {
            Optional<AIOutboundTask> byId = aiOutboundTaskRepository.findById(Long.parseLong(taskId));
            if (byId.isPresent()) {
                AIOutboundTask saveDto = byId.get();
                AIOutboundTask taskStatus = new AIOutboundTask();
                taskStatus.setId(saveDto.getId());
                deleteTaskFromRedis(saveDto.getTaskName(), saveDto.getId().toString(), saveDto.getPhoneNum());
                if (!verifyVariableAndTemplatedEditTask(saveDto, aiOutboundTask)) {
                    if (phoneRecordRepository.findPhoneRecordCountByTaskIdNoChangeSource(String.valueOf(saveDto.getId())) > 0) {
                        taskStatus.setBatchStatus("执行失败");
                        res.add(taskStatus);
                        log.info("批量编辑变量不匹配 taskId: {}", taskId);
                        continue;
                    }
                }
                taskStatus.setBatchStatus("执行成功");
                validRes.add(taskStatus);
                res.add(taskStatus);
                if (saveDto.getNextDayCall() != null && saveDto.getNextDayCall() == 1) {
                    permanentTaskIds.add(saveDto.getId());
                }
            } else {
                log.info("Task Id not exists: " + taskId);
            }
        }
        for (AIOutboundTask task : validRes) {
            aiOutboundTask.setId(task.getId());
            edit(aiOutboundTask);
        }
        //固定任务
        for (Long permanentId : permanentTaskIds) {
            redissonClient.getBucket(REDIS_TASK_TMP + permanentId + "_permTask").delete();
        }
        return res;
    }

    public List<AIOutboundTask> volcanoEditScriptBatch(AIOutboundTask aiOutboundTask) {
        String[] taskIdList = aiOutboundTask.getTaskIds().split(",");
        log.info("火山批量编辑任务：" + Arrays.toString(taskIdList));
        List<AIOutboundTask> res = new ArrayList<>();
        List<AIOutboundTask> validRes = new ArrayList<>();
        for (String taskId : taskIdList) {
            Optional<AIOutboundTask> byId = aiOutboundTaskRepository.findById(Long.parseLong(taskId));
            if (byId.isPresent()) {
                AIOutboundTask saveDto = byId.get();
                AIOutboundTask taskStatus = new AIOutboundTask();
                taskStatus.setId(saveDto.getId());
                deleteTaskFromRedis(saveDto.getTaskName(), saveDto.getId().toString(), saveDto.getPhoneNum());
                taskStatus.setBatchStatus("执行成功");
                validRes.add(taskStatus);
                res.add(taskStatus);
            } else {
                log.info("Task Id not exists: " + taskId);
            }
        }
        for (AIOutboundTask task : validRes) {
            aiOutboundTask.setId(task.getId());
            volcanoEditScript(aiOutboundTask);
        }
        return res;
    }

    public void volcanoEditScript(AIOutboundTask aiOutboundTask) {
        if (aiOutboundTask.getId() == null) {
            log.info("Task id is null");
            return;
        }
        StringBuilder updateContent = new StringBuilder();
        if (StringUtils.isNotBlank(aiOutboundTask.getScriptStringId())) {
            updateContent.append("script_string_id = '").append(aiOutboundTask.getScriptStringId()).append("', ");
            updateContent.append("speech_craft_id = ").append(aiOutboundTask.getSpeechCraftId()).append(", ");
            updateContent.append("speech_craft_name = '").append(aiOutboundTask.getSpeechCraftName()).append("', ");
            updateContent.append("version = ").append(aiOutboundTask.getVersion()).append(", ");
        }
        if (updateContent.length() > 0) {
            updateContent.setLength(updateContent.length() - 2);
            aiTaskWriteService.volcanoEditScriptSave(updateContent.toString(), aiOutboundTask.getId());
            log.info("火山编辑任务 {}", aiOutboundTask);
        }
    }

    public void updateLineName(AIOutboundQueryDto aiOutboundQueryDto) {
        List<AIOutboundTask> data = aiOutboundTaskRepository.findAllByIdIn(Arrays.stream(aiOutboundQueryDto.getUpdateIds().split(",")).map(Long::valueOf).collect(Collectors.toList()));
        for (AIOutboundTask aiOutboundTask : data) {
            aiOutboundTask.setLineName(aiOutboundQueryDto.getLineName());
        }
        aiTaskWriteService.updateLineNameSaveAll(data);
    }


    public String getTaskUuidByNameFromRedis(String name) {
        String taskId = (String) redissonClient.getBucket("REDIS_TASK_TMP::" + name + "_id").get();
        return taskId;
    }

    @RedisLock(paramName = "taskName")
    public synchronized Long createTask(Long id, String taskName) {
        AIOutboundTask aiOutboundTask = new AIOutboundTask();
        if (StringUtils.isNotBlank(taskName)) {
            AIOutboundTask ifExistTask = aiOutboundTaskRepository.findFirstByTaskNameAndCreateTimeBetween(taskName, LocalDate.now().atStartOfDay(), LocalDate.now().atStartOfDay().plusDays(1L));
            if (ifExistTask != null) {
                return ifExistTask.getId();
            }
        }
        AIOutboundTaskTemplate aiOutboundTaskTemplate = aiOutboundTaskTemplateRepository.findById(id).get();
        BeanUtils.copyProperties(aiOutboundTaskTemplate, aiOutboundTask);
        RAtomicLong max_task_id = redissonClient.getAtomicLong("MAX_TASK_ID");
        long taskId = max_task_id.addAndGet(1);
        aiOutboundTask.setId(taskId);
        if (StringUtils.isNotBlank(taskName)) {
            aiOutboundTask.setTaskName(taskName);
        } else {
            taskName = aiOutboundTaskTemplate.getTaskName();
            Integer taskIncrId = aiOutboundTaskTemplate.getTaskIncrId();
            taskIncrId = taskIncrId == null ? 1 : taskIncrId + 1;
            taskName = taskName + "_" + taskIncrId;
            aiOutboundTaskTemplate.setTaskIncrId(taskIncrId);
            aiOutboundTask.setTaskName(taskName);
        }
        TenantProgramAdmin tenantProgramAdmin = tenantProgramAdminRepository.findFirstByGroupId(aiOutboundTaskTemplate.getGroupId());
        if (tenantProgramAdmin != null) {
            aiOutboundTask.setProductId(tenantProgramAdmin.getProductId());
            aiOutboundTask.setIndustrySecondFieldId(tenantProgramAdmin.getSecondIndustryId());
            aiOutboundTask.setProgramId(tenantProgramAdmin.getId().toString());
        }
        aiOutboundTask.setCallStatus("待执行");
        aiOutboundTask.setAiAnswerNum(1);
        aiOutboundTask.setPhoneNum(0);
        aiOutboundTask.setCalledPhoneNum(0);
        aiOutboundTask.setPutThroughPhoneNum(0);
        aiOutboundTask.setFinishedPhoneNum(0);
        aiOutboundTask.setGroupId(aiOutboundTaskTemplate.getGroupId());
        aiOutboundTask.setTenantName(aiOutboundTaskTemplate.getTenantName());
        aiOutboundTask.setFeeMinute(0);
        aiOutboundTask.setIsAutoStop(0);
        aiTaskWriteService.initTask(aiOutboundTask);
        aiOutboundTaskTemplateRepository.save(aiOutboundTaskTemplate);
        return aiOutboundTask.getId();
    }

    public void updateWorkTimes(AIOutboundQueryDto aiOutboundQueryDto) {
        List<AIOutboundTask> data = aiOutboundTaskRepository.findAllByIdIn(Arrays.stream(aiOutboundQueryDto.getUpdateIds().split(",")).map(Long::valueOf).collect(Collectors.toList()));
        List<String> startWorkTimeList = aiOutboundQueryDto.getStartWorkTimeList();
        List<String> endWorkTimeList = aiOutboundQueryDto.getEndWorkTimeList();
        String startWorkTimes = String.join(",", startWorkTimeList);
        String endWorkTimes = String.join(",", endWorkTimeList);
        for (AIOutboundTask aiOutboundTask : data) {
            aiOutboundTask.setStartWorkTimes(startWorkTimes);
            aiOutboundTask.setEndWorkTimes(endWorkTimes);
        }
        aiTaskWriteService.updateWorkTimesSaveAll(data);
    }

    public void reAddPhones() {
        List<AIOutboundTask> aiOutboundTaskList = aiOutboundTaskRepository.findAllByCreateTimeBetween(LocalDate.now().atStartOfDay(), LocalDate.now().plusDays(1L).atStartOfDay());
        for (AIOutboundTask aiOutboundTask : aiOutboundTaskList) {
            System.out.println("当前正在将任务：" + aiOutboundTask.getId() + "中待呼叫手机号添加进Redis");
            List<PhoneRecord> phoneRecordList = phoneRecordRepository.findAllByTaskIdAndCallStatus(aiOutboundTask.getId().toString(), "待呼叫");
            List<String> encryptedPhoneList = phoneRecordList.stream().map(PhoneRecord::getPhone).collect(Collectors.toList());
            JSONObject decryptionResultBatch = RaiYiEncryptionUtil.getDecryptionResultBatch(String.join(",", encryptedPhoneList));
            List<String> redisKeys = new ArrayList<>();
            for (PhoneRecord phoneRecord : phoneRecordList) {
                redisKeys.add((decryptionResultBatch == null ? phoneRecord.getPhone() : decryptionResultBatch.getString(phoneRecord.getPhone())) + "," + phoneRecord.getRecordId() + "," + phoneRecord.getPhone() + "," + phoneRecord.getProvince() + "," + phoneRecord.getProvinceCode() + "," + phoneRecord.getCity() + "," + phoneRecord.getCityCode() + "," + phoneRecord.getOperator());
            }
            RBatch batch = redissonClient.createBatch();
            RQueueAsync<Object> queue = batch.getQueue(aiOutboundTask.getId().toString());
            queue.addAllAsync(redisKeys);
            batch.execute();
        }
    }

    public void reAddPhones2(List<Long> ids) {
        List<AIOutboundTask> aiOutboundTaskList = aiOutboundTaskRepository.findAllByIdIn(ids);
        for (AIOutboundTask aiOutboundTask : aiOutboundTaskList) {
            System.out.println("当前正在将任务：" + aiOutboundTask.getId() + "中待呼叫手机号添加进Redis");
            List<PhoneRecord> phoneRecordList = phoneRecordRepository.findAllByTaskIdAndCallStatus(aiOutboundTask.getId().toString(), "待呼叫");
            int len = phoneRecordList.size();
            int step = 10000;
            for (int i = 0; i < (len % step == 0 ? len / step : len / step + 1); i++) {
                List<PhoneRecord> phoneRecordListSub = phoneRecordList.subList(i * step, Math.min((i + 1) * step, phoneRecordList.size()));
                List<String> encryptedPhoneList = phoneRecordListSub.stream().map(PhoneRecord::getPhone).collect(Collectors.toList());
                JSONObject decryptionResultBatch = RaiYiEncryptionUtil.getDecryptionResultBatch(String.join(",", encryptedPhoneList));
                System.out.println("当前正在将任务：" + aiOutboundTask.getId() + "中待呼叫手机号添加进Redis" + "[" + (i * step) + "," + (Math.min((i + 1) * step, phoneRecordList.size())) + "]");
                List<String> redisKeys = new ArrayList<>();
                for (PhoneRecord phoneRecord : phoneRecordListSub) {
                    redisKeys.add((decryptionResultBatch == null ? phoneRecord.getPhone() : decryptionResultBatch.getString(phoneRecord.getPhone())) + "," + phoneRecord.getRecordId() + "," + phoneRecord.getPhone() + "," + phoneRecord.getProvince() + "," + phoneRecord.getProvinceCode() + "," + phoneRecord.getCity() + "," + phoneRecord.getCityCode() + "," + phoneRecord.getOperator());
                }
                RBatch batch = redissonClient.createBatch();
                RQueueAsync<Object> queue = batch.getQueue(aiOutboundTask.getId().toString());
                queue.addAllAsync(redisKeys);
                batch.execute();
            }

        }

    }

    public void addMobileToRedis() {
        List<MobileBelong> all = mobileBelongRepository.findAll();
        Map<String, String> addMap = new HashMap<>();
        List<String> keys = new ArrayList<>();
        int count = 0;
        for (MobileBelong mobileBelong : all) {
            String key = "MOBILE_BELONG:";
            String section = mobileBelong.getSection();
            MobileBelongResultWrapper resultWrapper = new MobileBelongResultWrapper();
            resultWrapper.setProvince(mobileBelong.getProvince());
            resultWrapper.setProvinceCode(mobileBelong.getProvinceId());
            resultWrapper.setCity(mobileBelong.getCity());
            resultWrapper.setCityCode(mobileBelong.getCityId());
            resultWrapper.setOperator(mobileBelong.getName());
            resultWrapper.setSection(section);
            //将密文值放入Redis
            addMap.put(key + section, JsonUtil.objectToJson(resultWrapper));
            keys.add(key + section);
            count++;
            if (count % 5000 == 0) {
                redisTemplate.opsForValue().multiSet(addMap);
                addMap = new HashMap<>();
            }
        }
        redisTemplate.opsForValue().multiSet(addMap);
    }

    public boolean ifInWorkingTime(AIOutboundTask aiOutboundTask) {
        aiOutboundTask.setStartWorkTimeList(Arrays.stream(aiOutboundTask.getStartWorkTimes().split(",")).collect(Collectors.toList()));
        aiOutboundTask.setEndWorkTimeList(Arrays.stream(aiOutboundTask.getEndWorkTimes().split(",")).collect(Collectors.toList()));
        List<String> startWorkTimeList = aiOutboundTask.getStartWorkTimeList();
        List<String> endWorkTimeList = aiOutboundTask.getEndWorkTimeList();
        if (startWorkTimeList.size() != endWorkTimeList.size()) {
            return false;
        }
        LocalDateTime now = LocalDateTime.now();
        String preFix = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " ";
        String subFix = ":00";
        for (int i = 0; i < startWorkTimeList.size(); i++) {
            LocalDateTime sTime = LocalDateTime.parse(preFix + startWorkTimeList.get(i) + subFix, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            LocalDateTime eTime = LocalDateTime.parse(preFix + endWorkTimeList.get(i) + subFix, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            if (now.compareTo(sTime) >= 0 && now.compareTo(eTime) <= 0) {
                return true;
            }
        }
        return false;
    }

    public void checkIfNeedStop(Long taskId) {
        if (redisGetPhoneService.ifQueueEmpty(taskId)) {
            AIOutboundQueryDto aiOutboundQueryDto = new AIOutboundQueryDto();
            aiOutboundQueryDto.setCallStatus("已停止");
            aiOutboundQueryDto.setTaskId(taskId);
            aiOutboundTaskService.stopTask(aiOutboundQueryDto, true);
        }
    }

    public boolean switchTask(AIOutboundQueryDto aiOutboundQueryDto) {
        AIOutboundTask aiOutboundTask = aiOutboundTaskRepository.findById(aiOutboundQueryDto.getTaskId()).get();
        if (aiOutboundTask.getIfLock() != null && aiOutboundTask.getIfLock() == 1) {
            return false;
        }
        if (!"进行中".equals(aiOutboundTask.getCallStatus()) && !redisGetPhoneService.ifQueueEmpty(aiOutboundTask.getId())) {
            if (!verifyVariableAndTemplatedStartTask(aiOutboundTask)) {
                return false;
            }
            if (!checkTriggerNames(aiOutboundTask)) {
                return false;
            }
            deleteTaskFromRedis(aiOutboundTask.getTaskName(), aiOutboundTask.getId().toString(), aiOutboundTask.getPhoneNum());
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            aiOutboundTask.setTaskStartTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            aiOutboundTask.setCallStatus("进行中");
            aiOutboundTask.setAiAnswerNum(aiOutboundQueryDto.getConcurrent());
            aiOutboundTask.setLineId(aiOutboundQueryDto.getLineId());
            aiOutboundTask.setLineCode(aiOutboundQueryDto.getLineCode());
            aiOutboundTask.setLineName(aiOutboundQueryDto.getLineName());
            aiOutboundTask.setSmsTemplateAbnormal(0);
            tenantLineUtils.updateRemainConcurrentNumWithCAS(aiOutboundTask.getLineId(), -aiOutboundTask.getAiAnswerNum());
        } else if ("进行中".equals(aiOutboundTask.getCallStatus())) {
            aiOutboundTask.setTaskEndTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            Integer totalNum = phoneRecordDailyHistoryService.findPhoneNums(0, 0, null, aiOutboundTask);
            Integer finishedNum = phoneRecordDailyHistoryService.findPhoneNums(0, 0, "呼叫完成", aiOutboundTask);
            Integer shieldedNum = phoneRecordDailyHistoryService.findPhoneNums(0, 0, "已屏蔽", aiOutboundTask);
            if (finishedNum + shieldedNum >= totalNum) {
                aiOutboundTask.setCallStatus("已停止");
            } else {
                aiOutboundTask.setCallStatus("未完成");
            }
            tenantLineUtils.updateRemainConcurrentNumWithCAS(aiOutboundTask.getLineId(), aiOutboundTask.getAiAnswerNum());
        }
        aiTaskWriteService.switchTaskSave(aiOutboundTask);
        sendTaskStatus(String.valueOf(aiOutboundTask.getId()), aiOutboundTask.getTaskName(), aiOutboundTask.getCallStatus(), aiOutboundTask.getGroupId(), "switchTask");
        return true;
    }

    public boolean checkTriggerNames(AIOutboundTask aiOutboundTask) {
        Optional<Script> script = scriptRepository.findById(aiOutboundTask.getSpeechCraftId());
        if (script.isPresent()) {
            Set<String> triggerNames = script.get().getSmsTriggerNames();
            if (aiOutboundTask.getScriptSms() == null) {
                return true;
            }
            Set<String> taskScriptSms = aiOutboundTask.getScriptSms().stream().map(ScriptSmsTriggerPojo::getTriggerName).collect(Collectors.toSet());
            List<Long> taskScriptTemplateIds = aiOutboundTask.getScriptSms().stream().filter(t -> {
                return Objects.nonNull(t.getSmsTemplateId());
            }).map(ScriptSmsTriggerPojo::getSmsTemplateId).collect(Collectors.toList());
            if (!taskScriptSms.equals(triggerNames) || taskScriptSms.size() != taskScriptTemplateIds.size()) {
                throw new ShowOnPageRemindException("任务启动失败，短信触发点未设置短信模板");
            }
        }
        return true;
    }

    public void deleteTaskFromRedis(String taskName, String taskId, int phoneNum) {
        RBucket<Object> bucketTaskIdBody = redissonClient.getBucket("REDIS_TASK_TMP::" + taskId + "_body");
        if (!bucketTaskIdBody.isExists()) {
            return;
        }
        long taskPhoneNum = redissonClient.getAtomicLong("REDIS_TASK_TMP::" + taskId + "_phoneNum").get();
        long taskSyncPhoneNum = redissonClient.getAtomicLong("REDIS_TASK_TMP::" + taskId + "_syncPhoneNum").get();
        long taskSyncRecordNum = redissonClient.getAtomicLong("REDIS_TASK_TMP::" + taskId + "_syncRecordNum").get();
        if (phoneNum != taskSyncPhoneNum || taskPhoneNum != taskSyncPhoneNum || phoneNum != taskSyncRecordNum) {
            log.info("deleteTaskFromRedis taskName: {} taskId: {} phoneNum: {} taskPhoneNum : {} taskSyncPhoneNum: {} taskSyncRecordNum: {}",
                    taskName, taskId, phoneNum, taskPhoneNum, taskSyncPhoneNum, taskSyncRecordNum);
            throw new AdminCheckException("任务" + taskName + "还在传输数据，不具备启动/操作条件");
        }
        String templateId = (String) redissonClient.getBucket(REDIS_TASK_TMP + taskId + "_templateId").get();
        RBucket<Object> bucketTaskName = redissonClient.getBucket("REDIS_TASK_TMP::" + taskName + "_" + templateId + "_id");
        RSet<String> taskNameKeyList = redissonClient.getSet(REDIS_TASK_TMP + "taskNameKeyList");
        taskNameKeyList.remove(REDIS_TASK_TMP + taskName + "_" + templateId + "_id");
        taskNameKeyList.remove(REDIS_TASK_TMP + taskId + "_body");
        bucketTaskName.delete();
        bucketTaskIdBody.delete();
        log.info("deleteTaskFromRedis {} {} {}", taskName, templateId, taskId);
    }

    public AIOutboundTask startTask(AIOutboundQueryDto aiOutboundQueryDto) {
        AIOutboundTask aiOutboundTask = aiOutboundTaskRepository.findById(aiOutboundQueryDto.getTaskId()).get();
        if (aiOutboundTask.getIfLock() != null && aiOutboundTask.getIfLock() == 1) {
            aiOutboundTask.setBatchStatus("执行失败");
            return aiOutboundTask;
        }
        if (aiOutboundTask.getIsAutoStop() != null && aiOutboundTask.getIsAutoStop() == 1 && aiOutboundQueryDto.getIncludeAutoStop() == null) {
            aiOutboundTask.setBatchStatus("执行失败");
            return aiOutboundTask;
        }
        if (!checkTask(aiOutboundTask, aiOutboundQueryDto)) {
            aiOutboundTask.setBatchStatus("执行失败");
            return aiOutboundTask;
        }
        if (!verifyVariableAndTemplatedStartTask(aiOutboundTask)) {
            aiOutboundTask.setBatchStatus("执行失败");
            return aiOutboundTask;
        }
        if (!checkTriggerNames(aiOutboundTask)) {
            aiOutboundTask.setBatchStatus("执行失败");
            return aiOutboundTask;
        }
        aiOutboundTask.setTaskStartTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        aiOutboundTask.setBatchStatus("执行成功");
        if (!"进行中".equals(aiOutboundTask.getCallStatus()) && !redisGetPhoneService.ifQueueEmpty(aiOutboundTask.getId())) {
            //删除redis中的key
            try {
                deleteTaskFromRedis(aiOutboundTask.getTaskName(), aiOutboundTask.getId().toString(), aiOutboundTask.getPhoneNum());
            } catch (AdminCheckException e) {
                aiOutboundTask.setBatchStatus("执行失败");
                return aiOutboundTask;
            }
            aiOutboundTask.setCallStatus("进行中");
            aiOutboundTask.setAiAnswerNum(aiOutboundQueryDto.getConcurrent());
            aiOutboundTask.setLineId(aiOutboundQueryDto.getLineId());
            aiOutboundTask.setLineCode(aiOutboundQueryDto.getLineCode());
            aiOutboundTask.setLineName(aiOutboundQueryDto.getLineName());
            aiOutboundTask.setSmsTemplateAbnormal(0);
            boolean ifSuccess = tenantLineUtils.updateRemainConcurrentNumWithCAS(aiOutboundTask.getLineId(), -aiOutboundTask.getAiAnswerNum());
            aiOutboundTask.setBatchStatus(ifSuccess ? "执行成功" : "执行失败");
            if (ifSuccess) {
                aiTaskWriteService.startTaskSave(aiOutboundTask);
                sendTaskStatus(String.valueOf(aiOutboundTask.getId()), aiOutboundTask.getTaskName(), aiOutboundTask.getCallStatus(), aiOutboundTask.getGroupId(), "startTask");
            }
        }
        return aiOutboundTask;
    }


    public AIOutboundTask startTaskWithTime(AIOutboundQueryDto aiOutboundQueryDto) {
        AIOutboundTask aiOutboundTask = aiOutboundTaskRepository.findById(aiOutboundQueryDto.getTaskId()).get();
        if (aiOutboundTask.getIfLock() != null && aiOutboundTask.getIfLock() == 1) {
            aiOutboundTask.setBatchStatus("执行失败");
            return aiOutboundTask;
        }
        if (aiOutboundTask.getIsAutoStop() != null && aiOutboundTask.getIsAutoStop() == 1 && aiOutboundQueryDto.getIncludeAutoStop() == null) {
            aiOutboundTask.setBatchStatus("执行失败");
            return aiOutboundTask;
        }
        if (!checkTask(aiOutboundTask, aiOutboundQueryDto)) {
            aiOutboundTask.setBatchStatus("执行失败");
            return aiOutboundTask;
        }
        if (!verifyVariableAndTemplatedStartTask(aiOutboundTask)) {
            aiOutboundTask.setBatchStatus("执行失败");
            return aiOutboundTask;
        }
        if (!checkTriggerNames(aiOutboundTask)) {
            aiOutboundTask.setBatchStatus("执行失败");
            return aiOutboundTask;
        }
        aiOutboundTask.setTaskStartTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        aiOutboundTask.setBatchStatus("执行成功");
        if (!"进行中".equals(aiOutboundTask.getCallStatus()) && !redisGetPhoneService.ifQueueEmpty(aiOutboundTask.getId())) {
            //删除redis中的key
            try {
                deleteTaskFromRedis(aiOutboundTask.getTaskName(), aiOutboundTask.getId().toString(), aiOutboundTask.getPhoneNum());
            } catch (AdminCheckException e) {
                aiOutboundTask.setBatchStatus("执行失败");
                return aiOutboundTask;
            }
            aiOutboundTask.setCallStatus("进行中");
            aiOutboundTask.setExpectedFinishTime(aiOutboundQueryDto.getExpectedFinishTime());
            LocalDateTime expectedFinishTime = LocalDateTime.parse(aiOutboundQueryDto.getExpectedFinishTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            Integer neededConcurrent = neededConcurrent(aiOutboundTask, expectedFinishTime);  //算出来的是一分钟需要消耗的手机号，并非真正的并发量
            System.out.println("任务" + aiOutboundTask.getId() + "根据时间计算的需要并发数:" + neededConcurrent);
            aiOutboundTask.setAiAnswerNum(BigDecimal.valueOf(neededConcurrent).divide(BigDecimal.valueOf(concurrentPhoneNumRatio), 0, RoundingMode.CEILING).intValue());
            aiOutboundTask.setLineId(aiOutboundQueryDto.getLineId());
            aiOutboundTask.setLineCode(aiOutboundQueryDto.getLineCode());
            aiOutboundTask.setLineName(aiOutboundQueryDto.getLineName());
            boolean ifSuccess = tenantLineUtils.updateRemainConcurrentNumWithCAS(aiOutboundTask.getLineId(), -aiOutboundTask.getAiAnswerNum());
            aiOutboundTask.setBatchStatus(ifSuccess ? "执行成功" : "执行失败");
            if (ifSuccess) {
                aiTaskWriteService.startTaskWithTimeSave(aiOutboundTask);
                sendTaskStatus(String.valueOf(aiOutboundTask.getId()), aiOutboundTask.getTaskName(), aiOutboundTask.getCallStatus(), aiOutboundTask.getGroupId(), "startTaskWithTime");
            }
        }
        return aiOutboundTask;
    }

    public boolean checkTask(AIOutboundTask aiOutboundTask, AIOutboundQueryDto aiOutboundQueryDto) {
        if (StringUtils.isBlank(aiOutboundTask.getScriptStringId()) || aiOutboundTask.getSpeechCraftId() == null || StringUtils.isBlank(aiOutboundTask.getSpeechCraftName())) {
            return false;
        }
        if (StringUtils.isBlank(aiOutboundQueryDto.getLineCode()) || aiOutboundQueryDto.getLineId() == null || StringUtils.isBlank(aiOutboundQueryDto.getLineName())) {
            return false;
        }
        return true;
    }

    private Integer neededConcurrent(AIOutboundTask aiOutboundTask, LocalDateTime expectedFinishTime) {
        Long remainExecuteMinutes = remainExecuteMinutes(aiOutboundTask, expectedFinishTime);
        if (remainExecuteMinutes == 0) return 0;
        int unfinishedNum = aiOutboundTask.getPhoneNum() - aiOutboundTask.getFinishedPhoneNum();
        return BigDecimal.valueOf(unfinishedNum).divide(BigDecimal.valueOf(remainExecuteMinutes), 0, RoundingMode.CEILING).intValue();
    }

    private Long remainExecuteMinutes(AIOutboundTask aiOutboundTask, LocalDateTime expectedFinishTime) {
        String[] startWorkTimes = aiOutboundTask.getStartWorkTimes().split(",");
        String[] endWorkTimes = aiOutboundTask.getEndWorkTimes().split(",");
        int n = startWorkTimes.length;
        LocalDateTime now = LocalDateTime.now();
        Long res = 0L;
        for (int i = 0; i < n; i++) {
            LocalDateTime startTime = LocalDateTime.parse(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " " + startWorkTimes[i] + ":00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            LocalDateTime endTime = LocalDateTime.parse(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " " + endWorkTimes[i] + ":00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            if (now.compareTo(endTime) >= 0) {
                continue;
            }
            if (expectedFinishTime.compareTo(startTime) < 0) {
                continue;
            }
            if (expectedFinishTime.compareTo(endTime) < 0) {
                endTime = expectedFinishTime;
            }
            startTime = startTime.compareTo(now) > 0 ? startTime : now;
            if (startTime.compareTo(endTime) >= 0) {
                continue;
            }
            res += Duration.between(startTime, endTime).toMinutes();
        }
        return res;
    }

    public AIOutboundTask stopTask(AIOutboundQueryDto aiOutboundQueryDto, boolean ifForce) {
        AIOutboundTask aiOutboundTask = aiOutboundTaskRepository.findById(aiOutboundQueryDto.getTaskId()).get();
        if (aiOutboundTask.getIfLock() != null && aiOutboundTask.getIfLock() == 1) {
            aiOutboundTask.setBatchStatus("执行失败");
            return aiOutboundTask;
        }
        try {
            if ("进行中".equals(aiOutboundTask.getCallStatus())) {
                if (ifForce) {
                    aiOutboundTask.setCallStatus("已停止");
                } else {
                    Integer totalNum = phoneRecordDailyHistoryService.findPhoneNums(0, 0, null, aiOutboundTask);
                    Integer finishedNum = phoneRecordDailyHistoryService.findPhoneNums(0, 0, "呼叫完成", aiOutboundTask);
                    Integer shieldNum = phoneRecordDailyHistoryService.findPhoneNums(0, 0, "已屏蔽", aiOutboundTask);
                    if (finishedNum + shieldNum >= totalNum) {
                        aiOutboundTask.setCallStatus("已停止");
                    } else {
                        aiOutboundTask.setCallStatus("未完成");
                    }
                }
                if (aiOutboundTask.getLineId() != null) {
                    boolean ifSuccess = tenantLineUtils.updateRemainConcurrentNumWithCAS(aiOutboundTask.getLineId(), aiOutboundTask.getAiAnswerNum());
                }
                aiOutboundTask.setTaskEndTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            } else {
                if (ifForce) {
                    aiOutboundTask.setCallStatus("已停止");
                }
            }
            aiTaskWriteService.updateStatus(aiOutboundTask.getId(), aiOutboundTask.getCallStatus(), aiOutboundTask.getTaskEndTime());
            sendTaskStatus(String.valueOf(aiOutboundTask.getId()), aiOutboundTask.getTaskName(), aiOutboundTask.getCallStatus(), aiOutboundTask.getGroupId(), "stopTask");
            aiOutboundTask.setBatchStatus("执行成功");
        } catch (Exception e) {
            aiOutboundTask.setBatchStatus("执行失败");
        }
        return aiOutboundTask;
    }

    public void stopTaskForChannelPending(Long taskId) {
        Optional<AIOutboundTask> taskOptional = aiOutboundTaskRepository.findById(taskId);
        if (!taskOptional.isPresent()) {
            log.info("Exception=>任务:{}停止失败,任务未找到", taskId);
            return;
        }
        AIOutboundTask aiOutboundTask = taskOptional.get();
        if (aiOutboundTask.getIfLock() != null && aiOutboundTask.getIfLock() == 1) {
            aiOutboundTask.setBatchStatus("执行失败");
            log.info("Exception=>任务:{}停止失败,任务已锁住", taskId);
            return;
        }
        try {
            if ("进行中".equals(aiOutboundTask.getCallStatus())) {
                Integer totalNum = phoneRecordDailyHistoryService.findPhoneNums(0, 0, null, aiOutboundTask);
                Integer finishedNum = phoneRecordDailyHistoryService.findPhoneNums(0, 0, "呼叫完成", aiOutboundTask);
                Integer shieldNum = phoneRecordDailyHistoryService.findPhoneNums(0, 0, "已屏蔽", aiOutboundTask);
                if (finishedNum + shieldNum >= totalNum) {
                    aiOutboundTask.setCallStatus("已停止");
                } else {
                    aiOutboundTask.setCallStatus("未完成");
                }
                if (aiOutboundTask.getLineId() != null) {
                    tenantLineUtils.updateRemainConcurrentNumWithCAS(aiOutboundTask.getLineId(), aiOutboundTask.getAiAnswerNum());
                }
                aiOutboundTask.setTaskEndTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                sendTaskStatus(String.valueOf(aiOutboundTask.getId()), aiOutboundTask.getTaskName(), aiOutboundTask.getCallStatus(), aiOutboundTask.getGroupId(), "stopTaskForChannelPending");
            }
            aiTaskWriteService.updateStatusForChannelPending(
                    aiOutboundTask.getId(),
                    aiOutboundTask.getCallStatus(),
                    aiOutboundTask.getTaskEndTime());
        } catch (Exception e) {
            log.info("Exception=>任务:{}停止失败:{}", taskId, e.getMessage());
        }
    }

    public List<AIOutboundTask> startTaskBatch(AIOutboundQueryDto aiOutboundQueryDto) {
        String[] taskIdList = aiOutboundQueryDto.getTaskIds().split(",");
        List<AIOutboundTask> res = new ArrayList<>();
        if (aiOutboundQueryDto.getTotalConcurrent() == null) {
            for (String taskId : taskIdList) {
                aiOutboundQueryDto.setTaskId(Long.parseLong(taskId));
                if (aiOutboundQueryDto.getConcurrent() == null) {
                    res.add(startTaskWithTime(aiOutboundQueryDto));
                } else {
                    res.add(startTask(aiOutboundQueryDto));
                }
            }
        } else {
            int taskSize = taskIdList.length;
            int totalConcurrent = aiOutboundQueryDto.getTotalConcurrent();
            if (totalConcurrent < taskSize) {
                return res;
            }
            List<Long> taskIdLongList = Arrays.stream(taskIdList)
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
            List<AIOutboundTask> allByIdIn = aiOutboundTaskRepository.findAllByIdIn(taskIdLongList);
            Map<String, AIOutboundTask> taskMap = allByIdIn.stream().collect(Collectors.toMap(task -> String.valueOf(task.getId()), task -> task));
            int totalPhoneNum = 0;
            int totalCalledNum = 0;
            for (AIOutboundTask task : allByIdIn) {

                totalPhoneNum += Math.abs(task.getPhoneNum());
                totalCalledNum += Math.abs(task.getFinishedPhoneNum());
            }
            Map<String, Integer> concurrentMap = new HashMap<>();
            int totalSum = totalPhoneNum - totalCalledNum;
            if (totalSum <= 0) {
                return res;
            }
            int usedConcurrent = 0;
            for (String taskId : taskIdList) {
                AIOutboundTask task = taskMap.get(taskId);
                double concurrentDouble = totalConcurrent * Math.abs((task.getPhoneNum() - task.getFinishedPhoneNum()) * 1.0 / totalSum);
                int concurrent = (int) concurrentDouble;
                if (concurrent <= 0) {
                    concurrent = 1;
                }
                concurrentMap.put(taskId, concurrent);
                usedConcurrent += concurrent;
            }
            if (totalConcurrent - usedConcurrent > 0) {
                int distributedConcurrent = totalConcurrent - usedConcurrent;
                int i = 0;
                while (distributedConcurrent > 0) {
                    String taskId = taskIdList[i % taskIdList.length];
                    concurrentMap.put(taskId, concurrentMap.get(taskId) + 1);
                    distributedConcurrent--;
                    i++;
                }
            }
            for (String taskId : taskIdList) {
                Integer concurrent = concurrentMap.get(taskId);
                aiOutboundQueryDto.setTaskId(Long.parseLong(taskId));
                aiOutboundQueryDto.setConcurrent(concurrent);
                res.add(startTask(aiOutboundQueryDto));
                log.info("任务 {} 并发计算数: {}", taskId, concurrent);
            }
        }
        return res;
    }

    public List<AIOutboundTask> stopTaskBatch(AIOutboundQueryDto aiOutboundQueryDto) {
        String[] taskIdList = aiOutboundQueryDto.getTaskIds().split(",");
        List<AIOutboundTask> res = new ArrayList<>();
        for (String taskId : taskIdList) {
            aiOutboundQueryDto.setTaskId(Long.parseLong(taskId));
            res.add(stopTask(aiOutboundQueryDto, false));
        }
        return res;
    }

    public void stopTaskBatchForChannelPending(List<Long> taskIds) {
        for (Long taskId : taskIds) {
            stopTaskForChannelPending(taskId);
        }
    }

    @Cacheable(value = "DISTRIBUTION::QUERY")
    public List<TaskDistributionDto> distributionQuery(Long taskId, List<String> operatorList, LocalDate qDate, String groupId) {
        List<String> taskIdList = new ArrayList<>();
        if (taskId == null) {
            List<AIOutboundTask> aiOutboundTaskList = aiOutboundTaskRepository.findTaskListByTimeAndGroupId(qDate.atStartOfDay(), qDate.plusDays(1L).atStartOfDay(), groupId);
            taskIdList = aiOutboundTaskList.stream().map(a -> a.getId().toString()).collect(Collectors.toList());
        } else {
            taskIdList = Collections.singletonList(taskId.toString());
        }
        return phoneRecordDailyHistoryService.getDistributionFromRedis(qDate, taskIdList, operatorList, groupId);
    }

    public TaskStatisticDto taskStatistic(LocalDate queryDate) {
        String groupId = AIContext.getGroupId();
        Admin adminById = adminRepository.findFirstById(Long.valueOf(groupId.split("_")[2]));
        List<AIOutboundTask> aiOutboundTaskList = aiOutboundTaskRepository.findTaskListByTimeAndGroupId(queryDate.atStartOfDay(), queryDate.plusDays(1L).atStartOfDay(), groupId);
        if (CollectionUtils.isEmpty(aiOutboundTaskList)) {
            return new TaskStatisticDto();
        }
        List<String> taskIdList = aiOutboundTaskList.stream().map(a -> String.valueOf(a.getId())).collect(Collectors.toList());
        TaskStatisticDto taskStatisticDto = new TaskStatisticDto();
        taskStatisticDto.setTotalTasksNum(taskIdList.size());
        for (AIOutboundTask aiOutboundTask : aiOutboundTaskList) {
            //特殊处理首呼剩余补呼剩余小于0时设置为0
            checkAITaskStatistic(aiOutboundTask);
            taskStatisticDto.setWaitingTasksNum(taskStatisticDto.getWaitingTasksNum() + (("待执行".equals(aiOutboundTask.getCallStatus()) || "未完成".equals(aiOutboundTask.getCallStatus())) ? 1 : 0));
            taskStatisticDto.setExecutingTasksNum(taskStatisticDto.getExecutingTasksNum() + ("进行中".equals(aiOutboundTask.getCallStatus()) ? 1 : 0));
            taskStatisticDto.setExecutedTasksNum(taskStatisticDto.getExecutedTasksNum() + ((!"待执行".equals(aiOutboundTask.getCallStatus())) ? 1 : 0));
            taskStatisticDto.setCalledNum(aiOutboundTask.getCalledPhoneNum() + taskStatisticDto.getCalledNum());
            taskStatisticDto.setRecalledRemainNum(aiOutboundTask.getRecallingPhoneNum() + taskStatisticDto.getRecalledRemainNum());
            taskStatisticDto.setPlanedNum(aiOutboundTask.getPhoneNum() + taskStatisticDto.getPlanedNum());
            taskStatisticDto.setPutThroughNum(aiOutboundTask.getPutThroughPhoneNum() + taskStatisticDto.getPutThroughNum());
            aiOutboundTask.setCallRecordNum(aiOutboundTask.getCallRecordNum() == null ? 0 : aiOutboundTask.getCallRecordNum());
            taskStatisticDto.setOutboundCalledNum(taskStatisticDto.getOutboundCalledNum() + aiOutboundTask.getCallRecordNum());
            taskStatisticDto.setOutboundTotalNum(taskStatisticDto.getOutboundTotalNum() + aiOutboundTask.getCallRecordNum() + aiOutboundTask.getCallingPhoneNum() + aiOutboundTask.getRecallingPhoneNum());
            aiOutboundTask.setPhoneIntentionNum(aiOutboundTask.getPhoneIntentionNum() == null ? 0 : aiOutboundTask.getPhoneIntentionNum());
            taskStatisticDto.setIntentionNums(taskStatisticDto.getIntentionNums() + aiOutboundTask.getPhoneIntentionNum());
            taskStatisticDto.setFirstCallRemainNum(taskStatisticDto.getFirstCallRemainNum() + aiOutboundTask.getCallingPhoneNum());
        }
//        Integer averageCallDuration = outboundCallRecordStatisticRepository.findAverageCallDuration(queryDate, taskIdList);
        Integer averageCallDuration = callRecordUnitSearchClickHouseService.findAverageCallDuration(queryDate, adminById.getAccount());
        taskStatisticDto.setAverageCallDurations(averageCallDuration);

        taskStatisticDto.setExecutingRates(taskStatisticDto.getTotalTasksNum() == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(taskStatisticDto.getExecutedTasksNum()).divide(BigDecimal.valueOf(taskStatisticDto.getTotalTasksNum()), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
        taskStatisticDto.setCalledRates(taskStatisticDto.getPlanedNum() == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(taskStatisticDto.getCalledNum()).divide(BigDecimal.valueOf(taskStatisticDto.getPlanedNum()), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
        taskStatisticDto.setPutThroughRates(taskStatisticDto.getCalledNum() == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(taskStatisticDto.getPutThroughNum()).divide(BigDecimal.valueOf(taskStatisticDto.getCalledNum()), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
        taskStatisticDto.setOutboundCalledRates(taskStatisticDto.getOutboundTotalNum() == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(taskStatisticDto.getOutboundCalledNum()).divide(BigDecimal.valueOf(taskStatisticDto.getOutboundTotalNum()), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));

        fillSMSStatistic(taskStatisticDto, queryDate, groupId);
        return taskStatisticDto;
    }


    public TaskStatisticDto taskStatistic(LocalDate queryDate, String groupId) {
        Admin adminById = adminRepository.findFirstById(Long.valueOf(groupId.split("_")[2]));
        List<AIOutboundTask> aiOutboundTaskList = aiOutboundTaskRepository.findTaskListByTimeAndGroupId(queryDate.atStartOfDay(), queryDate.plusDays(1L).atStartOfDay(), groupId);
        if (CollectionUtils.isEmpty(aiOutboundTaskList)) {
            return new TaskStatisticDto();
        }
        List<String> taskIdList = aiOutboundTaskList.stream().map(a -> String.valueOf(a.getId())).collect(Collectors.toList());
        TaskStatisticDto taskStatisticDto = new TaskStatisticDto();
        taskStatisticDto.setTotalTasksNum(taskIdList.size());
        for (AIOutboundTask aiOutboundTask : aiOutboundTaskList) {
            //特殊处理首呼剩余补呼剩余小于0时设置为0
            checkAITaskStatistic(aiOutboundTask);
            taskStatisticDto.setWaitingTasksNum(taskStatisticDto.getWaitingTasksNum() + (("待执行".equals(aiOutboundTask.getCallStatus()) || "未完成".equals(aiOutboundTask.getCallStatus())) ? 1 : 0));
            taskStatisticDto.setExecutingTasksNum(taskStatisticDto.getExecutingTasksNum() + ("进行中".equals(aiOutboundTask.getCallStatus()) ? 1 : 0));
            taskStatisticDto.setExecutedTasksNum(taskStatisticDto.getExecutedTasksNum() + ((!"待执行".equals(aiOutboundTask.getCallStatus())) ? 1 : 0));
            taskStatisticDto.setCalledNum(aiOutboundTask.getCalledPhoneNum() + taskStatisticDto.getCalledNum());
            taskStatisticDto.setRecalledRemainNum(aiOutboundTask.getRecallingPhoneNum() + taskStatisticDto.getRecalledRemainNum());
            taskStatisticDto.setPlanedNum(aiOutboundTask.getPhoneNum() + taskStatisticDto.getPlanedNum());
            taskStatisticDto.setPutThroughNum(aiOutboundTask.getPutThroughPhoneNum() + taskStatisticDto.getPutThroughNum());
            aiOutboundTask.setCallRecordNum(aiOutboundTask.getCallRecordNum() == null ? 0 : aiOutboundTask.getCallRecordNum());
            taskStatisticDto.setOutboundCalledNum(taskStatisticDto.getOutboundCalledNum() + aiOutboundTask.getCallRecordNum());
            taskStatisticDto.setOutboundTotalNum(taskStatisticDto.getOutboundTotalNum() + aiOutboundTask.getCallRecordNum() + aiOutboundTask.getCallingPhoneNum() + aiOutboundTask.getRecallingPhoneNum());
            aiOutboundTask.setPhoneIntentionNum(aiOutboundTask.getPhoneIntentionNum() == null ? 0 : aiOutboundTask.getPhoneIntentionNum());
            taskStatisticDto.setIntentionNums(taskStatisticDto.getIntentionNums() + aiOutboundTask.getPhoneIntentionNum());
            taskStatisticDto.setFirstCallRemainNum(taskStatisticDto.getFirstCallRemainNum() + aiOutboundTask.getCallingPhoneNum());
        }
        Integer averageCallDuration = callRecordUnitSearchClickHouseService.findAverageCallDuration(queryDate, adminById.getAccount());
        taskStatisticDto.setAverageCallDurations(averageCallDuration);

        taskStatisticDto.setExecutingRates(taskStatisticDto.getTotalTasksNum() == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(taskStatisticDto.getExecutedTasksNum()).divide(BigDecimal.valueOf(taskStatisticDto.getTotalTasksNum()), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
        taskStatisticDto.setCalledRates(taskStatisticDto.getPlanedNum() == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(taskStatisticDto.getCalledNum()).divide(BigDecimal.valueOf(taskStatisticDto.getPlanedNum()), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
        taskStatisticDto.setPutThroughRates(taskStatisticDto.getCalledNum() == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(taskStatisticDto.getPutThroughNum()).divide(BigDecimal.valueOf(taskStatisticDto.getCalledNum()), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
        taskStatisticDto.setOutboundCalledRates(taskStatisticDto.getOutboundTotalNum() == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(taskStatisticDto.getOutboundCalledNum()).divide(BigDecimal.valueOf(taskStatisticDto.getOutboundTotalNum()), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));

        fillSMSStatistic(taskStatisticDto, queryDate, groupId);
        return taskStatisticDto;
    }


    private void fillSMSStatistic(TaskStatisticDto taskStatisticDto, LocalDate queryDate, String groupId) {
        String startTime = queryDate.atStartOfDay().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String endTime = queryDate.atStartOfDay().plusDays(1L).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        int successCount;
        int triggerCount;
        LocalDate today = LocalDate.now();
        // 临界条件的判断
        boolean isDailySearch = today.isEqual(queryDate);
        if (isDailySearch) {
            successCount = SMSRecordRepository.findSuccessCountByGroupId(startTime, endTime, groupId);
            triggerCount = SMSRecordRepository.findTriggerCount(startTime, endTime, groupId);
        } else {
            successCount = SMSRecordHistoryRepository.findSuccessCountByGroupId(startTime, endTime, groupId);
            triggerCount = SMSRecordHistoryRepository.findTriggerCount(startTime, endTime, groupId);
        }

        taskStatisticDto.setSuccessSmsNum(successCount);
        taskStatisticDto.setTriggerSmsNum(triggerCount);
        taskStatisticDto.setSuccessSmsRate(triggerCount == 0 ? BigDecimal.ZERO :
                BigDecimal.valueOf(successCount).divide(BigDecimal.valueOf(triggerCount), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
    }

    private void checkAITaskStatistic(AIOutboundTask aiOutboundTask) {
        Integer callingPhoneNum = aiOutboundTask.getCallingPhoneNum();
        if (callingPhoneNum < 0) {
            aiOutboundTask.setCallingPhoneNum(0);
        }
        Integer recallingPhoneNum = aiOutboundTask.getRecallingPhoneNum();
        if (recallingPhoneNum < 0) {
            aiOutboundTask.setRecallingPhoneNum(0);
        }
    }

    /**
     * 添加缓存，一分钟之内只用一次
     *
     * @return
     */
    @Cacheable(value = RedisCacheConstant.TASK_NAME_CACHE_KEY)
    public List<TaskData> getTaskData() {
        LocalDateTime startTime = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
        LocalDateTime endTime = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59);
        List<AIOutboundTask> tasks = aiOutboundTaskRepository.findAllByUpdateTimeBetween(startTime, endTime);
        List<TaskData> list = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(tasks)) {
            tasks.stream().forEach(new Consumer<AIOutboundTask>() {
                @Override
                public void accept(AIOutboundTask aiOutboundTask) {
                    TaskData ele = new TaskData();
                    ele.setTaskId(aiOutboundTask.getId().toString());
                    ele.setTaskName(aiOutboundTask.getTaskName());
                    ele.setTaskStatus(aiOutboundTask.getCallStatus());
                    ele.setAllRestrictProvince(aiOutboundTask.getAllRestrictProvince());
                    ele.setAllRestrictCity(aiOutboundTask.getAllRestrictCity());
                    ele.setYdRestrictProvince(aiOutboundTask.getYdRestrictProvince());
                    ele.setYdRestrictCity(aiOutboundTask.getYdRestrictCity());
                    ele.setDxRestrictProvince(aiOutboundTask.getDxRestrictProvince());
                    ele.setDxRestrictCity(aiOutboundTask.getDxRestrictCity());
                    ele.setLtRestrictProvince(aiOutboundTask.getLtRestrictProvince());
                    ele.setLtRestrictCity(aiOutboundTask.getLtRestrictCity());
                    ele.setStartTime(aiOutboundTask.getStartWorkTimes());
                    ele.setEndTime(aiOutboundTask.getEndWorkTimes());
                    boolean b = ifInWorkingTime(aiOutboundTask.getStartWorkTimes(), aiOutboundTask.getEndWorkTimes(), LocalDateTime.now());
                    if ("进行中".equals(aiOutboundTask.getCallStatus()) && !b) {
                        //"非执行时间" 设置为 待执行
//                        log.info("任务id{}, {}不在可执行时间中",aiOutboundTask.getId(), aiOutboundTask.getTaskName());
                        ele.setTaskStatus("待执行");
                    }
                    list.add(ele);
                }
            });
        }
        //log.info("返回的任务数据"+JSON.toJSONString(list));
        return list;
    }

    public boolean ifInWorkingTime(String startTime, String endTime, LocalDateTime now) {
        if (StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) {
            return true;
        }

        List<String> startWorkTimeList = Arrays.asList(startTime.split(","));
        List<String> endWorkTimeList = Arrays.asList(endTime.split(","));
        if (startWorkTimeList.size() != endWorkTimeList.size()) {
            return false;
        }
        String preFix = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " ";
        String subFix = ":00";
        for (int i = 0; i < startWorkTimeList.size(); i++) {
            LocalDateTime sTime = LocalDateTime.parse(preFix + startWorkTimeList.get(i) + subFix, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            LocalDateTime eTime = LocalDateTime.parse(preFix + endWorkTimeList.get(i) + subFix, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            if (!now.isBefore(sTime) && !now.isAfter(eTime)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 不添加缓存
     *
     * @return
     */
    public TaskDataVO getTaskDataNoCache() {
        LocalDateTime startTime = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
        LocalDateTime endTime = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59);
        List<AIOutboundTask> tasks = aiOutboundTaskRepository.findAllByUpdateTimeBetween(startTime, endTime);
        // 任务数据
        Map<String, Integer> tenantLinesLockConcurrentLimit = findTenantLinesLockConcurrentLimit(tasks);

        List<TaskData> list = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(tasks)) {
            tasks.stream().forEach(new Consumer<AIOutboundTask>() {
                @Override
                public void accept(AIOutboundTask aiOutboundTask) {
                    TaskData ele = new TaskData();
                    ele.setTaskId(aiOutboundTask.getId().toString());
                    ele.setTaskName(aiOutboundTask.getTaskName());
                    ele.setTaskStatus(aiOutboundTask.getCallStatus());
                    ele.setAllRestrictProvince(aiOutboundTask.getAllRestrictProvince());
                    ele.setAllRestrictCity(aiOutboundTask.getAllRestrictCity());
                    ele.setYdRestrictProvince(aiOutboundTask.getYdRestrictProvince());
                    ele.setYdRestrictCity(aiOutboundTask.getYdRestrictCity());
                    ele.setDxRestrictProvince(aiOutboundTask.getDxRestrictProvince());
                    ele.setDxRestrictCity(aiOutboundTask.getDxRestrictCity());
                    ele.setLtRestrictProvince(aiOutboundTask.getLtRestrictProvince());
                    ele.setLtRestrictCity(aiOutboundTask.getLtRestrictCity());
                    ele.setStartTime(aiOutboundTask.getStartWorkTimes());
                    ele.setEndTime(aiOutboundTask.getEndWorkTimes());
                    boolean b = ifInWorkingTime(aiOutboundTask.getStartWorkTimes(), aiOutboundTask.getEndWorkTimes(), LocalDateTime.now());
                    if ("进行中".equals(aiOutboundTask.getCallStatus()) && !b) {
                        //"非执行时间" 设置为 待执行
//                        log.info("任务id{}, {}不在可执行时间中",aiOutboundTask.getId(), aiOutboundTask.getTaskName());
                        ele.setTaskStatus("待执行");
                    }
                    list.add(ele);
                }
            });
        }
        TaskDataVO taskDataVO = new TaskDataVO();
        taskDataVO.setTaskDataList(list);
        taskDataVO.setTenantLineLockMap(tenantLinesLockConcurrentLimit);
//        log.info("返回的任务数据" + JSON.toJSONString(taskDataVO));
        return taskDataVO;
    }

    public TaskData getSingleTaskData(String taskId) {
        Optional<AIOutboundTask> tasks = aiOutboundTaskRepository.findById(Long.valueOf(taskId));
        TaskData ele = null;
        if (tasks.isPresent()) {
            AIOutboundTask aiOutboundTask = tasks.get();
            ele = new TaskData();
            ele.setTaskId(aiOutboundTask.getId().toString());
            ele.setTaskName(aiOutboundTask.getTaskName());
            ele.setTaskStatus(aiOutboundTask.getCallStatus());
            ele.setAllRestrictProvince(aiOutboundTask.getAllRestrictProvince());
            ele.setAllRestrictCity(aiOutboundTask.getAllRestrictCity());
            ele.setYdRestrictProvince(aiOutboundTask.getYdRestrictProvince());
            ele.setYdRestrictCity(aiOutboundTask.getYdRestrictCity());
            ele.setDxRestrictProvince(aiOutboundTask.getDxRestrictProvince());
            ele.setDxRestrictCity(aiOutboundTask.getDxRestrictCity());
            ele.setLtRestrictProvince(aiOutboundTask.getLtRestrictProvince());
            ele.setLtRestrictCity(aiOutboundTask.getLtRestrictCity());
            ele.setStartTime(aiOutboundTask.getStartWorkTimes());
            ele.setEndTime(aiOutboundTask.getEndWorkTimes());
        }
        //log.info("返回的任务数据"+JSON.toJSONString(ele));
        return ele;
    }

    private void initCallTeamsWhenCreateTask(AIOutboundTask aiOutboundTask) {
        Long taskId = aiOutboundTask.getId();
        if (AIOutboundTaskType.AI_MANUAL.equals(aiOutboundTask.getTaskType())) {
            List<Long> callTeamIds = aiOutboundTask.getCallTeamIds();
            if (callTeamIds == null) {
                throw new ClueCheckException("人机协同任务需要配置坐席组");
            }
            List<CallTeam> callTeams = callTeamRepository.findAllById(callTeamIds);
            for (CallTeam callTeam : callTeams) {
                // 1. 坐席组和任务关联
                Set<Long> taskIds = callTeam.getTaskIds() == null ? new HashSet<>() : callTeam.getTaskIds();
                taskIds.add(aiOutboundTask.getId());
                callTeam.setTaskIds(taskIds);

                // 2. 初始化可签入坐席
                Map<Long, List<Long>> taskIdActiveCallSeatIdMap = callTeam.getTaskIdActiveCallSeatIdMap();
                taskIdActiveCallSeatIdMap = taskIdActiveCallSeatIdMap == null ? new HashMap<>() : taskIdActiveCallSeatIdMap;
                taskIdActiveCallSeatIdMap.put(taskId, callTeam.getCallSeatIds());
                callTeam.setTaskIdActiveCallSeatIdMap(taskIdActiveCallSeatIdMap);
            }
            callTeamRepository.saveAll(callTeams);
        }
    }

    private void changeCallTeamsWhenEditTask(Long taskId, AIOutboundTask aiOutboundTaskNew) {
        Optional<AIOutboundTask> byId = aiOutboundTaskRepository.findById(taskId);
        if (!byId.isPresent()) return;
        AIOutboundTask aiOutboundTaskOld = byId.get();
        if (AIOutboundTaskType.AI_MANUAL.equals(aiOutboundTaskOld.getTaskType())) {
            List<Long> callTeamIdsOld = aiOutboundTaskOld.getCallTeamIds();
            List<Long> callTeamIdsNew = aiOutboundTaskNew.getCallTeamIds();
            List<Long> needDelete = new LinkedList<>();
            List<Long> needAdd = new LinkedList<>();
            if (callTeamIdsOld == null && callTeamIdsNew == null) {
                return;
            }
            if (callTeamIdsOld != null && callTeamIdsNew != null) {
                needDelete = new ArrayList<>(Sets.difference(Sets.newHashSet(callTeamIdsOld), Sets.newHashSet(callTeamIdsNew)));
                needAdd = new ArrayList<>(Sets.difference(Sets.newHashSet(callTeamIdsNew), Sets.newHashSet(callTeamIdsOld)));
            }
            if (callTeamIdsOld == null) {
                needAdd = callTeamIdsNew;
            } else {
                needDelete = callTeamIdsOld;
            }

            List<CallTeam> callTeams = new ArrayList<>();
            for (Long callTeamIdOld : needDelete) {
                callTeamRepository.findById(callTeamIdOld).ifPresent(callTeam -> {
                    Set<Long> taskIds = callTeam.getTaskIds();
                    Map<Long, List<Long>> taskIdActiveCallSeatIdMap = callTeam.getTaskIdActiveCallSeatIdMap();
                    if (taskIds != null) {
                        taskIds.remove(taskId);
                    }
                    if (taskIdActiveCallSeatIdMap != null) {
                        taskIdActiveCallSeatIdMap.remove(taskId);
                    }
                    callTeams.add(callTeam);
                });
            }
            for (Long callTeamIdNew : needAdd) {
                callTeamRepository.findById(callTeamIdNew).ifPresent(callTeam -> {
                    Set<Long> taskIds = callTeam.getTaskIds() == null ? new HashSet<>() : callTeam.getTaskIds();
                    taskIds.add(taskId);
                    callTeam.setTaskIds(taskIds);
                    Map<Long, List<Long>> taskIdActiveCallSeatIdMap = callTeam.getTaskIdActiveCallSeatIdMap();
                    taskIdActiveCallSeatIdMap = taskIdActiveCallSeatIdMap == null ? new HashMap<>() : taskIdActiveCallSeatIdMap;
                    taskIdActiveCallSeatIdMap.put(taskId, callTeam.getCallSeatIds());
                    callTeam.setTaskIdActiveCallSeatIdMap(taskIdActiveCallSeatIdMap);
                    callTeams.add(callTeam);
                });
            }
            if (callTeams.size() != 0) {
                callTeamRepository.saveAll(callTeams);
            }
        }
    }

    public void lockTask(AIOutboundQueryDto aiOutboundQueryDto) {
        String[] taskIdList = aiOutboundQueryDto.getTaskIds().split(",");
        for (String taskId : taskIdList) {
            Optional<AIOutboundTask> taskOptional = aiOutboundTaskRepository.findById(Long.valueOf(taskId));
            if (taskOptional.isPresent()) {
                AIOutboundTask byId = taskOptional.get();
                deleteTaskFromRedis(byId.getTaskName(), byId.getId().toString(), byId.getPhoneNum());
                aiTaskWriteService.lockUnlockTaskById(1, Long.valueOf(taskId));
            }
        }
    }


    public void unLockTask(AIOutboundQueryDto aiOutboundQueryDto) {
        String[] taskIdList = aiOutboundQueryDto.getTaskIds().split(",");
        for (String taskId : taskIdList) {
            Optional<AIOutboundTask> taskOptional = aiOutboundTaskRepository.findById(Long.valueOf(taskId));
            if (taskOptional.isPresent()) {
                AIOutboundTask byId = taskOptional.get();
                deleteTaskFromRedis(byId.getTaskName(), byId.getId().toString(), byId.getPhoneNum());
                aiTaskWriteService.lockUnlockTaskById(0, Long.valueOf(taskId));
            }
        }
    }

    public List<AIOutboundTaskResponse> findListPageQuery(AIOutboundPageQueryDto aiOutboundQueryDto) {
        List<Tuple> tuples = aiOutboundTaskRepository.findTaskListByPageQueryDto(aiOutboundQueryDto.getStartTime(),
                aiOutboundQueryDto.getEndTime(),
                StringUtils.isEmpty(aiOutboundQueryDto.getTaskName()) ? null : "%" + aiOutboundQueryDto.getTaskName() + "%",
                StringUtils.isEmpty(aiOutboundQueryDto.getSpeechCraftName()) ? null : "%" + aiOutboundQueryDto.getSpeechCraftName() + "%",
                aiOutboundQueryDto.getTaskType(),
                "1".equals(aiOutboundQueryDto.getIfFindAll()) ? null : AIContext.getGroupId(),
                aiOutboundQueryDto.getPageNum(),
                aiOutboundQueryDto.getStartPage() * aiOutboundQueryDto.getPageNum()
        );
        return JpaResultUtils.processResult(tuples, AIOutboundTaskResponse.class);
    }


    public List<AIOutboundTaskResponse> findListPageQueryForOperation(AIOutboundPageQueryDto aiOutboundQueryDto) {
        List<Tuple> tuples = aiOutboundTaskRepository.findTaskListByPageQueryDto(aiOutboundQueryDto.getStartTime(),
                aiOutboundQueryDto.getEndTime(),
                StringUtils.isEmpty(aiOutboundQueryDto.getTaskName()) ? null : "%" + aiOutboundQueryDto.getTaskName() + "%",
                StringUtils.isEmpty(aiOutboundQueryDto.getSpeechCraftName()) ? null : "%" + aiOutboundQueryDto.getSpeechCraftName() + "%",
                aiOutboundQueryDto.getTaskType(),
                "1".equals(aiOutboundQueryDto.getIfFindAll()) ? null : aiOutboundQueryDto.getGroupId(),
                aiOutboundQueryDto.getPageNum(),
                aiOutboundQueryDto.getStartPage() * aiOutboundQueryDto.getPageNum()
        );
        return JpaResultUtils.processResult(tuples, AIOutboundTaskResponse.class);
    }

    public Integer findListNumPageQuery(AIOutboundPageQueryDto aiOutboundQueryDto) {
        return aiOutboundTaskRepository.findTaskListNumByPageQueryDto(aiOutboundQueryDto.getStartTime(),
                aiOutboundQueryDto.getEndTime(),
                StringUtils.isEmpty(aiOutboundQueryDto.getTaskName()) ? null : "%" + aiOutboundQueryDto.getTaskName() + "%",
                StringUtils.isEmpty(aiOutboundQueryDto.getSpeechCraftName()) ? null : "%" + aiOutboundQueryDto.getSpeechCraftName() + "%",
                aiOutboundQueryDto.getTaskType(),
                "1".equals(aiOutboundQueryDto.getIfFindAll()) ? null : AIContext.getGroupId()
        );
    }

    public Integer findListNumPageQueryForOperation(AIOutboundPageQueryDto aiOutboundQueryDto) {
        return aiOutboundTaskRepository.findTaskListNumByPageQueryDto(aiOutboundQueryDto.getStartTime(),
                aiOutboundQueryDto.getEndTime(),
                StringUtils.isEmpty(aiOutboundQueryDto.getTaskName()) ? null : "%" + aiOutboundQueryDto.getTaskName() + "%",
                StringUtils.isEmpty(aiOutboundQueryDto.getSpeechCraftName()) ? null : "%" + aiOutboundQueryDto.getSpeechCraftName() + "%",
                aiOutboundQueryDto.getTaskType(),
                "1".equals(aiOutboundQueryDto.getIfFindAll()) ? null : aiOutboundQueryDto.getGroupId()
        );
    }

    public Integer neededConcurrentTotal(AIOutboundQueryDto aiOutboundQueryDto) {
        String[] taskIdList = aiOutboundQueryDto.getTaskIds().split(",");
        int res = 0;
        for (String taskId : taskIdList) {
            aiOutboundQueryDto.setTaskId(Long.parseLong(taskId));
            AIOutboundTask aiOutboundTask = aiOutboundTaskRepository.findById(aiOutboundQueryDto.getTaskId()).get();
            LocalDateTime expectedFinishTime = LocalDateTime.parse(aiOutboundQueryDto.getExpectedFinishTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            Integer neededConcurrent = neededConcurrent(aiOutboundTask, expectedFinishTime);  //算出来的是一分钟需要消耗的手机号，并非真正的并发量
            res += BigDecimal.valueOf(neededConcurrent).divide(BigDecimal.valueOf(concurrentPhoneNumRatio), 0, RoundingMode.CEILING).intValue();
        }
        return res;
    }

    public AIOutboundTask findFirstById(Long taskId) {
        return aiOutboundTaskRepository.findFirstById(taskId);
    }

    public List<AIOutboundTask> findProductIdProgramIdIndustryId(List<Long> taskIds) {
        List<Tuple> tuples = aiOutboundTaskRepository.findFilterTaskIdsByIds(taskIds);
        return JpaResultUtils.processResult(tuples, AIOutboundTask.class);
    }

    public void preProcess(AIOutboundTask aiOutboundTask) {
        String[] taskIdList = aiOutboundTask.getTaskIds().split(",");
        log.info("批量预处理：" + Arrays.toString(taskIdList));
        List<Long> ids = Arrays.stream(taskIdList).map(Long::valueOf).collect(Collectors.toList());
        List<AIOutboundTask> allByIdIn = aiOutboundTaskRepository.findAllByIdIn(ids);
        boolean deleteRedis = false;
        for (AIOutboundTask aiTask : allByIdIn) {
            String callStatus = aiTask.getCallStatus();
            if ("待执行".equals(callStatus)) {
                deleteTaskFromRedis(aiTask.getTaskName(), aiTask.getId().toString(), aiTask.getPhoneNum());
                deleteRedis = true;
            }
        }
        if (deleteRedis) {
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }

    public Boolean verifyVariableAndTemplatedStartTask(AIOutboundTask aiOutboundTask) {
        if (aiOutboundTask.getScriptSms() != null || aiOutboundTask.getHangUpSms() != null) {
            List<TenantSmsTemplate> smsTemplates = aiOutboundTaskTemplateService.findSmsTemplateByScriptSmsHangUpSms(aiOutboundTask.getScriptSms(), aiOutboundTask.getHangUpSms());
            for (TenantSmsTemplate tenantSmsTemplate : smsTemplates) {
                if (!tenantSmsTemplate.getTemplateStatus().equals("ENABLE")) {
                    throw new ShowOnPageRemindException("任务启动失败，短信模板异常");
                }
                if (tenantSmsTemplate.getTenantSmsChannel().getIsPending()) {
                    throw new ShowOnPageRemindException("任务启动失败，短信通道异常");
                }
            }
            Map<String, VariableSmsPojo> requiredVariables = aiOutboundTaskTemplateService.findVariableBySmsTemplates(smsTemplates);
            List<VariableSmsPojo> variableSms = aiOutboundTask.getVariableSms();
            Set<String> currentVariables = new HashSet<>();
            if (variableSms != null) {
                variableSms.forEach(variable -> currentVariables.add(variable.getVariableName()));
            }
            for (Map.Entry<String, VariableSmsPojo> entry : requiredVariables.entrySet()) {
                String key = entry.getKey();
                if (!key.equals(VariableConstants.SYS_ISSUE_DATE)
                        && !key.equals(VariableConstants.SYS_CITY)
                        && !key.equals(VariableConstants.SYS_LAST_FOUR_DIGITS) && !currentVariables.contains(key)) {
                    log.info("启动时短信变量不匹配: 任务{} 变量{} 不存在", aiOutboundTask.getId(), key);
                    return false;
                }
            }
        }
        return true;
    }

    public Boolean verifyVariableAndTemplatedEditTask(AIOutboundTask oldTask, AIOutboundTask newTask) {
        if (newTask.getScriptSms() != null || newTask.getHangUpSms() != null) {
            List<TenantSmsTemplate> smsTemplates = aiOutboundTaskTemplateService.findSmsTemplateByScriptSmsHangUpSms(newTask.getScriptSms(), newTask.getHangUpSms());
            Map<String, VariableSmsPojo> requiredVariables = aiOutboundTaskTemplateService.findVariableBySmsTemplates(smsTemplates);
            List<VariableSmsPojo> variableSms = oldTask.getVariableSms();
            Set<String> currentVariables = new HashSet<>();
            if (variableSms != null) {
                variableSms.forEach(variable -> currentVariables.add(variable.getVariableName()));
            }
            for (Map.Entry<String, VariableSmsPojo> entry : requiredVariables.entrySet()) {
                String key = entry.getKey();
                if (!key.equals(VariableConstants.SYS_ISSUE_DATE)
                        && !key.equals(VariableConstants.SYS_CITY)
                        && !key.equals(VariableConstants.SYS_LAST_FOUR_DIGITS) && !currentVariables.contains(key)) {
                    log.info("编辑时短信变量不匹配: 任务{} 变量{} 不存在", oldTask.getId(), key);
                    return false;
                }
            }
        }
        return true;
    }


    public List<ExportClassRatioDto> exportIntentions(AIOutboundQueryDto aiOutboundQueryDto) {
        LocalDateTime startTime = aiOutboundQueryDto.getStartTime();
        LocalDateTime endTime = aiOutboundQueryDto.getEndTime();
        LocalDateTime startOfDay = startTime.toLocalDate().atStartOfDay();
        LocalDateTime endOfDay = startTime.toLocalDate().plusDays(1).atStartOfDay();
        List<AIOutboundTask> taskList = aiOutboundTaskRepository.findTaskListByTimeWithAccount(startOfDay, endOfDay, AIContext.getGroupId());
        Map<String, AIOutboundTask> taskMap = taskList.stream().collect(Collectors.toMap(task -> String.valueOf(task.getId()), Function.identity()));
        List<ExportClassRatioDto> exportClassRatioDtos = callRecordDailyHistoryService.exportClassRatio(startTime, endTime, new ArrayList<>(taskMap.keySet()));
        exportClassRatioDtos.forEach(exportClassRatioDto -> {
            exportClassRatioDto.setTaskName(taskMap.get(exportClassRatioDto.getTaskId()).getTaskName());
        });
        return exportClassRatioDtos;
    }

    public void sendTaskStatus(String taskId, String taskName, String taskStatus, String groupId, String location) {
        TaskStatusNoticeDTO taskNotice = new TaskStatusNoticeDTO();
        taskNotice.setTaskId(taskId);
        taskNotice.setTaskName(taskName);
        taskNotice.setTaskStatus(taskStatus);
        taskNotice.setGroupId(groupId);
        taskNotice.setLocation(location);
        taskStatusNoticeProducer.send(taskNotice);
    }

    public Integer singleTaskPreProcess(AIOutboundQueryDto aiOutboundQueryDto) {
        Optional<AIOutboundTask> byId = aiOutboundTaskRepository.findById(aiOutboundQueryDto.getTaskId());
        if (byId.isPresent()) {
            AIOutboundTask aiOutboundTask = byId.get();
            if ("待执行".equals(aiOutboundTask.getCallStatus())) {
                try {
                    deleteTaskFromRedis(aiOutboundTask.getTaskName(), String.valueOf(aiOutboundTask.getId()), aiOutboundTask.getPhoneNum());
                } catch (Exception e) {
                    if (e.toString().contains("不具备启动")) {
                        return phoneRecordRepository.findPhoneRecordCountByTaskIdNoChangeSource(aiOutboundTask.getId().toString());
                    }
                }
            }
        }
        return -1;
    }


    public void resetPhoneNum(AIOutboundQueryDto aiOutboundQueryDto) {

        Optional<AIOutboundTask> byId = aiOutboundTaskRepository.findById(aiOutboundQueryDto.getTaskId());
        if (byId.isPresent()) {
            AIOutboundTask aiOutboundTask = byId.get();
            String taskId = aiOutboundTask.getId().toString();
            RBucket<Object> bucketTaskIdBody = redissonClient.getBucket("REDIS_TASK_TMP::" + taskId + "_body");
            if (!bucketTaskIdBody.isExists()) {
                return;
            }
            long taskPhoneNum = redissonClient.getAtomicLong("REDIS_TASK_TMP::" + taskId + "_phoneNum").get();
            long taskSyncPhoneNum = redissonClient.getAtomicLong("REDIS_TASK_TMP::" + taskId + "_syncPhoneNum").get();
            long taskSyncRecordNum = redissonClient.getAtomicLong("REDIS_TASK_TMP::" + taskId + "_syncRecordNum").get();
            RQueue<String> queue = redissonClient.getQueue(taskId);
            int redisSize = queue.size();
            int redisSizeUpdated = redisSize;
            boolean redisReset = false;
            int phoneNum = aiOutboundTask.getPhoneNum();
            if (phoneNum != taskPhoneNum || phoneNum != taskSyncPhoneNum || phoneNum != taskSyncRecordNum) {
                Integer num = phoneRecordRepository.findPhoneRecordCountByTaskIdNoChangeSource(aiOutboundTask.getId().toString());
                redissonClient.getAtomicLong("REDIS_TASK_TMP::" + taskId + "_phoneNum").set(num);
                redissonClient.getAtomicLong("REDIS_TASK_TMP::" + taskId + "_syncPhoneNum").set(num);
                redissonClient.getAtomicLong("REDIS_TASK_TMP::" + taskId + "_syncRecordNum").set(num);
                aiOutboundTask.setPhoneNum(num);
                aiOutboundTask.setCallingPhoneNum(num);
                aiTaskWriteService.resetPhoneNumSave(aiOutboundTask);
                if (num != redisSize) {
                    Set<String> uniqueItems = new LinkedHashSet<>();
                    while (queue.size() > 0) {
                        List<String> allItems = queue.poll(Math.min(queue.size(), 10000));
                        uniqueItems.addAll(allItems);
                    }
                    queue.addAll(uniqueItems);
                    redisSizeUpdated = queue.size();
                    redisReset = true;
                }
                if (redisReset) {
                    if (redisSizeUpdated != num) {
                        log.info("Exception 任务量级重置/Redis重置/重置再观察:{} {} taskPhoneNum {} taskSyncPhoneNum {}" +
                                        " taskSyncRecordNum {} phoneNum {} num {} redis原长度 {} redis现长度{}",
                                taskId, aiOutboundTask.getTaskName(), taskPhoneNum, taskSyncPhoneNum, taskSyncRecordNum, phoneNum, num, redisSize, redisSizeUpdated);
                    } else {
                        log.info("Exception 任务量级重置/Redis重置:{} {} taskPhoneNum {} taskSyncPhoneNum {}" +
                                        " taskSyncRecordNum {} phoneNum {} num {} redis原长度 {} redis现长度{}",
                                taskId, aiOutboundTask.getTaskName(), taskPhoneNum, taskSyncPhoneNum, taskSyncRecordNum, phoneNum, num, redisSize, redisSizeUpdated);
                    }
                } else {
                    log.info("Exception 任务量级重置:{} {} taskPhoneNum {} taskSyncPhoneNum {}" +
                                    " taskSyncRecordNum {} phoneNum {} num {} redis原长度 {} redis现长度{}",
                            taskId, aiOutboundTask.getTaskName(), taskPhoneNum, taskSyncPhoneNum, taskSyncRecordNum, phoneNum, num, redisSize, redisSizeUpdated);
                }

            }
        }
    }

    public List<PriorityTaskData> getPriorityTaskData(List<String> lineCodes) {
        LocalDateTime startTime = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
        LocalDateTime endTime = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59);
        List<AIOutboundTask> tasks = aiOutboundTaskRepository.findAllByUpdateTimeBetweenAndCallStatusAndLineCodeIn(startTime, endTime, "进行中", lineCodes);
        List<PriorityTaskData> list = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(tasks)) {
            tasks.forEach(task -> {
                PriorityTaskData priorityTaskData = new PriorityTaskData();
                priorityTaskData.setTaskId(String.valueOf(task.getId()));
                priorityTaskData.setTaskName(task.getTaskName());
                priorityTaskData.setTaskStatus(task.getCallStatus());
                priorityTaskData.setLineCode(task.getLineCode());
                priorityTaskData.setAutoReCall(task.getAutoReCall());
                priorityTaskData.setStartTime(task.getStartWorkTimes());
                priorityTaskData.setEndTime(task.getEndWorkTimes());
                priorityTaskData.setAiAnswerNum(task.getAiAnswerNum());
                list.add(priorityTaskData);
            });
        }
        return list;
    }

    public Map<String, Integer> findTenantLinesLockConcurrentLimit() {
        AIManagerLineConfig priority = aiManagerLineConfig.findByType("priority");
        // 提供给ai-manager使用 线路锁定限制打开时 不返回数据
        if (null == priority || priority.getIsTenantLineLock()) {
            return new HashMap<>();
        }
        LocalDateTime startTime = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
        LocalDateTime endTime = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59);
        List<AIOutboundTask> tasks = aiOutboundTaskRepository.findAllByUpdateTimeBetween(startTime, endTime);
        Map<String, Integer> tenantLinesLockConcurrentLimit = findTenantLinesLockConcurrentLimit(tasks);
        return tenantLinesLockConcurrentLimit;
    }

    public Map<String, Integer> findTenantLinesLockConcurrentLimit(List<AIOutboundTask> taskIdList) {
        AIManagerLineConfig priority = aiManagerLineConfig.findByType("priority");
        // 提供给ai-manager使用 线路锁定限制打开时 不返回数据
        if (null == priority || priority.getIsTenantLineLock()) {
            return new HashMap<>();
        }
        if (CollectionUtils.isEmpty(taskIdList)) {
            return new HashMap<>();
        }
        List<AIOutboundTask> needTaskIdList = taskIdList.stream().filter(a -> StringUtils.isNotBlank(a.getLineCode())
                        && "进行中".equals(a.getCallStatus()))
                .collect(Collectors.toList());
//        Set<String> aiManualLineList = needTaskIdList.stream().filter(a -> AIOutboundTaskType.AI_MANUAL.equals(a.getTaskType())).map(AIOutboundTask::getLineCode).collect(Collectors.toSet());
        Map<String, Integer> collect = new HashMap<>();
        for (AIOutboundTask task : needTaskIdList) {
            boolean isAIAutoLine = (AIOutboundTaskType.AI_AUTO.equals(task.getTaskType())
                     || AIOutboundTaskType.AI_MANUAL.equals(task.getTaskType()) )
                    && null != task.getAiAnswerNum() && task.getAiAnswerNum() >= 0;
            if (isAIAutoLine) {
                Integer totalAnswerNum = collect.getOrDefault(task.getLineCode(), 0);
                collect.put(task.getLineCode(), totalAnswerNum + task.getAiAnswerNum());
            }
        }
        return collect;
    }

}
