package com.raipeng.aispeech.service;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.raipeng.aispeech.config.HotConfig;
import com.raipeng.aispeech.controller.request.scriptedit.ScriptDTO;
import com.raipeng.aispeech.controller.response.RegexCheckResponse;
import com.raipeng.aispeech.entity.*;
import com.raipeng.aispeech.enums.PriorType;
import com.raipeng.aispeech.exceptionadvice.exception.ScriptCheckException;
import com.raipeng.aispeech.exceptionadvice.exception.ScriptGenerateException;
import com.raipeng.aispeech.model.*;
import com.raipeng.aispeech.model.script.*;
import com.raipeng.aispeech.repository.*;
import com.raipeng.aispeech.utils.ScriptServiceUtil;
import com.raipeng.aispeech.utils.ScriptUtil;
import com.raipeng.aispeech.utils.SemanticUtils;
import com.raipeng.aispeech.utils.WebsocketUtil;
import com.raipeng.common.entity.script.AiScript;
import com.raipeng.common.entity.script.abstractcorpus.AbstractBaseCorpus;
import com.raipeng.common.entity.script.abstractcorpus.BaseScriptBranch;
import com.raipeng.common.entity.script.abstractcorpus.PhrasePack;
import com.raipeng.common.entity.script.acoustics.AiAcousticParameters;
import com.raipeng.common.entity.script.intention.IntentionLevel;
import com.raipeng.common.entity.script.intention.IntentionTag;
import com.raipeng.common.entity.script.knowledgebase.KnowledgeBaseGroup;
import com.raipeng.common.entity.script.multicontent.MultiContent;
import com.raipeng.common.entity.script.multicontent.UnitContent;
import com.raipeng.common.entity.script.semantic.SemCombineEntity;
import com.raipeng.common.enums.*;
import com.raipeng.common.model.BaseEntity;
import com.raipeng.common.util.CollectionUtil;
import com.raipeng.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.raipeng.common.constant.AIRedisCacheKeyConstant.*;
import static com.raipeng.common.constant.ScriptConstants.*;
import static com.raipeng.common.enums.FieldConditionType.HIT_TRANS_TO_HUMAN;
import static com.raipeng.common.enums.FieldConditionType.HIT_TRIGGER_SMS;

@Slf4j
@Service
public class ScriptService {
    public static final int MIN_FUNC_CORPUS_SIZE = 2;

    @Autowired
    private OkHttpClient okHttpClient;

    @Autowired
    private ScriptRepository scriptRepository;

    @Autowired
    private ScriptCorpusRepository scriptCorpusRepository;

    @Autowired
    private ScriptBranchRepository scriptBranchRepository;

    @Autowired
    private ScriptCanvasRepository scriptCanvasRepository;

    @Autowired
    private AcousticParametersRepository acousticParametersRepository;

    @Autowired
    private AILabelRepository aiLabelRepository;

    @Autowired
    private AIIntentionTypeRepository aiIntentionTypeRepository;

    @Autowired
    private AICorePhraseRepository aiCorePhraseRepository;

    @Autowired
    private AISemanticRepository aiSemanticRepository;

    @Autowired
    private AISemanticLabelRepository aiSemanticLabelRepository;

    @Autowired
    private ScriptCheckRecordRepository scriptCheckRecordRepository;

    @Autowired
    private AITenantRelatedHistoryScriptRepository aiTenantRelatedHistoryScriptRepository;

    @Autowired
    private AdvancedRulesRepository advancedRulesRepository;

    @Autowired
    private FinalIntentionRulesRepository finalIntentionRulesRepository;

    @Autowired
    private AdvancedRuleConditionRepository advancedRuleConditionRepository;

    @Autowired
    private GlobalConfigRepository globalConfigRepository;

    @Autowired
    private InfoQueryKeyRepository infoQueryKeyRepository;

    @Autowired
    private InfoQueryValueRepository infoQueryValueRepository;

    @Autowired
    private InfoQueryKeyService infoQueryKeyService;

    @Autowired
    private EventTriggerValueRepository eventTriggerValueRepository;

    @Autowired
    private EventTriggerForScriptRepository eventTriggerForScriptRepository;

    @Autowired
    private AISemanticService aiSemanticService;

    @Autowired
    private IndustrySecondFieldRepository industrySecondFieldRepository;

    @Autowired
    @Qualifier("redissonScriptClient")
    private RedissonClient redissonScriptClient;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private AIOutboundTaskRepository aiOutboundTaskRepository;

    @Autowired
    private HotConfig hotConfig;

    @Autowired
    private ScriptMultiContentRepository scriptMultiContentRepository;

    @Autowired
    private ScriptUnitContentRepository scriptUnitContentRepository;

    @Autowired
    private KnowledgeGroupRepository knowledgeGroupRepository;

    @Autowired
    private ScriptPriorGroupRepository scriptPriorGroupRepository;

    @Autowired
    private ScriptEditorService scriptEditorService;

    @Autowired
    private ScriptEditorRepository scriptEditorRepository;

    @Value("${script.audio.upload.path:/static/file/ai/audio_record}")
    private String fileNewUploadPath;

    @Value("${script.audio.download.path:https://uat.bountech.com/marketfront/file/ai-speech/ai/audio_record}")
    private String fileNewDownloadPath;

    @Autowired
    private KnowledgeGroupService knowledgeGroupService;

    @Transactional(rollbackFor = Exception.class)
    public void checkOneScript(Long scriptId) {
        Optional<Script> scriptOptional = scriptRepository.findById(scriptId);
        if (!scriptOptional.isPresent()) {
            throw new ScriptCheckException("没有这个话术");
        }

        Script script = scriptOptional.get();
        List<ScriptCorpus> topCorpus = scriptCorpusRepository.findAllByScriptIdAndIsTopHead(scriptId, true);
        if (topCorpus.size() != 1) {
            throw new ScriptCheckException("头节点数量出错");
        }
        if (Boolean.TRUE.equals(script.getIsDeleted())) {
            throw new ScriptCheckException("话术已被删除，请检查");
        }
        Set<ScriptCorpus> corpusStoreList = new HashSet<>();
        Set<ScriptBranch> branchStoreList = new HashSet<>();

        // 0. 删除被逻辑删除的语料和分支
        deleteExtraCorpus(scriptId);
        // 1. 空画布检测
        checkCanvas(scriptId);
        // 2. 检测语义中的正则表达式是否正确
        Set<Long> activeSemanticIds = checkCorePhrases(script.getSecondaryIndustryId());
        // 3. 检测主流程话术
        ScriptServiceUtil.checkMasterScript(topCorpus.get(0), corpusStoreList, branchStoreList, scriptBranchRepository, scriptCorpusRepository);
        // 4. 检测功能话术-最高优先级
        List<Long> funcPriorQAIds = generateFuncPriorQAIds(scriptId);
        // 5. 检测功能话术-沉默
        List<Long> funcSilenceIds = generateFuncSilenceIds(scriptId);
        // 6. 检测功能话术-重复
        List<Long> funcRepeatIds = generateFuncRepeatIds(scriptId);
        // 7. 检测优先级分组
        PriorGroupInfo priorGroupInfo = getPriorGroupInfo(scriptId);
        // 8. 检测知识库分组
        KnowledgeGroupInfo knowledgeGroupInfo = getKnowledgeBaseGroupMap(scriptId, corpusStoreList, branchStoreList, priorGroupInfo.getKnowledgeGroupIdsOfPriorGroup(), true);
        // 9. 检测重复触发关键字
        findRepeatSemCombineEntity(scriptId);
        // 10. 检测声学参数
        generateAcousticParameters(scriptId);
        // 11. 检测意向等级
        IntentionLevelInfo intentionLevelInfo = generateIntentionLevelList(scriptId);
        // 12. 检测意向标签
        AiLabelInfo aiLabelInfo = generateIntentionTagList(scriptId);
        List<AILabel> aiLabels = aiLabelRepository.findAllByScriptId(scriptId);
        // 13. 检查多语句
        MultiContentInfo multiContentInfo = getMultiContentMap(scriptId, true);
        // 14. 检测语料中的标签是否有错误
        CorpusInfo corpusInfo = getCorpusMap(scriptId, aiLabels, activeSemanticIds, multiContentInfo, funcPriorQAIds,
                priorGroupInfo.getPriorGroupMap(), knowledgeGroupInfo.getKnowledgeGroupIds(), true);

        Map<Long, AbstractBaseCorpus> corpusMap = corpusInfo.getCorpusMap();
        List<ScriptCorpus> corpusList = corpusInfo.getCorpusList();
        Set<Long> allCorpusIds = corpusMap.keySet();

        // 15. 检测高级规则
        Set<Long> aiSemanticIdSet = aiSemanticRepository.findAISemanticsByActiveAndSecondIndustryId(1, script.getSecondaryIndustryId()).stream().map(BaseEntity::getId).collect(Collectors.toSet());
        Set<Long> aiSemanticLabelIdSet = aiSemanticLabelRepository.findAISemanticsBySecondIndustryId(script.getSecondaryIndustryId()).stream().map(BaseEntity::getId).collect(Collectors.toSet());
        List<ScriptCorpus> scriptCorpusList = scriptCorpusRepository.findAllByScriptIdAndIsDeleted(scriptId, false);
        // 16. 处理高级规则和最终意向中的触发转人工，触发发短信相关语料
        checkAdvanceRulesCorpus(scriptId, scriptCorpusList);
        checkAdvanceRules(scriptId, aiLabels, knowledgeGroupInfo.getKnowledgeGroupIds(), aiSemanticIdSet, aiSemanticLabelIdSet, scriptCorpusList);
        // 17. 检测最终意向
        checkFinalIntentionRules(scriptId, aiLabels, knowledgeGroupInfo.getKnowledgeGroupIds(), aiSemanticIdSet, aiSemanticLabelIdSet, scriptCorpusList);

        // 18. 检测打断垫句,续播垫句,承接语句
        List<Long> preInterruptCorpusIds = generatePreInterruptCorpusIds(scriptId, corpusInfo.getPreInterruptCorpusIds());
        List<Long> preContinueCorpusIds = generatePreContinueCorpusIds(scriptId, corpusInfo.getPreContinueCorpusIds());
        List<Long> preUndertakeCorpusIds = generatePreUndertakeCorpusIds(scriptId, corpusInfo.getPreUndertakeCorpusIds());

        // 19. 检测分支中是否有空条件的查询分支
        getBranchMap(scriptId, activeSemanticIds, priorGroupInfo.getBranchIdsOfPriorGroup(), true);

        // 20. 行业语义发布状态检测
        aiSemanticService.checkSemanticIndustryPublishStatus(script.getSecondaryIndustryId());

        // 21. 存储所有校验过的语料ID
        List<Long> checkedCorpusIds = new ArrayList<>();
        checkedCorpusIds.addAll(funcRepeatIds);
        checkedCorpusIds.addAll(funcSilenceIds);
        checkedCorpusIds.addAll(funcPriorQAIds);
        checkedCorpusIds.addAll(preInterruptCorpusIds);
        checkedCorpusIds.addAll(preContinueCorpusIds);
        checkedCorpusIds.addAll(preUndertakeCorpusIds);

        for (ScriptCorpus scriptCorpus : corpusStoreList) {
            checkedCorpusIds.add(scriptCorpus.getId());
        }

        for (Long aLong : allCorpusIds) {
            if (!checkedCorpusIds.contains(aLong)) {
                throw new ScriptCheckException("您制作的话术中有无用语料或者画布，请从语料" + corpusMap.get(aLong).getName() + "开始检查");
            }
        }

        ScriptServiceUtil.saveAndFlushCorpusWhenUpdateIntentionType(intentionLevelInfo.getAiIntentionTypeMap(), corpusList, scriptCorpusRepository);
        ScriptServiceUtil.saveAndFlushCorpusWhenUpdateIntentionTag(aiLabelInfo.getAiLabelMap(), corpusList, scriptCorpusRepository);
        script.setTransferHuman(corpusMap.values().stream().anyMatch(AbstractBaseCorpus::isListenInOrTakeOver));
        script.setSmsTriggerNames(corpusInfo.getSmsTriggerNameSet());
        scriptRepository.save(script);
        RBucket<Object> bucket = redissonClient.getBucket(NEW_ADVANCED_RULES_BY_SCRIPT_ID);
        bucket.delete();
    }

    private void checkAdvanceRulesCorpus(Long scriptId, List<ScriptCorpus> scriptCorpusList) {
        List<AdvancedRuleCondition> advancedRules = advancedRuleConditionRepository.findAllByScriptId(scriptId);

        List<AdvancedRuleCondition> hitTransToHuman = advancedRules.stream().filter(a -> HIT_TRANS_TO_HUMAN.toString().equals(a.getConditionType())).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(hitTransToHuman)) {
            //查询触发事件的语料id
            List<String> collect = scriptCorpusList.stream().filter(ScriptCorpus::isListenInOrTakeOver).map(a -> a.getId().toString()).collect(Collectors.toList());
            String join = String.join(",", collect);
            for (AdvancedRuleCondition advancedRuleCondition : hitTransToHuman) {
                advancedRuleCondition.setHitTriggerCorpusIds(join);
            }
        }
        advancedRuleConditionRepository.saveAll(hitTransToHuman);

        List<AdvancedRuleCondition> hitTriggerSms = advancedRules.stream().filter(a -> HIT_TRIGGER_SMS.toString().equals(a.getConditionType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(hitTriggerSms)) {
            // 触发短信的语料id
            List<String> collect = scriptCorpusList.stream().filter(a -> StringUtils.isNotEmpty(a.getSmsTriggerName())).map(a -> a.getId().toString()).collect(Collectors.toList());
            String join = String.join(",", collect);
            for (AdvancedRuleCondition advancedRuleCondition : hitTriggerSms) {
                advancedRuleCondition.setHitTriggerCorpusIds(join);
            }
        }
        advancedRuleConditionRepository.saveAll(hitTriggerSms);
    }


    /**
     * 发布一个话术
     *
     * @param scriptStringId 话术字符串Id
     */
    public void pushAiScriptToAICall(String scriptStringId) {
        Script latestVersionScript = findLatestVersionScript(scriptStringId);
        if (Boolean.TRUE.equals(latestVersionScript.getIsDeleted())) {
            throw new ScriptCheckException("话术已删除，无法发布");
        }
        Long scriptId = latestVersionScript.getId();
        AiScript aiScript = createOneAICallScriptById(scriptId);

        if (aiScript != null && ScriptStatusEnum.VERIFY.equals(latestVersionScript.getStatus())) {
            List<String> aiCallServiceAddressList = hotConfig.getAiCallServiceAddressList();
            Integer scriptMessageSplitLength = hotConfig.getScriptMessageSplitLength();
            Long scriptSendSplitMs = hotConfig.getScriptSendSplitMs();

            String message = JSONObject.toJSONString(aiScript, SerializerFeature.WriteClassName);
            message = SCRIPT_PRE + SCRIPT_SPLIT + message;
            List<String> scriptMessages = ScriptServiceUtil.getScriptMessages(message, scriptMessageSplitLength);
            WebsocketUtil.publishToAiCall(okHttpClient, scriptMessages, aiCallServiceAddressList, scriptSendSplitMs);
            addScriptIntoRedis(scriptId, scriptStringId, message);
        }
    }

    /**
     * 激活一个停用话术
     *
     * @param scriptId 话术ID
     */
    public void pushAiScriptToAICall(Long scriptId) {
        Optional<Script> scriptOptional = scriptRepository.findById(scriptId);
        if (!scriptOptional.isPresent()) {
            throw new ScriptCheckException("话术未找到");
        }
        Script script = scriptOptional.get();
        String scriptStringId = script.getScriptStringId();
        List<Script> scripts = scriptRepository.findScriptsByScriptStringIdAndIsDeleted(scriptStringId, false);
        if (scripts.size() > 1) {
            throw new ScriptCheckException("存在多余话术，请联系管理员");
        }
        if (ScriptStatusEnum.STOP.equals(script.getStatus())) {
            log.info("激活话术重新发布话术:{} 到ai-call", scriptId);
            List<String> aiCallServiceAddressList = hotConfig.getAiCallServiceAddressList();
            Integer scriptMessageSplitLength = hotConfig.getScriptMessageSplitLength();
            Long scriptSendSplitMs = hotConfig.getScriptSendSplitMs();

            String message = findLatestActiveScriptByScriptId(scriptId);
            List<String> scriptMessages = ScriptServiceUtil.getScriptMessages(message, scriptMessageSplitLength);
            WebsocketUtil.publishToAiCall(okHttpClient, scriptMessages, aiCallServiceAddressList, scriptSendSplitMs);
        }
    }

    /**
     * 发布一个训练话术
     *
     * @param scriptId 话术字符串Id
     */
    public void pushAiScriptForTrainToAiCall(Long scriptId) {
        Script script = scriptRepository.findFirstById(scriptId);
        if (script == null) {
            throw new ScriptCheckException("话术训练出现错误，没有发现话术");
        }
        if (Boolean.TRUE.equals(script.getIsDeleted())) {
            throw new ScriptCheckException("话术训练出现错误，话术已删除，无法发布，请清理缓存后重试");
        }
        AiScript aiScript = createOneAICallScriptById(scriptId);
        if (aiScript != null) {
            Integer scriptMessageSplitLength = hotConfig.getScriptMessageSplitLength();
            List<String> trainAiCallAddress = Collections.singletonList(hotConfig.getAiCallAddressForTrain());
            Long scriptSendSplitMs = hotConfig.getScriptSendSplitMs();

            String message = JSONObject.toJSONString(aiScript, SerializerFeature.WriteClassName);
            message = SCRIPT_PRE_FOR_TRAIN + SCRIPT_SPLIT + message;
            List<String> scriptMessages = ScriptServiceUtil.getScriptMessages(message, scriptMessageSplitLength);
            WebsocketUtil.publishToAiCallForTrain(okHttpClient, scriptMessages, trainAiCallAddress, scriptSendSplitMs);
        }
    }

    /**
     * 预发布一个行业语义
     *
     * @param secondIndustryId 行业ID
     */
    public RegexCheckResponse prePublishSemantics(Long secondIndustryId) {
        RegexCheckResponse regexCheckResponse = aiSemanticService.checkAiSemanticsBySecondIndustryId(secondIndustryId);
        if (!regexCheckResponse.getOutTimeRegexChecks().isEmpty()
                || !regexCheckResponse.getErrorRegexChecks().isEmpty()
                || !regexCheckResponse.getErrorRegexCheckMap().isEmpty()) {
            regexCheckResponse.setSuccess(false);
            return regexCheckResponse;
        }
        aiSemanticService.changeIndustryPublishStatus(secondIndustryId, true);
        regexCheckResponse.setSuccess(true);
        return regexCheckResponse;
    }

    /**
     * 发布一个行业语义
     *
     * @param secondIndustryId 行业ID
     */
    public RegexCheckResponse publishSemantics(Long secondIndustryId) {
        RegexCheckResponse regexCheckResponse = aiSemanticService.checkAiSemanticsBySecondIndustryId(secondIndustryId);
        if (!regexCheckResponse.getOutTimeRegexChecks().isEmpty()
                || !regexCheckResponse.getErrorRegexChecks().isEmpty()
                || !regexCheckResponse.getErrorRegexCheckMap().isEmpty()) {
            regexCheckResponse.setSuccess(false);
            return regexCheckResponse;
        }
        List<PhrasePack> phrasePacks = getPhrasePackList(secondIndustryId);
        List<String> aiCallServiceAddressList = hotConfig.getAiCallServiceAddressList();
        Long scriptSendSplitMs = hotConfig.getScriptSendSplitMs();
        String message = JSONObject.toJSONString(phrasePacks, SerializerFeature.WriteClassName);
        message = SEMANTICS_PRE + SEMANTICS_SPLIT + secondIndustryId + SEMANTICS_SPLIT + message;
        List<String> semanticMessage = ScriptServiceUtil.getSemanticMessages(message, hotConfig.getScriptMessageSplitLength());
        WebsocketUtil.publishToAiCall(okHttpClient, semanticMessage, aiCallServiceAddressList, scriptSendSplitMs);
        aiSemanticService.changeIndustryPublishStatus(secondIndustryId, true);
        regexCheckResponse.setSuccess(true);
        return regexCheckResponse;
    }

    /**
     * 获取一个行业语义的发布文本
     *
     * @param secondIndustryId 行业ID
     * @return 语义发布文本
     */
    public String getSemanticsString(Long secondIndustryId) {
        Optional<IndustrySecondField> fieldOptional = industrySecondFieldRepository.findById(secondIndustryId);
        if (!fieldOptional.isPresent()) {
            return SEMANTICS_ERROR;
        }
        IndustrySecondField industrySecondField = fieldOptional.get();
        if (!industrySecondField.getPublishStatus()) {
            return SEMANTICS_ERROR;
        }
        List<PhrasePack> phrasePacks = getPhrasePackList(secondIndustryId);
        String message = JSONObject.toJSONString(phrasePacks, SerializerFeature.WriteClassName);
        return SEMANTICS_PRE + SEMANTICS_SPLIT + secondIndustryId + SEMANTICS_SPLIT + message;
    }

    /**
     * 发布所有大版本话术中的，有效的话术中的，最新版本
     *
     * @param scriptId 话术id
     * @return 话术
     */
    public String findLatestActiveScriptByScriptId(Long scriptId) {
        RBucket<String> scriptBucket = redissonScriptClient.getBucket(NEW_SCRIPT_ID_PREV + scriptId);
        if (scriptBucket.get() != null) {
            log.info("scriptID:{},从speech的redis缓存中获取话术", scriptId);
            return scriptBucket.get();
        }
        Optional<Script> scriptOptional = scriptRepository.findById(scriptId);
        if (scriptOptional.isPresent()) {
            AiScript aiScript = createOneAICallScriptById(scriptOptional.get().getId());
            String message = JSONObject.toJSONString(aiScript, SerializerFeature.WriteClassName);
            message = SCRIPT_PRE + SCRIPT_SPLIT + message;
            addScriptIntoRedis(aiScript.getId(), aiScript.getScriptId(), message);
            log.info("scriptID:{},从speech的postgres中获取话术", scriptId);
            return message;
        } else {
            return SCRIPT_ERROR;
        }
    }

    public List<Long> findActiveScriptIds() {
        return scriptRepository.findActiveScriptIds();
    }

    /**
     * 添加一个话术
     *
     * @param script 话术
     * @return 话术
     */
    public Script addOneScript(ScriptDTO script, String ownerAccount) {
        String scriptName = script.getScriptName();
        Script firstByScriptName = scriptRepository.findFirstByScriptName(scriptName);
        if (firstByScriptName != null) {
            throw new ScriptCheckException("不允许存在同名话术");
        }
        Script scriptSave = new Script();
        BeanUtils.copyProperties(script, scriptSave);
        scriptSave.setScriptStringId(UUID.randomUUID().toString());
        scriptSave.setVersion(0);
        scriptSave.setLatestVersion(0);
        scriptSave.setMaxSilenceCount(3);
        scriptSave.setOwnerAccount(ownerAccount);
        if (Boolean.TRUE.equals(script.isMultiContentVersion())) {
            scriptSave.setMultiContentVersion(true);
        }
        Script save = scriptRepository.saveAndFlush(scriptSave);
        // 创建新话术时创建默认的知识库分组
        knowledgeGroupService.createDefault(save.getId());
        return save;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean setScriptDeleteStatusById(Long id) {
        boolean flag = false;
        Optional<Script> scriptOptional = scriptRepository.findById(id);
        if (scriptOptional.isPresent()) {
            Script script = scriptOptional.get();
            if (ScriptStatusEnum.ACTIVE.equals(script.getStatus()) || ScriptStatusEnum.VERIFY.equals(script.getStatus())) {
                throw new ScriptCheckException("话术已经生效或在审核中，无法删除");
            }
            script.setIsDeleted(true);
            scriptRepository.saveAndFlush(script);
            flag = true;
        }
        return flag;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteOneScriptById(Long id) {
        boolean flag = false;
        Optional<Script> scriptOptional = scriptRepository.findById(id);
        if (scriptOptional.isPresent()) {
            Script script = scriptOptional.get();
            if (script.getIsDeleted()) {
                scriptRepository.deleteById(id);
                scriptCorpusRepository.deleteAllByScriptId(id);
                scriptCanvasRepository.deleteAllByScriptId(id);
                scriptBranchRepository.deleteAllByScriptId(id);
                aiLabelRepository.deleteAllByScriptId(id);
                aiIntentionTypeRepository.deleteAllByScriptId(id);
                acousticParametersRepository.deleteAllByScriptId(id);
                scriptCheckRecordRepository.deleteAllByScriptId(id);
                knowledgeGroupService.deleteAllByScriptId(id);
                flag = true;
            }
        }
        return flag;
    }

    @Transactional(rollbackFor = Exception.class)
    public Script updateOneScript(ScriptDTO script) {
        // 1. 检测话术是否存在
        Script oldScript = scriptRepository.findFirstById(script.getId());
        if (oldScript == null) {
            throw new ScriptCheckException("话术不存在");
        }

        // 1.1 检测话术是否可以编辑
        ScriptServiceUtil.checkScriptCanBeEdit(scriptRepository, oldScript.getId());

        // 2. 重名检测
        if (!Objects.equals(oldScript.getScriptName(), script.getScriptName())) {
            List<Script> scripts = scriptRepository.findScriptsByScriptName(script.getScriptName(), oldScript.getScriptStringId());
            if (scripts != null && scripts.size() != 0) {
                throw new ScriptCheckException("修改的话术名称和其他话术重名了，请重新操作");
            }
        }

        // 3. 如果更改行业，清空语义
        if (!oldScript.getSecondaryIndustryId().equals(script.getSecondaryIndustryId())) {
            List<ScriptBranch> branches = scriptBranchRepository.findAllByScriptId(script.getId());
            List<ScriptCorpus> corpusList = scriptCorpusRepository.findAllByScriptId(script.getId());
            branches.forEach(branch -> {
                branch.setSemanticIdsInUse(null);
                SemCombineEntity semCombineEntity = branch.getSemCombineEntity();
                SemanticUtils.clearSemanticsOfSemCombine(semCombineEntity);
            });
            corpusList.forEach(corpus -> {
                corpus.setSemanticIdsInUse(null);
                SemCombineEntity semCombineEntity = corpus.getSemCombineEntity();
                SemanticUtils.clearSemanticsOfSemCombine(semCombineEntity);
                List<ScriptBranch> branchList = corpus.getBranchList();
                if (branchList != null && branchList.size() != 0) {
                    branchList.forEach(branch -> {
                        branch.setSemanticIdsInUse(null);
                        SemCombineEntity semCombineEntityBranch = branch.getSemCombineEntity();
                        SemanticUtils.clearSemanticsOfSemCombine(semCombineEntityBranch);
                    });
                }
            });
            scriptBranchRepository.saveAll(branches);
            scriptCorpusRepository.saveAll(corpusList);
        }

        // 4. 保存话术
        BeanUtils.copyProperties(script, oldScript);
        if (Boolean.TRUE.equals(script.isMultiContentVersion())) {
            oldScript.setMultiContentVersion(true);
        }
        return scriptRepository.save(oldScript);
    }

    public Optional<Script> findOneScriptById(Long id) {
        return scriptRepository.findById(id);
    }

    public List<Script> findAllScript() {
        return scriptRepository.findScriptsByIsDeletedOrderByCreateTimeDesc(false);
    }

    public List<Script> findAllScriptInDeleteStatus() {
        return scriptRepository.findScriptsByIsDeletedOrderByCreateTimeDesc(true);
    }

    public List<Script> findAllScriptByStatus(ScriptStatusEnum status) {
        return scriptRepository.findScriptsByIsDeletedAndStatusOrderByUpdateTimeDesc(false, status);
    }

    public List<Script> findScriptListByNameAndStatus(String name, ScriptStatusEnum status) {
        return scriptRepository.findScriptsByIsDeletedAndScriptNameAndStatus(false, name, status);
    }

    /**
     * 发布话术成功后，其他版本话术逻辑删除
     *
     * @param scriptStringId 话术字符串Id
     */
    public void afterPublishSetDeleteStatus(String scriptStringId) {
        List<Script> scriptList = scriptRepository.findScriptsByScriptStringId(scriptStringId);
        for (Script scriptTemp : scriptList) {
            if (Objects.equals(scriptTemp.getVersion(), scriptTemp.getLatestVersion())) {
                continue;
            }
            scriptTemp.setIsDeleted(true);
            scriptRepository.save(scriptTemp);
        }
    }

    /**
     * 升级一个话术,但是大版本不变
     *
     * @param oldScript 老话术
     * @return 新话术
     */
    public Script updateOneScriptVersionById(Script oldScript) {
        Long oldScriptId = oldScript.getId();
        LocalDateTime startTime = LocalDateTime.now();
        Script newScript = ScriptServiceUtil.copyScriptReturn(oldScript, scriptRepository);
        ScriptServiceUtil.copyAcousticParametersReturn(oldScriptId, newScript.getId(), acousticParametersRepository);
        Map<Long, AIIntentionType> aIIntentionTypeMap = ScriptServiceUtil.copyIntentionTypeReturnOldIdIntentionTypeMap(oldScriptId, newScript.getId(), aiIntentionTypeRepository);
        Map<Long, AILabel> aiLabelMap = ScriptServiceUtil.copyAILabelReturnOldIdAiLableMap(oldScriptId, newScript.getId(), aiLabelRepository);
        ScriptServiceUtil.copyGlobalConfig(oldScriptId, newScript.getId(), globalConfigRepository);
        ScriptServiceUtil.copyEventTriggerKey(oldScriptId, newScript.getId(), eventTriggerForScriptRepository);
        Map<Long, KnowledgeGroup> kgMap = ScriptServiceUtil.copyKnowledegGroupReturnOldIdGroupMap(oldScriptId, newScript.getId(), knowledgeGroupRepository);
        Map<Long, ScriptBranch> branchMap = ScriptServiceUtil.copyBranchReturnOldIdBranchMap(oldScriptId, newScript.getId(), scriptBranchRepository);
        Map<Long, ScriptCorpus> corpusMap = ScriptServiceUtil.copyCorpusReturnCorpusMap(oldScriptId, newScript.getId(), scriptCorpusRepository);
        ScriptServiceUtil.copyPriorGroupNoReturn(oldScriptId, newScript.getId(), corpusMap, branchMap, kgMap, scriptPriorGroupRepository);
        Map<Long, ScriptMultiContent> multiContentMap = ScriptServiceUtil.copyMultiContentReturnMultiMap(newScript.getId(), corpusMap, scriptMultiContentRepository);
        Map<Long, ScriptUnitContent> unitContentMap = ScriptServiceUtil.copyUnitContentReturnContentMap(newScript.getId(), corpusMap, multiContentMap, scriptUnitContentRepository,
                "", newScript.getScriptStringId(), fileNewUploadPath, fileNewDownloadPath, false);
        Map<Long, ScriptCanvas> canvasMap = ScriptServiceUtil.copyCanvasReturnCanvasMap(oldScriptId, newScript.getId(), scriptCanvasRepository);
        Map<Long, ScriptServiceUtil.InfoQueryData> infoQueryValueMap = ScriptServiceUtil.copyInfoQueryReturnInfoQueryValueMap(oldScriptId, newScript.getId(), infoQueryKeyRepository, infoQueryValueRepository, infoQueryKeyService);
        ScriptServiceUtil.copyAdvanceRulesNew(oldScriptId, newScript.getId(), advancedRulesRepository, advancedRuleConditionRepository, corpusMap, aIIntentionTypeMap, aiLabelMap, kgMap, branchMap);
        ScriptServiceUtil.copyFinalIntentionAdvanceRules(oldScriptId, newScript.getId(), finalIntentionRulesRepository, advancedRuleConditionRepository, corpusMap, aIIntentionTypeMap, aiLabelMap, kgMap, branchMap);
        ScriptServiceUtil.saveAndFlushCorpus(kgMap, corpusMap, branchMap, aIIntentionTypeMap, aiLabelMap, scriptCorpusRepository, scriptBranchRepository);
        ScriptServiceUtil.saveAndFlushCanvas(kgMap, canvasMap, corpusMap, branchMap, scriptCanvasRepository, scriptBranchRepository);
        ScriptServiceUtil.saveAndFlushBranch(infoQueryValueMap, newScript.getId(), scriptBranchRepository);
        ScriptServiceUtil.saveAndFlushUnitContent(corpusMap, unitContentMap, scriptUnitContentRepository);
        Script script = ScriptServiceUtil.saveAndFlushScript(newScript, corpusMap, scriptRepository, aiLabelMap);
        log.info("升级话术 id:" + script.getId() + ",大版本也会变化, 耗时：" + ChronoUnit.MILLIS.between(startTime, LocalDateTime.now()));
        return script;
    }

    /**
     * 复制一个话术,大版本也会变化
     *
     * @param oldScript    老话术
     * @param ownerAccount 创建人
     * @return 新话术
     */
    public Script copyOneScriptTotallyById(Script oldScript, String ownerAccount) {
        Long oldScriptId = oldScript.getId();
        LocalDateTime startTime = LocalDateTime.now();
        Script newScript = ScriptServiceUtil.copyScriptTotallyReturn(oldScript, scriptRepository, ownerAccount);
        ScriptServiceUtil.copyAcousticParametersReturn(oldScriptId, newScript.getId(), acousticParametersRepository);
        Map<Long, AIIntentionType> aIIntentionTypeMap = ScriptServiceUtil.copyIntentionTypeReturnOldIdIntentionTypeMap(oldScriptId, newScript.getId(), aiIntentionTypeRepository);
        Map<Long, AILabel> aiLabelMap = ScriptServiceUtil.copyAILabelReturnOldIdAiLableMap(oldScriptId, newScript.getId(), aiLabelRepository);
        ScriptServiceUtil.copyGlobalConfig(oldScriptId, newScript.getId(), globalConfigRepository);
        ScriptServiceUtil.copyEventTriggerKey(oldScriptId, newScript.getId(), eventTriggerForScriptRepository);
        Map<Long, KnowledgeGroup> kgMap = ScriptServiceUtil.copyKnowledegGroupReturnOldIdGroupMap(oldScriptId, newScript.getId(), knowledgeGroupRepository);
        Map<Long, ScriptBranch> branchMap = ScriptServiceUtil.copyBranchReturnOldIdBranchMap(oldScriptId, newScript.getId(), scriptBranchRepository);
        Map<Long, ScriptCorpus> corpusMap = ScriptServiceUtil.copyCorpusReturnCorpusMap(oldScriptId, newScript.getId(), scriptCorpusRepository);
        ScriptServiceUtil.copyPriorGroupNoReturn(oldScriptId, newScript.getId(), corpusMap, branchMap, kgMap, scriptPriorGroupRepository);
        Map<Long, ScriptMultiContent> multiContentMap = ScriptServiceUtil.copyMultiContentReturnMultiMap(newScript.getId(), corpusMap, scriptMultiContentRepository);
        Map<Long, ScriptUnitContent> unitContentMap = ScriptServiceUtil.copyUnitContentReturnContentMap(newScript.getId(), corpusMap, multiContentMap, scriptUnitContentRepository,
                oldScript.getScriptStringId(), newScript.getScriptStringId(), fileNewUploadPath, fileNewDownloadPath, true);
        Map<Long, ScriptCanvas> canvasMap = ScriptServiceUtil.copyCanvasReturnCanvasMap(oldScriptId, newScript.getId(), scriptCanvasRepository);
        Map<Long, ScriptServiceUtil.InfoQueryData> infoQueryValueMap = ScriptServiceUtil.copyInfoQueryReturnInfoQueryValueMap(oldScriptId, newScript.getId(), infoQueryKeyRepository, infoQueryValueRepository, infoQueryKeyService);
        ScriptServiceUtil.copyAdvanceRulesNew(oldScriptId, newScript.getId(), advancedRulesRepository, advancedRuleConditionRepository, corpusMap, aIIntentionTypeMap, aiLabelMap, kgMap, branchMap);
        ScriptServiceUtil.copyFinalIntentionAdvanceRules(oldScriptId, newScript.getId(), finalIntentionRulesRepository, advancedRuleConditionRepository, corpusMap, aIIntentionTypeMap, aiLabelMap, kgMap, branchMap);
        ScriptServiceUtil.saveAndFlushCorpus(kgMap, corpusMap, branchMap, aIIntentionTypeMap, aiLabelMap, scriptCorpusRepository, scriptBranchRepository);
        ScriptServiceUtil.saveAndFlushCanvas(kgMap, canvasMap, corpusMap, branchMap, scriptCanvasRepository, scriptBranchRepository);
        ScriptServiceUtil.saveAndFlushBranch(infoQueryValueMap, newScript.getId(), scriptBranchRepository);
        ScriptServiceUtil.saveAndFlushUnitContent(corpusMap, unitContentMap, scriptUnitContentRepository);
        Script script = ScriptServiceUtil.saveAndFlushScript(newScript, corpusMap, scriptRepository, aiLabelMap);
        log.info("复制话术 id:" + script.getId() + ",大版本不变, 耗时：" + ChronoUnit.MILLIS.between(startTime, LocalDateTime.now()));
        return script;
    }

    /**
     * 生成ai-call使用的话术
     *
     * @param scriptId 话术Id
     * @return 话术
     */
    public AiScript createOneAICallScriptById(Long scriptId) {
        //todo
        try {
            AiScript aiScript = new AiScript();
            Optional<Script> scriptOptional = findOneScriptById(scriptId);
            if (!scriptOptional.isPresent()) {
                return null;
            }
            Script script = scriptOptional.get();

            // 1. 设置话术基本属性
            aiScript.setId(script.getId());
            aiScript.setScriptName(script.getScriptName());
            aiScript.setScriptId(script.getScriptStringId());
            aiScript.setVersion(script.getVersion());
            aiScript.setMaxSilenceCount(script.getMaxSilenceCount());
            aiScript.setSecondIndustryId(script.getSecondaryIndustryId());
            aiScript.setMultiContentVersion(script.isMultiContentVersion());

            Long triggerSilenceHangupTagId = script.getTriggerSilenceHangupTagId();
            if (triggerSilenceHangupTagId != null) {
                aiLabelRepository.findById(triggerSilenceHangupTagId).ifPresent(tag -> {
                    IntentionTag intentionTag = new IntentionTag();
                    intentionTag.setId(tag.getId());
                    intentionTag.setSequence(tag.getSequence());
                    intentionTag.setLabelName(tag.getLabelName());
                    aiScript.setTriggerSilenceHangupTag(intentionTag);
                });
            }

            // 2. 设置头节点Id
            aiScript.setHeadCorpusId(script.getHeadCorpusId());

            MultiContentInfo multiContentInfo = getMultiContentMap(scriptId, false);
            PriorGroupInfo priorGroupInfo = getPriorGroupInfo(scriptId);
            KnowledgeGroupInfo knowledgeGroupInfo = getKnowledgeBaseGroupMap(scriptId, null, null, null, false);
            CorpusInfo corpusInfo = getCorpusMap(scriptId, null, null, multiContentInfo, null, priorGroupInfo.getPriorGroupMap(), knowledgeGroupInfo.getKnowledgeGroupIds(), false);

            // 3. 设置语料属性
            aiScript.setMultiContentMap(multiContentInfo.getMultiContentMap());
            aiScript.setUnitContentMap(multiContentInfo.getUnitContentMap());
            aiScript.setCorpusMap(corpusInfo.getCorpusMap());
            aiScript.setBranchMap(getBranchMap(scriptId, null, null, false));
            aiScript.setSemCombineEntity(findRepeatSemCombineEntity(scriptId));
            aiScript.setFuncPriorQACorpusIds(generateFuncPriorQAIds(scriptId));
            aiScript.setFuncRepeatCorpusIds(generateFuncRepeatIds(scriptId));
            aiScript.setFuncSilenceCorpusIds(generateFuncSilenceIds(scriptId));
            aiScript.setKnowledgeBaseGroupMap(knowledgeGroupInfo.getKnowledgeBaseGroupMap());

            // 4. 设置其他属性
            aiScript.setAiAcousticParameters(generateAcousticParameters(scriptId));
            aiScript.setIntentionLevelList(generateIntentionLevelList(scriptId).getIntentionLevels());
            aiScript.setIntentionTagList(generateIntentionTagList(scriptId).getIntentionTags());
            aiScript.setHeadMasterOrdinaryList(generateHeadMasterOrdinaryIdList(scriptId));
            aiScript.setInfoQueryPriority(generateInfoQueryPriority(scriptId));
            return aiScript;
        } catch (Exception e) {
            log.info("话术生成失败:{}", e.getMessage());
            e.printStackTrace();
            throw new ScriptGenerateException(e.getMessage());
        }
    }

    public MultiContentInfo getMultiContentMap(Long scriptId, boolean isCheck) {
        MultiContentInfo multiContentInfo = new MultiContentInfo();
        Map<Long, MultiContent> multiContentMap = new HashMap<>();
        Map<Long, UnitContent> unitContentMap = new HashMap<>();

        List<ScriptMultiContent> scriptMultiContents = scriptMultiContentRepository.findActivesByScriptId(scriptId);
        List<ScriptUnitContent> scriptUnitContents = scriptUnitContentRepository.findActivesByScriptId(scriptId);

        Map<Long, List<ScriptUnitContent>> relatedMap = scriptUnitContents.stream().collect(Collectors.groupingBy(ScriptUnitContent::getMultiContentId, Collectors.toList()));
        Map<Long, List<ScriptMultiContent>> map = new HashMap<>();
        for (ScriptMultiContent scriptMultiContent : scriptMultiContents) {
            Long scriptMultiContentId = scriptMultiContent.getId();
            scriptMultiContent.setScriptUnitContents(relatedMap.get(scriptMultiContentId));
            List<ScriptMultiContent> scriptMultiContentList = map.computeIfAbsent(scriptMultiContent.getCorpusId(), k -> new ArrayList<>());
            scriptMultiContentList.add(scriptMultiContent);
            if (!isCheck) {
                List<ScriptUnitContent> contents = scriptMultiContent.getScriptUnitContents();
                List<Long> orderUnitIds = contents.stream().sorted(Comparator.comparingInt(ScriptUnitContent::getOrders)).map(ScriptUnitContent::getId).collect(Collectors.toList());
                MultiContent multiContent = new MultiContent();
                multiContent.setUnitContentIds(orderUnitIds);
                multiContentMap.put(scriptMultiContentId, multiContent);
                for (ScriptUnitContent content : contents) {
                    UnitContent unitContent = new UnitContent();
                    BeanUtils.copyProperties(content, unitContent);
                    unitContentMap.put(content.getId(), unitContent);
                }
            }
        }
        multiContentInfo.setScriptMultiContentMap(map);
        if (!isCheck) {
            multiContentInfo.setMultiContentMap(multiContentMap);
            multiContentInfo.setUnitContentMap(unitContentMap);
        }
        return multiContentInfo;
    }

    /**
     * 生成语料map
     *
     * @param scriptId 话术Id
     * @return 语料map
     */
    public CorpusInfo getCorpusMap(Long scriptId, List<AILabel> aiLabels, Set<Long> activeSemanticIds,
                                   MultiContentInfo multiContentInfo, List<Long> funcPriorQAIds,
                                   Map<Long, PriorGroup> priorGroupMap, List<Long> knowledgeGroupIds, boolean isCheck) {
        Map<Long, List<ScriptMultiContent>> scriptMultiContentMap = multiContentInfo.getScriptMultiContentMap();
        CorpusInfo corpusInfo = new CorpusInfo();
        List<ScriptCorpus> scriptCorpusList = scriptCorpusRepository.findAllByScriptId(scriptId);
        corpusInfo.setCorpusList(scriptCorpusList);
        Map<String, String> smsScriptTriggerNameMap = new HashMap<>();
        if (scriptCorpusList.size() > 0) {
            List<Long> corpusIds = corpusInfo.getCorpusList().stream().map(BaseEntity::getId).collect(Collectors.toList());
            for (ScriptCorpus corpus : scriptCorpusList) {
                String smsTriggerName = corpus.getSmsTriggerName();
                if (smsTriggerName != null) {
                    String corpusName = smsScriptTriggerNameMap.get(smsTriggerName);
                    if (corpusName != null) {
                        throw new ScriptCheckException("短信触发语料:" + corpusName + "与" + corpus.getName() + "的短信触发语料名称重复，请检查");
                    } else {
                        smsScriptTriggerNameMap.put(smsTriggerName, corpus.getName());
                    }
                }
                if (aiLabels != null) {
                    if (ScriptServiceUtil.isIntentionTagsError(corpus, aiLabels)) {
                        throw new ScriptCheckException(corpus.getName() + "的标签出现错误，可能是标签有删减，未更新");
                    }
                }
                if (corpus.getCorpusType().equals(ScriptCorpusTypeEnum.MASTER_CONNECT) || corpus.getCorpusType().equals(ScriptCorpusTypeEnum.KNOWLEDGE_CONNECT) || corpus.getCorpusType().equals(ScriptCorpusTypeEnum.KNOWLEDGE_BASE_QA)) {
                    if (corpus.getConnectType() == null) {
                        throw new ScriptCheckException(corpus.getName() + "的连接属性没有填写，请检查");
                    } else {
                        if (ConnectTypeEnum.SELECT_MASTER_PROCESS.equals(corpus.getConnectType()) && corpus.getConnectCorpusId() == null) {
                            throw new ScriptCheckException(corpus.getName() + "的连接属性没有填写，请检查");
                        }
                    }
                }

                // 语义空校验
                ScriptUtil.checkSemanticEmpty(corpus);
                // 校验语义行业
                if (isCheck) {
                    ScriptUtil.checkSemanticIndustry(corpus, activeSemanticIds, aiSemanticRepository);
                }
                if (corpus.getKnowledgeGroupId() != null) {
                    if (!knowledgeGroupIds.contains(corpus.getKnowledgeGroupId())) {
                        throw new ScriptCheckException(corpus.getName() + "的知识库分组已删除");
                    }
                }

                if (OpenScopeType.CUSTOM == corpus.getOpenScopeType() && corpus.getGroupOpenScope() == null) {
                    throw new ScriptCheckException(corpus.getName() + "的开放范围未选择");
                }

                if (corpus.getGroupOpenScope() != null) {
                    List<Long> groupOpenScope = corpus.getGroupOpenScope();
                    for (Long groupId : groupOpenScope) {
                        if (!knowledgeGroupIds.contains(groupId)) {
                            throw new ScriptCheckException(corpus.getName() + "的开放范围内包含已删除的知识库");
                        }
                    }
                }

                if (corpus.getConnectCorpusId() != null) {
                    if (!corpusIds.contains(corpus.getConnectCorpusId())) {
                        throw new ScriptCheckException(corpus.getName() + "的连接语料已删除");
                    }
                }
                if (corpus.getPreContinueCorpusIdBeforeDefault() != null) {
                    if (!corpusIds.contains(corpus.getPreContinueCorpusIdBeforeDefault())) {
                        throw new ScriptCheckException(corpus.getName() + "的默认续播垫句已删除");
                    }
                    corpusInfo.getPreContinueCorpusIds().add(corpus.getPreContinueCorpusIdBeforeDefault());
                }
                if (corpus.getPreContinueCorpusIdForReturn() != null) {
                    if (!corpusIds.contains(corpus.getPreContinueCorpusIdForReturn())) {
                        throw new ScriptCheckException(corpus.getName() + "的返回续播垫句已删除");
                    }
                    corpusInfo.getPreContinueCorpusIds().add(corpus.getPreContinueCorpusIdForReturn());
                }
                if (corpus.getPreUndertakeCorpusId() != null) {
                    if (!corpusIds.contains(corpus.getPreUndertakeCorpusId())) {
                        throw new ScriptCheckException(corpus.getName() + "的返回承接语料已删除");
                    }
                    corpusInfo.getPreUndertakeCorpusIds().add(corpus.getPreUndertakeCorpusId());
                }

                // 校验语料优先级
                checkCorpusPrior(corpus, priorGroupMap, knowledgeGroupIds);

                setEventTriggerTemplateList(corpus); // 填充EventTriggerTemplateList
                AbstractBaseCorpus newCorpus;

                List<ScriptMultiContent> scriptMultiContents = scriptMultiContentMap.get(corpus.getId());
                if (!isCheck) {
                    corpus.setScriptMultiContents(scriptMultiContents);
                    newCorpus = ScriptUtil.createNewCorpus(corpus, priorGroupMap, knowledgeGroupIds);
                } else {
                    checkMultiContent(scriptMultiContents, corpus, corpusIds, corpusInfo, funcPriorQAIds);
                    newCorpus = new AbstractBaseCorpus() {
                        @Override
                        public String getName() {
                            return super.getName();
                        }
                    };
                    newCorpus.setName(corpus.getName());
                    if (corpus.isListenInOrTakeOver()) {
                        newCorpus.setListenInOrTakeOver(true);
                    }
                }
                corpusInfo.getCorpusMap().put(corpus.getId(), newCorpus);
            }
        }
        corpusInfo.setSmsTriggerNameSet(new HashSet<>(smsScriptTriggerNameMap.keySet()));
        return corpusInfo;
    }

    /**
     * 生成分支map
     *
     * @param scriptId 话术Id
     * @return 分支map
     */
    public Map<Long, BaseScriptBranch> getBranchMap(Long scriptId, Set<Long> activeSemanticIds, Set<Long> branchIdsOfPriorGroup, boolean isCheck) {
        List<ScriptBranch> scriptBranchList = scriptBranchRepository.findAllByScriptId(scriptId);
        Map<Long, BaseScriptBranch> map = new HashMap<>();
        if (scriptBranchList.size() > 0) {
            for (ScriptBranch branch : scriptBranchList) {
                String preCorpusName = "";
                if (branch.getNextCorpusId() == null) {
                    // 如果这个分支没有下游语料，检查一下这个分支的上游语料，是否包含这个分支选项，如果没有，自动删除这个分支
                    if (branch.getPreCorpusId() != null) {
                        List<Long> preCorpusBranchIds = new ArrayList<>();
                        Optional<ScriptCorpus> preCorpusOptional = scriptCorpusRepository.findById(branch.getPreCorpusId());
                        if (preCorpusOptional.isPresent()) {
                            ScriptCorpus preScriptCorpus = preCorpusOptional.get();
                            preCorpusName = preScriptCorpus.getName();
                            preScriptCorpus.getBranchList().forEach(scriptBranchTemp -> {
                                preCorpusBranchIds.add(scriptBranchTemp.getId());
                            });
                        }
                        if (!preCorpusBranchIds.contains(branch.getId())) {
                            scriptBranchRepository.deleteById(branch.getId());
                            continue;
                        }
                        // 无上游语料，也无下游语料，那么这个分支自动删除
                    } else {
                        scriptBranchRepository.deleteById(branch.getId());
                        continue;
                    }
                }
                if (branch.getColor() != null && (branch.getInfoQueryValueIds() == null || branch.getInfoQueryValueIds().size() == 0)) {
                    String message = "";
                    if (branch.getPreCorpusId() != null) {
                        Optional<ScriptCorpus> corpusOptional = scriptCorpusRepository.findById(branch.getPreCorpusId());
                        if (corpusOptional.isPresent()) {
                            message = corpusOptional.get().getName();
                        }
                    }
                    throw new ScriptCheckException("查询分支:" + message + ":" + branch.getName() + "没有查询条件");
                }

                // 语义校验
                ScriptUtil.checkSemanticEmpty(branch, preCorpusName);
                if (isCheck) {
                    // 语义行业校验
                    ScriptUtil.checkSemanticIndustry(branch, activeSemanticIds, preCorpusName, aiSemanticRepository);
                    // 优先级分支校验
                    if (!ScriptUtil.UNIFY_RESPONSE_BRANCH_NAME.equals(branch.getName())) {
                        if (!branchIdsOfPriorGroup.contains(branch.getId())) {
                            if (branch.getPreCorpusId() != null) {
                                Optional<ScriptCorpus> preCorpusOptional = scriptCorpusRepository.findById(branch.getPreCorpusId());
                                if (preCorpusOptional.isPresent()) {
                                    ScriptCorpus preScriptCorpus = preCorpusOptional.get();
                                    preCorpusName = preScriptCorpus.getName();
                                }
                            }
                            throw new ScriptCheckException("语料:" + preCorpusName + "的分支:" + branch.getName() + "没有别列为优先级");
                        }
                    }
                }

                // 生产话术分支
                if (!isCheck) {
                    BaseScriptBranch newBranch = ScriptUtil.createNewBranch(branch);
                    map.put(branch.getId(), newBranch);
                }
            }
        }
        return map;
    }

    /**
     * 生成重复语料触发关键字列表
     *
     * @param scriptId 话术Id
     * @return 重复语料触发关键字列表
     */
    public SemCombineEntity findRepeatSemCombineEntity(Long scriptId) {
        List<ScriptCorpus> all = scriptCorpusRepository.findAllByScriptIdAndCorpusType(scriptId, ScriptCorpusTypeEnum.FUNC_REPEAT);
        ScriptCorpus scriptCorpusTemp = all.get(0);
        SemCombineEntity semCombineEntity = scriptCorpusTemp.getSemCombineEntity();
        if (SemanticUtils.isEmptyOfSemCombine(semCombineEntity)) {
            throw new ScriptCheckException("重复语料触发关键字缺失");
        }
        return semCombineEntity;
    }


    /**
     * 生成功能话术-问答id列表
     *
     * @param scriptId 话术Id
     * @return 功能话术-问答id列表
     */
    public List<Long> generateFuncPriorQAIds(Long scriptId) {
        List<ScriptCorpus> sourceCorpusList = scriptCorpusRepository.findAllByScriptIdAndCorpusTypeOrderByWeightAsc(scriptId, ScriptCorpusTypeEnum.FUNC_PRIOR_QA);
        if (sourceCorpusList.size() < MIN_FUNC_CORPUS_SIZE) {
            throw new ScriptCheckException("话术最高优先级语料过少，至少2条");
        }
        List<Long> funcPriorQACorpusIds = new ArrayList<>();
        for (ScriptCorpus corpus : sourceCorpusList) {
            funcPriorQACorpusIds.add(corpus.getId());
        }
        return funcPriorQACorpusIds;
    }

    /**
     * 生成功能话术-沉默id列表
     *
     * @param scriptId 话术Id
     * @return 功能话术-沉默id列表
     */
    public List<Long> generateFuncSilenceIds(Long scriptId) {
        List<ScriptCorpus> sourceCorpusList = scriptCorpusRepository.findAllByScriptIdAndCorpusTypeOrderByWeightAsc(scriptId, ScriptCorpusTypeEnum.FUNC_SILENCE);
        if (sourceCorpusList.size() < MIN_FUNC_CORPUS_SIZE) {
            throw new ScriptCheckException("话术沉默语料过少，至少2条");
        }
        List<Long> generateFuncSilenceIds = new ArrayList<>();
        for (ScriptCorpus corpus : sourceCorpusList) {
            generateFuncSilenceIds.add(corpus.getId());
        }
        return generateFuncSilenceIds;
    }

    /**
     * 生成功能话术-重复id列表
     *
     * @param scriptId 话术Id
     * @return 功能话术-重复id列表
     */
    public List<Long> generateFuncRepeatIds(Long scriptId) {
        List<ScriptCorpus> sourceCorpusList = scriptCorpusRepository.findAllByScriptIdAndCorpusTypeOrderByWeightAsc(scriptId, ScriptCorpusTypeEnum.FUNC_REPEAT);
        if (sourceCorpusList.size() < MIN_FUNC_CORPUS_SIZE) {
            throw new ScriptCheckException("话术重复语料过少，至少2条");
        }
        List<Long> generateFuncRepeatIds = new ArrayList<>();
        for (ScriptCorpus corpus : sourceCorpusList) {
            generateFuncRepeatIds.add(corpus.getId());
        }
        return generateFuncRepeatIds;
    }

    /**
     * 校验优先级
     *
     * @param scriptId 话术ID
     * @return 优先级Map(key - > 语料ID, value - > 优先级)
     */
    public PriorGroupInfo getPriorGroupInfo(Long scriptId) {
        List<PriorGroup> priorGroups = scriptPriorGroupRepository.findAllByScriptId(scriptId);
        PriorGroupInfo priorGroupInfo = new PriorGroupInfo();
        Map<Long, PriorGroup> priorGroupMap = new HashMap<>();
        Set<Long> knowledgeGroupIdsOfPriorGroup = new HashSet<>();
        Set<Long> branchIdsOfPriorGroup = new HashSet<>();
        for (PriorGroup priorGroup : priorGroups) {
            PriorGroup priorGroupTemp = priorGroupMap.get(priorGroup.getCorpusId());
            if (priorGroupTemp != null) {
                throw new ScriptCheckException("语料:" + priorGroup.getCorpusId() + "优先级重复,请检查优先级数据");
            }
            List<PriorPojo> priorList = priorGroup.getPriorList();
            if (priorList == null || priorList.isEmpty()) {
                throw new ScriptCheckException("存在空的优先级,请检查");
            }
            for (PriorPojo priorPojo : priorList) {
                PriorType type = priorPojo.getType();
                if (type == null) {
                    throw new ScriptCheckException("优先级类型为空,请检查");
                }
                if (type.equals(PriorType.KNOWLEDGE_GROUPS)) {
                    knowledgeGroupIdsOfPriorGroup.add(priorPojo.getId());
                } else {
                    branchIdsOfPriorGroup.add(priorPojo.getId());
                }
            }
            priorGroupMap.put(priorGroup.getCorpusId(), priorGroup);
        }
        priorGroupInfo.setPriorGroupMap(priorGroupMap);
        priorGroupInfo.setKnowledgeGroupIdsOfPriorGroup(knowledgeGroupIdsOfPriorGroup);
        priorGroupInfo.setBranchIdsOfPriorGroup(branchIdsOfPriorGroup);
        return priorGroupInfo;
    }

    /**
     * 生成知识库分组,并完成校验
     *
     * @param scriptId 话术ID
     * @return 知识库分组
     */
    public KnowledgeGroupInfo getKnowledgeBaseGroupMap(Long scriptId,
                                                       Set<ScriptCorpus> corpusStoreList,
                                                       Set<ScriptBranch> branchStoreList,
                                                       Set<Long> knowledgeGroupIdsOfPriorGroup,
                                                       boolean isCheck) {
        List<Long> knowledgeGroupIds = knowledgeGroupRepository.findKnowledgeGroupIdsByScriptId(scriptId);
        List<ScriptCorpus> sourceCorpusList = scriptCorpusRepository.findAllByScriptIdAndIsKnowledgeBase(scriptId, true);
        if (isCheck) {
            checkKnowOrdCorpus(sourceCorpusList, corpusStoreList, branchStoreList);
            for (Long knowledgeGroupId : knowledgeGroupIdsOfPriorGroup) {
                if (!knowledgeGroupIds.contains(knowledgeGroupId)) {
                    throw new ScriptCheckException("某优先级中的知识库分组,具体ID：" + knowledgeGroupId + "不应该存在于这个话术中");
                }
            }
        }
        KnowledgeGroupInfo knowledgeGroupInfo = new KnowledgeGroupInfo();
        knowledgeGroupInfo.setKnowledgeGroupIds(knowledgeGroupIds);
        knowledgeGroupInfo.setKnowledgeBaseGroupMap(createKnowledgeGroupMap(knowledgeGroupIds, scriptId, isCheck));
        return knowledgeGroupInfo;
    }

    private void checkKnowOrdCorpus(List<ScriptCorpus> sourceCorpusList,
                                    Set<ScriptCorpus> corpusStoreList,
                                    Set<ScriptBranch> branchStoreList) {
        for (ScriptCorpus corpus : sourceCorpusList) {
            if (ScriptCorpusTypeEnum.KNOWLEDGE_ORDINARY.equals(corpus.getCorpusType())) {
                ScriptServiceUtil.checkKnowledgeScript(corpus, corpusStoreList, branchStoreList, scriptBranchRepository, scriptCorpusRepository);
            } else {
                ScriptUtil.checkSemanticEmpty(corpus);
                corpusStoreList.add(corpus);
            }
        }
    }

    /**
     * 生成打断垫句id列表
     *
     * @param scriptId              话术id
     * @param preInterruptCorpusIds 使用中的打断垫句id列表
     * @return 打断垫句id列表
     */
    public List<Long> generatePreInterruptCorpusIds(Long scriptId, Set<Long> preInterruptCorpusIds) {
        List<Long> corpusIds = new ArrayList<>();
        List<ScriptCorpus> corpusList = scriptCorpusRepository.findActivesByScriptIdAndCorpusType(scriptId, ScriptCorpusTypeEnum.PRE_INTERRUPT.toString());
        for (ScriptCorpus corpus : corpusList) {
            if (!preInterruptCorpusIds.contains(corpus.getId())) {
                throw new ScriptCheckException("打断垫句:" + corpus.getName() + "没有被使用到,请删除");
            }
            corpusIds.add(corpus.getId());
        }
        System.out.println(corpusIds);
        return corpusIds;
    }

    /**
     * 生成续播垫句id列表
     *
     * @param scriptId             话术id
     * @param preContinueCorpusIds 使用中的续播垫句id列表
     * @return 续播垫句id列表
     */
    public List<Long> generatePreContinueCorpusIds(Long scriptId, Set<Long> preContinueCorpusIds) {
        List<Long> corpusIds = new ArrayList<>();
        List<ScriptCorpus> corpusList = scriptCorpusRepository.findActivesByScriptIdAndCorpusType(scriptId, ScriptCorpusTypeEnum.PRE_CONTINUE.toString());
        for (ScriptCorpus corpus : corpusList) {
            if (!preContinueCorpusIds.contains(corpus.getId())) {
                throw new ScriptCheckException("继续垫句:" + corpus.getName() + "没有被使用到,请删除");
            }
            corpusIds.add(corpus.getId());
        }
        return corpusIds;
    }

    /**
     * 生成承接语句id列表
     *
     * @param scriptId              话术id
     * @param preUndertakeCorpusIds 使用中的承接语句id列表
     * @return 接语句id列表
     */
    public List<Long> generatePreUndertakeCorpusIds(Long scriptId, Set<Long> preUndertakeCorpusIds) {
        List<Long> corpusIds = new ArrayList<>();
        List<ScriptCorpus> corpusList = scriptCorpusRepository.findActivesByScriptIdAndCorpusType(scriptId, ScriptCorpusTypeEnum.PRE_UNDERTAKE.toString());
        for (ScriptCorpus corpus : corpusList) {
            if (!preUndertakeCorpusIds.contains(corpus.getId())) {
                throw new ScriptCheckException("转接垫句:" + corpus.getName() + "没有被使用到,请删除");
            }
            corpusIds.add(corpus.getId());
        }
        return corpusIds;
    }

    /**
     * 生成ai-call 声学参数
     *
     * @param scriptId 话术Id
     * @return 声学参数
     */
    public AiAcousticParameters generateAcousticParameters(Long scriptId) {
        List<Integer> standardParams = Arrays.asList(100, 500, 1000, 1250, 1500, 1750, 2000, 2250, 2500, 2750, 3000);
        AiAcousticParameters aiAcousticParameters = new AiAcousticParameters();
        AcousticParameters acousticParameters = acousticParametersRepository.findFirstByScriptId(scriptId);
        if (acousticParameters == null) {
            throw new ScriptCheckException("话术声学参数缺失");
        }
        Integer vocalStopWaitTime = acousticParameters.getVocalStopWaitTime();
        Integer meaningStopWaitTime = acousticParameters.getMeaningStopWaitTime();
        if (!standardParams.contains(vocalStopWaitTime) || !standardParams.contains(meaningStopWaitTime)) {
            throw new ScriptCheckException("打断停顿参数设置不正确");
        }
        BeanUtils.copyProperties(acousticParameters, aiAcousticParameters);
        return aiAcousticParameters;
    }

    /**
     * 生成ai-call 所有意向标签
     *
     * @param scriptId 话术Id
     * @return 所有意向标签列表
     */
    public AiLabelInfo generateIntentionTagList(Long scriptId) {
        AiLabelInfo aiLabelInfo = new AiLabelInfo();
        List<AILabel> all = aiLabelRepository.findAllByScriptId(scriptId);
        List<IntentionTag> intentionTags = new ArrayList<>();
        Map<Long, AILabel> aiLabelMap = new HashMap<>();
        for (AILabel label : all) {
            aiLabelMap.put(label.getId(), label);
            IntentionTag intentionTag = new IntentionTag();
            BeanUtils.copyProperties(label, intentionTag);
            intentionTags.add(intentionTag);
        }
        aiLabelInfo.setIntentionTags(intentionTags);
        aiLabelInfo.setAiLabelMap(aiLabelMap);
        return aiLabelInfo;
    }

    /**
     * 生成ai-call 所有意向等级
     *
     * @param scriptId 话术Id
     * @return 所有意向等级列表
     */
    public IntentionLevelInfo generateIntentionLevelList(Long scriptId) {
        IntentionLevelInfo intentionLevelInfo = new IntentionLevelInfo();
        List<AIIntentionType> all = aiIntentionTypeRepository.findAllByScriptId(scriptId);
        if (all.size() == 0) {
            throw new ScriptCheckException("话术意向等级缺失");
        }
        Map<Long, AIIntentionType> aiIntentionTypeMap = new HashMap<>();
        List<IntentionLevel> intentionLevels = new ArrayList<>();
        for (AIIntentionType intentionType : all) {
            aiIntentionTypeMap.put(intentionType.getId(), intentionType);
            IntentionLevel intentionLevel = new IntentionLevel();
            BeanUtils.copyProperties(intentionType, intentionLevel);
            intentionLevels.add(intentionLevel);
        }
        intentionLevelInfo.setIntentionLevels(intentionLevels);
        intentionLevelInfo.setAiIntentionTypeMap(aiIntentionTypeMap);
        return intentionLevelInfo;
    }

    public List<Long> generateHeadMasterOrdinaryIdList(Long scriptId) {
        List<ScriptCanvas> canvasList = scriptCanvasRepository.findAllByScriptIdAndIsMasterCanvasOrderByWeightAsc(scriptId, true);
        List<Long> headMasterOrdinaryIdList = new ArrayList<>();
        for (ScriptCanvas canvas : canvasList) {
            headMasterOrdinaryIdList.add(canvas.getHeadCorpusId());
        }
        return headMasterOrdinaryIdList;
    }

    public Map<String, Integer> generateInfoQueryPriority(Long scriptId) {
        Map<String, Integer> infoQueryPriority = new HashMap<>();
        List<InfoQueryValue> infoQueryValues = new ArrayList<>();
        List<InfoQueryKey> infoQueryKeys = infoQueryKeyService.findInfoQueryKeysByScriptLongId(scriptId);
        if (infoQueryKeys != null && infoQueryKeys.size() != 0) {
            for (InfoQueryKey infoQueryKey : infoQueryKeys) {
                infoQueryValues.addAll(infoQueryKey.getInfoQueryValues());
            }
            for (InfoQueryValue infoQueryValue : infoQueryValues) {
                infoQueryPriority.put(infoQueryValue.getValue(), infoQueryValue.getWeight());
            }
        }
        return infoQueryPriority;
    }

    private Map<Long, KnowledgeBaseGroup> createKnowledgeGroupMap(List<Long> knowledgeGroupIds, Long scriptId, boolean isCheck) {
        Map<Long, KnowledgeBaseGroup> knowledgeBaseGroupMap = new HashMap<>();
        for (Long knowledgeGroupId : knowledgeGroupIds) {
            KnowledgeBaseGroup group = new KnowledgeBaseGroup();
            List<ScriptCorpus> scriptCorpusList = scriptCorpusRepository.findOrderCorpusIdsByKnowledgeGroupId(knowledgeGroupId, scriptId);
            List<Long> ids = scriptCorpusList.stream().map(ScriptCorpus::getId).collect(Collectors.toList());
            if (isCheck) {
                Set<Integer> weights = scriptCorpusList.stream().map(ScriptCorpus::getWeight).filter(Objects::nonNull).collect(Collectors.toSet());
                if (ids.size() != weights.size()) {
                    throw new ScriptCheckException("知识库组中有重复的优先级,请重新排序");
                }
            }
            group.setKnowledgeBaseIds(ids);
            knowledgeBaseGroupMap.put(knowledgeGroupId, group);
        }
        return knowledgeBaseGroupMap;
    }

    private Script findLatestVersionScript(String scriptStringId) {
        List<Script> scriptList = scriptRepository.findScriptsByScriptStringId(scriptStringId);
        if (scriptList.size() == 0) {
            throw new ScriptCheckException("没有话术错误");
        }
        Integer latestVersion = scriptList.get(0).getLatestVersion();
        Script script = scriptRepository.findScriptByScriptStringIdAndVersion(scriptStringId, latestVersion);
        if (script == null) {
            throw new ScriptCheckException("没有话术错误");
        }
        return script;
    }

    private void checkAdvanceRules(Long scriptId, List<AILabel> aiLabels, List<Long> knowledgeBaseIds,
                                   Set<Long> aiSemanticIdSet, Set<Long> aiSemanticLabelIdSet,
                                   List<ScriptCorpus> scriptCorpusList) {
        List<AdvancedRules> advancedRules = advancedRulesRepository.findAllByScriptId(scriptId);
        List<Long> typeIds = aiIntentionTypeRepository.findAllByScriptId(scriptId).stream().map(AIIntentionType::getId).collect(Collectors.toList());
        Set<Long> advanceOldTagIds = new HashSet<>();
        List<String> conditionUniqueIds = new ArrayList<>();
        Set<Long> scriptCorpusSet = scriptCorpusList.stream().map(ScriptCorpus::getId).collect(Collectors.toSet());
        Map<Long, ScriptCorpus> scriptCorpusMap = scriptCorpusList.stream().collect(Collectors.toMap(ScriptCorpus::getId, Function.identity()));

        Map<String, String> conditionIdRuleMap = new HashMap<>();
        for (AdvancedRules advancedRule : advancedRules) {
            String matchConditionUniqueIds = advancedRule.getMatchConditionUniqueIds();
            if (StringUtils.isNotEmpty(matchConditionUniqueIds)) {
                List<String> strings = Arrays.asList(matchConditionUniqueIds.split(","));
                strings.forEach(a -> {
                            conditionIdRuleMap.put(a, advancedRule.getRuleName());
                            conditionUniqueIds.add(a);
                        }
                );
            }
            String excludeConditionUniqueIds = advancedRule.getExcludeConditionUniqueIds();
            if (StringUtils.isNotEmpty(excludeConditionUniqueIds)) {
                List<String> strings = Arrays.asList(excludeConditionUniqueIds.split(","));
                strings.forEach(a -> {
                    conditionIdRuleMap.put(a, advancedRule.getRuleName());
                    conditionUniqueIds.add(a);

                });
            }
            String intentionTagId = advancedRule.getIntentionTagId();
            if (StringUtils.isNotEmpty(intentionTagId)) {
                String[] intentionTagIdArray = intentionTagId.split(",");
                for (String s : intentionTagIdArray) {
                    advanceOldTagIds.add(Long.parseLong(s));
                }
            }
            String intentionLevelStringIds = advancedRule.getIntentionLevelId();
            if (StringUtils.isNotEmpty(intentionLevelStringIds)) {
                for (String s : intentionLevelStringIds.split(",")) {
                    if (!typeIds.contains(Long.parseLong(s))) {
                        throw new ScriptCheckException("高级规则中，意向等级id存在错误");
                    }
                }
            }
            if (!StringUtils.isNotEmpty(intentionTagId) && !StringUtils.isNotEmpty(intentionLevelStringIds)) {
                throw new ScriptCheckException(advancedRule.getRuleName() + "高级规则中，意向等级id和标签id均为空");
            }
        }
        List<Long> newAiLabelIds = new ArrayList<>();
        aiLabels.forEach((aiLabel) -> {
            newAiLabelIds.add(aiLabel.getId());
        });
        for (Long advanceOldTagId : advanceOldTagIds) {
            if (!newAiLabelIds.contains(advanceOldTagId)) {
                throw new ScriptCheckException("高级规则中，有标签错误");
            }
        }
        List<AdvancedRuleCondition> advancedRuleConditionList = advancedRuleConditionRepository.findByConditionUniqueIdList(conditionUniqueIds);
        //校验规则是否规范 advancedRuleConditionList
        checkRuleConditions(knowledgeBaseIds, scriptCorpusSet, scriptCorpusMap, newAiLabelIds, advancedRuleConditionList, aiSemanticIdSet, aiSemanticLabelIdSet, conditionIdRuleMap, "高级规则");
    }

    private void checkFinalIntentionRules(Long scriptId, List<AILabel> aiLabels, List<Long> knowledgeBaseIds,
                                          Set<Long> aiSemanticIdSet, Set<Long> aiSemanticLabelIdSet,
                                          List<ScriptCorpus> scriptCorpusList) {

        List<FinalIntentionRules> finalIntentionRulesList = finalIntentionRulesRepository.findAllByScriptId(scriptId);
        List<String> conditionUniqueIds = new ArrayList<>();
        Set<Long> scriptCorpusSet = scriptCorpusList.stream().map(ScriptCorpus::getId).collect(Collectors.toSet());
        Map<Long, ScriptCorpus> scriptCorpusMap = scriptCorpusList.stream().collect(Collectors.toMap(ScriptCorpus::getId, Function.identity()));
        Map<String, String> conditionIdRuleMap = new HashMap<>();

        for (FinalIntentionRules finalIntentionRule : finalIntentionRulesList) {
            String excludeConditionUniqueIds = finalIntentionRule.getExcludeConditionUniqueIds();
            if (StringUtils.isNotEmpty(excludeConditionUniqueIds)) {
                List<String> strings = Arrays.asList(excludeConditionUniqueIds.split(","));
                strings.forEach(a -> {
                    conditionIdRuleMap.put(a, finalIntentionRule.getIntentionLevelName());
                    conditionUniqueIds.add(a);
                });
            }
        }
        List<Long> newAiLabelIds = new ArrayList<>();
        aiLabels.forEach((aiLabel) -> newAiLabelIds.add(aiLabel.getId()));
        List<AdvancedRuleCondition> advancedRuleConditionList = advancedRuleConditionRepository.findByConditionUniqueIdList(conditionUniqueIds);
        checkRuleConditions(knowledgeBaseIds, scriptCorpusSet, scriptCorpusMap, newAiLabelIds, advancedRuleConditionList, aiSemanticIdSet, aiSemanticLabelIdSet, conditionIdRuleMap, "最终意向");
    }

    private static void checkRuleConditions(List<Long> knowledgeBaseIds, Set<Long> scriptCorpusSet, Map<Long, ScriptCorpus> scriptCorpusMap,
                                            List<Long> newAiLabelIds,
                                            List<AdvancedRuleCondition> advancedRuleConditionList,
                                            Set<Long> aiSemanticIdSet, Set<Long> aiSemanticLabelIdSet, Map<String, String> conditionIdRuleMap,
                                            String type) {
        for (AdvancedRuleCondition advancedRuleCondition : advancedRuleConditionList) {
            String rulePrompt = conditionIdRuleMap.get(advancedRuleCondition.getConditionUniqueId());
            if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_ANSWER_NUM.toString())) {
                //基本问答
                String hitAnswerIds = advancedRuleCondition.getHitAnswerIds();
                if (StringUtils.isEmpty(hitAnswerIds)) {
                    throw new ScriptCheckException(type + "中," + rulePrompt + ", 条件设置不完整, 命中的基本问答的语料可能已被删除, 请检查");
                }
                List<Long> longList = Arrays.stream(hitAnswerIds.split(",")).map(Long::valueOf).collect(Collectors.toList());
                for (Long aLong : longList) {
                    if (!scriptCorpusSet.contains(aLong)) {
                        throw new ScriptCheckException(type + "中," + rulePrompt + ", 命中的基本问答的语料可能已被删除, 请检查");
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_ANSWER_GROUP.toString())) {
                //知识库
                String hitKnowledgeIds = advancedRuleCondition.getHitKnowledgeIds();
                if (StringUtils.isEmpty(hitKnowledgeIds)) {
                    throw new ScriptCheckException(type + "中," + rulePrompt + ", 条件设置不完整, 命中的知识库分组可能已被删除, 请检查");
                }
                List<Long> longList = Arrays.stream(hitKnowledgeIds.split(",")).map(Long::valueOf).collect(Collectors.toList());
                for (Long aLong : longList) {
                    if (!knowledgeBaseIds.contains(aLong)) {
                        throw new ScriptCheckException(type + "中," + rulePrompt + ", 命中的知识库分组可能已被删除, 请检查");
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_TAG_NUM.toString()) ||
                    advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_TAG_TIMES.toString())) {
                //标签
                String hitTagIds = advancedRuleCondition.getHitTagIds();
                if (StringUtils.isEmpty(hitTagIds)) {
                    throw new ScriptCheckException(type + "中," + rulePrompt + ", 条件设置不完整, 命中的标签可能已被删除, 请检查");
                }
                List<Long> longList = Arrays.stream(hitTagIds.split(",")).map(Long::valueOf).collect(Collectors.toList());
                for (Long aLong : longList) {
                    if (!newAiLabelIds.contains(aLong)) {
                        throw new ScriptCheckException(type + "中," + rulePrompt + ", 命中的标签可能已被删除, 请检查");
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_SEMANTIC_NUM.toString()) ||
                    advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_SEMANTIC_TIMES.toString())) {
                //语义
                String hitSemanticIds = advancedRuleCondition.getHitSemanticIds();
                if (StringUtils.isEmpty(hitSemanticIds)) {
                    throw new ScriptCheckException(type + "中," + rulePrompt + ", 条件设置不完整, 命中的语义可能已被删除, 请检查");
                }
                List<Long> longList = Arrays.stream(hitSemanticIds.split(",")).map(Long::valueOf).collect(Collectors.toList());
                for (Long aLong : longList) {
                    if (!aiSemanticIdSet.contains(aLong)) {
                        throw new ScriptCheckException(type + "中," + rulePrompt + ", 命中的语义可能已被删除, 请检查");
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_SEMANTIC_LABEL.toString())) {
                //语义标签
                String hitSemanticLabelIds = advancedRuleCondition.getHitSemanticLabelIds();
                if (StringUtils.isEmpty(hitSemanticLabelIds)) {
                    throw new ScriptCheckException(type + "中," + rulePrompt + ", 条件设置不完整, 命中的语义标签可能已被删除, 请检查");
                }
                List<Long> longList = Arrays.stream(hitSemanticLabelIds.split(",")).map(Long::valueOf).collect(Collectors.toList());
                for (Long aLong : longList) {
                    if (!aiSemanticLabelIdSet.contains(aLong)) {
                        throw new ScriptCheckException(type + "中," + rulePrompt + ", 命中的语义标签可能已被删除, 请检查");
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_MASTER_PROCESS.toString())) {
                //主动流程流程语料
                String hitMasterProcessIdsString = advancedRuleCondition.getHitMasterProcessIds();
                if (StringUtils.isEmpty(hitMasterProcessIdsString)) {
                    throw new ScriptCheckException(type + "中," + rulePrompt + ", 条件设置不完整, 命中的流程语料头语料不存在, 请检查");
                }
                Long hitMasterProcessIds = Long.valueOf(hitMasterProcessIdsString);
                if (!scriptCorpusSet.contains(hitMasterProcessIds)) {
                    throw new ScriptCheckException(type + "中," + rulePrompt + ", 命中的流程语料头语料不存在, 请检查");
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_DEEP_COMMUNICATION.toString())) {
                String hitKnowledgeIds = advancedRuleCondition.getHitKnowledgeIds();
                if (StringUtils.isEmpty(hitKnowledgeIds)) {
                    throw new ScriptCheckException(type + "中," + rulePrompt + ", 条件设置不完整, 命中深层沟通的知识库分组可能已被删除, 请检查");
                }
                List<Long> longList = Arrays.stream(hitKnowledgeIds.split(",")).map(Long::valueOf).collect(Collectors.toList());
                for (Long aLong : longList) {
                    if (!knowledgeBaseIds.contains(aLong)) {
                        throw new ScriptCheckException(type + "中," + rulePrompt + ", 命中深层沟通的知识库分组可能已被删除, 请检查");
                    }
                }
                //深层沟通
                String hitDeepCommunicationIds = advancedRuleCondition.getHitDeepCommunicationIds();
                if (StringUtils.isEmpty(hitDeepCommunicationIds)) {
                    throw new ScriptCheckException(type + "中," + rulePrompt + ", 条件设置不完整, 命中的基本问答的深层沟通的头语料可能已被删除, 请检查");
                }
                List<Long> longDeepCommunicationList = Arrays.stream(hitDeepCommunicationIds.split(",")).map(Long::valueOf).collect(Collectors.toList());
                for (Long aLong : longDeepCommunicationList) {
                    if (!scriptCorpusSet.contains(aLong)) {
                        throw new ScriptCheckException(type + "中," + rulePrompt + ", 命中的基本问答的深层沟通的头语料可能已被删除, 请检查");
                    }
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_PROCESS_CORPUS.toString())) {
                //主动流程流程语料
                String hitMasterProcessIdsString = advancedRuleCondition.getHitMasterProcessIds();
                if (StringUtils.isEmpty(hitMasterProcessIdsString)) {
                    throw new ScriptCheckException(type + "中," + rulePrompt + ", 条件设置不完整, 命中的流程语料头语料不存在, 请检查");
                }
                Long hitMasterProcessIds = Long.valueOf(hitMasterProcessIdsString);
                if (!scriptCorpusSet.contains(hitMasterProcessIds)) {
                    throw new ScriptCheckException(type + "中," + rulePrompt + ", 命中的流程语料头语料不存在, 请检查");
                }
                String hitCorpusIdsString = advancedRuleCondition.getHitCorpusIds();
                if (StringUtils.isEmpty(hitCorpusIdsString)) {
                    throw new ScriptCheckException(type + "中," + rulePrompt + ", 条件设置不完整, 命中的流程语料语料不存在, 请检查");
                }
                Long hitCorpusIds = Long.valueOf(hitCorpusIdsString);
                if (!scriptCorpusSet.contains(hitCorpusIds)) {
                    throw new ScriptCheckException(type + "中," + rulePrompt + ", 命中的流程语料语料不存在, 请检查");
                }
            } else if (advancedRuleCondition.getConditionType().equals(FieldConditionType.HIT_BRANCH.toString())) {
                //主动流程流程语料
                String hitMasterProcessIdsString = advancedRuleCondition.getHitMasterProcessIds();
                if (StringUtils.isEmpty(hitMasterProcessIdsString)) {
                    throw new ScriptCheckException(type + "中," + rulePrompt + ", 条件设置不完整, 命中的流程语料头语料不存在, 请检查");
                }
                Long hitMasterProcessIds = Long.valueOf(hitMasterProcessIdsString);
                if (!scriptCorpusSet.contains(hitMasterProcessIds)) {
                    throw new ScriptCheckException(type + "中," + rulePrompt + ",  命中的流程语料头语料不存在, 请检查");
                }

                String hitCorpusIdsString = advancedRuleCondition.getHitCorpusIds();
                if (StringUtils.isEmpty(hitCorpusIdsString)) {
                    throw new ScriptCheckException(type + "中," + rulePrompt + ", 条件设置不完整, 命中的流程语料语料不存在, 请检查");
                }
                Long hitCorpusIds = Long.valueOf(hitCorpusIdsString);
                ScriptCorpus scriptCorpus = scriptCorpusMap.get(hitCorpusIds);
                List<ScriptBranch> branchList = scriptCorpus.getBranchList();
                Set<String> branchIdSet = branchList.stream().map(a -> a.getId().toString()).collect(Collectors.toSet());
                String hitBranchIds = advancedRuleCondition.getHitBranchIds();
                if (StringUtils.isEmpty(hitBranchIds)) {
                    throw new ScriptCheckException(type + "中," + rulePrompt + ", 命中的流程语料分支不存在, 请检查");
                }
                if (!branchIdSet.contains(hitBranchIds)) {
                    throw new ScriptCheckException(type + "中，" + rulePrompt + "命中的流程语料分支不存在，请检查");
                }
            }
        }
    }


    public Set<Long> checkCorePhrases(Long secondIndustryId) {
        List<AICorePhrase> corePhrases = aiCorePhraseRepository.findAICorePhrasesOfActiveSemanticBySecondIndustry(secondIndustryId);
        Set<Long> aiSemanticIds = aiSemanticRepository.findAISemanticsByActiveAndSecondIndustryId(1, secondIndustryId).stream().map(BaseEntity::getId).collect(Collectors.toSet());
        Set<Long> aiSemanticIdsWithCorePhrases = new HashSet<>();
        for (AICorePhrase corePhrase : corePhrases) {
            aiSemanticIdsWithCorePhrases.add(corePhrase.getSemanticId());
            try {
                String phraseName = corePhrase.getPhraseName();
                Pattern pattern = Pattern.compile(phraseName);
                pattern.matcher("this is a test");
            } catch (Exception e) {
                throw new ScriptCheckException("正则表达式有误, corePhraseId:" + corePhrase.getId());
            }
        }
        if (!aiSemanticIds.equals(aiSemanticIdsWithCorePhrases)) {
            throw new ScriptCheckException("您使用的这批语义中，有核心短语缺失现象");
        }
        return aiSemanticIds;
    }

    public void checkRunningTasks(Script script, Set<String> smsTriggerNames) {
        List<AIOutboundTask> tasks = aiOutboundTaskRepository.findRunningTaskByScriptStringId(
                LocalDateTime.of(LocalDate.now(), LocalTime.MIN), script.getScriptStringId());
        if (tasks.size() > 0) {
            tasks.forEach(task -> {
                List<ScriptSmsTriggerPojo> scriptSms = task.getScriptSms();
                Set<String> triggerNames = new HashSet<>();
                if (scriptSms != null) {
                    triggerNames = scriptSms.stream().map(ScriptSmsTriggerPojo::getTriggerName).collect(Collectors.toSet());
                }
                if (!triggerNames.equals(smsTriggerNames)) {
                    throw new ScriptCheckException("进行中的任务:" + task.getId() + ",短信触发发生改变");
                }
            });
        }
    }

    private void checkMultiContent(List<ScriptMultiContent> scriptMultiContents,
                                   ScriptCorpus scriptCorpus,
                                   List<Long> corpusIds,
                                   CorpusInfo corpusInfo,
                                   List<Long> funcPriorQAIds) {
        ScriptCorpusTypeEnum corpusType = scriptCorpus.getCorpusType();
        switch (corpusType) {
            case FUNC_PRIOR_QA:
            case FUNC_REPEAT:
            case FUNC_SILENCE:
            case KNOWLEDGE_BASE_QA:
            case KNOWLEDGE_ORDINARY:
            case MASTER_ORDINARY:
                if (ConnectTypeEnum.HANG_UP.equals(scriptCorpus.getConnectType())) {
                    checkMultiContentsForOneUnit(scriptMultiContents, scriptCorpus, corpusIds, corpusInfo, funcPriorQAIds);
                } else {
                    checkMultiContentsForUnits(scriptMultiContents, scriptCorpus, corpusIds, corpusInfo, funcPriorQAIds);
                }
                break;
            case KNOWLEDGE_CONNECT:
            case MASTER_CONNECT:
                ConnectTypeEnum connectType = scriptCorpus.getConnectType();
                if (ConnectTypeEnum.HANG_UP.equals(connectType)) {
                    checkMultiContentsForOneUnit(scriptMultiContents, scriptCorpus, corpusIds, corpusInfo, funcPriorQAIds);
                }
                break;
            case PRE_INTERRUPT:
            case PRE_CONTINUE:
            case PRE_UNDERTAKE:
                checkMultiContentsForOneUnit(scriptMultiContents, scriptCorpus, corpusIds, corpusInfo, funcPriorQAIds);
                break;
            default:
                break;
        }
    }

    private void checkMultiContentsForUnits(List<ScriptMultiContent> scriptMultiContents, ScriptCorpus scriptCorpus,
                                            List<Long> corpusIds, CorpusInfo corpusInfo, List<Long> funcPriorQAIds) {
        if (scriptMultiContents == null || scriptMultiContents.size() == 0) {
            throw new ScriptCheckException(scriptCorpus.getName() + "中，没有多语句");
        }
        for (ScriptMultiContent scriptMultiContent : scriptMultiContents) {
            List<ScriptUnitContent> scriptUnitContents = scriptMultiContent.getScriptUnitContents();
            if (scriptUnitContents == null || scriptUnitContents.size() == 0) {
                throw new ScriptCheckException(scriptCorpus.getName() + "中，多语句没有语句内容");
            }
            for (ScriptUnitContent scriptUnitContent : scriptUnitContents) {
                String content = scriptUnitContent.getContent();
                String audioPath = scriptUnitContent.getAudioPath();
                String audioStatus = scriptUnitContent.getAudioStatus();
                if (StringUtils.isEmpty(content) || StringUtils.isEmpty(audioPath)) {
                    throw new ScriptCheckException(scriptCorpus.getName() + "内容或者音频地址为空");
                }
//                if (scriptUnitContent.getIsPlayed() == null || !scriptUnitContent.getIsPlayed()) {
//                    throw new ScriptCheckException(scriptCorpus.getName() + "的音频没有验听");
//                }
                if (StringUtils.isEmpty(audioStatus) || !"1".equals(audioStatus)) {
                    throw new ScriptCheckException(scriptCorpus.getName() + "的音频验听状态 非 已验听");
                }
                checkUnitContent(scriptUnitContent, corpusIds, corpusInfo, funcPriorQAIds);
            }
        }
    }

    private void checkMultiContentsForOneUnit(List<ScriptMultiContent> scriptMultiContents, ScriptCorpus scriptCorpus,
                                              List<Long> corpusIds, CorpusInfo corpusInfo, List<Long> funcPriorQAIds) {
        if (scriptMultiContents == null || scriptMultiContents.size() != 1) {
            throw new ScriptCheckException(scriptCorpus.getName() + "中，没有多语句");
        }
        List<ScriptUnitContent> scriptUnitContents = scriptMultiContents.get(0).getScriptUnitContents();
        if (scriptUnitContents == null || scriptUnitContents.size() != 1) {
            throw new ScriptCheckException(scriptCorpus.getName() + "中，多语句没有语句内容");
        }
        ScriptUnitContent scriptUnitContent = scriptUnitContents.get(0);
        String content = scriptUnitContent.getContent();
        String audioPath = scriptUnitContent.getAudioPath();
        String audioStatus = scriptUnitContent.getAudioStatus();
        if (StringUtils.isEmpty(content) || StringUtils.isEmpty(audioPath)) {
            throw new ScriptCheckException(scriptCorpus.getName() + "内容或者音频地址为空");
        }
//        if (scriptUnitContent.getIsPlayed() == null || !scriptUnitContent.getIsPlayed()) {
//            throw new ScriptCheckException(scriptCorpus.getName() + "的音频没有验听");
//        }
        if (StringUtils.isEmpty(audioStatus) || !"1".equals(audioStatus)) {
            throw new ScriptCheckException(scriptCorpus.getName() + "的音频验听状态 非 已验听");
        }
        checkUnitContent(scriptUnitContent, corpusIds, corpusInfo, funcPriorQAIds);
    }

    private void checkUnitContent(ScriptUnitContent unitContent, List<Long> corpusIds, CorpusInfo corpusInfo,
                                  List<Long> funcPriorQAIds) {
        List<Long> interruptCorpusIdsForEnd = unitContent.getInterruptCorpusIdsForEnd();
        Long preContinueCorpusIdForInterrupt = unitContent.getPreContinueCorpusIdForInterrupt();
        Long preContinueCorpusIdForReturn = unitContent.getPreContinueCorpusIdForReturn();
        Long preUndertakeCorpusId = unitContent.getPreUndertakeCorpusId();
        Long preInterruptCorpusId = unitContent.getPreInterruptCorpusId();
        if (preContinueCorpusIdForInterrupt != null) {
            if (!corpusIds.contains(preContinueCorpusIdForInterrupt)) {
                throw new ScriptCheckException(unitContent.getContentName() + ":的打断续播垫句已被删除");
            }
            corpusInfo.getPreContinueCorpusIds().add(preContinueCorpusIdForInterrupt);
        }
        if (preContinueCorpusIdForReturn != null) {
            if (!corpusIds.contains(preContinueCorpusIdForReturn)) {
                throw new ScriptCheckException(unitContent.getContentName() + ":的返回续播垫句已被删除");
            }
            corpusInfo.getPreContinueCorpusIds().add(preContinueCorpusIdForReturn);
        }
        if (preUndertakeCorpusId != null) {
            if (!corpusIds.contains(preUndertakeCorpusId)) {
                throw new ScriptCheckException(unitContent.getContentName() + ":的返回承接语句已被删除");
            }
            corpusInfo.getPreUndertakeCorpusIds().add(preUndertakeCorpusId);
        }
        if (preInterruptCorpusId != null) {
            if (!corpusIds.contains(preInterruptCorpusId)) {
                throw new ScriptCheckException(unitContent.getContentName() + ":的打断垫句已被删除");
            }
            corpusInfo.getPreInterruptCorpusIds().add(preInterruptCorpusId);
        }
        if (interruptCorpusIdsForEnd != null) {
            for (Long aLong : interruptCorpusIdsForEnd) {
                if (!corpusIds.contains(aLong)) {
                    throw new ScriptCheckException(unitContent.getContentName() + ":最高优先打断范围内有最高优先被删除(1)");
                }
                if (!funcPriorQAIds.contains(aLong)) {
                    throw new ScriptCheckException(unitContent.getContentName() + ":最高优先打断范围内有最高优先被删除(2)");
                }
            }
        }
    }

    private void checkCorpusPrior(ScriptCorpus corpus, Map<Long, PriorGroup> priorGroupMap, List<Long> knowGroupIds) {
        List<ScriptBranch> branches = corpus.getBranchList();
        //只有客户回答是自定义而非统一回复的才有优先级分组
        if (CollectionUtils.isNotEmpty(branches) && !branches.stream().map(ScriptBranch::getName).collect(Collectors.toList()).contains("统一回复")) {
            PriorGroup priorGroup = priorGroupMap.get(corpus.getId());
            if (Objects.isNull(priorGroup) || CollectionUtils.isEmpty(priorGroup.getPriorList())) {
                throw new ScriptCheckException("语料：" + corpus.getName() + "的优先级分组不存在，请检查");
            }
            //优先级分组中的知识库分组是否都存在
            priorGroup.getPriorList().forEach(t -> {
                if (PriorType.KNOWLEDGE_GROUPS.equals(t.getType()) && !knowGroupIds.contains(t.getId())) {
                    throw new ScriptCheckException("语料：" + corpus.getName() + "的知识库分组：" + t.getId() + "不存在，请检查");
                }
            });

            int scopeSize = corpus.getGroupOpenScope() == null ? 0 : corpus.getGroupOpenScope().size();
            if (priorGroup.getPriorList().size() != branches.size() + scopeSize) { //开放语境下，branch数量 + groupscope数量 = 优先级数量
                throw new ScriptCheckException("语料：" + corpus.getName() + "的优先级分组数量异常，请检查");
            }
        }
    }

    private void checkCanvas(Long scriptId) {
        List<ScriptCanvas> canvasList = scriptCanvasRepository.findAllByScriptId(scriptId);
        for (ScriptCanvas scriptCanvas : canvasList) {
            if (scriptCanvas.getCanvasCorpusDataMap() == null || scriptCanvas.getCanvasCorpusDataMap().size() == 0) {
                throw new ScriptCheckException("有空画布，无法提交");
            }
        }
    }

    private void deleteExtraCorpus(Long scriptId) {
        List<ScriptCorpus> corpusList = scriptCorpusRepository.findAllByScriptId(scriptId);
        for (ScriptCorpus corpus : corpusList) {
            if (corpus.isDeleted()) {
                Optional<ScriptCorpus> corpusOptional = scriptCorpusRepository.findById(corpus.getId());
                corpusOptional.ifPresent(tempCorpus -> {
                    // 1.删除分支
                    Set<Long> needDeletedBranchIds = new HashSet<>();
                    List<ScriptBranch> scriptBranchList = scriptBranchRepository.findScriptBranchesByPreCorpusId(tempCorpus.getId());
                    List<ScriptBranch> branchList = tempCorpus.getBranchList();
                    scriptBranchList.forEach(scriptBranch -> needDeletedBranchIds.add(scriptBranch.getId()));
                    if (branchList != null) {
                        branchList.forEach(scriptBranch -> needDeletedBranchIds.add(scriptBranch.getId()));
                    }
                    if (needDeletedBranchIds.size() != 0) {
                        for (Long scriptBranchId : needDeletedBranchIds) {
                            if (scriptBranchRepository.existsById(scriptBranchId)) {
                                scriptBranchRepository.deleteById(scriptBranchId);
                                scriptBranchRepository.flush();
                            }
                        }
                    }

                    // 2.删除语句
                    List<ScriptMultiContent> scriptMultiContents = scriptMultiContentRepository.findByCorpusId(tempCorpus.getId());
                    List<ScriptUnitContent> scriptUnitContents = scriptUnitContentRepository.findByCorpusId(tempCorpus.getId());
                    for (ScriptMultiContent scriptMultiContent : scriptMultiContents) {
                        if (scriptMultiContentRepository.existsById(scriptMultiContent.getId())) {
                            scriptMultiContentRepository.deleteById(scriptMultiContent.getId());
                            scriptMultiContentRepository.flush();
                        }
                    }
                    for (ScriptUnitContent scriptUnitContent : scriptUnitContents) {
                        if (scriptUnitContentRepository.existsById(scriptUnitContent.getId())) {
                            scriptUnitContentRepository.deleteById(scriptUnitContent.getId());
                            scriptUnitContentRepository.flush();
                        }
                    }
                    scriptCorpusRepository.deleteById(corpus.getId());
                    scriptCorpusRepository.flush();
                });
            }
        }
        List<ScriptMultiContent> scriptMultiContents = scriptMultiContentRepository.findAllDeletedContentByScriptId(scriptId);
        for (ScriptMultiContent scriptMultiContent : scriptMultiContents) {
            if (scriptMultiContentRepository.existsById(scriptMultiContent.getId())) {
                scriptMultiContentRepository.deleteById(scriptMultiContent.getId());
                scriptMultiContentRepository.flush();
            }
        }
        List<ScriptUnitContent> scriptUnitContents = scriptUnitContentRepository.findAllDeletedContentByScriptId(scriptId);
        for (ScriptUnitContent scriptUnitContent : scriptUnitContents) {
            if (scriptUnitContentRepository.existsById(scriptUnitContent.getId())) {
                scriptUnitContentRepository.deleteById(scriptUnitContent.getId());
                scriptUnitContentRepository.flush();
            }
        }
        List<Long> corpusIds = scriptCorpusRepository.findAllByScriptId(scriptId).stream().map(ScriptCorpus::getId).collect(Collectors.toList());
        List<ScriptBranch> scriptBranches = scriptBranchRepository.findAllByScriptId(scriptId);
        for (ScriptBranch scriptBranch : scriptBranches) {
            Long preCorpusId = scriptBranch.getPreCorpusId();
            if (!corpusIds.contains(preCorpusId)) {
                if (scriptBranchRepository.existsById(scriptBranch.getId())) {
                    scriptBranchRepository.deleteById(scriptBranch.getId());
                    scriptBranchRepository.flush();
                }
            }
        }
    }

    private void setEventTriggerTemplateList(ScriptCorpus corpus) {
        List<Long> eventTriggerValueIds = corpus.getEventTriggerValueIds();
        if (eventTriggerValueIds != null) {
            List<EventTriggerValue> list = new ArrayList<>();
            for (Long eventTriggerValueId : eventTriggerValueIds) {
                Optional<EventTriggerValue> valueOptional = eventTriggerValueRepository.findById(eventTriggerValueId);
                if (!valueOptional.isPresent()) {
                    throw new ScriptCheckException(corpus.getName() + "的事件触发查询出现问题");
                }
                list.add(valueOptional.get());
            }
            corpus.setEventTriggerValueList(list);
        }
    }

    private void addScriptIntoRedis(Long scriptId, String scriptStringId, String message) {
        RBucket<Long> scriptIdBucket = redissonScriptClient.getBucket(NEW_SCRIPT_STRING_ID_PREV + scriptStringId);
        Long oldScriptId = scriptIdBucket.get();
        if (oldScriptId != null) {
            redissonScriptClient.getBucket(NEW_SCRIPT_ID_PREV + oldScriptId).delete();
        }
        scriptIdBucket.set(scriptId);
        RBucket<String> messageBucket = redissonScriptClient.getBucket(NEW_SCRIPT_ID_PREV + scriptId);
        messageBucket.set(message);
    }

    public List<Script> findAllHistoryScriptByGroupId(String groupId) {
        List<AITenantRelatedHistoryScript> historyScript = aiTenantRelatedHistoryScriptRepository.findByGroupId(groupId);
        if (CollectionUtil.isEmpty(historyScript)) {
            return new ArrayList<>();
        }
        List<String> scriptStringIdList = historyScript.stream().map(AITenantRelatedHistoryScript::getScriptStringId).collect(Collectors.toList());
        return scriptRepository.findMaxScriptListByScriptStringIdList(scriptStringIdList);
    }

    public List<PhrasePack> getPhrasePackList(Long secondIndustryId) {
        List<AISemantic> aiSemantics = aiSemanticRepository.findAISemanticsByActiveAndSecondIndustryId(1, secondIndustryId);
        Map<Long, String> semanticMap = aiSemantics.stream().collect(Collectors.toMap(AISemantic::getId, AISemantic::getSemantic));
        List<AICorePhrase> phrases = aiCorePhraseRepository.findAICorePhrasesOfActiveSemanticBySecondIndustry(secondIndustryId);
        Map<Long, List<AICorePhrase>> phraseMap = new HashMap<>();
        for (AICorePhrase phrase : phrases) {
            Long semanticId = phrase.getSemanticId();
            if (!phraseMap.containsKey(semanticId)) {
                phraseMap.put(semanticId, new ArrayList<>());
            }
            phraseMap.get(semanticId).add(phrase);
        }

        for (AISemantic aiSemantic : aiSemantics) {
            List<AICorePhrase> corePhrases = phraseMap.get(aiSemantic.getId());
            if (corePhrases == null || corePhrases.size() == 0) {
                throw new ScriptCheckException("语义:" + aiSemantic.getSemantic() + "中没有核心词汇");
            }
        }
        List<PhrasePack> phrasePacks = new ArrayList<>();
        for (AICorePhrase phrase : phrases) {
            PhrasePack phrasePack = new PhrasePack();
            boolean regex = ScriptUtil.isRegex(phrase.getPhraseName());
            phrasePack.setSemantic(semanticMap.get(phrase.getSemanticId()));
            phrasePack.setId(phrase.getSemanticId());
            phrasePack.setRegex(regex);
            if (regex) {
                phrasePack.setPattern(Pattern.compile(phrase.getPhraseName()));
            } else {
                phrasePack.setWord(phrase.getPhraseName());
            }
            phrasePacks.add(phrasePack);
        }
        return phrasePacks;
    }

    public List<Script> findScriptListByStatusAndIndustry(ScriptStatusEnum status, Long secondaryIndustryId) {
        return scriptRepository.findScriptsByIsDeletedAndStatusAndSecondaryIndustryIdOrderByUpdateTimeDesc(false, status, secondaryIndustryId);
    }
}
