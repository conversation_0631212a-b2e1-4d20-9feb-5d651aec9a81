package com.raipeng.aispeech.service;

import com.raipeng.aispeech.context.AIContext;
import com.raipeng.aispeech.controller.request.AutoExecuteRuleLogDTO;
import com.raipeng.aispeech.enums.ClueOperationType;
import com.raipeng.aispeech.exceptionadvice.exception.ClueAutoException;
import com.raipeng.aispeech.exceptionadvice.exception.ClueCheckException;
import com.raipeng.aispeech.model.*;
import com.raipeng.aispeech.model.callteam.CallSeat;
import com.raipeng.aispeech.model.callteam.CallSeatStatisticInfo;
import com.raipeng.aispeech.model.callteam.CallTeam;
import com.raipeng.aispeech.model.clue.Clue;
import com.raipeng.aispeech.model.clue.ClueTransferRecord;
import com.raipeng.aispeech.model.dto.PhoneField;
import com.raipeng.aispeech.model.record.CallRecord;
import com.raipeng.aispeech.repository.*;
import com.raipeng.aispeech.utils.ClueUtil;
import com.raipeng.common.enums.*;
import com.raipeng.common.util.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.raipeng.aispeech.constant.CommonConstant.*;
import static com.raipeng.common.enums.ClueStatus.*;

@Slf4j
@Service
public class AutoClueService {
    @Autowired
    private CallRecordMultiService callRecordMultiService;
    @Autowired
    private CluePhoneDeduplicationService cluePhoneDeduplicationService;
    @Autowired
    private ClueSearchService clueSearchService;
    @Autowired
    private CallSeatStatisticService callSeatStatisticService;
    @Autowired
    private AdminService adminService;
    @Autowired
    private ClueHandleService clueHandleService;
    @Autowired
    private AutoTransferClueRuleRepository autoTransferClueRuleRepository;
    @Autowired
    private AutoDistributeCallSeatClueRuleRepository autoDistributeCallSeatClueRuleRepository;
    @Autowired
    private AutoDistributeCallTeamClueRuleRepository autoDistributeCallTeamClueRuleRepository;

    @Autowired
    private AutoArchiveClueRuleRepository autoArchiveClueRuleRepository;
    @Autowired
    private AutoExecuteTimePointRepository autoExecuteTimePointRepository;
    @Autowired
    private AIOutboundTaskRepository aiOutboundTaskRepository;
    @Autowired
    private ClueRepository clueRepository;
    @Autowired
    private CallSeatRepository callSeatRepository;
    @Autowired
    private PhoneRecordService phoneRecordService;
    @Autowired
    private AutoClueRuleExecuteLogRepository autoClueRuleExecuteLogRepository;
    @Autowired
    private AdminRepository adminRepository;
    @Autowired
    private CallSeatStatisticInfoRepository callSeatStatisticInfoRepository;
    @Autowired
    private BatchService batchService;
    @Autowired
    private CallTeamRepository callTeamRepository;


    public AutoTransferClueRule addAutoTransferClueRule(AutoTransferClueRule autoTransferClueRuleDTO) {
        AutoTransferClueRule autoTransferClueRule = new AutoTransferClueRule();
        BeanUtils.copyProperties(autoTransferClueRuleDTO, autoTransferClueRule);
        String groupId = AIContext.getGroupId();
        autoTransferClueRule.setGroupId(groupId);
        autoTransferClueRule.setStatus("0");
        return autoTransferClueRuleRepository.save(autoTransferClueRule);
    }


    public AutoTransferClueRule editAutoTransferClueRule(AutoTransferClueRule autoTransferClueRuleDTO) {
        Optional<AutoTransferClueRule> byId = autoTransferClueRuleRepository.findById(autoTransferClueRuleDTO.getId());
        if (!byId.isPresent()) {
            throw new ClueAutoException("不存在该规则");
        }
        AutoTransferClueRule autoTransferClueRule = byId.get();
        String groupId = AIContext.getGroupId();
        if (!autoTransferClueRule.getGroupId().equals(groupId)) {
            throw new ClueAutoException("账号下不存在该规则");
        }
        if ("1".equals(autoTransferClueRule.getStatus())) {
            throw new ClueAutoException("该规则开启中无法编辑");
        }
        return autoTransferClueRuleRepository.save(autoTransferClueRuleDTO);
    }

    public void deleteAutoTransferClueRuleById(Long id) {
        autoTransferClueRuleRepository.deleteById(id);
        // 删除执行时刻数据
        autoExecuteTimePointRepository.deleteByRuleIdAndRuleType(id.toString(), AUTO_CLUE_RULE_TYPE_TRANSFER);
    }


    public AutoTransferClueRule findAutoTransferClueRule() {
        String groupId = AIContext.getGroupId();
        return autoTransferClueRuleRepository.findAutoTransferClueRuleByGroupId(groupId);
    }

    public void editAutoTransferClueRuleStatus(String status, Long id, String autoStop, String autoStopTime) {
        Optional<AutoTransferClueRule> byId = autoTransferClueRuleRepository.findById(id);
        if (!byId.isPresent()) {
            throw new ClueAutoException("不存在该规则");
        }

        autoTransferClueRuleRepository.updateStatus(status, autoStop, autoStopTime, id);
        // 删除之前定义的执行时刻,重新生成新的执行时刻
        AutoTransferClueRule autoTransferClueRule2 = byId.get();

        autoExecuteTimePointRepository.deleteByRuleIdAndRuleType(id.toString(), AUTO_CLUE_RULE_TYPE_TRANSFER);
        if (!status.equals("1")) {
            return;
        }
        String groupId = AIContext.getGroupId();
        Integer timeGap = autoTransferClueRule2.getExecuteTimeGap();
        List<String> startWorkTimes = Arrays.asList(autoTransferClueRule2.getStartWorkTimes().split(","));
        List<String> endWorkTimes = Arrays.asList(autoTransferClueRule2.getEndWorkTimes().split(","));
        createExecuteTimeWithoutNowTime(timeGap, startWorkTimes, endWorkTimes, id.toString(), AUTO_CLUE_RULE_TYPE_TRANSFER, groupId);

        // 并立刻执行一次 使用公共方法
        LocalDateTime dateStart = LocalDate.now().atStartOfDay();
        LocalDateTime dateEnd = LocalDate.now().plusDays(1).atStartOfDay();
        LocalDateTime endTime = LocalDateTime.now().minusMinutes(5L);
        LocalDateTime startTime = endTime.minusMinutes(timeGap);
        List<AIOutboundTask> taskList = aiOutboundTaskRepository.findTaskListByTimeAndGroupId(dateStart, dateEnd, groupId);
        List<String> taskIdList = taskList.stream().filter(task -> AIOutboundTaskType.AI_AUTO.equals(task.getTaskType())).map(a -> a.getId().toString()).collect(Collectors.toList());
        List<String> intentionClass = autoTransferClueRule2.getIntentionClass();
        List<String> excludeIntentionLabels = autoTransferClueRule2.getExcludeIntentionLabels();
        List<String> containIntentionLabels = autoTransferClueRule2.getContainIntentionLabels();
        String excludeEmptyLabel = autoTransferClueRule2.getExcludeEmptyLabel();
        Integer callDurationSecMin = autoTransferClueRule2.getMinCallDuration();
        Integer callDurationSecMax = autoTransferClueRule2.getMaxCallDuration();
        Integer cycleCountMin = autoTransferClueRule2.getMinCycleCount();
        Integer cycleCountMax = autoTransferClueRule2.getMaxCycleCount();
        String callStatus = "7";
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        List<CallRecord> callRecordPhoneByCallOutTime = callRecordMultiService.findCallRecordPhoneByCallOutTime(startTime.format(formatter), endTime.format(formatter),
                intentionClass,
                CollectionUtil.isEmpty(intentionClass) ? "1" : "0",
                callDurationSecMin, callDurationSecMax, cycleCountMin, cycleCountMax,
                callStatus, taskIdList
        );
        boolean containIntentionLabelEmpty = CollectionUtils.isEmpty(containIntentionLabels);
        boolean excludeIntentionLabelEmpty = CollectionUtils.isEmpty(excludeIntentionLabels);
        // 排除标签不为空并且排除空标签为1则认为需要排除空标签
        boolean excludeEmptyLabelIsTrue = StringUtils.isNotEmpty(excludeEmptyLabel) && "1".equals(excludeEmptyLabel);
        List<CallRecord> result = new ArrayList<>();
        for (CallRecord callRecord : callRecordPhoneByCallOutTime) {
            if (StringUtils.isBlank(callRecord.getIntentionLabels())) {
                if (containIntentionLabelEmpty && !excludeEmptyLabelIsTrue) {
                    result.add(callRecord);
                }
            } else {
                String intentionLabels = callRecord.getIntentionLabels();
                List<String> intentionList = Arrays.asList(intentionLabels.split(","));
                // (排除标签为空 或者 排除标签中不存在当前记录的标签） 并且 （包含标签为空 或者 包含标签中存在当前记录的标签）
                boolean check = (excludeIntentionLabelEmpty || intentionList.stream().noneMatch(excludeIntentionLabels::contains)) &&
                        (containIntentionLabelEmpty || intentionList.stream().anyMatch(containIntentionLabels::contains));
                if (check) {
                    result.add(callRecord);
                }
            }
        }
        int clueSize = 0;
        if (CollectionUtil.isNotEmpty(result)) {
            clueSize = handBatchCallRecords(result, groupId);
        }
        // 保存定时任务的操作日志
        AutoClueRuleExecuteLog autoClueRuleExecuteLog = new AutoClueRuleExecuteLog();
        autoClueRuleExecuteLog.setRuleId(id.toString());
        autoClueRuleExecuteLog.setClueNum(clueSize);
        autoClueRuleExecuteLog.setRuleType(AUTO_CLUE_RULE_TYPE_TRANSFER);
        autoClueRuleExecuteLog.setExecuteTime(LocalDateTime.now());
        autoClueRuleExecuteLog.setGroupId(groupId);
        autoClueRuleExecuteLogRepository.save(autoClueRuleExecuteLog);

    }

    private int handBatchCallRecords(List<CallRecord> callRecords, String groupId) {
        List<Clue> clues = new ArrayList<>();
        List<Clue> activeClues = new ArrayList<>();
        List<ClueTransferRecord> records = new ArrayList<>();
        String nowTimeString = ClueUtil.getNowTimeString();
        List<CallRecord> distinctList = new ArrayList<>(callRecords.stream()
                .filter(record -> StringUtils.isNotBlank(record.getPhone()))
                .collect(Collectors.toMap(
                        CallRecord::getPhone,
                        record -> record,
                        (existing, replacement) -> existing
                ))
                .values());
        Long userId = Long.parseLong(groupId.split("_")[2]);
        String account = adminService.getAdminById(userId).getAccount();
        List<String> phoneList = distinctList.stream().map(CallRecord::getPhone).collect(Collectors.toList());
        Map<String, Clue> existPhoneMap = clueSearchService.findExistPhoneMap(groupId, phoneList);
        //存入布隆过滤器的手机号
        List<String> addBloomPhoneList = new ArrayList<>();
        for (CallRecord callRecord : distinctList) {
            String phone = callRecord.getPhone();
            if (!existPhoneMap.containsKey(phone)) {
                addBloomPhoneList.add(phone);
                Clue clue = createClueFromRecord(phone, groupId, callRecord.getProvince(),
                        callRecord.getProvinceCode(), callRecord.getCity(), callRecord.getCityCode(),
                        callRecord.getOperator(), callRecord.getTaskId(), callRecord.getTaskName(),
                        callRecord.getSpeechCraftId(), callRecord.getScriptStringId(), callRecord.getCallOutTime(),
                        callRecord.getCallStatus(), callRecord.getIntentionClass(), callRecord.getSayCount(),
                        callRecord.getCycleCount(), callRecord.getCallDuration(), callRecord.getIntentionLabels(),
                        callRecord.getIntentionLabelIds());
                clues.add(clue);
                records.add(clueHandleService.createClueTransferRecord(
                        clue.getClueUniqueId(),
                        ClueOperationType.IMPORT,
                        nowTimeString,
                        userId,
                        account
                ));
            } else {
                // 处理重复手机号
                Clue clue = existPhoneMap.get(phone);
                //线索状态是已归档时激活线索
                if (clue.getClueStatus() == ClueStatus.BE_ARCHIVED) {
                    activeClues.add(clue);
                }
            }
        }
        // 对不重复的手机号加入布隆过滤器
        cluePhoneDeduplicationService.addPhoneToBloom(groupId, addBloomPhoneList);
        clueHandleService.updateTransferIdOfCluesForAuto(records, clues);
        batchService.batchInsert(clues);
        if (activeClues.size() > 0) {
            saveActiveClues(activeClues, nowTimeString, userId, account);
        }
        return clues.size() + activeClues.size();

    }

    private void saveActiveClues(List<Clue> clues, String nowTimeString, Long userId, String account) {
        List<ClueTransferRecord> records = new ArrayList<>();
        for (Clue clue : clues) {
            clue.setClueStatus(ClueStatus.TO_BE_SEND);
            clue.setCanBeSend(true);
            clue.setCanBeClueOperator(true);
            clue.setCallTeamId(null);
            clue.setCallSeatId(null);
            clue.setLatestFollowUpStatus(null);
            clue.setBeSendTime(nowTimeString);
            clue.setImportTime(nowTimeString);
            clue.setExamineStatus(null);
            clue.setFollowCount(0);
            clue.setStar(false);
            clue.setFromType(FromType.TASK_IMPORT);
            records.add(clueHandleService.createClueTransferRecord(
                    clue.getClueUniqueId(),
                    ClueOperationType.ACTIVE,
                    nowTimeString,
                    userId,
                    account
            ));
        }
        clueHandleService.updateTransferIdOfCluesForAuto(records, clues);
        clueRepository.saveAll(clues);
    }


    private Clue createClueFromRecord(String phone, String groupId, String province, String provinceCode,
                                      String city, String cityCode, String operator, String taskId, String taskName,
                                      Long scriptId, String scriptStringId, String callOutTime,
                                      String callStatusCode, String intentionClass, Integer sayCount,
                                      Integer cycleCount, Integer callDuration, String intentionLabels,
                                      String intentionLabelIds) {
        Clue clue = new Clue();
        clue.setClueUniqueId(ClueUtil.getUniqueID());
        clue.setCanBeClueOperator(true);
        clue.setCanBeSend(true);
        clue.setPhone(phone);
        clue.setGroupId(groupId);
        clue.setClueStatus(TO_BE_SEND);
        clue.setFromType(FromType.TASK_IMPORT);
        clue.setImportTime(ClueUtil.getNowTimeString());
        clue.setProvince(province);
        clue.setProvinceCode(provinceCode);
        clue.setCity(city);
        clue.setCityCode(cityCode);
        clue.setOperator(operator);
        clue.setTaskId(taskId);
        clue.setTaskName(taskName);
        clue.setScriptId(scriptId);
        clue.setScriptStringId(scriptStringId);
        clue.setCallOutTime(callOutTime);
        clue.setCallStatus(callStatusCode == null ? null : CallStatus.getClueCallStatusFromCallStatusCode(callStatusCode));
        clue.setIntentionClass(intentionClass);
        clue.setSayCount(sayCount);
        clue.setCycleCount(cycleCount);
        clue.setCallDuration(callDuration);
        clue.setIntentionLabels(intentionLabels);
        clue.setFormId(ClueUtil.getUniqueID());
        clue.setIntentionLabelIds(StringUtils.isEmpty(intentionLabelIds) ? null : Arrays
                .stream(intentionLabelIds.split(","))
                .filter(NumberUtils::isDigits)
                .map(Long::parseLong).collect(Collectors.toList()));
        clue.setFormId(ClueUtil.getUniqueID());
        return clue;
    }


    public AutoDistributeCallTeamClueRule addDistributeCallTeamClueRule(AutoDistributeCallTeamClueRule autoTransferClueRuleDTO) {
        AutoDistributeCallTeamClueRule autoTransferClueRule = new AutoDistributeCallTeamClueRule();
        BeanUtils.copyProperties(autoTransferClueRuleDTO, autoTransferClueRule);
        String groupId = AIContext.getGroupId();
        autoTransferClueRule.setGroupId(groupId);
        autoTransferClueRule.setStatus("0");
        return autoDistributeCallTeamClueRuleRepository.save(autoTransferClueRule);
    }


    public AutoDistributeCallTeamClueRule editDistributeCallTeamClueRule(AutoDistributeCallTeamClueRule autoTransferClueRuleDTO) {
        Optional<AutoDistributeCallTeamClueRule> byId = autoDistributeCallTeamClueRuleRepository.findById(autoTransferClueRuleDTO.getId());
        if (!byId.isPresent()) {
            throw new ClueAutoException("不存在该规则");
        }
        AutoDistributeCallTeamClueRule autoTransferClueRule = byId.get();
        String groupId = AIContext.getGroupId();
        if (!autoTransferClueRule.getGroupId().equals(groupId)) {
            throw new ClueAutoException("账号下不存在该规则");
        }
        if ("1".equals(autoTransferClueRule.getStatus())) {
            throw new ClueAutoException("该规则开启中无法编辑");
        }
        return autoDistributeCallTeamClueRuleRepository.save(autoTransferClueRuleDTO);
    }

    public void editDistributeCallTeamClueRuleStatus(String status, Long id, String autoStop, String autoStopTime) {

        Optional<AutoDistributeCallTeamClueRule> byId = autoDistributeCallTeamClueRuleRepository.findById(id);
        if (!byId.isPresent()) {
            throw new ClueAutoException("不存在该规则");
        }
        AutoDistributeCallTeamClueRule autoTransferClueRule2 = byId.get();
        autoDistributeCallTeamClueRuleRepository.updateStatus(status, autoStop, autoStopTime, id);
        // 删除之前定义的执行时刻
        autoExecuteTimePointRepository.deleteByRuleIdAndRuleType(id.toString(), AUTO_CLUE_RULE_TYPE_CALL_TEAM);
        if (!status.equals("1")) {
            return;
        }
        // 开启时 重新生成新的执行时刻
        String groupId = AIContext.getGroupId();
        Integer timeGap = autoTransferClueRule2.getExecuteTimeGap();
        List<String> startWorkTimes = Arrays.asList(autoTransferClueRule2.getStartWorkTimes().split(","));
        List<String> endWorkTimes = Arrays.asList(autoTransferClueRule2.getEndWorkTimes().split(","));
        createExecuteTimeWithoutNowTime(timeGap, startWorkTimes, endWorkTimes, id.toString(), AUTO_CLUE_RULE_TYPE_CALL_TEAM, groupId);
        // 按照规则类型，按坐席数分配还是平均分配 并立刻执行一次 使用公共方法
        String allocationType = autoTransferClueRule2.getAllocationType();
        List<Long> callTeamIds = autoTransferClueRule2.getCallTeamIds();
        int size = callTeamIds.size();
        if (size == 0) {
            return;
        }
        // 查询线索
        List<Clue> clues = clueRepository.getAutoToBeSendCluesByGroupId(groupId);
        List<String> joinedTaskPhoneIds = clues.stream().map(Clue::getJoinedTaskPhoneId).filter(Objects::nonNull).collect(Collectors.toList());
        List<PhoneField> phoneRecords = phoneRecordService.getPhoneRecordCallStatusList(joinedTaskPhoneIds);
        Map<String, String> phoneStatusMap = phoneRecords.stream().collect(Collectors.toMap(PhoneField::getRecordId, PhoneField::getCallStatus));

        List<Clue> clueSaveList = new ArrayList<>();
        for (Clue clue : clues) {
            String joinedTaskPhoneId = clue.getJoinedTaskPhoneId();
            if (joinedTaskPhoneId != null) {
                String phoneStatus = phoneStatusMap.get(joinedTaskPhoneId);
                if (phoneStatus == null || phoneStatus.equals("已删除") || phoneStatus.equals("呼叫完成")
                        || phoneStatus.equals("取消呼叫") || phoneStatus.equals("已屏蔽")) {
                    clue.setJoinedTaskPhoneId(null);
                    clue.setJoinedTaskId(null);
                    clue.setJoinedTaskName(null);
                    clue.setJoinedTaskStatus(null);
                    clue.setCanBeSend(true);
                    clueSaveList.add(clue);
                }
            }
        }
        clueRepository.saveAll(clueSaveList);
        List<Clue> collect = clues.stream().filter(Clue::isCanBeSend).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(collect)) {
            List<AutoClueRuleExecuteLog> autoClueRuleExecuteLogs = new ArrayList<>();
            for (Long callTeamId : callTeamIds) {
                AutoClueRuleExecuteLog autoClueRuleExecuteLog = getAutoClueRuleExecuteLog(groupId, id.toString(), callTeamId, 0, LocalDateTime.now());
                autoClueRuleExecuteLogs.add(autoClueRuleExecuteLog);
            }
            autoClueRuleExecuteLogRepository.saveAll(autoClueRuleExecuteLogs);
            return;
        }
        List<AutoClueRuleExecuteLog> executeLogs = new ArrayList<>();
        Map<Long, List<Clue>> longListMap = new HashMap<>();
        if ("0".equals(allocationType)) {
            // 平均分配到每个坐席
            Map<Long, Integer> seatCountMap = new HashMap<>();
            for (Long callTeamId : callTeamIds) {
                seatCountMap.put(callTeamId, 1);
            }
            longListMap = allocatedAccordSeatNumber(collect, callTeamIds, seatCountMap);
        } else {
            Map<Long, Integer> seatCountMap = new HashMap<>();
            int totalSeats = 0;
            List<Long> hasSeatCallTeamIds = new ArrayList<>();

            for (Long callTeamId : callTeamIds) {
                AutoDistributeCallSeatClueRule distributeCallSeatClueRuleByGroupId = autoDistributeCallSeatClueRuleRepository.findDistributeCallSeatClueRuleByGroupId(groupId, callTeamId);
                if (null == distributeCallSeatClueRuleByGroupId || CollectionUtils.isEmpty(distributeCallSeatClueRuleByGroupId.getCallSeatIds())) {
                    AutoClueRuleExecuteLog autoClueRuleExecuteLog = getAutoClueRuleExecuteLog(groupId, id.toString(), callTeamId, 0, LocalDateTime.now());
                    executeLogs.add(autoClueRuleExecuteLog);
                } else {
                    int callSeat = distributeCallSeatClueRuleByGroupId.getCallSeatIds().size();
                    totalSeats += callSeat;
                    hasSeatCallTeamIds.add(callTeamId);
                    seatCountMap.put(callTeamId, callSeat);
                }
            }
            if (totalSeats == 0) {
                log.info("坐席组无坐席分配");
            } else {
                // 按坐席数量分配
                longListMap = allocatedAccordSeatNumber(collect, hasSeatCallTeamIds, seatCountMap);
            }
        }
        Admin masterAccount = adminRepository.findFirstById(Long.valueOf(groupId.split("_")[2]));

        for (Map.Entry<Long, List<Clue>> map : longListMap.entrySet()) {
            List<Clue> clueList = map.getValue();
            Long callTeamId = map.getKey();

            int clueListSize = clueList.size();
            AutoClueRuleExecuteLog autoClueRuleExecuteLog = getAutoClueRuleExecuteLog(groupId, id.toString(), callTeamId, clueListSize, LocalDateTime.now());
            executeLogs.add(autoClueRuleExecuteLog);
            sendCluesToCallTeam(clueList, callTeamId, masterAccount);
        }
        // 保存执行日志
        autoClueRuleExecuteLogRepository.saveAll(executeLogs);

    }


    public void sendCluesToCallTeam(List<Clue> clues, Long callTeamId, Admin masterAccount) {
        CallTeam callTeam = clueHandleService.findCallTeamById(callTeamId);
        List<ClueTransferRecord> records = new ArrayList<>();
        String nowTimeString = ClueUtil.getNowTimeString();
        Long userId = masterAccount.getId();
        String account = masterAccount.getAccount();
        Set<Long> clueIdsForSend = new HashSet<>();
        for (Clue clue : clues) {
            if (clue.isCanBeSend() && clue.isCanBeClueOperator()) {
                clue.setCallTeamId(callTeamId);
                clue.setClueStatus(TO_BE_DISTRIBUTE);
                clue.setBeSendTime(nowTimeString);
                clueIdsForSend.add(clue.getId());
                records.add(clueHandleService.createClueTransferRecord(
                        clue.getClueUniqueId(),
                        ClueOperationType.SEND,
                        nowTimeString,
                        userId,
                        account));
            }
        }
        callSeatStatisticService.updateTeamAcquireCountAndSave(callTeamId, LocalDate.now(), clueIdsForSend);
        callTeamRepository.save(callTeam);
        clueHandleService.updateTransferIdOfCluesForAuto(records, clues);
        clueRepository.saveAll(clues);
    }

    public Map<Long, List<Clue>> allocatedAccordSeatNumber(List<Clue> dataList, List<Long> callTeamIdList, Map<Long, Integer> seatCounts) {
        Map<Long, List<Clue>> result = new HashMap<>();

        // Calculate total seats
        int totalSeats = 0;
        for (int seats : seatCounts.values()) {
            totalSeats += seats;
        }

        // Initialize result map with empty lists
        for (Long callTeamId : callTeamIdList) {
            result.put(callTeamId, new ArrayList<>());
        }

        double totalDataSize = dataList.size();
        double accumulated = 0.0;
        int start = 0;
        for (int i = 0; i < callTeamIdList.size(); i++) {
            Long callTeamId = callTeamIdList.get(i);
            int seats = seatCounts.get(callTeamId);
            double allocated = seats * totalDataSize / totalSeats;
            accumulated += allocated;
            int end = (int) Math.round(accumulated);
            end = Math.min(end, dataList.size());
            List<Clue> clues = dataList.subList(start, end);
            result.get(callTeamId).addAll(clues);
            start = end;
        }

        return result;
    }


    public void deleteDistributeCallTeamClueRuleById(Long id) {
        autoDistributeCallTeamClueRuleRepository.deleteById(id);
        // 删除执行时刻数据
        autoExecuteTimePointRepository.deleteByRuleIdAndRuleType(id.toString(), AUTO_CLUE_RULE_TYPE_CALL_TEAM);

    }


    public AutoDistributeCallTeamClueRule findDistributeCallTeamClueRuleById() {
        String groupId = AIContext.getGroupId();
        return autoDistributeCallTeamClueRuleRepository.findDistributeCallTeamClueRuleByGroupId(groupId);

    }


    public AutoDistributeCallSeatClueRule addDistributeCallSeatClueRule(AutoDistributeCallSeatClueRule autoTransferClueRule) {
        AutoDistributeCallSeatClueRule autoTransferClueRule1 = new AutoDistributeCallSeatClueRule();
        CallTeam callTeam = callTeamRepository.findCallTeamByLeaderAccountId(AIContext.getUserId());
        if (callTeam == null) {
            throw new ClueCheckException("该账号不是任何坐席组的组长");
        }
        String groupId = AIContext.getGroupId();
        autoTransferClueRule.setGroupId(groupId);
        autoTransferClueRule.setCallTeamId(callTeam.getId());
        autoTransferClueRule.setStatus("0");
        BeanUtils.copyProperties(autoTransferClueRule, autoTransferClueRule1);
        return autoDistributeCallSeatClueRuleRepository.save(autoTransferClueRule1);
    }


    public AutoDistributeCallSeatClueRule editDistributeCallSeatClueRule(AutoDistributeCallSeatClueRule autoTransferClueRule) {
        Optional<AutoDistributeCallSeatClueRule> byId = autoDistributeCallSeatClueRuleRepository.findById(autoTransferClueRule.getId());
        if (!byId.isPresent()) {
            throw new ClueAutoException("不存在该规则");
        }
        AutoDistributeCallSeatClueRule autoTransferClueRule1 = byId.get();
        String groupId = AIContext.getGroupId();
        if (!autoTransferClueRule1.getGroupId().equals(groupId)) {
            throw new ClueAutoException("账号下不存在该规则");
        }
        if ("1".equals(autoTransferClueRule1.getStatus())) {
            throw new ClueAutoException("该规则开启中无法编辑");
        }
        return autoDistributeCallSeatClueRuleRepository.save(autoTransferClueRule);
    }

    public void editDistributeCallSeatClueRuleStatus(String status, Long id, String autoStop, String autoStopTime) {
        Optional<AutoDistributeCallSeatClueRule> byId = autoDistributeCallSeatClueRuleRepository.findById(id);
        if (!byId.isPresent()) {
            throw new ClueAutoException();
        }
        AutoDistributeCallSeatClueRule autoTransferClueRule = byId.get();
        String groupId = AIContext.getGroupId();

        List<Long> callSeatIds = autoTransferClueRule.getCallSeatIds();
        Long callTeamId = autoTransferClueRule.getCallTeamId();
        List<Clue> clueList = clueRepository.getToBeDistributeCluesByGroupIdAndCallSeatId(groupId, callTeamId);

        autoDistributeCallSeatClueRuleRepository.updateStatus(status, autoStop, autoStopTime, id);
        // 删除之前定义的执行时刻,重新生成新的执行时刻
        autoExecuteTimePointRepository.deleteByRuleIdAndRuleType(id.toString(), AUTO_CLUE_RULE_TYPE_CALL_SEAT);
        if (!status.equals("1")) {
            return;
        }

        Integer timeGap = autoTransferClueRule.getExecuteTimeGap();
        List<String> startWorkTimes = Arrays.asList(autoTransferClueRule.getStartWorkTimes().split(","));
        List<String> endWorkTimes = Arrays.asList(autoTransferClueRule.getEndWorkTimes().split(","));
        createExecuteTimeWithoutNowTime(timeGap, startWorkTimes, endWorkTimes, id.toString(), AUTO_CLUE_RULE_TYPE_CALL_SEAT, groupId);
        Admin masterAccount = adminRepository.findFirstById(Long.valueOf(groupId.split("_")[2]));

        //  按照规则类型，按坐席分配 并立刻执行一次 使用公共方法
        List<AutoClueRuleExecuteLog> executeLogs = new ArrayList<>();
        Map<Long, Integer> seatCountMap = new HashMap<>();
        for (Long callSeatId : callSeatIds) {
            seatCountMap.put(callSeatId, 1);
        }
        Map<Long, List<Clue>> longListMap = allocatedAccordSeatNumber(clueList, callSeatIds, seatCountMap);
        for (Map.Entry<Long, List<Clue>> a : longListMap.entrySet()) {
            List<Clue> value = a.getValue();
            Long callSeatId = a.getKey();
            AutoClueRuleExecuteLog autoClueRuleExecuteLog = getAutoClueRuleExecuteLog(groupId, id.toString(), callTeamId, value, callSeatId, LocalDateTime.now());
            executeLogs.add(autoClueRuleExecuteLog);
            distributeCluesIntoCallSeat(value, callSeatId, masterAccount);
        }
        // 保存执行日志
        autoClueRuleExecuteLogRepository.saveAll(executeLogs);
    }

    private AutoClueRuleExecuteLog getAutoClueRuleExecuteLog(String groupId, String ruleId, Long callTeamId, List<Clue> value, Long callSeatId, LocalDateTime executeTime) {
        AutoClueRuleExecuteLog autoClueRuleExecuteLog = new AutoClueRuleExecuteLog();
        autoClueRuleExecuteLog.setRuleId(ruleId);
        autoClueRuleExecuteLog.setClueNum(value.size());
        autoClueRuleExecuteLog.setCallTeamId(callTeamId);
        autoClueRuleExecuteLog.setCallSeatId(callSeatId);
        autoClueRuleExecuteLog.setRuleType(AUTO_CLUE_RULE_TYPE_CALL_SEAT);
        autoClueRuleExecuteLog.setExecuteTime(executeTime);
        autoClueRuleExecuteLog.setGroupId(groupId);
        return autoClueRuleExecuteLog;
    }

    private void createExecuteTimeWithoutNowTime(Integer timeGap, List<String> startWorkTimes, List<String> endWorkTimes,
                                                 String ruleId, String ruleType, String groupId) {

        int m = startWorkTimes.size();
        int n = endWorkTimes.size();
        if (m != n) {
            log.info("Exception:执行时间段有误无法创建正常的自动执行执行时刻");
            return;
        }
        Long timeGapLong = Long.valueOf(timeGap);
        // 创建出的执行时间点要在当前时间时之后
        LocalDateTime executeLimitTime = LocalDateTime.now().plusMinutes(timeGapLong);
        String prefix = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " ";
        List<LocalDateTime> executeTimeList = new ArrayList<>();
        for (int i = 0; i < n; i++) {
            LocalDateTime startTime = LocalDateTime.parse(prefix + startWorkTimes.get(i) + ":00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            LocalDateTime endTime = LocalDateTime.parse(prefix + endWorkTimes.get(i) + ":00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            while (startTime.isBefore(endTime)) {
                if (!startTime.isBefore(executeLimitTime)) {
                    executeTimeList.add(startTime);
                }
                startTime = startTime.plusMinutes(timeGapLong);
            }
        }
        List<AutoExecuteTimePoint> autoExecuteTimePointList = new ArrayList<>();

        if (CollectionUtil.isNotEmpty(executeTimeList)) {
            for (LocalDateTime executeTime : executeTimeList) {
                AutoExecuteTimePoint autoExecuteTimePoint = new AutoExecuteTimePoint();
                autoExecuteTimePoint.setExecuteTime(executeTime);
                autoExecuteTimePoint.setRuleId(ruleId);
                autoExecuteTimePoint.setRuleType(ruleType);
                autoExecuteTimePoint.setGroupId(groupId);
                autoExecuteTimePointList.add(autoExecuteTimePoint);
            }
        }
        autoExecuteTimePointRepository.saveAll(autoExecuteTimePointList);
    }

    private void createExecuteTime(Integer timeGap, List<String> startWorkTimes, List<String> endWorkTimes,
                                   String ruleId, String ruleType, String groupId) {

        int m = startWorkTimes.size();
        int n = endWorkTimes.size();
        if (m != n) {
            log.info("Exception:规则id{},规则类型{}执行时间段有误无法创建正常的自动执行执行时刻", ruleId, ruleType);
            return;
        }
        Long res = Long.valueOf(timeGap);
        String s = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " ";
        List<LocalDateTime> executeTimeList = new ArrayList<>();
        for (int i = 0; i < n; i++) {
            LocalDateTime startTime = LocalDateTime.parse(s + startWorkTimes.get(i) + ":00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            LocalDateTime endTime = LocalDateTime.parse(s + endWorkTimes.get(i) + ":00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            while (startTime.isBefore(endTime)) {
                executeTimeList.add(startTime);
                startTime = startTime.plusMinutes(res);
            }
        }
        List<AutoExecuteTimePoint> autoExecuteTimePointList = new ArrayList<>();

        if (CollectionUtil.isNotEmpty(executeTimeList)) {
            for (LocalDateTime executeTime : executeTimeList) {
                AutoExecuteTimePoint autoExecuteTimePoint = new AutoExecuteTimePoint();
                autoExecuteTimePoint.setExecuteTime(executeTime);
                autoExecuteTimePoint.setRuleId(ruleId);
                autoExecuteTimePoint.setRuleType(ruleType);
                autoExecuteTimePoint.setGroupId(groupId);
                autoExecuteTimePointList.add(autoExecuteTimePoint);
            }
        }
        autoExecuteTimePointRepository.saveAll(autoExecuteTimePointList);

    }

    private void createExecuteTime(List<String> workTimes, String ruleId, String ruleType, String groupId) {
        String s = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " ";
        List<AutoExecuteTimePoint> autoExecuteTimePointList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(workTimes)) {
            for (String workTime : workTimes) {
                LocalDateTime executeTime = LocalDateTime.parse(s + workTime + ":00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                AutoExecuteTimePoint autoExecuteTimePoint = new AutoExecuteTimePoint();
                autoExecuteTimePoint.setExecuteTime(executeTime);
                autoExecuteTimePoint.setRuleId(ruleId);
                autoExecuteTimePoint.setRuleType(ruleType);
                autoExecuteTimePoint.setGroupId(groupId);
                autoExecuteTimePointList.add(autoExecuteTimePoint);
            }
        }
        autoExecuteTimePointRepository.saveAll(autoExecuteTimePointList);

    }

    public void deleteDistributeCallSeatClueRule(Long id) {
        autoDistributeCallSeatClueRuleRepository.deleteById(id);
        // 删除执行时刻数据
        autoExecuteTimePointRepository.deleteByRuleIdAndRuleType(id.toString(), AUTO_CLUE_RULE_TYPE_CALL_SEAT);
    }


    public AutoDistributeCallSeatClueRule findDistributeCallSeatClueRule() {
        CallTeam callTeam = callTeamRepository.findCallTeamByLeaderAccountId(AIContext.getUserId());
        if (callTeam == null) {
            throw new ClueCheckException("该账号不是任何坐席组的组长");
        }
        String groupId = AIContext.getGroupId();

        return autoDistributeCallSeatClueRuleRepository.findDistributeCallSeatClueRuleByGroupId(groupId, callTeam.getId());

    }


    public List<AutoClueRuleExecuteLog> findRuleLogList(AutoExecuteRuleLogDTO autoExecuteRuleLogDTO) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startTime = LocalDateTime.parse(autoExecuteRuleLogDTO.getStartTime(), formatter);
        LocalDateTime endTime = LocalDateTime.parse(autoExecuteRuleLogDTO.getEndTime(), formatter);
        List<AutoClueRuleExecuteLog> ruleLogListByRuleIdAndRuleType = autoClueRuleExecuteLogRepository.findRuleLogListByRuleIdAndRuleType(autoExecuteRuleLogDTO.getRuleId().toString(),
                autoExecuteRuleLogDTO.getExecuteType(), startTime, endTime);

        if (null != autoExecuteRuleLogDTO.getRemoveEmpty() && autoExecuteRuleLogDTO.getRemoveEmpty()){
            ruleLogListByRuleIdAndRuleType = ruleLogListByRuleIdAndRuleType.stream().filter(a -> 0 != a.getClueNum()).collect(Collectors.toList());
        }

        String groupId = AIContext.getGroupId();
        List<CallTeam> callTeamList = callTeamRepository.findCallTeamsByGroupId(groupId);
        List<CallSeat> callSeatList = callSeatRepository.findCallSeatsByGroupId(groupId);
        Map<Long, String> callTeamMap = callTeamList.stream().collect(Collectors.toMap(CallTeam::getId, CallTeam::getCallTeamName));
        Map<Long, String> seatMap = callSeatList.stream().collect(Collectors.toMap(CallSeat::getId, CallSeat::getName));
        for (AutoClueRuleExecuteLog autoClueRuleExecuteLog : ruleLogListByRuleIdAndRuleType) {
            Long callSeatId = autoClueRuleExecuteLog.getCallSeatId();
            String callSeatName = null != callSeatId ? seatMap.getOrDefault(callSeatId, callSeatId.toString()) : "";
            Long callTeamId = autoClueRuleExecuteLog.getCallTeamId();
            String callTeamName = null != callTeamId ? callTeamMap.getOrDefault(callTeamId, callTeamId.toString()) : "";
            String executeLog = getExecuteLog(callTeamName, callSeatName, autoClueRuleExecuteLog.getRuleType(), autoClueRuleExecuteLog.getClueNum());
            autoClueRuleExecuteLog.setExecuteLog(executeLog);
        }
        return ruleLogListByRuleIdAndRuleType;
    }

    private String getExecuteLog(String callTeamName, String callSeatName, String executeType, Integer clueNum) {
        String executeLog = "";
        if (AUTO_CLUE_RULE_TYPE_TRANSFER.equals(executeType)) {
            executeLog = clueNum + "条线索导入";
        } else if (AUTO_CLUE_RULE_TYPE_CALL_TEAM.equals(executeType)) {
            executeLog = clueNum + "条线索下发到坐席组: " + callTeamName;
        } else if (AUTO_CLUE_RULE_TYPE_CALL_SEAT.equals(executeType)) {
            executeLog = clueNum + "条线索分配到坐席: " + callSeatName;
        } else if (AUTO_CLUE_RULE_TYPE_ARCHIVE.equals(executeType)) {
            executeLog = clueNum + "条线索归档";
        }
        return executeLog;
    }


    public void distributeCluesIntoCallSeat(List<Clue> clues, Long callSeatId, Admin masterAccount) {
        List<Long> clueIds = clues.stream().map(Clue::getId).collect(Collectors.toList());
        CallSeat callSeat = clueHandleService.findCallSeatById(callSeatId);
        List<Long> clueIdsOld = callSeat.getClueIds();
        List<ClueTransferRecord> records = new ArrayList<>();
        String nowTimeString = ClueUtil.getNowTimeString();
        String account = masterAccount.getAccount();
        if (clueIdsOld == null) {
            clueIdsOld = new ArrayList<>();
        }
        for (Clue clue : clues) {
            clue.setCallSeatId(callSeatId);
            clue.setClueStatus(BE_DISTRIBUTED);
            clue.setLatestFollowUpStatus(FollowUpStatus.BEING);
            clue.setBeAllocatedTime(nowTimeString);
            clue.setAutoRecoveryTime(null);

            if (!clueIdsOld.contains(clue.getId())) {
                clueIdsOld.add(clue.getId());
            }
            records.add(clueHandleService.createClueTransferRecord(
                    clue.getClueUniqueId(),
                    ClueOperationType.DISTRIBUTE,
                    nowTimeString,
                    masterAccount.getId(),
                    account));
        }
        callSeat.setClueIds(clueIdsOld);
        callSeatStatisticService.updateSeatAcquireCountAndSave(callSeatId, LocalDate.now(), clueIds);
        clueHandleService.updateTransferIdOfCluesForAuto(records, clues);
        callSeatRepository.save(callSeat);
        clueRepository.saveAll(clues);

    }

    public void clueAuto(Boolean recordToClue, Boolean clueDispatchCallTeam, Boolean clueDispatchCallSeat, Boolean archiveClue) {
        // 按照顺序执行
        LocalDateTime now = LocalDateTime.now();
        if (recordToClue) {
            List<AutoExecuteTimePoint> executeTimePointBeforeTime = autoExecuteTimePointRepository.findExecuteTimePointBeforeTime(now, AUTO_CLUE_RULE_TYPE_TRANSFER);
            recordToClue(executeTimePointBeforeTime);
            autoExecuteTimePointRepository.deleteByExecuteTimeAndType(now, AUTO_CLUE_RULE_TYPE_TRANSFER);
        }
        if (clueDispatchCallTeam) {
            List<AutoExecuteTimePoint> executeTimePointBeforeTime = autoExecuteTimePointRepository.findExecuteTimePointBeforeTime(now, AUTO_CLUE_RULE_TYPE_CALL_TEAM);
            clueDispatchCallTeam(executeTimePointBeforeTime);
            autoExecuteTimePointRepository.deleteByExecuteTimeAndType(now, AUTO_CLUE_RULE_TYPE_CALL_TEAM);

        }
        if (clueDispatchCallSeat) {
            List<AutoExecuteTimePoint> executeTimePointBeforeTime = autoExecuteTimePointRepository.findExecuteTimePointBeforeTime(now, AUTO_CLUE_RULE_TYPE_CALL_SEAT);
            clueDispatchCallSeat(executeTimePointBeforeTime);
            autoExecuteTimePointRepository.deleteByExecuteTimeAndType(now, AUTO_CLUE_RULE_TYPE_CALL_SEAT);
        }
        if (archiveClue) {
            List<AutoExecuteTimePoint> executeTimePointBeforeTime = autoExecuteTimePointRepository.findExecuteTimePointBeforeTime(now, AUTO_CLUE_RULE_TYPE_ARCHIVE);
            archiveClue(executeTimePointBeforeTime);
            autoExecuteTimePointRepository.deleteByExecuteTimeAndType(now, AUTO_CLUE_RULE_TYPE_ARCHIVE);
        }
    }

    private void archiveClue(List<AutoExecuteTimePoint> executeTimePointBeforeTime) {
        for (AutoExecuteTimePoint autoExecuteTimePoint : executeTimePointBeforeTime) {
            String ruleId = autoExecuteTimePoint.getRuleId();
            LocalDateTime executeTime = autoExecuteTimePoint.getExecuteTime();
            Optional<AutoArchiveClueRule> byId = autoArchiveClueRuleRepository.findById(Long.valueOf(ruleId));
            if (!byId.isPresent()) {
                continue;
            }
            AutoArchiveClueRule autoTransferClueRule = byId.get();
            String groupId = autoTransferClueRule.getGroupId();
            if (!autoTransferClueRule.getStatus().equals("1")) {
                continue;
            }
            List<ClueStatus> clueStatusList = autoTransferClueRule.getClueStatusList();
            // 状态待下发的数据由于存在线索加入任务，需要判断是否可以下发并且更新线索信息
            // 之后需要和 待分配，已分配，已回收 三种状态的线索数据判断是否可以执行，已分配的数据可能正在拨打，可以操作的标识未false
            // 对已分配的数据判断是否需要过滤星标，待首呼的数据
            // 对可以回收的线索执行归档操作

            if (clueStatusList.contains(TO_BE_SEND)) {
                List<Clue> toBeSendList = clueRepository.getAutoToBeSendCluesByGroupId(groupId);
                // 将加入任务的代下发数据判断是否需要解锁处理
                List<String> joinedTaskPhoneIds = toBeSendList.stream().map(Clue::getJoinedTaskPhoneId).filter(Objects::nonNull).collect(Collectors.toList());
                List<PhoneField> phoneRecords = phoneRecordService.getPhoneRecordCallStatusList(joinedTaskPhoneIds);
                Map<String, String> phoneStatusMap = phoneRecords.stream().collect(Collectors.toMap(PhoneField::getRecordId, PhoneField::getCallStatus));
                List<Clue> clueSaveList = new ArrayList<>();
                for (Clue clue : toBeSendList) {
                    String joinedTaskPhoneId = clue.getJoinedTaskPhoneId();
                    if (joinedTaskPhoneId != null) {
                        String phoneStatus = phoneStatusMap.get(joinedTaskPhoneId);
                        if (phoneStatus == null || phoneStatus.equals("已删除") || phoneStatus.equals("呼叫完成")
                                || phoneStatus.equals("取消呼叫") || phoneStatus.equals("已屏蔽")) {
                            clue.setJoinedTaskPhoneId(null);
                            clue.setJoinedTaskId(null);
                            clue.setJoinedTaskName(null);
                            clue.setJoinedTaskStatus(null);
                            clue.setCanBeSend(true);
                            clueSaveList.add(clue);
                        }
                    }
                }
                clueRepository.saveAll(clueSaveList);
            }
            List<String> collect = clueStatusList.stream().map(a -> a.toString()).collect(Collectors.toList());
            List<Clue> clueList = clueRepository.getToBeDistributeCluesByGroupId(groupId, collect);
            clueList = clueList.stream().filter(Clue::isCanBeSend).collect(Collectors.toList());
            // 对线索 去除跟进次数 去除星标
            if ("1".equals(autoTransferClueRule.getExcludeStart())) {
                clueList = clueList.stream().filter(a -> !BE_DISTRIBUTED.equals(a.getClueStatus()) || !a.isStar()).collect(Collectors.toList());
            }
            if ("1".equals(autoTransferClueRule.getExcludeNotCalled())) {
                clueList = clueList.stream().filter(a -> !BE_DISTRIBUTED.equals(a.getClueStatus()) || a.getFollowCount() != 0).collect(Collectors.toList());
            }

            Admin masterAccount = adminRepository.findFirstById(Long.valueOf(groupId.split("_")[2]));
            // 归档线索
            archiveClue(clueList, masterAccount);
            // 保存执行日志
            AutoClueRuleExecuteLog autoClueRuleExecuteLog = new AutoClueRuleExecuteLog();
            autoClueRuleExecuteLog.setClueNum(clueList.size());
            autoClueRuleExecuteLog.setRuleType("3");
            autoClueRuleExecuteLog.setGroupId(groupId);
            autoClueRuleExecuteLog.setExecuteTime(executeTime);
            autoClueRuleExecuteLog.setRuleId(ruleId);

            // 保存执行日志
            autoClueRuleExecuteLogRepository.save(autoClueRuleExecuteLog);
            autoExecuteTimePointRepository.deleteExecuteTimePointById(autoExecuteTimePoint.getId());
        }


    }




    private void archiveClue(List<Clue> clues, Admin admin) {
        if (CollectionUtil.isEmpty(clues)) {
            return;
        }
        List<Long> callSeatIdList = clues.stream().map(Clue::getCallSeatId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<CallSeat> callSeatList = callSeatRepository.findByCallSeatIdList(callSeatIdList);
        Map<Long, CallSeat> callSeatMap = callSeatList.stream().collect(Collectors.toMap(CallSeat::getId, a -> a));

        List<CallSeatStatisticInfo> callSeatStatisList = callSeatStatisticInfoRepository.findCallSeatStatisticInfosByCallSeatIdInAndDate(callSeatIdList, LocalDate.now());
        Map<Long, CallSeatStatisticInfo> callSeatStatisMap = callSeatStatisList.stream().collect(Collectors.toMap(CallSeatStatisticInfo::getCallSeatId, a -> a, (a, b) -> a));

        List<ClueTransferRecord> records = new ArrayList<>();
        String now = ClueUtil.getNowTimeString();
        Long userId = admin.getId();
        String account = admin.getAccount();
        for (Clue clue : clues) {
            if (clue.isCanBeSend()) {
                clue.setClueStatus(ClueStatus.BE_ARCHIVED);
                clue.setArchivedTime(now);
                clue.setCanBeClueOperator(true);
                Long callSeatId = clue.getCallSeatId();
                if (callSeatId != null) {
                    // 理论上不会出现线索中的坐席是不存在的，但是为了防止异常，需要判断一下
                    if (!callSeatMap.containsKey(callSeatId)) {
                        log.error("Exception :自动归档线索时该线索{}中的 坐席{}不存在，请检查", clue.getId(), callSeatId);
                    } else {
                        CallSeat callSeat = callSeatMap.get(callSeatId);
                        callSeat.getClueIds().remove(clue.getId());
                        CallSeatStatisticInfo callSeatStatisticInfo = callSeatStatisMap.get(callSeatId);
                        callSeatStatisticInfo.getCallSeatClueStatisticInfo().getBeArchivedSet().add(clue.getId());
                    }
                }
                records.add(clueHandleService.createClueTransferRecord(
                        clue.getClueUniqueId(),
                        ClueOperationType.ARCHIVE,
                        now,
                        userId,
                        account));
            }
        }
        clueHandleService.updateTransferIdOfCluesForAuto(records, clues);
        List<CallSeatStatisticInfo> callSeatStatisticInfos = new ArrayList<>(callSeatStatisMap.values());
        callSeatStatisticInfoRepository.saveAll(callSeatStatisticInfos);
        List<CallSeat> callSeats = new ArrayList<>(callSeatMap.values());
        callSeatRepository.saveAll(callSeats);
        clueRepository.saveAll(clues);
    }


    private void clueDispatchCallSeat(List<AutoExecuteTimePoint> collect) {
        for (AutoExecuteTimePoint autoExecuteTimePoint : collect) {
            String ruleId = autoExecuteTimePoint.getRuleId();
            LocalDateTime executeTime = autoExecuteTimePoint.getExecuteTime();
            Optional<AutoDistributeCallSeatClueRule> byId = autoDistributeCallSeatClueRuleRepository.findById(Long.valueOf(ruleId));
            if (!byId.isPresent()) {
                continue;
            }
            AutoDistributeCallSeatClueRule autoTransferClueRule = byId.get();
            String groupId = autoTransferClueRule.getGroupId();

            List<Long> callSeatIds = autoTransferClueRule.getCallSeatIds();
            Long callTeamId = autoTransferClueRule.getCallTeamId();
            List<Clue> clueList = clueRepository.getToBeDistributeCluesByGroupIdAndCallSeatId(groupId, callTeamId);

            if (!autoTransferClueRule.getStatus().equals("1")) {
                continue;
            }

            Admin masterAccount = adminRepository.findFirstById(Long.valueOf(groupId.split("_")[2]));
            //  按照规则类型，按坐席分配 并立刻执行一次 使用公共方法
            List<AutoClueRuleExecuteLog> executeLogs = new ArrayList<>();
            Map<Long, Integer> seatCountMap = new HashMap<>();
            for (Long callSeatId : callSeatIds) {
                seatCountMap.put(callSeatId, 1);
            }
            Map<Long, List<Clue>> longListMap = allocatedAccordSeatNumber(clueList, callSeatIds, seatCountMap);

            for (Map.Entry<Long, List<Clue>> callSeatClue : longListMap.entrySet()) {
                List<Clue> value = callSeatClue.getValue();
                Long callSeatId = callSeatClue.getKey();
                AutoClueRuleExecuteLog autoClueRuleExecuteLog = getAutoClueRuleExecuteLog(groupId, ruleId, callTeamId, value, callSeatId, executeTime);
                executeLogs.add(autoClueRuleExecuteLog);
                distributeCluesIntoCallSeat(value, callSeatId, masterAccount);
            }
            // 保存执行日志
            autoClueRuleExecuteLogRepository.saveAll(executeLogs);
            autoExecuteTimePointRepository.deleteExecuteTimePointById(autoExecuteTimePoint.getId());
        }
    }

    private void clueDispatchCallTeam(List<AutoExecuteTimePoint> collect) {
        for (AutoExecuteTimePoint autoExecuteTimePoint : collect) {
            String ruleId = autoExecuteTimePoint.getRuleId();
            LocalDateTime executeTime = autoExecuteTimePoint.getExecuteTime();

            Optional<AutoDistributeCallTeamClueRule> byId = autoDistributeCallTeamClueRuleRepository.findById(Long.valueOf(ruleId));
            if (!byId.isPresent()) {
                log.info("{}不存在该规则", ruleId);
                continue;
            }
            AutoDistributeCallTeamClueRule autoTransferClueRule = byId.get();
            if (!autoTransferClueRule.getStatus().equals("1")) {
                continue;
            }
            String groupId = autoTransferClueRule.getGroupId();
            Admin masterAccount = adminRepository.findFirstById(Long.valueOf(groupId.split("_")[2]));
            // 按照规则类型，按坐席数分配还是平均分配 并立刻执行一次 使用公共方法
            String allocationType = autoTransferClueRule.getAllocationType();
            List<Long> callTeamIds = autoTransferClueRule.getCallTeamIds();
            int size = callTeamIds.size();
            if (size == 0) {
                continue;
            }
            // 查询线索
            List<Clue> clues = clueRepository.getAutoToBeSendCluesByGroupId(groupId);
            List<String> joinedTaskPhoneIds = clues.stream().map(Clue::getJoinedTaskPhoneId).filter(Objects::nonNull).collect(Collectors.toList());
            List<PhoneField> phoneRecords = phoneRecordService.getPhoneRecordCallStatusList(joinedTaskPhoneIds);
            Map<String, String> phoneStatusMap = phoneRecords.stream().collect(Collectors.toMap(PhoneField::getRecordId, PhoneField::getCallStatus));

            List<Clue> clueSaveList = new ArrayList<>();
            for (Clue clue : clues) {
                String joinedTaskPhoneId = clue.getJoinedTaskPhoneId();
                if (joinedTaskPhoneId != null) {
                    String phoneStatus = phoneStatusMap.get(joinedTaskPhoneId);
                    if (phoneStatus == null || phoneStatus.equals("已删除") || phoneStatus.equals("呼叫完成")
                            || phoneStatus.equals("取消呼叫") || phoneStatus.equals("已屏蔽")) {
                        clue.setJoinedTaskPhoneId(null);
                        clue.setJoinedTaskId(null);
                        clue.setJoinedTaskName(null);
                        clue.setJoinedTaskStatus(null);
                        clue.setCanBeSend(true);
                        clueSaveList.add(clue);
                    }
                }
            }
            clueRepository.saveAll(clueSaveList);
            List<Clue> clueCollect = clues.stream().filter(Clue::isCanBeSend).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(clueCollect)) {
                List<AutoClueRuleExecuteLog> autoClueRuleExecuteLogs = new ArrayList<>();
                for (Long callTeamId : callTeamIds) {
                    AutoClueRuleExecuteLog autoClueRuleExecuteLog = getAutoClueRuleExecuteLog(groupId, ruleId, callTeamId, 0, LocalDateTime.now());
                    autoClueRuleExecuteLogs.add(autoClueRuleExecuteLog);
                }
                autoClueRuleExecuteLogRepository.saveAll(autoClueRuleExecuteLogs);
                continue;
            }

            List<AutoClueRuleExecuteLog> executeLogs = new ArrayList<>();
            Map<Long, List<Clue>> longListMap = new HashMap<>();
            if ("0".equals(allocationType)) {
                // 平均分配到每个坐席
                Map<Long, Integer> seatCountMap = new HashMap<>();
                for (Long callTeamId : callTeamIds) {
                    seatCountMap.put(callTeamId, 1);
                }
                longListMap = allocatedAccordSeatNumber(clueCollect, callTeamIds, seatCountMap);
            } else {
                Map<Long, Integer> seatCountMap = new HashMap<>();
                int totalSeats = 0;
                List<Long> hasSeatCallTeamIds = new ArrayList<>();
                for (Long callTeamId : callTeamIds) {
                    AutoDistributeCallSeatClueRule distributeCallSeatClueRuleByGroupId = autoDistributeCallSeatClueRuleRepository.findDistributeCallSeatClueRuleByGroupId(groupId, callTeamId);
                    if (null == distributeCallSeatClueRuleByGroupId || CollectionUtils.isEmpty(distributeCallSeatClueRuleByGroupId.getCallSeatIds())) {
                        AutoClueRuleExecuteLog autoClueRuleExecuteLog = getAutoClueRuleExecuteLog(groupId, ruleId, callTeamId, 0, executeTime);
                        executeLogs.add(autoClueRuleExecuteLog);
                    } else {
                        int callSeat = distributeCallSeatClueRuleByGroupId.getCallSeatIds().size();
                        totalSeats += callSeat;
                        hasSeatCallTeamIds.add(callTeamId);
                        seatCountMap.put(callTeamId, callSeat);
                    }
                }
                if (totalSeats == 0) {
                    log.info("坐席组无坐席分配");
                } else {
                    // 按坐席数量分配
                    longListMap = allocatedAccordSeatNumber(clueCollect, hasSeatCallTeamIds, seatCountMap);
                }
            }

            for (Map.Entry<Long, List<Clue>> map : longListMap.entrySet()) {
                List<Clue> clueList = map.getValue();
                Long callTeamId = map.getKey();

                int clueListSize = clueList.size();
                AutoClueRuleExecuteLog autoClueRuleExecuteLog = getAutoClueRuleExecuteLog(groupId, ruleId, callTeamId, clueListSize, executeTime);
                executeLogs.add(autoClueRuleExecuteLog);
                sendCluesToCallTeam(clueList, callTeamId, masterAccount);
            }
            // 保存执行日志
            autoClueRuleExecuteLogRepository.saveAll(executeLogs);
            autoExecuteTimePointRepository.deleteExecuteTimePointById(autoExecuteTimePoint.getId());
        }
    }

    private AutoClueRuleExecuteLog getAutoClueRuleExecuteLog(String groupId, String ruleId, Long callTeamId, int clueListSize, LocalDateTime executeTime) {
        AutoClueRuleExecuteLog autoClueRuleExecuteLog = new AutoClueRuleExecuteLog();
        autoClueRuleExecuteLog.setRuleId(ruleId);
        autoClueRuleExecuteLog.setClueNum(clueListSize);
        autoClueRuleExecuteLog.setCallTeamId(callTeamId);
        autoClueRuleExecuteLog.setRuleType(AUTO_CLUE_RULE_TYPE_CALL_TEAM);
        autoClueRuleExecuteLog.setExecuteTime(executeTime);
        autoClueRuleExecuteLog.setGroupId(groupId);
        return autoClueRuleExecuteLog;
    }

    private void recordToClue(List<AutoExecuteTimePoint> collect) {
        LocalDateTime dateStart = LocalDate.now().atStartOfDay();
        LocalDateTime dateEnd = LocalDate.now().plusDays(1).atStartOfDay();
        List<AIOutboundTask> taskList = aiOutboundTaskRepository.findTaskListByTimeAndGroupId(dateStart, dateEnd);
        taskList = taskList.stream().filter(task -> AIOutboundTaskType.AI_AUTO.equals(task.getTaskType())).collect(Collectors.toList());

        for (AutoExecuteTimePoint autoExecuteTimePoint : collect) {
            LocalDateTime executeTime = autoExecuteTimePoint.getExecuteTime();

            String ruleId = autoExecuteTimePoint.getRuleId();
            Optional<AutoTransferClueRule> byId = autoTransferClueRuleRepository.findById(Long.valueOf(ruleId));
            boolean present = byId.isPresent();
            if (!present) {
                log.info("{}转为线索定时任务规则不存在", ruleId);
                continue;
            }
            AutoTransferClueRule autoTransferClueRule = byId.get();
            if (!autoTransferClueRule.getStatus().equals("1")) {
                continue;
            }
            Integer timeGap = autoTransferClueRule.getExecuteTimeGap();
            String groupId = autoTransferClueRule.getGroupId();
            List<String> taskIdList = taskList.stream().filter(task -> groupId.equals(task.getGroupId())).map(a -> a.getId().toString()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(taskIdList)) {
                continue;
            }
            LocalDateTime endTime = executeTime.minusMinutes(5L);
            LocalDateTime startTime = endTime.minusMinutes(timeGap);
            List<String> intentionClass = autoTransferClueRule.getIntentionClass();
            List<String> excludeIntentionLabels = autoTransferClueRule.getExcludeIntentionLabels();
            List<String> containIntentionLabels = autoTransferClueRule.getContainIntentionLabels();
            String excludeEmptyLabel = autoTransferClueRule.getExcludeEmptyLabel();

            Integer callDurationSecMin = autoTransferClueRule.getMinCallDuration();
            Integer callDurationSecMax = autoTransferClueRule.getMaxCallDuration();
            Integer cycleCountMin = autoTransferClueRule.getMinCycleCount();
            Integer cycleCountMax = autoTransferClueRule.getMaxCycleCount();
            String callStatus = "7";
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            List<CallRecord> callRecordPhoneByCallOutTime = callRecordMultiService.findCallRecordPhoneByCallOutTime(startTime.format(formatter), endTime.format(formatter),
                    intentionClass,
                    CollectionUtil.isEmpty(intentionClass) ? "1" : "0",
                    callDurationSecMin, callDurationSecMax, cycleCountMin, cycleCountMax,
                    callStatus, taskIdList
            );
            boolean containIntentionLabelEmpty = CollectionUtils.isEmpty(containIntentionLabels);
            boolean excludeIntentionLabelEmpty = CollectionUtils.isEmpty(excludeIntentionLabels);
            // 排除标签不为空并且排除空标签为1则认为需要排除空标签
            boolean excludeEmptyLabelIsTrue = StringUtils.isNotEmpty(excludeEmptyLabel) && "1".equals(excludeEmptyLabel);
            List<CallRecord> result = new ArrayList<>();
            for (CallRecord callRecord : callRecordPhoneByCallOutTime) {
                if (StringUtils.isBlank(callRecord.getIntentionLabels())) {
                    if (containIntentionLabelEmpty && !excludeEmptyLabelIsTrue) {
                        result.add(callRecord);
                    }
                } else {
                    String intentionLabels = callRecord.getIntentionLabels();
                    List<String> intentionList = Arrays.asList(intentionLabels.split(","));
                    // (排除标签为空 或者 排除标签中不存在当前记录的标签） 并且 （包含标签为空 或者 包含标签中存在当前记录的标签）
                    boolean check = (excludeIntentionLabelEmpty || intentionList.stream().noneMatch(excludeIntentionLabels::contains)) &&
                            (containIntentionLabelEmpty || intentionList.stream().anyMatch(containIntentionLabels::contains));
                    if (check) {
                        result.add(callRecord);
                    }
                }


            }
            int clueSize = 0;
            if (CollectionUtil.isNotEmpty(result)) {
                clueSize = handBatchCallRecords(result, groupId);
            }
            // 保存定时任务的操作日志
            AutoClueRuleExecuteLog autoClueRuleExecuteLog = new AutoClueRuleExecuteLog();
            autoClueRuleExecuteLog.setRuleId(ruleId);
            autoClueRuleExecuteLog.setClueNum(clueSize);
            autoClueRuleExecuteLog.setRuleType(AUTO_CLUE_RULE_TYPE_TRANSFER);
            autoClueRuleExecuteLog.setExecuteTime(LocalDateTime.now());
            autoClueRuleExecuteLog.setGroupId(groupId);
            autoClueRuleExecuteLogRepository.save(autoClueRuleExecuteLog);

            autoExecuteTimePointRepository.deleteExecuteTimePointById(autoExecuteTimePoint.getId());
        }

    }

    public void createClueAutoExecuteTimePointTask() {
        // 删除七天前的
        LocalDate now = LocalDate.now();
        LocalDateTime sevenDay = now.minusDays(7L).atStartOfDay();
        autoClueRuleExecuteLogRepository.deleteByTime(sevenDay);

        String nowDay = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        autoTransferClueRuleRepository.updateClueStatusByStopTime("0", nowDay);
        // 清除执行的时刻
        autoExecuteTimePointRepository.deleteAll();
        List<AutoTransferClueRule> autoTransferClueRuleList = autoTransferClueRuleRepository.findEnableRule("1");
        for (AutoTransferClueRule autoTransferClueRule : autoTransferClueRuleList) {
            List<String> startWorkTimes = Arrays.asList(autoTransferClueRule.getStartWorkTimes().split(","));
            List<String> endWorkTimes = Arrays.asList(autoTransferClueRule.getEndWorkTimes().split(","));
            Integer timeGap = autoTransferClueRule.getExecuteTimeGap();
            createExecuteTime(timeGap, startWorkTimes, endWorkTimes, autoTransferClueRule.getId().toString(), AUTO_CLUE_RULE_TYPE_TRANSFER, autoTransferClueRule.getGroupId());
        }

        autoDistributeCallTeamClueRuleRepository.updateClueStatusByStopTime("0", nowDay);
        List<AutoDistributeCallTeamClueRule> enableCallTeamRuleList = autoDistributeCallTeamClueRuleRepository.findEnableRule("1");
        for (AutoDistributeCallTeamClueRule autoDistributeCallTeamClueRule : enableCallTeamRuleList) {
            List<String> startWorkTimes = Arrays.asList(autoDistributeCallTeamClueRule.getStartWorkTimes().split(","));
            List<String> endWorkTimes = Arrays.asList(autoDistributeCallTeamClueRule.getEndWorkTimes().split(","));
            Integer timeGap = autoDistributeCallTeamClueRule.getExecuteTimeGap();
            createExecuteTime(timeGap, startWorkTimes, endWorkTimes, autoDistributeCallTeamClueRule.getId().toString(), AUTO_CLUE_RULE_TYPE_CALL_TEAM, autoDistributeCallTeamClueRule.getGroupId());
        }

        autoDistributeCallSeatClueRuleRepository.updateClueStatusByStopTime("0", nowDay);
        List<AutoDistributeCallSeatClueRule> enableCallSeatRuleList = autoDistributeCallSeatClueRuleRepository.findEnableRule("1");
        for (AutoDistributeCallSeatClueRule autoDistributeCallSeatClueRule : enableCallSeatRuleList) {
            List<String> startWorkTimes = Arrays.asList(autoDistributeCallSeatClueRule.getStartWorkTimes().split(","));
            List<String> endWorkTimes = Arrays.asList(autoDistributeCallSeatClueRule.getEndWorkTimes().split(","));
            Integer timeGap = autoDistributeCallSeatClueRule.getExecuteTimeGap();
            createExecuteTime(timeGap, startWorkTimes, endWorkTimes, autoDistributeCallSeatClueRule.getId().toString(), AUTO_CLUE_RULE_TYPE_CALL_SEAT, autoDistributeCallSeatClueRule.getGroupId());
        }

        autoArchiveClueRuleRepository.updateClueStatusByStopTime("0", nowDay);
        List<AutoArchiveClueRule> enableAutoArchiveClueRuleList = autoArchiveClueRuleRepository.findEnableRule("1");
        for (AutoArchiveClueRule autoDistributeCallSeatClueRule : enableAutoArchiveClueRuleList) {
            List<String> workTimes = Arrays.asList(autoDistributeCallSeatClueRule.getWorkTimes().split(","));
            createExecuteTime(workTimes, autoDistributeCallSeatClueRule.getId().toString(), AUTO_CLUE_RULE_TYPE_ARCHIVE, autoDistributeCallSeatClueRule.getGroupId());
        }

    }


    public AutoArchiveClueRule addAutoArchiveClueRule(AutoArchiveClueRule autoTransferClueRuleDTO) {
        AutoArchiveClueRule autoTransferClueRule = new AutoArchiveClueRule();
        BeanUtils.copyProperties(autoTransferClueRuleDTO, autoTransferClueRule);
        String groupId = AIContext.getGroupId();
        autoTransferClueRule.setGroupId(groupId);
        autoTransferClueRule.setStatus("0");
        return autoArchiveClueRuleRepository.save(autoTransferClueRule);
    }


    public AutoArchiveClueRule editAutoArchiveClueRule(AutoArchiveClueRule autoTransferClueRuleDTO) {
        Optional<AutoArchiveClueRule> byId = autoArchiveClueRuleRepository.findById(autoTransferClueRuleDTO.getId());
        if (!byId.isPresent()) {
            throw new ClueAutoException("不存在该规则");
        }
        AutoArchiveClueRule autoTransferClueRule = byId.get();
        String groupId = AIContext.getGroupId();
        if (!autoTransferClueRule.getGroupId().equals(groupId)) {
            throw new ClueAutoException("账号下不存在该规则");
        }
        if ("1".equals(autoTransferClueRule.getStatus())) {
            throw new ClueAutoException("该规则开启中无法编辑");
        }
        return autoArchiveClueRuleRepository.save(autoTransferClueRuleDTO);
    }

    public void deleteAutoArchiveClueRuleById(Long id) {
        autoArchiveClueRuleRepository.deleteById(id);
        // 删除执行时刻数据
        autoExecuteTimePointRepository.deleteByRuleIdAndRuleType(id.toString(), AUTO_CLUE_RULE_TYPE_ARCHIVE);
    }


    public AutoArchiveClueRule findAutoArchiveClueRule() {
        String groupId = AIContext.getGroupId();
        return autoArchiveClueRuleRepository.findAutoTransferClueRuleByGroupId(groupId);
    }

    public void editAutoArchiveClueRuleStatus(String status, Long id, String autoStop, String autoStopTime) {
        Optional<AutoArchiveClueRule> byId = autoArchiveClueRuleRepository.findById(id);
        if (!byId.isPresent()) {
            throw new ClueAutoException("不存在该规则");
        }
        autoArchiveClueRuleRepository.updateStatus(status, autoStop, autoStopTime, id);
        // 删除之前定义的执行时刻,重新生成新的执行时刻
        autoExecuteTimePointRepository.deleteByRuleIdAndRuleType(id.toString(), AUTO_CLUE_RULE_TYPE_ARCHIVE);
        if (!status.equals("1")) {
            return;
        }
        AutoArchiveClueRule archiveClueRule = byId.get();
        String groupId = AIContext.getGroupId();
        List<String> workTimes = Arrays.asList(archiveClueRule.getWorkTimes().split(","));
        createExecuteTimeWithoutNowTime(workTimes, id.toString(), AUTO_CLUE_RULE_TYPE_ARCHIVE, groupId);
    }

    private void createExecuteTimeWithoutNowTime(List<String> workTimes, String ruleId, String ruleType, String groupId) {
        LocalDateTime now = LocalDateTime.now();
        List<AutoExecuteTimePoint> autoExecuteTimePointList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(workTimes)) {
            String prefix = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " ";
            for (String workTime : workTimes) {
                LocalDateTime executeTime = LocalDateTime.parse(prefix + workTime + ":00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                if (executeTime.isBefore(now)) {
                    continue;
                }
                AutoExecuteTimePoint autoExecuteTimePoint = new AutoExecuteTimePoint();
                autoExecuteTimePoint.setExecuteTime(executeTime);
                autoExecuteTimePoint.setRuleId(ruleId);
                autoExecuteTimePoint.setRuleType(ruleType);
                autoExecuteTimePoint.setGroupId(groupId);
                autoExecuteTimePointList.add(autoExecuteTimePoint);
            }
        }
        autoExecuteTimePointRepository.saveAll(autoExecuteTimePointList);
    }

    public void reloadCallSeatClueIdTask(List<String> groupIdList) {
        for (String groupId : groupIdList) {
            List<CallSeat> all = callSeatRepository.findAllByGroupId(groupId);
            for (CallSeat callSeat : all) {
                List<Clue> clues = clueRepository.findAllByCallSeatId(callSeat.getId());
                List<Long> clueIdLIst = clues.stream().filter(clue -> BE_DISTRIBUTED.equals(clue.getClueStatus())).map(Clue::getId).collect(Collectors.toList());
                callSeat.setClueIds(clueIdLIst);
                callSeatRepository.save(callSeat);
            }
        }
    }

}
