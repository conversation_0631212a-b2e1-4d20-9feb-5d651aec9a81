package com.raipeng.aispeech.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 宽带运营商类型枚举
 * 
 * <AUTHOR>
 * @description 定义宽带运营商类型，支持中文显示和英文存储
 * @date 2025-01-04
 */
public enum BroadbandOperatorType {

    /**
     * 中国移动
     */
    CMCC("cmcc", "移动"),
    
    /**
     * 中国电信
     */
    CTCC("ctcc", "电信"),
    
    /**
     * 中国联通
     */
    CUCC("cucc", "联通"),

    /**
     * 其他
     */
    OTHER("other", "其他"),

    ;

    /**
     * 英文代码（用于数据库存储）
     */
    private final String code;
    
    /**
     * 中文名称（用于前端显示）
     */
    private final String name;

    /**
     * 构造函数
     * 
     * @param code 英文代码
     * @param name 中文名称
     */
    BroadbandOperatorType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 获取英文代码（用于数据库存储）
     * 
     * @return 英文代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取中文名称（用于前端显示）
     * 
     * @return 中文名称
     */
    @JsonValue
    public String getName() {
        return name;
    }

    /**
     * 根据中文名称获取枚举对象
     * 
     * @param name 中文名称
     * @return 对应的枚举对象
     * @throws IllegalArgumentException 如果找不到对应的枚举
     */
    @JsonCreator
    public static BroadbandOperatorType fromName(String name) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("宽带运营商名称不能为空");
        }
        
        String trimmedName = name.trim();
        for (BroadbandOperatorType type : values()) {
            if (type.getName().equals(trimmedName)) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的宽带运营商名称: " + name);
    }

    /**
     * 根据英文代码获取枚举对象
     * 
     * @param code 英文代码
     * @return 对应的枚举对象
     * @throws IllegalArgumentException 如果找不到对应的枚举
     */
    public static BroadbandOperatorType fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            throw new IllegalArgumentException("宽带运营商代码不能为空");
        }
        
        String trimmedCode = code.trim().toLowerCase();
        for (BroadbandOperatorType type : values()) {
            if (type.getCode().equals(trimmedCode)) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的宽带运营商代码: " + code);
    }

    /**
     * 检查给定的名称是否为有效的宽带运营商名称
     * 
     * @param name 中文名称
     * @return 如果有效返回true，否则返回false
     */
    public static boolean isValidName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return false;
        }
        
        String trimmedName = name.trim();
        for (BroadbandOperatorType type : values()) {
            if (type.getName().equals(trimmedName)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查给定的代码是否为有效的宽带运营商代码
     * 
     * @param code 英文代码
     * @return 如果有效返回true，否则返回false
     */
    public static boolean isValidCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return false;
        }
        
        String trimmedCode = code.trim().toLowerCase();
        for (BroadbandOperatorType type : values()) {
            if (type.getCode().equals(trimmedCode)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public String toString() {
        return name;
    }
}
