package com.raipeng.aispeech.schedule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.raipeng.aispeech.model.AIOutboundTask;
import com.raipeng.aispeech.model.dto.AIOutboundQueryDto;
import com.raipeng.aispeech.repository.AIOutboundTaskRepository;
import com.raipeng.aispeech.repository.AiOutboundTaskWriteRepository;
import com.raipeng.aispeech.service.AIOutboundTaskService;
import com.raipeng.common.util.JpaResultUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.persistence.Tuple;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
@RefreshScope
public class StopLastDayTask {
    @Autowired
    private AIOutboundTaskService aiOutboundTaskService;

    @Autowired
    private AIOutboundTaskRepository aiOutboundTaskRepository;

    @Autowired
    private AiOutboundTaskWriteRepository aiOutboundTaskWriteRepository;

    @XxlJob("stopLastDayTask")
    public ReturnT<String> execute(String s) {
        XxlJobLogger.log("XXL-JOB, stopLastDayTask.param=" + s);
        Map<String, String> sMap = JSON.parseObject(s, new TypeReference<Map<String, String>>(){});
        try {
            LocalDateTime startTime = LocalDateTime.now().toLocalDate().atStartOfDay();
            LocalDateTime endTime = LocalDateTime.now().plusDays(1L).toLocalDate().atStartOfDay();
            List<Tuple> tuples = aiOutboundTaskRepository.findAllByTime(startTime, endTime);
            List<AIOutboundTask> aiOutboundTaskList = JpaResultUtils.processResult(tuples, AIOutboundTask.class);
            for(AIOutboundTask aiOutboundTask : aiOutboundTaskList){
                AIOutboundQueryDto aiOutboundQueryDto = new AIOutboundQueryDto();
                aiOutboundQueryDto.setTaskId(aiOutboundTask.getId());
                aiOutboundTaskService.stopTask(aiOutboundQueryDto, true);
            }
        } catch (Exception e) {
            XxlJobLogger.log("XXL-JOB, stopLastDayTask.Error=" + e);
            log.error("stopLastDayTask Error:", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }



    @XxlJob("stopHistoryTask")
    public ReturnT<String> stopHistoryTask(String s) {
        XxlJobLogger.log("XXL-JOB, stopHistoryTask.param=" + s);
        try {
            LocalDateTime endTime = LocalDateTime.now().toLocalDate().atStartOfDay();
            aiOutboundTaskWriteRepository.updateHistoryTaskStatus(endTime);
        } catch (Exception e) {
            XxlJobLogger.log("XXL-JOB, stopHistoryTask.Error=" + e);
            log.error("stopHistoryTask Error:", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob("stopDailyTask")
    public ReturnT<String> stopDailyTask(String s) {
        XxlJobLogger.log("XXL-JOB, stopDailyTask.param=" + s);
        try {
            LocalDateTime startTime = LocalDateTime.now().minusDays(1L).toLocalDate().atStartOfDay();
            LocalDateTime endTime = LocalDateTime.now().toLocalDate().atStartOfDay();
            if (s != null && !s.isEmpty()) {
                DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                Map<String, String> sMap = JSON.parseObject(s, new TypeReference<Map<String, String>>() {
                });
                String startTimeString = sMap.get("startDate");
                String endTimeString = sMap.get("endDate");
                if (startTimeString == null || endTimeString == null) {
                    return ReturnT.FAIL;
                }
                LocalDate startDate = LocalDate.parse(startTimeString, dateFormatter);
                LocalDate endDate = LocalDate.parse(endTimeString, dateFormatter);
                startTime = startDate.atStartOfDay();
                endTime = endDate.atStartOfDay();
            }
            log.info("stopDailyTask 开始时间 {} 结束时间 {} ", startTime, endTime);
            XxlJobLogger.log("stopDailyTask 开始时间 {} 结束时间 {} ", startTime, endTime);
            List<AIOutboundTask> processingTaskList = aiOutboundTaskRepository.findProcessingTaskListByTime(startTime, endTime);
            for (AIOutboundTask aiOutboundTask : processingTaskList) {
                if (aiOutboundTask.getNextDayCall() != null && aiOutboundTask.getNextDayCall() == 1) {
                    continue;
                }
                aiOutboundTask.setCallStatus("未完成");
                log.info("stopDailyTask 任务ID {} 任务名称 {} 类型 {} groupID {}", aiOutboundTask.getId(), aiOutboundTask.getTaskName(), aiOutboundTask.getTaskType(), aiOutboundTask.getGroupId());
                XxlJobLogger.log("stopDailyTask 任务ID {} 任务名称 {} 类型 {} groupID {}", aiOutboundTask.getId(), aiOutboundTask.getTaskName(), aiOutboundTask.getTaskType(), aiOutboundTask.getGroupId());
                aiOutboundTaskWriteRepository.save(aiOutboundTask);

            }
        } catch (Exception e) {
            XxlJobLogger.log("XXL-JOB, stopDailyTask.Error=" + e);
            log.error("stopDailyTask Error:", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}
