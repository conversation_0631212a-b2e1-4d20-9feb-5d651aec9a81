package com.raipeng.aispeech.schedule;

import com.raipeng.aispeech.model.AIOutboundTask;
import com.raipeng.aispeech.repository.AIOutboundManualTaskRepository;
import com.raipeng.aispeech.service.AIOutboundManualTaskService;
import com.raipeng.aispeech.service.AiTaskWriteService;
import com.raipeng.common.util.StringUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@Component
@Slf4j
@RefreshScope
public class AiManualPrincipleConcurrencyTask {
    @Autowired
    private AIOutboundManualTaskRepository aiOutboundManualTaskRepository;

    @Autowired
    private AiTaskWriteService aiTaskWriteService;

    @Autowired
    private AIOutboundManualTaskService aiOutboundManualTaskService;

    @XxlJob("aiManualPrincipleConcurrencyTask")
    public ReturnT<String> execute(String s) {
        XxlJobLogger.log("XXL-JOB, aiManualPrincipleConcurrencyBatchTask.param=" + s);
        LocalDate currentDate = LocalDate.now();
        LocalDateTime startOfDay = currentDate.atStartOfDay();
        LocalDateTime endOfDay = currentDate.atTime(LocalTime.MAX);


        try {
            XxlJobLogger.log("start to update PrincipleConcurrency for AI_MANUAL task ");
            long startTime = System.currentTimeMillis();
            List<AIOutboundTask> byId = aiOutboundManualTaskRepository.findAllByTaskTypeAndCallStatus(startOfDay,endOfDay);
            XxlJobLogger.log("Read task:  "+ (System.currentTimeMillis() - startTime) + " ms" + " task size:" + byId.size());

            if (byId == null || byId.isEmpty()) {
                XxlJobLogger.log("No AI Manual task founded.");
                return ReturnT.SUCCESS;
            }
            aiOutboundManualTaskService.findPrincipleConcurrencyBatchBackUp(byId);

        } catch (Exception e) {
            XxlJobLogger.log("XXL-JOB, aiManualPrincipleConcurrencyBatchTask Error=" + e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}
