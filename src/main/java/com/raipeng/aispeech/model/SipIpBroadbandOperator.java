package com.raipeng.aispeech.model;

import com.raipeng.common.model.BaseEntity;
import lombok.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * 线路运营商数据实体类
 */
@Entity
@Table(name = "sip_ip_broadband_operator", indexes = {
    @Index(name = "idx_line_number", columnList = "line_number"),
    @Index(name = "idx_ip_address", columnList = "ip_address"),
    @Index(name = "idx_ip_query_time", columnList = "ip_query_time")
})
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class SipIpBroadbandOperator extends BaseEntity {
    
    /**
     * 线路号码
     */
    @Column(name = "line_number", nullable = false, unique = true, columnDefinition = "varchar(20)")
    private String lineNumber;
    
    /**
     * IP地址
     */
    @Column(name = "ip_address", nullable = true, columnDefinition = "varchar(20)")
    private String ipAddress;


    /**
     * ip查询宽带商，用于运营维护的broadband_operator对比，并告警
     */
    @Column(name = "operator", nullable = true, columnDefinition = "varchar(20)")
    private String operator;

    /**
     * 宽带网络商
     */
    @Column(name = "broadband_operator", nullable = true, columnDefinition = "varchar(20)")
    private String broadbandOperator;
    
    /**
     * 备注信息
     */
    @Column(name = "remark", nullable = true, columnDefinition = "varchar(300)")
    private String remark;
    
    /**
     * ISP信息（从IP接口返回的原始信息）
     */
    @Column(name = "isp_info", nullable = true, columnDefinition = "varchar(100)")
    private String ispInfo;
    
    /**
     * 是否已查询IP接口
     */
    @Column(name = "ip_queried", nullable = false, columnDefinition = "boolean default false")
    private Boolean ipQueried = false;
    
    /**
     * IP查询时间
     */
    @Column(name = "ip_query_time", nullable = true)
    private LocalDateTime ipQueryTime;
}
