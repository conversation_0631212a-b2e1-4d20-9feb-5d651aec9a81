package com.raipeng.aispeech.model;

import com.raipeng.aispeech.entity.HangUpSmsTriggerPojo;
import com.raipeng.aispeech.entity.ScriptSmsTriggerPojo;
import com.raipeng.aispeech.entity.VariableSmsPojo;
import com.raipeng.common.enums.AIManualCallTeamHandleType;
import com.raipeng.common.enums.AIManualCallTeamPushType;
import com.raipeng.common.enums.AIOutboundTaskType;
import com.raipeng.common.model.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "t_ai_outbound_task_permanent")
@Data
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class AIOutboundTaskPermanent extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 202504030000L;
    @Id
    private Long id;
    @Column(
            updatable = false
    )
    @CreationTimestamp
    private LocalDateTime createTime;
    @UpdateTimestamp
    private LocalDateTime updateTime;
    public static final int COMMON_DISABLE = 0;
    public static final int COMMON_ENABLE = 1;

    @Column(name = "group_id", nullable = true, columnDefinition = "varchar(20)")
    private String groupId;

    @Column(name = "taskName", nullable = true, columnDefinition = "varchar(100) NOT NULL")
    private String taskName;

    @Column(name = "phone_num", nullable = false, columnDefinition = "int DEFAULT 0 ")
    private Integer phoneNum = 0;

    @Column(name = "tenant_name", columnDefinition = " varchar(100) DEFAULT NULL ")
    private String tenantName;

    @Column(name = "called_phone_num", nullable = false, columnDefinition = "int DEFAULT 0 ")
    private Integer calledPhoneNum = 0;

    @Column(name = "calling_phone_num", nullable = false, columnDefinition = "int DEFAULT 0 ")
    private Integer callingPhoneNum = 0;

    @Column(name = "recalling_phone_num", nullable = false, columnDefinition = "int DEFAULT 0 ")
    private Integer recallingPhoneNum = 0;

    @Column(name = "finished_phone_num", nullable = false, columnDefinition = "int DEFAULT 0 ")
    private Integer finishedPhoneNum = 0;

    @Column(name = "put_through_phone_num", nullable = false, columnDefinition = "int DEFAULT 0 ")
    private Integer putThroughPhoneNum = 0;

    @Column(name = "call_cycle", nullable = false, columnDefinition = "int DEFAULT 0 ")
    private Integer callCycle = 0;

    //触达率
    @Column(name = "calledPhoneRate", nullable = true, columnDefinition = "int DEFAULT 0 ")
    private BigDecimal calledPhoneRate;

    //完成率
    @Column(name = "finishedPhoneRate", nullable = true, columnDefinition = "int DEFAULT 0 ")
    private BigDecimal finishedPhoneRate;

    //接通率
    @Column(name = "put_through_phone_rate", nullable = true, columnDefinition = "int DEFAULT 0 ")
    private BigDecimal putThroughPhoneRate;



    @Column(name = "aiAnswerNum", nullable = true,  columnDefinition = "int DEFAULT 1 ")
    private Integer aiAnswerNum;

    @Column(name = "if_send_sms", nullable = false, columnDefinition = "varchar(10) default '否'")
    private String ifSendSms = "否";

    //未完成，进行中，已停止，待执行
    @Column(name = "callStatus", nullable = true,  columnDefinition = "varchar(50)")
    private String callStatus;

    @Column(name = "phone_op_percent", nullable = true,  columnDefinition = "varchar(50)")
    private String phoneOpPercent;

    @Column(name = "batch_status", nullable = true,  columnDefinition = "varchar(50)")
    private String batchStatus;


    @Column(name = "speechCraftName", nullable = true,  columnDefinition = "varchar(100)")
    private String speechCraftName;

    @Column(name = "speechCraftId", nullable = true,  columnDefinition = "int")
    private Long speechCraftId;

    @Column(name = "script_string_id", nullable = true, columnDefinition = "varchar(40)")
    private String scriptStringId;

    @Column(name = "version", nullable = true, columnDefinition = "int")
    private Integer version;

    @Column(name = "lineName", nullable = true,  columnDefinition = "varchar(100)")
    private String lineName;

    @Column(name = "lineCode", nullable = true,  columnDefinition = "varchar(100)")
    private String lineCode;

    @Column(name = "lineId", nullable = true,  columnDefinition = "int")
    private Long lineId;

    @Column(name = "startWorkTimes", nullable = true,  columnDefinition = "varchar(500)")
    private String startWorkTimes;

    @Column(name = "endWorkTimes", nullable = true,  columnDefinition = "varchar(500)")
    private String endWorkTimes;

    @Column(name = "autoReCall", nullable = true,  columnDefinition = "int DEFAULT 1")
    private Integer autoReCall;

    //1-首呼优先分配， 2-多轮次呼叫按比例分配
    @Column(name = "call_ratio_type", nullable = true,  columnDefinition = "int")
    private Integer callRatioType;

    @Column(name = "first_recall_time", nullable = true,  columnDefinition = "int")
    private Integer firstRecallTime;

    @Column(name = "second_recall_time", nullable = true,  columnDefinition = "int")
    private Integer secondRecallTime;

    @Column(name = "all_restrict_province", nullable = true,  columnDefinition = "text")
    private String allRestrictProvince;

    @Column(name = "all_restrict_city", nullable = true,  columnDefinition = "text")
    private String allRestrictCity;

    @Column(name = "yd_restrict_province", nullable = true,  columnDefinition = "text")
    private String ydRestrictProvince;

    @Column(name = "yd_restrict_city", nullable = true,  columnDefinition = "text")
    private String ydRestrictCity;

    @Column(name = "lt_restrict_province", nullable = true,  columnDefinition = "text")
    private String ltRestrictProvince;

    @Column(name = "lt_restrict_city", nullable = true,  columnDefinition = "text")
    private String ltRestrictCity;

    @Column(name = "dx_restrict_city", nullable = true,  columnDefinition = "text")
    private String dxRestrictCity;

    @Column(name = "dx_restrict_province", nullable = true,  columnDefinition = "text")
    private String dxRestrictProvince;

    @Column(name = "virtual_restrict_city", nullable = true,  columnDefinition = "text")
    private String virtualRestrictCity;

    @Column(name = "virtual_restrict_province", nullable = true,  columnDefinition = "text")
    private String virtualRestrictProvince;

    @Column(name = "unknown_restrict_city", nullable = true,  columnDefinition = "text")
    private String unknownRestrictCity;

    @Column(name = "unknown_restrict_province", nullable = true,  columnDefinition = "text")
    private String unknownRestrictProvince;

    @Column(name = "fee_minute", nullable = true,  columnDefinition = "int DEFAULT 0")
    private Integer feeMinute;

    @Column(name = "task_start_time", nullable = true,  columnDefinition = "varchar(50)")
    private String taskStartTime;

    @Column(name = "task_end_time", nullable = true,  columnDefinition = "varchar(50)")
    private String taskEndTime;

    //是否自动止损
    @Column(name = "is_auto_stop", nullable = true,  columnDefinition = "int DEFAULT 0")
    private Integer isAutoStop;

    @Column(name = "call_record_num", nullable = true,  columnDefinition = "int DEFAULT 0")
    private Integer callRecordNum;

    @Column(name = "phone_intention_num", nullable = true,  columnDefinition = "int DEFAULT 0")
    private Integer phoneIntentionNum;

    @Type(type = "jsonb")
    @Column(name = "call_team_ids", columnDefinition = "jsonb")
    private List<Long> callTeamIds;

    @Enumerated(EnumType.STRING)
    @Column(name = "task_type", nullable = true, columnDefinition = "varchar(100)")
    private AIOutboundTaskType taskType;

    @Enumerated(EnumType.STRING)
    @Column(name = "call_team_push_type", nullable = true,  columnDefinition = "varchar(100)")
    private AIManualCallTeamPushType callTeamPushType;

    @Enumerated(EnumType.STRING)
    @Column(name = "call_team_handle_type", nullable = true,  columnDefinition = "varchar(100)")
    private AIManualCallTeamHandleType callTeamHandleType;

    @Column(name = "line_ratio", nullable = true,  columnDefinition = "decimal(20,2)")
    private Double lineRatio;

    @Column(name = "occupy_rate", nullable = true,  columnDefinition = "int")
    private Integer occupyRate;

    @Type(type = "jsonb")
    @Column(name = "task_clue_ids", columnDefinition = "jsonb")
    private List<Long> taskClueIds;

    @Column(name = "if_lock", nullable = true, columnDefinition = "int DEFAULT 0 ")
    private Integer ifLock;

    @Column(name = "virtual_seat_ratio", nullable = true,  columnDefinition = "decimal(20,2)")
    private Double virtualSeatRatio;

    @Column(name = "program_id", nullable = true, columnDefinition = "varchar(100)")
    private String programId;

    @Column(name = "product_id", nullable = true, columnDefinition = "varchar(100)")
    private String productId;

    @Column(name = "industry_second_field_id", nullable = true, columnDefinition = "varchar(100)")
    private String industrySecondFieldId;

    //预计结束时间
    @Column(name = "expected_finish_time", nullable = true, columnDefinition = "varchar(30)")
    private String expectedFinishTime;

    //话术短信触发
    @Type(type = "jsonb")
    @Column(name = "script_sms", nullable = true, columnDefinition = "jsonb")
    private List<ScriptSmsTriggerPojo> scriptSms;

    //挂机短信触发信息
    @Type(type = "jsonb")
    @Column(name = "hang_up_sms", nullable = true, columnDefinition = "jsonb")
    private List<HangUpSmsTriggerPojo> hangUpSms;

    // 挂机排除短信触发
    @Type(type = "jsonb")
    @Column(name = "hang_up_excluded", nullable = true, columnDefinition = "jsonb")
    private List<String> hangUpExcluded;

    //短信变量
    @Type(type = "jsonb")
    @Column(name = "variable_sms", nullable = true, columnDefinition = "jsonb")
    private List<VariableSmsPojo> variableSms;

    //短信模板状态
    @Column(name = "sms_template_abnormal", nullable = true,  columnDefinition = "int")
    private Integer smsTemplateAbnormal;
    //是否续呼
    @Column(name = "next_day_call", nullable = true,  columnDefinition = "int")
    private Integer nextDayCall;

    @Column(name = "task_id", nullable = false, columnDefinition = "bigint")
    private Long taskId;

    @Column(name = "template_id", nullable = true, columnDefinition = "varchar(100)")
    private String templateId;

    @Type(type = "jsonb")
    @Column(name = "tenant_black_list", nullable = true, columnDefinition = "jsonb")
    private List<Long> tenantBlackList;

    //a类等级数量
    @Column(name = "a_class_num", nullable = true, columnDefinition = "int DEFAULT 0")
    private Integer aClassNum;

}
