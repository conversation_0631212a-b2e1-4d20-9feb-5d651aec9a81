package com.raipeng.aispeech.model.script;

import com.raipeng.aispeech.enums.AsrStatus;
import com.raipeng.common.enums.ContentType;
import com.raipeng.common.enums.CorpusReturnType;
import com.raipeng.common.enums.InterruptType;
import com.raipeng.common.enums.ScriptCorpusTypeEnum;
import com.raipeng.common.model.BaseEntity;
import lombok.*;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.util.List;

@Entity
@Table(name = "script_unit_content")
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class ScriptUnitContent extends BaseEntity {
    /**
     * 语句内容
     */
    @Column(name = "content", columnDefinition = "varchar(500)")
    private String content;

    /**
     * 是否播放,当语句被修改后前端会置false，需要重新验听
     */
    @Column(name = "is_played", columnDefinition = "bool")
    private Boolean isPlayed;

    /**
     * 语料内容id
     */
    @Column(name = "multi_content_id", columnDefinition = "bigint")
    private Long multiContentId;

    /**
     * 排序
     */
    @Column(name = "orders", columnDefinition = "int")
    private int orders;

    /**
     * 语句名称
     */
    @Column(name = "content_name", columnDefinition = "varchar(200)")
    private String contentName;

    /**
     * 话术id
     */
    @Column(name = "script_id", columnDefinition = "bigint")
    private Long scriptId;

    /**
     * 语料id
     */
    @Column(name = "corpus_id", columnDefinition = "bigint")
    private Long corpusId;

    /**
     * 音频路径
     */
    @Column(name = "audio_path", columnDefinition = "varchar(500)")
    private String audioPath;

    /**
     * 上传状态
     */
    @Column(name = "upload_status", columnDefinition = "varchar(10)")
    private String uploadStatus;

    /**
     * 修改状态
     */
    @Column(name = "update_status", nullable = true, columnDefinition = "varchar(40)")
    private String updateStatus;

    /**
     * 上传时间
     */
    @Column(name = "upload_time", columnDefinition = "varchar(50)")
    private String uploadTime;

    /**
     * 删除标志位
     */
    @Column(name = "is_deleted", columnDefinition = "bool default false")
    private boolean isDeleted;

    /**
     * 语料类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "corpus_type", nullable = false, columnDefinition = "varchar(30)")
    private ScriptCorpusTypeEnum corpusType;

    /**
     * 文本类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "content_type", nullable = true, columnDefinition = "varchar(30)")
    private ContentType contentType;

    /**
     * 音频识别文本
     */
    @Column(name = "asr_txt", nullable = true, columnDefinition = "varchar(1000)")
    private String asrTxt;

    /**
     * 音频识别结果
     */
    @Column(name = "asr_result", nullable = true, columnDefinition = "decimal(20,1)")
    private Double asrResult;

    /**
     * 处理状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "asr_status", nullable = true, columnDefinition = "varchar(30)")
    private AsrStatus asrStatus;

    /**
     * 打断类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "interrupt_type", columnDefinition = "varchar(50)")
    private InterruptType interruptType;

    /**
     * 允许打断时间
     */
    @Column(name = "allowed_interrupt_time", columnDefinition = "int default 0")
    private int allowedInterruptTime;

    /**
     * 打断垫句ID
     */
    @Column(name = "pre_interrupt_corpus_id", columnDefinition = "bigint")
    private Long preInterruptCorpusId;

    /**
     * 续播垫句(打断)ID
     */
    @Column(name = "pre_continue_corpus_id_for_interrupt", columnDefinition = "bigint")
    private Long preContinueCorpusIdForInterrupt;

    /**
     * 续播垫句(返回)ID
     */
    @Column(name = "pre_continue_corpus_id_for_return", columnDefinition = "bigint")
    private Long preContinueCorpusIdForReturn;

    /**
     * 续播类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "corpus_return_type", columnDefinition = "varchar(20)")
    private CorpusReturnType corpusReturnType;

    /**
     * 承接语句ID
     */
    @Column(name = "pre_undertake_corpus_id", columnDefinition = "bigint")
    private Long preUndertakeCorpusId;

    /**
     * 打断语料
     */
    @Type(type = "jsonb")
    @Column(name = "interrupt_corpus_ids_for_end", columnDefinition = "jsonb")
    private List<Long> interruptCorpusIdsForEnd;

    /**
     * 验听标记文本
     */
    @Column(name = "audio_tag", nullable = true, columnDefinition = "varchar(100)")
    private String audioTag;

    /**
     * 是否播放,0待验听，1已验听，2已标记 重新上传和修改文字内容会改为0，其他状态修改时如果存在audioTag则均视为已标记状态  当语句被修改后前端会置false，需要重新验听
     */
    @Column(name = "audio_status", columnDefinition = "varchar(1)")
    private String audioStatus;

}
