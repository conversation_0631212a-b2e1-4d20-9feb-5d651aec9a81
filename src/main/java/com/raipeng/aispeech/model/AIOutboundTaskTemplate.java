package com.raipeng.aispeech.model;

import com.raipeng.aispeech.entity.HangUpSmsTriggerPojo;
import com.raipeng.aispeech.entity.ScriptSmsTriggerPojo;
import com.raipeng.aispeech.entity.VariableSmsPojo;
import com.raipeng.common.enums.AIManualCallTeamHandleType;
import com.raipeng.common.enums.AIManualCallTeamPushType;
import com.raipeng.common.enums.AIOutboundTaskType;
import com.raipeng.common.model.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.util.List;

@Entity
@Table(name = "t_ai_outbound_task_template")
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(callSuper = true)
@NoArgsConstructor
public class AIOutboundTaskTemplate extends BaseEntity {
    @Column(name = "templateName", nullable = true, columnDefinition = "varchar(100)")
    private String templateName;

    @Column(name = "comment", nullable = true, columnDefinition = "varchar(500)")
    private String comment;

    @Column(name = "taskName", nullable = true, columnDefinition = "varchar(100)")
    private String taskName;

    @Column(name = "tenant_name", columnDefinition = " varchar(100) DEFAULT NULL ")
    private String tenantName;

    @Column(name = "speechCraftId", nullable = true,  columnDefinition = "bigint")
    private Long speechCraftId;

    @Column(name = "speechCraftName", nullable = true, columnDefinition = "varchar(100)")
    private String speechCraftName;

    @Column(name = "script_string_id", nullable = true, columnDefinition = "varchar(40)")
    private String scriptStringId;

    @Column(name = "version", nullable = true, columnDefinition = "varchar(40)")
    private Integer version;

    @Column(name = "startWorkTimes", nullable = true, columnDefinition = "varchar(500)")
    private String startWorkTimes;

    @Column(name = "endWorkTimes", nullable = true, columnDefinition = "varchar(500)")
    private String endWorkTimes;

    @Column(name = "autoReCall", nullable = true, columnDefinition = "int")
    private Integer autoReCall;

    @Column(name = "task_incr_id", nullable = true, columnDefinition = "int")
    private Integer taskIncrId;

    //1-首呼优先分配， 2-多轮次呼叫按比例分配
    @Column(name = "call_ratio_type", nullable = true,  columnDefinition = "int")
    private Integer callRatioType;

    @Column(name = "first_recall_time", nullable = true,  columnDefinition = "int")
    private Integer firstRecallTime;

    @Column(name = "second_recall_time", nullable = true,  columnDefinition = "int")
    private Integer secondRecallTime;

    @Column(name = "group_id", nullable = true, columnDefinition = "varchar(20)")
    private String groupId;

    @Enumerated(EnumType.STRING)
    @Column(name = "task_type", nullable = true, columnDefinition = "varchar(100)")
    private AIOutboundTaskType taskType;


    @Type(type = "jsonb")
    @Column(name = "call_team_ids", nullable = true,  columnDefinition = "jsonb")
    private List<Long> callTeamIds;

    @Enumerated(EnumType.STRING)
    @Column(name = "call_team_push_type", nullable = true,  columnDefinition = "varchar(100)")
    private AIManualCallTeamPushType callTeamPushType;

    @Enumerated(EnumType.STRING)
    @Column(name = "call_team_handle_type", nullable = true,  columnDefinition = "varchar(100)")
    private AIManualCallTeamHandleType callTeamHandleType;

    @Column(name = "line_ratio", nullable = true,  columnDefinition = "decimal(20,2)")
    private Double lineRatio;

    @Column(name = "occupy_rate", nullable = true,  columnDefinition = "int")
    private Integer occupyRate;

    @Column(name = "virtual_seat_ratio", nullable = true,  columnDefinition = "decimal(20,2)")
    private Double virtualSeatRatio;
    /**
     * 话术短信触发
     */
    @Type(type = "jsonb")
    @Column(name = "script_sms", nullable = true, columnDefinition = "jsonb")
    private List<ScriptSmsTriggerPojo> scriptSms;
    /**
     * 挂机短信触发信息
     */
    @Type(type = "jsonb")
    @Column(name = "hang_up_sms", nullable = true, columnDefinition = "jsonb")
    private List<HangUpSmsTriggerPojo> hangUpSms;
    /**
     * 挂机排除短信触发
     */
    @Type(type = "jsonb")
    @Column(name = "hang_up_excluded", nullable = true, columnDefinition = "jsonb")
    private List<String> hangUpExcluded;
    /**
     * 短信变量
     */
    @Type(type = "jsonb")
    @Column(name = "variable_sms", nullable = true, columnDefinition = "jsonb")
    private List<VariableSmsPojo> variableSms;

    //是否续呼
    @Column(name = "next_day_call", nullable = true,  columnDefinition = "int")
    private Integer nextDayCall;

    @Type(type = "jsonb")
    @Column(name = "tenant_black_list", nullable = true, columnDefinition = "jsonb")
    private List<Long> tenantBlackList;

    @Column(name = "all_restrict_province", nullable = true,  columnDefinition = "text")
    private String allRestrictProvince;

    @Column(name = "all_restrict_city", nullable = true,  columnDefinition = "text")
    private String allRestrictCity;

    @Column(name = "yd_restrict_province", nullable = true,  columnDefinition = "text")
    private String ydRestrictProvince;

    @Column(name = "yd_restrict_city", nullable = true,  columnDefinition = "text")
    private String ydRestrictCity;

    @Column(name = "lt_restrict_province", nullable = true,  columnDefinition = "text")
    private String ltRestrictProvince;

    @Column(name = "lt_restrict_city", nullable = true,  columnDefinition = "text")
    private String ltRestrictCity;

    @Column(name = "dx_restrict_city", nullable = true,  columnDefinition = "text")
    private String dxRestrictCity;

    @Column(name = "dx_restrict_province", nullable = true,  columnDefinition = "text")
    private String dxRestrictProvince;

    @Column(name = "unknown_restrict_city", nullable = true,  columnDefinition = "text")
    private String unknownRestrictCity;

    @Column(name = "unknown_restrict_province", nullable = true,  columnDefinition = "text")
    private String unknownRestrictProvince;

    /**
     * 状态      0或空为正常 1停用
     */
    @Column(name = "template_status", nullable = true, columnDefinition = "varchar(1) ")
    private String templateStatus;
    //a类等级数量
    @Column(name = "a_class_num", nullable = true, columnDefinition = "int DEFAULT 0")
    private Integer aClassNum;
    @Transient
    private List<String> startWorkTimeList;

    @Transient
    private List<String> endWorkTimeList;
}
