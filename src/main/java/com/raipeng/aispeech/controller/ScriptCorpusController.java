package com.raipeng.aispeech.controller;


import com.raipeng.aispeech.controller.request.AudioListRequestDTO;
import com.raipeng.aispeech.controller.request.CorpusContentDTO;
import com.raipeng.aispeech.controller.request.PriorGroupDTO;
import com.raipeng.aispeech.controller.request.scriptedit.MasterConnectCorpusDTO;
import com.raipeng.aispeech.controller.request.scriptedit.MasterOrdinaryCorpusBatchUpdateDTO;
import com.raipeng.aispeech.controller.request.scriptedit.MasterOrdinaryCorpusDTO;
import com.raipeng.aispeech.controller.response.RecordShowCorpusDTO;
import com.raipeng.aispeech.controller.response.Response;
import com.raipeng.aispeech.controller.response.ScriptAudioResponse;
import com.raipeng.aispeech.model.script.PriorGroup;
import com.raipeng.aispeech.model.script.ScriptBranch;
import com.raipeng.aispeech.model.script.ScriptCorpus;
import com.raipeng.aispeech.service.ScriptBranchService;
import com.raipeng.aispeech.service.ScriptCorpusService;
import com.raipeng.aispeech.service.ScriptEditService;
import com.raipeng.aispeech.service.ScriptPriorGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;
import java.util.Optional;



@Api(value = "/scriptCorpus", tags = {"语料接口"})
@RequestMapping("/scriptCorpus")
@RestController
public class ScriptCorpusController {
    @Autowired
    private ScriptCorpusService scriptCorpusService;

    @Autowired
    private ScriptBranchService scriptBranchService;

    @Autowired
    private ScriptEditService scriptEditService;

    @Autowired
    private ScriptPriorGroupService scriptPriorGroupService;

    @ApiOperation(value = "批量下载语料音频")
    @PostMapping("/downloadAudioFiles")
    public ResponseEntity<byte[]> downloadAudioFiles(@RequestBody List<Long> contentIds, HttpServletRequest request) {
        return scriptCorpusService.downloadAudioFiles(contentIds, request);
    }

    @ApiOperation(value = "单独更新语料的内容")
    @PostMapping("/updateOneCorpusContent")
    public Response<String> updateOneCorpusContent(@RequestBody CorpusContentDTO corpusContentDTO) {
        scriptCorpusService.updateOneCorpusContent(corpusContentDTO);
        Response<String> response = new Response<>();
        response.setResponseSuccess();
        response.setData("update success");
        return response;
    }

    @PostMapping("/saveMasterOrdinaryCorpus")
    public Response<ScriptCorpus> saveMasterOrdinaryCorpus(@RequestBody MasterOrdinaryCorpusDTO corpus) {
        ScriptCorpus responseCorpus = scriptEditService.saveMasterOrdinaryCorpus(corpus);
        Response<ScriptCorpus> response = new Response<>();
        response.setResponseSuccess();
        response.setData(responseCorpus);
        return response;
    }

    @PostMapping("/batchUpdateMasterOrdinaryCorpus")
    public Response<Integer> batchUpdateMasterOrdinaryCorpus(@RequestBody MasterOrdinaryCorpusBatchUpdateDTO corpus) {
        Integer count = scriptEditService.batchUpdateMasterOrdinaryCorpus(corpus);
        Response<Integer> response = new Response<>();
        response.setResponseSuccess();
        response.setData(count);
        return response;
    }

    @PostMapping("/saveMasterConnectCorpus")
    public Response<ScriptCorpus> saveMasterConnectCorpus(@RequestBody MasterConnectCorpusDTO corpus) {
        ScriptCorpus responseCorpus = scriptEditService.saveMasterConnectCorpus(corpus);
        Response<ScriptCorpus> response = new Response<>();
        response.setResponseSuccess();
        response.setData(responseCorpus);
        return response;
    }

    @GetMapping("/findOneMasterScriptCorpusById")
    public Response<ScriptCorpus> findOneMasterScriptCorpusById(@RequestParam("corpusId") Long id) {
        Optional<ScriptCorpus> scriptCorpusOptional = scriptCorpusService.findOneScriptCorpusById(id);
        Response<ScriptCorpus> response = new Response<>();
        response.setResponseSuccess();
        scriptCorpusOptional.ifPresent(response::setData);
        return response;
    }

    @PostMapping("/deleteOneScriptBranchById")
    public Response<Void> deleteOneScriptBranchById(@RequestParam Long id, @RequestParam Long scriptId) {
        scriptEditService.deleteOneScriptBranchById(id, scriptId);
        Response<Void> response = new Response<>();
        response.setResponseSuccess();
        return response;
    }

    @GetMapping("/findOneScriptBranchById")
    public Response<ScriptBranch> findOneScriptBranchById(Long id) {
        Optional<ScriptBranch> scriptBranchOptional = scriptBranchService.findOneScriptBranchById(id);
        Response<ScriptBranch> response = new Response<>();
        scriptBranchOptional.ifPresent(response::setData);
        response.setResponseSuccess();
        return response;
    }

    @PostMapping("/copyOneScriptCorpusByCorpusId")
    public Response<ScriptCorpus> copyOneScriptCorpusByCorpusId(@RequestParam Long scriptId, @RequestParam Long corpusId, @RequestParam boolean isHead) {
        ScriptCorpus corpus = scriptEditService.copyOneScriptCorpusByCorpusId(scriptId, corpusId, isHead);
        Response<ScriptCorpus> response = new Response<>();
        response.setData(corpus);
        response.setResponseSuccess();
        return response;
    }

    @PostMapping("/deleteOneScriptCorpusById")
    public Response<Void> deleteOneScriptCorpusById(@RequestParam Long scriptId, @RequestParam Long corpusId) {
        scriptEditService.deleteOneScriptCorpusById(scriptId, corpusId);
        Response<Void> response = new Response<>();
        response.setResponseSuccess();
        return response;
    }

    /**
     * audioList
     *
     * @return 语料的id
     */
    @PostMapping("/audioList")
    public Response<ScriptAudioResponse> audioList(@RequestBody AudioListRequestDTO dto) {
        Response<ScriptAudioResponse> response = new Response<>();
        response.setResponseSuccess();
        response.setData(scriptCorpusService.audioList(dto));
        return response;
    }

    @ApiOperation("import")
    @PostMapping("/import")
    public Response<String> importAudioFiles(@RequestPart MultipartFile file, @RequestParam Long contentId) throws IOException {
        Response<String> res = new Response<>();
        res.setResponseSuccess();
        res.setData(scriptCorpusService.importAudioFiles(file, contentId));
        return res;
    }

    @ApiOperation("importSingle")
    @PostMapping("/importSingle")
    public Response<String> importAudioFileSingle(@RequestPart MultipartFile file, @RequestParam Long contentId) throws IOException {
        Response<String> res = new Response<>();
        res.setResponseSuccess();
        res.setData(scriptCorpusService.importAudioFiles(file, contentId));
        return res;
    }

    @GetMapping("/deleteAudios")
    public Response<Void> deleteAudios(@RequestParam Long id) {
        Response<Void> response = new Response<>();
        response.setResponseSuccess();
        scriptCorpusService.deleteAudio(id);
        return response;
    }



    @ApiOperation(value = "已播放音频")
    @PostMapping("/updatePlayedAudio")
    public Response<String> updatePlayedAudio(@RequestParam Long id) {
        String audioStatus = scriptCorpusService.updatePlayedAudio(id);
        Response<String> response = new Response<>();
        response.setResponseSuccess();
        response.setData(audioStatus);
        return response;
    }

    @PostMapping("/addOrUpdatePriorGroup")
    public Response<PriorGroup> addOrUpdatePriorGroup(@RequestBody PriorGroupDTO priorGroupDTO) {
        synchronized (this) {
            PriorGroup prior = scriptPriorGroupService.addOrUpdatePriorGroup(priorGroupDTO);
            Response<PriorGroup> response = new Response<>();
            response.setData(prior);
            response.setResponseSuccess();
            return response;
        }
    }

    @DeleteMapping("/deletePriorGroupById")
    public Response<Void> deletePriorGroupById(@RequestParam Long id) {
        scriptPriorGroupService.deletePriorGroupById(id);
        Response<Void> response = new Response<>();
        response.setResponseSuccess();
        return response;
    }

    @GetMapping("/findTriggerCorpusByScriptId")
    public Response<RecordShowCorpusDTO> findTriggerCorpusByScriptId(@RequestParam Long scriptId) {
        RecordShowCorpusDTO scriptCorpusOptional = scriptCorpusService.findTriggerCorpusByScriptId(scriptId);
        Response<RecordShowCorpusDTO> response = new Response<>();
        response.setResponseSuccess();
        response.setData(scriptCorpusOptional);
        return response;
    }


}
