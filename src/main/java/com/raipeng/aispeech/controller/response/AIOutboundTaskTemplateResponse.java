package com.raipeng.aispeech.controller.response;

import com.raipeng.aispeech.entity.HangUpSmsTriggerPojo;
import com.raipeng.aispeech.entity.ScriptSmsTriggerPojo;
import com.raipeng.aispeech.entity.VariableSmsPojo;
import com.raipeng.common.enums.AIManualCallTeamHandleType;
import com.raipeng.common.enums.AIManualCallTeamPushType;
import com.raipeng.common.enums.AIOutboundTaskType;
import lombok.Data;

import java.util.List;

/**
 * 1.所属商户名、group_id 2.所属主账号 3.模板状态：启用/停用4）模板详情信息，
 * 包括：模板名称、备注说明、任务名称、任务类型（人机协同时需再包含人机协同信息）、拨打时段、自动补呼、挂机短信、黑名单、屏蔽地区
 *
 */
@Data
public class AIOutboundTaskTemplateResponse {
    private Long id;

    private String templateName;

    private String comment;

    private String taskName;

    private String tenantName;

    private Long speechCraftId;

    private String speechCraftName;

    private String scriptStringId;

    private Integer version;

    private String startWorkTimes;

    private String endWorkTimes;

    private Integer autoReCall;

    private Integer taskIncrId;

    //1-首呼优先分配， 2-多轮次呼叫按比例分配
    private Integer callRatioType;

    private Integer firstRecallTime;

    private Integer secondRecallTime;

    private String groupId;

    private AIOutboundTaskType taskType;


    private List<Long> callTeamIds;

    private AIManualCallTeamPushType callTeamPushType;

    private AIManualCallTeamHandleType callTeamHandleType;

    private Double lineRatio;

    private Integer occupyRate;

    private Double virtualSeatRatio;
    /**
     * 话术短信触发
     */
    private List<ScriptSmsTriggerPojo> scriptSms;
    /**
     * 挂机短信触发信息
     */
    private List<HangUpSmsTriggerPojo> hangUpSms;
    /**
     * 挂机排除短信触发
     */
    private List<String> hangUpExcluded;
    /**
     * 短信变量
     */
    private List<VariableSmsPojo> variableSms;

    //是否续呼
    private Integer nextDayCall;

    private List<Long> tenantBlackList;
    private String allRestrictProvince;

    private String allRestrictCity;

    private String ydRestrictProvince;

    private String ydRestrictCity;

    private String ltRestrictProvince;

    private String ltRestrictCity;

    private String dxRestrictCity;

    private String dxRestrictProvince;

    private String unknownRestrictCity;

    private String unknownRestrictProvince;

    /**
     * 状态      0或空为正常 1停用
     */
    private String templateStatus;

    private List<String> startWorkTimeList;

    private List<String> endWorkTimeList;
    private String account;
}
