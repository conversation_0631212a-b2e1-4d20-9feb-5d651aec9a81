package com.raipeng.aispeech.controller.response;

import com.raipeng.aispeech.model.AdvancedRules;
import com.raipeng.aispeech.model.script.ScriptCorpus;
import lombok.Data;

import java.util.List;

@Data
public class AILabelResponse {
    private Long id;
    private Long scriptId;
    private String labelName;
    private Integer sequence;

    private List<ScriptCorpus> scriptCorpusList;
    private List<AdvancedRules> advancedRuleList;
}
