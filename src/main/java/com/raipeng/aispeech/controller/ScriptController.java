package com.raipeng.aispeech.controller;

import com.raipeng.aispeech.config.HotConfig;
import com.raipeng.aispeech.controller.request.scriptedit.ScriptDTO;
import com.raipeng.aispeech.controller.response.Response;
import com.raipeng.aispeech.model.script.Script;
import com.raipeng.aispeech.model.script.ScriptExtend;
import com.raipeng.aispeech.service.AISemanticService;
import com.raipeng.aispeech.service.ScriptEditorService;
import com.raipeng.aispeech.service.ScriptService;
import com.raipeng.common.enums.ScriptStatusEnum;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;


@RequestMapping("/script")
@RestController
@Api(value = "/script", tags = {"话术编辑接口"})
public class ScriptController {
    @Autowired
    private ScriptService scriptService;

    @Autowired
    private ScriptEditorService scriptEditorService;

    @Autowired
    private AISemanticService aiSemanticService;

    @Autowired
    private HotConfig hotConfig;

    @PostMapping("/deleteOneScriptById")
    public Response<Boolean> deleteOneScriptById(Long id) {
        Boolean flag = scriptService.deleteOneScriptById(id);
        Response<Boolean> response = new Response<>();
        response.setResponseSuccess();
        response.setData(flag);
        return response;
    }

    @PostMapping("/updateOneScript")
    public Response<Script> updateOneScript(@RequestBody ScriptDTO script) {
        Script newScript = scriptService.updateOneScript(script);
        Response<Script> response = new Response<>();
        response.setResponseSuccess();
        response.setData(newScript);
        return response;
    }

    @GetMapping("/findOneScriptById")
    public Response<Script> findOneScriptById(Long id) {
        Optional<Script> scriptOptional = scriptService.findOneScriptById(id);
        Response<Script> response = new Response<>();
        if (scriptOptional.isPresent()) {
            Script script = scriptOptional.get();
            ScriptExtend scriptExtend = scriptEditorService.findScriptExtendByScriptStringId(script.getScriptStringId());
            if (scriptExtend != null){
                script.setRemark(scriptExtend.getRemark());
            }
            response.setData(script);
        }
        response.setResponseSuccess();
        return response;
    }

    @GetMapping("/findAllScript")
    public Response<List<Script>> findAllScript() {
        List<Script> allScript = scriptService.findAllScript();
        Response<List<Script>> response = new Response<>();
        response.setData(allScript);
        response.setResponseSuccess();
        return response;
    }

    @GetMapping("/findAllHistoryScriptByGroupId")
    public Response<List<Script>> findAllHistoryScriptByGroupId(String groupId) {
        List<Script> allScript = scriptService.findAllHistoryScriptByGroupId(groupId);
        Response<List<Script>> response = new Response<>();
        response.setData(allScript);
        response.setResponseSuccess();
        return response;
    }

    @GetMapping("/findAllScriptInDeleteStatus")
    public Response<List<Script>> findAllScriptInDeleteStatus() {
        List<Script> allScript = scriptService.findAllScriptInDeleteStatus();
        Response<List<Script>> response = new Response<>();
        response.setData(allScript);
        response.setResponseSuccess();
        return response;
    }

    @GetMapping("/findAllScriptByStatus")
    public Response<List<Script>> findAllScriptByStatus(ScriptStatusEnum status) {
        List<Script> allScript = scriptService.findAllScriptByStatus(status);
        Response<List<Script>> response = new Response<>();
        response.setData(allScript);
        response.setResponseSuccess();
        return response;
    }

    @GetMapping("/findScriptListByNameAndStatus")
    public Response<List<Script>> findScriptListByNameAndStatus(String name, ScriptStatusEnum status) {
        List<Script> scriptListByNameAndStatus = scriptService.findScriptListByNameAndStatus(name, status);
        Response<List<Script>> response = new Response<>();
        response.setData(scriptListByNameAndStatus);
        response.setResponseSuccess();
        return response;
    }

    @GetMapping("/findActiveScriptIds")
    public List<Long> findActiveScriptIds() {
        if (hotConfig.isVersionUpgrade()) {
            return new ArrayList<>();
        } else {
            return scriptService.findActiveScriptIds();
        }
    }

    @GetMapping("/getActiveSecondIndustryIds")
    public List<Long> getActiveSecondIndustryIds() {
        return aiSemanticService.getActiveSecondIndustryIds();
    }

    @GetMapping("/getSemanticsString")
    public String getSemanticsString(@RequestParam(name = "secondIndustryId") Long secondIndustryId) {
        return scriptService.getSemanticsString(secondIndustryId);
    }

    @GetMapping("/findLatestActiveScriptByScriptId")
    public String findLatestActiveScriptByScriptId(@RequestParam(name="id") Long id) {
        return scriptService.findLatestActiveScriptByScriptId(id);
    }
}
