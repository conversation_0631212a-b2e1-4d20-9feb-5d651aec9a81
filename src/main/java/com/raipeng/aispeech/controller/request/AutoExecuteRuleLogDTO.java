package com.raipeng.aispeech.controller.request;

import lombok.Data;

@Data
public class AutoExecuteRuleLogDTO {
    /**
     * 规则id
     */
    private Long ruleId;
    /**
     * 执行类型 0 收集线索 1 分配线索到坐席组 2分配线索到坐席 3归档线索
     */
    private String executeType;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;

    private Boolean removeEmpty;
}
