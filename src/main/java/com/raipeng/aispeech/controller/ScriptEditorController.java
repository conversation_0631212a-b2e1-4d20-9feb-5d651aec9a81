package com.raipeng.aispeech.controller;


import com.raipeng.aispeech.controller.request.EditWatchAccountParam;
import com.raipeng.aispeech.controller.request.scriptedit.ScriptDTO;
import com.raipeng.aispeech.controller.response.Response;
import com.raipeng.aispeech.model.script.Script;
import com.raipeng.aispeech.model.script.ScriptEditor;
import com.raipeng.aispeech.model.script.ScriptExtend;
import com.raipeng.aispeech.service.ScriptConvertService;
import com.raipeng.aispeech.service.ScriptEditorService;
import com.raipeng.common.enums.ScriptStatusEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.CompletableFuture;


@RestController
@RequestMapping("/scriptEditor")
@Api(value = "/scriptEditor", tags = {"话术师编辑接口"})
public class ScriptEditorController {
    @Autowired
    private ScriptEditorService scriptEditorService;
    @Autowired
    private ScriptConvertService scriptConvertService;

    @ApiOperation(value = "创建一个话术")
    @PostMapping("/createOneScript")
    public Response<Script> createOneScript(@RequestBody ScriptDTO param) {
        Script script = scriptEditorService.createOneScript(param);
        Response<Script> response = new Response<>();
        response.setResponseSuccess();
        response.setData(script);
        return response;
    }

    @ApiOperation(value = "复制一个话术")
    @PostMapping("/copyOneScript")
    public synchronized Response<Script> copyOneScript(@RequestParam Long scriptId) {
        Script script = scriptEditorService.copyOneScript(scriptId);
        Response<Script> response = new Response<>();
        response.setResponseSuccess();
        response.setData(script);
        CompletableFuture.runAsync(() -> scriptConvertService.handleOneUnhandledAudio(script.getId()));
        return response;
    }

    @ApiOperation(value = "升级一个话术的版本")
    @PostMapping("/updateOneScriptVersion")
    public synchronized Response<Script> updateOneScriptVersion(@RequestParam Long scriptId) {
        Script script = scriptEditorService.updateOneScriptVersion(scriptId);
        Response<Script> response = new Response<>();
        response.setResponseSuccess();
        response.setData(script);
        return response;
    }

    @ApiOperation(value = "逻辑删除一个话术")
    @PostMapping("/deleteOneScriptLogic")
    public Response<Void> deleteOneScriptLogic(@RequestParam Long scriptId) {
        scriptEditorService.deleteOneScriptLogic(scriptId);
        Response<Void> response = new Response<>();
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "停止一个话术")
    @PostMapping("/stopOneScript")
    public Response<Void> stopOneScript(@RequestParam Long scriptId) {
        scriptEditorService.stopOneScript(scriptId);
        Response<Void> response = new Response<>();
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "激活一个话术")
    @PostMapping("/activeOneScript")
    public Response<Void> activeOneScript(@RequestParam Long scriptId) {
        scriptEditorService.activeOneScript(scriptId);
        Response<Void> response = new Response<>();
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "锁定一个话术")
    @PostMapping("/lockOneScript")
    public Response<Void> lockOneScript(@RequestParam Long scriptId) {
        scriptEditorService.lockOneScript(scriptId);
        Response<Void> response = new Response<>();
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "解锁一个话术")
    @PostMapping("/unLockOneScript")
    public Response<Void> unLockOneScript(@RequestParam Long scriptId) {
        scriptEditorService.unLockOneScript(scriptId);
        Response<Void> response = new Response<>();
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "超管给一个话术添加可见账号")
    @PostMapping("/editScriptWatchPermissionForEditor")
    public Response<Void> editScriptWatchPermissionForEditor(@RequestBody EditWatchAccountParam param) {
        scriptEditorService.editScriptWatchPermissionForEditor(param.getScriptId(), param.getWatchAccounts());
        Response<Void> response = new Response<>();
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "当前账号的状态下的所有可见账号")
    @GetMapping("/findAllScriptInPermission")
    public Response<List<Script>> findAllScriptInPermission(@RequestParam ScriptStatusEnum status, @RequestParam(required = false) Integer days) {
        List<Script> scripts = scriptEditorService.findAllScriptInPermission(status, days);
        Response<List<Script>> response = new Response<>();
        response.setData(scripts);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "给一个话术添加备注")
    @GetMapping("/remarkOneScript")
    public Response<ScriptExtend> remarkOneScript(@RequestParam Long scriptId, @RequestParam String remark) {
        ScriptExtend scriptExtend = scriptEditorService.remarkOneScript(scriptId, remark);
        Response<ScriptExtend> response = new Response<>();
        response.setData(scriptExtend);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "根据行业查找当前账号的所有可见话术")
    @GetMapping("/findAllScriptInPermissionByIndustryId")
    public Response<List<Script>> findAllScriptInPermissionByIndustryId(@RequestParam ScriptStatusEnum status,
                                                                        @RequestParam Long secondaryIndustryId) {
        List<Script> scripts = scriptEditorService.findAllScriptInPermissionByIndustryId(status, secondaryIndustryId);
        Response<List<Script>> response = new Response<>();
        response.setData(scripts);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "查询所有话术师账号")
    @GetMapping("/findAllScriptEditorAccounts")
    public Response<List<String>> findAllScriptEditorAccounts() {
        List<String> accounts = scriptEditorService.findAllScriptEditorAccounts();
        Response<List<String>> response = new Response<>();
        response.setData(accounts);
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "添加一个话术师账号")
    @PostMapping("/addOneScriptEditor")
    public Response<Void> addOneScriptEditor(@RequestBody ScriptEditor scriptEditor) {
        String account = scriptEditor.getAccount();
        Integer maxScriptNum = scriptEditor.getMaxScriptNum();
        Boolean isSupperAccount = scriptEditor.getIsSupperAccount();
        scriptEditorService.addOneScriptEditor(account, maxScriptNum, isSupperAccount);
        Response<Void> response = new Response<>();
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "编辑一个话术师账号")
    @PostMapping("/editScriptEditorAttribute")
    public Response<Void> editScriptEditorAttribute(@RequestBody ScriptEditor scriptEditor) {
        String account = scriptEditor.getAccount();
        Integer maxScriptNum = scriptEditor.getMaxScriptNum();
        Boolean isSupperAccount = scriptEditor.getIsSupperAccount();
        Long scriptEditorId = scriptEditor.getId();
        scriptEditorService.editScriptEditorAttribute(scriptEditorId, account, maxScriptNum, isSupperAccount);
        Response<Void> response = new Response<>();
        response.setResponseSuccess();
        return response;
    }

    @ApiOperation(value = "查询所有话术师信息")
    @PostMapping("/findAllScriptEditor")
    public Response<List<ScriptEditor>> findAllScriptEditor() {
        List<ScriptEditor> allScriptEditor = scriptEditorService.findAllScriptEditor();
        Response<List<ScriptEditor>> response = new Response<>();
        response.setData(allScriptEditor);
        response.setResponseSuccess();
        return response;
    }
}
