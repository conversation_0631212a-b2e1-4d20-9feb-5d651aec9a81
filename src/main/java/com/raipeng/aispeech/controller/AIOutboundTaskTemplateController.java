package com.raipeng.aispeech.controller;

import com.raipeng.aispeech.controller.response.AIOutboundTaskTemplateResponse;
import com.raipeng.aispeech.controller.response.Response;
import com.raipeng.aispeech.model.AIOutboundTaskTemplate;
import com.raipeng.aispeech.model.callteam.CallTeam;
import com.raipeng.aispeech.model.dto.AIOutboundTaskTemplateQueryDto;
import com.raipeng.aispeech.service.AIOutboundTaskTemplateService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/aiOutboundTaskTemplate")
@Api(value = "/aiOutboundTaskTemplate", tags = {"AI外呼任务模板"})
public class AIOutboundTaskTemplateController {
    @Autowired
    private AIOutboundTaskTemplateService aiOutboundTaskTemplateService;

    @ApiOperation(value = "findByName")
    @PostMapping(value = "/findByName")
    public Response<List<AIOutboundTaskTemplate>> findList(@RequestBody AIOutboundTaskTemplateQueryDto aiOutboundTaskTemplateQueryDto) {
        List<AIOutboundTaskTemplate> data = aiOutboundTaskTemplateService.findList(
                aiOutboundTaskTemplateQueryDto.getName(),aiOutboundTaskTemplateQueryDto.getTaskType());
        Response<List<AIOutboundTaskTemplate>> res = new Response<>();
        res.setData(data);
        res.setResponseSuccess();
        return res;
    }

    @ApiOperation(value = "operatorFindByName")
    @PostMapping(value = "/operatorFindByName")
    public Response<List<AIOutboundTaskTemplate>> operatorFindByName(@RequestBody AIOutboundTaskTemplateQueryDto aiOutboundTaskTemplateQueryDto) {
        List<AIOutboundTaskTemplate> data = aiOutboundTaskTemplateService.operatorFindByName(
                aiOutboundTaskTemplateQueryDto.getName(),aiOutboundTaskTemplateQueryDto.getTaskType(),aiOutboundTaskTemplateQueryDto.getGroupId());
        Response<List<AIOutboundTaskTemplate>> res = new Response<>();
        res.setData(data);
        res.setResponseSuccess();
        return res;
    }


    @ApiOperation(value = "findTemplatePositionById")
    @PostMapping(value = "/findTemplatePositionById")
    public Response<AIOutboundTaskTemplateResponse> findTemplatePositionById(@RequestParam Long id) {
        AIOutboundTaskTemplateResponse data = aiOutboundTaskTemplateService.findTemplatePositionById(id);
        Response<AIOutboundTaskTemplateResponse> res = new Response<>();
        res.setResponseSuccess();
        res.setData(data);
        return res;
    }

    @ApiOperation(value = "findByAiAutoName")
    @PostMapping(value = "/findByAiAutoName")
    public Response<List<AIOutboundTaskTemplate>> findAiAutoList(@RequestBody AIOutboundTaskTemplateQueryDto aiOutboundTaskTemplateQueryDto) {
        List<AIOutboundTaskTemplate> data = aiOutboundTaskTemplateService.findAiAutoList(aiOutboundTaskTemplateQueryDto.getName());
        Response<List<AIOutboundTaskTemplate>> res = new Response<>();
        res.setData(data);
        res.setResponseSuccess();
        return res;
    }

    @ApiOperation(value = "findByAiManualName")
    @PostMapping(value = "/findByAiManualName")
    public Response<List<AIOutboundTaskTemplate>> findAiManualList(@RequestBody AIOutboundTaskTemplateQueryDto aiOutboundTaskTemplateQueryDto) {
        List<AIOutboundTaskTemplate> data = aiOutboundTaskTemplateService.findAiManualList(aiOutboundTaskTemplateQueryDto.getName());
        Response<List<AIOutboundTaskTemplate>> res = new Response<>();
        res.setData(data);
        res.setResponseSuccess();
        return res;
    }

    @ApiOperation(value = "save")
    @PostMapping(value = "/save")
    public Response<AIOutboundTaskTemplate> save(@RequestBody AIOutboundTaskTemplate aiOutboundTaskTemplate) {
        AIOutboundTaskTemplate data = aiOutboundTaskTemplateService.save(aiOutboundTaskTemplate);
        Response<AIOutboundTaskTemplate> res = new Response<>();
        res.setData(data);
        res.setResponseSuccess();
        return res;
    }

    @ApiOperation(value = "deleteById")
    @GetMapping(value = "/deleteById")
    public Response<Void> deleteById(@RequestParam Long id) {
        aiOutboundTaskTemplateService.deleteById(id);
        Response<Void> res = new Response<>();
        res.setResponseSuccess();
        return res;
    }

    @ApiOperation(value = "根据groupId查询所有坐席组列表")
    @GetMapping(value = "/findAllCallTeamsByGroupId")
    public Response<List<CallTeam>> findAllCallTeamsByGroupId(@RequestParam String groupId) {
        List<CallTeam> callTeams = aiOutboundTaskTemplateService.findAllCallTeamsByGroupId(groupId);
        Response<List<CallTeam>> response = new Response<>();
        response.setResponseSuccess();
        response.setData(callTeams);
        return response;
    }



    @ApiOperation(value = "统计话术在模板使用的个数")
    @GetMapping(value = "/findTemplateByScript")
    public Response<String> findTemplateByScript(@RequestParam String scriptStringId) {
        String info = aiOutboundTaskTemplateService.findTemplateByScript(scriptStringId);
        Response<String> res = new Response<>();
        res.setData(info);
        res.setResponseSuccess();
        return res;
    }

    @ApiOperation(value = "获取火山商户模板")
    @GetMapping(value = "/findVolcanoTaskTemplates")
    public Response<List<AIOutboundTaskTemplate>> findVolcanoTaskTemplates() {
        List<AIOutboundTaskTemplate> data = aiOutboundTaskTemplateService.findVolcanoTaskTemplates();
        Response<List<AIOutboundTaskTemplate>> res = new Response<>();
        res.setData(data);
        res.setResponseSuccess();
        return res;
    }

    @ApiOperation(value = "变更模板状态")
    @PostMapping(value = "/updateTemplateStatus")
    public Response<Void> updateTemplateStatus(@RequestBody AIOutboundTaskTemplate aiOutboundTaskTemplate) {
        aiOutboundTaskTemplateService.updateTemplateStatus(aiOutboundTaskTemplate);
        Response<Void> res = new Response<>();
        res.setResponseSuccess();
        return res;
    }

}
