package com.raipeng.aispeech.controller;

import com.raipeng.aispeech.controller.response.Response;
import com.raipeng.aispeech.dto.SipIpBroadbandOperatorCreateRequest;
import com.raipeng.aispeech.dto.SipIpBroadbandOperatorResponse;
import com.raipeng.aispeech.dto.SipIpBroadbandOperatorUpdateRequest;
import com.raipeng.aispeech.enums.BroadbandOperatorType;
import com.raipeng.aispeech.service.SipIpBroadbandOperatorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 线路运营商数据控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/sip-ip")
@Api(tags = "供应线路宽带商管理")
public class SipIpBroadbandOperatorController {

    @Autowired
    private SipIpBroadbandOperatorService sipIpBroadbandOperatorService;

    /**
     * 创建线路宽带商信息
     */
    @PostMapping("/broadband-operator")
    @ApiOperation("创建线路宽带商信息")
    public Response<SipIpBroadbandOperatorResponse> create(
            @Valid @RequestBody SipIpBroadbandOperatorCreateRequest request) {
        try {
            log.info("创建线路宽带商信息请求: {}", request.getLineNumber());
            SipIpBroadbandOperatorResponse response = sipIpBroadbandOperatorService.createLineOperator(request);

            Response<SipIpBroadbandOperatorResponse> result = new Response<>();
            result.setData(response);
            result.setResponseSuccess();
            return result;
        } catch (IllegalArgumentException e) {
            log.error("创建线路宽带商信息失败，参数错误: {}", e.getMessage());
            Response<SipIpBroadbandOperatorResponse> result = new Response<>();
            result.setCode("4001");
            result.setMsg("参数错误: " + e.getMessage());
            return result;
        } catch (Exception e) {
            log.error("创建线路宽带商信息失败", e);
            Response<SipIpBroadbandOperatorResponse> result = new Response<>();
            result.setCode("4000");
            result.setMsg("创建失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 根据线路号码查询
     */
    @GetMapping("/broadband-operator/{lineNumber}")
    @ApiOperation("根据线路号码查询")
    public Response<SipIpBroadbandOperatorResponse> getByLineNumber(
            @ApiParam("线路号码") @PathVariable String lineNumber) {
        try {
            log.info("根据线路号码查询请求: {}", lineNumber);
            SipIpBroadbandOperatorResponse response = sipIpBroadbandOperatorService.getByLineNumber(lineNumber);

            Response<SipIpBroadbandOperatorResponse> result = new Response<>();
            result.setData(response);
            result.setResponseSuccess();
            return result;
        } catch (Exception e) {
            log.error("根据线路号码查询失败", e);
            Response<SipIpBroadbandOperatorResponse> result = new Response<>();
            result.setCode("4000");
            result.setMsg("查询失败: " + e.getMessage());
            return result;
        }
    }


    /**
     * 根据线路号码删除
     */
    @DeleteMapping("/broadband-operator/{lineNumber}")
    @ApiOperation("根据线路号码删除")
    public Response<Boolean> deleteByLineNumber(
            @ApiParam("线路号码") @PathVariable String lineNumber) {
        try {
            log.info("根据线路号码删除请求: {}", lineNumber);
            boolean result = sipIpBroadbandOperatorService.deleteByLineNumber(lineNumber);

            Response<Boolean> response = new Response<>();
            response.setData(result);
            response.setResponseSuccess();
            return response;
        } catch (Exception e) {
            log.error("根据线路号码删除失败", e);
            Response<Boolean> response = new Response<>();
            response.setCode("4000");
            response.setMsg("删除失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 查询所有记录
     */
    @GetMapping("/broadband-operator")
    @ApiOperation("查询所有记录")
    public Response<List<SipIpBroadbandOperatorResponse>> getAll() {
        try {
            log.info("查询所有线路宽带商信息请求");
            List<SipIpBroadbandOperatorResponse> responseList = sipIpBroadbandOperatorService.getAllLineOperators();

            Response<List<SipIpBroadbandOperatorResponse>> result = new Response<>();
            result.setData(responseList);
            result.setResponseSuccess();
            return result;
        } catch (Exception e) {
            log.error("查询所有线路宽带商信息失败", e);
            Response<List<SipIpBroadbandOperatorResponse>> result = new Response<>();
            result.setCode("4000");
            result.setMsg("查询失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 获取宽带运营商选项
     */
    @GetMapping("/broadband-operator/options")
    @ApiOperation("获取宽带运营商选项")
    public Response<List<Map<String, String>>> getBroadbandOperatorOptions() {
        try {
            log.info("获取宽带运营商选项请求");

            List<Map<String, String>> options = Arrays.stream(BroadbandOperatorType.values())
                    .map(type -> {
                        Map<String, String> option = new HashMap<>();
                        option.put("code", type.getCode());
                        option.put("name", type.getName());
                        return option;
                    })
                    .collect(Collectors.toList());

            Response<List<Map<String, String>>> result = new Response<>();
            result.setData(options);
            result.setResponseSuccess();
            return result;
        } catch (Exception e) {
            log.error("获取宽带运营商选项失败", e);
            Response<List<Map<String, String>>> result = new Response<>();
            result.setCode("4000");
            result.setMsg("获取选项失败: " + e.getMessage());
            return result;
        }
    }
}
