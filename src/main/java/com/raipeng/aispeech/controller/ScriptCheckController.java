package com.raipeng.aispeech.controller;

import com.raipeng.aispeech.controller.request.ScriptCheckRequest;
import com.raipeng.aispeech.controller.response.Response;
import com.raipeng.aispeech.model.script.ScriptCheckRecord;
import com.raipeng.aispeech.model.script.ScriptUnitContent;
import com.raipeng.aispeech.service.ScriptCheckService;
import com.raipeng.aispeech.service.ScriptCorpusService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/scriptCheck")
@RestController
@Api(value = "/scriptCheck", tags = {"scriptCheck接口"})
public class ScriptCheckController {

    @Autowired
    private ScriptCheckService scriptCheckService;

    @Autowired
    private ScriptCorpusService scriptCorpusService;

    @PostMapping("/getList")
    public Response<List<ScriptCheckRecord>> getList(@RequestBody ScriptCheckRequest scriptCheckRequest) {
        Response<List<ScriptCheckRecord>> res = new Response<>();
        res.setResponseSuccess();
        res.setData(scriptCheckService.getList(scriptCheckRequest));
        return res;
    }

    @PostMapping("/check")
    public Response<ScriptCheckRecord> check(@RequestBody ScriptCheckRequest scriptCheckRequest) {
        Response<ScriptCheckRecord> res = new Response<>();
        res.setResponseSuccess();
        res.setData(scriptCheckService.check(scriptCheckRequest.getId(), scriptCheckRequest.getCheckRes(), scriptCheckRequest.getOpinion()));
        return res;
    }

    @GetMapping("/getRecordForProcess")
    public Response<List<ScriptCheckRecord>> getRecordForProcess() {
        Response<List<ScriptCheckRecord>> res = new Response<>();
        res.setResponseSuccess();
        res.setData(scriptCheckService.getRecordForProcess());
        return res;
    }


    @GetMapping("/audioListByUpdate")
    public Response<List<ScriptUnitContent>> audioListByUpdate(@RequestParam String onlyUpdate, @RequestParam Long id) {
        Response<List<ScriptUnitContent>> response = new Response<>();
        response.setResponseSuccess();
        response.setData(scriptCorpusService.audioListByUpdate(id, onlyUpdate));
        return response;
    }

    @PostMapping("/commit")
    public Response<ScriptCheckRecord> commit(@RequestBody ScriptCheckRecord scriptCheckRecord) {
        Response<ScriptCheckRecord> response = new Response<>();
        response.setResponseSuccess();
        response.setData(scriptCheckService.commit(scriptCheckRecord));
        return response;
    }

    @PostMapping("/updateAudioTag")
    public Response<Void> updateAudioTag(@RequestParam Long id, @RequestParam String audioTag) {
        Response<Void> response = new Response<>();
        response.setResponseSuccess();
        scriptCheckService.updateAudioTag(id, audioTag);
        return response;
    }
}
