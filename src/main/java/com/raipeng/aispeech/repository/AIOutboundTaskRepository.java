package com.raipeng.aispeech.repository;

import com.raipeng.aispeech.model.AIOutboundTask;
import feign.Param;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import javax.persistence.Tuple;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Repository
public interface AIOutboundTaskRepository extends JpaRepository<AIOutboundTask,Long> {
    List<AIOutboundTask> findAllByCreateTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    List<AIOutboundTask> findAllByUpdateTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    List<AIOutboundTask> findAllByUpdateTimeBetweenAndCallStatusAndLineCodeIn(LocalDateTime startTime, LocalDateTime endTime, String callStatus, List<String> lineCodes);

    @Query(value="select t.line_code as lineCode," +
            "t.task_type as taskType, " +
            "t.ai_answer_num as aiAnswerNum " +
            "from t_ai_outbound_task t \n" +
            " where t.create_time >= :startTime\n" +
            " and t.create_time < :endTime\n" +
            " and t.call_status = '进行中' \n" +
            " and t.line_code is not null\n ",nativeQuery=true)
    List<Tuple> findTenantLimit(LocalDateTime startTime, LocalDateTime endTime);


    @Query(value="select * from t_ai_outbound_task t \n" +
            " where t.create_time >= :startTime\n" +
            " and t.create_time < :endTime\n" +
            " and (:taskName is null or t.task_name like '%' || cast(:taskName as text) || '%' )  \n" +
            " and (:callStatus is null or t.call_status = cast(:callStatus as text))\n" +
            " and (:speechCraftName is null or t.speech_craft_name like '%' || cast(:speechCraftName as text)  || '%' ) \n" +
            " and (:lineName is null or t.line_name = cast(:lineName as text))\n"+
            "and (:groupId is null or t.group_id = cast(:groupId as text))",nativeQuery=true)
    List<AIOutboundTask> findTaskListByQueryDto(@Param("startTime")LocalDateTime startTime,
                                  @Param("endTime")LocalDateTime endTime,
                                  @Param("taskName")String taskName,
                                  @Param("callStatus")String callStatus,
                                  @Param("speechCraftName")String speechCraftName,
                                  @Param("lineName")String lineName,
                                  @Param("groupId")String groupId);

    @Query(value="select * from t_ai_outbound_task t \n" +
            " where t.create_time >= :startTime\n" +
            " and t.create_time < :endTime\n" +
            " and (:taskName is null or t.task_name like '%' || cast(:taskName as text) || '%' )  \n" +
            " and (:callStatus is null or t.call_status = cast(:callStatus as text))\n" +
            " and (:speechCraftName is null or t.speech_craft_name like '%' || cast(:speechCraftName as text)  || '%' ) \n" +
            " and (:ifLineCode = '1' or t.line_code in :lineCode )\n"+
            " and (:groupId is null or t.group_id = cast(:groupId as text))",nativeQuery=true)
    List<AIOutboundTask> findTaskAdditionalList(LocalDateTime startTime, LocalDateTime endTime, String taskName, String callStatus,
                                                String speechCraftName, String ifLineCode, List<String> lineCode, String groupId);


    @Query(value="select * from t_ai_outbound_task t \n" +
            " where t.create_time >= :startTime\n" +
            " and t.create_time < :endTime\n" +
            " and (:expectedStartTime is null or t.expected_finish_time >= cast(:expectedStartTime as text) ) \n" +
            " and (:expectedFinishTime is null or t.expected_finish_time <= cast(:expectedFinishTime as text) ) \n" +
            " and (:taskType is null or t.task_type = cast(:taskType as text) ) \n" +
            " and (:ifTaskIds = '1' or t.id in :ids ) \n"+
            " and (:ifAutoRecall is null or t.auto_re_call = cast(cast(:ifAutoRecall as text) as INTEGER) ) \n" +
            " and (:ifAutoStop is null or t.is_auto_stop = cast(cast(:ifAutoStop as text) as INTEGER) )  \n" +
            " and (:smsTemplateAbnormal is null or (CASE WHEN cast(cast(:smsTemplateAbnormal as text) as INTEGER) = 0 THEN ( t.sms_template_abnormal IS NULL OR t.sms_template_abnormal = 0 ) ELSE t.sms_template_abnormal = 1 end) )  \n" +
            " and (:concurrentMin is null or t.ai_answer_num >= cast(cast(:concurrentMin as text) as INTEGER) )  \n" +
            " and (:concurrentMax is null or t.ai_answer_num <= cast(cast(:concurrentMax as text) as INTEGER) )  \n" +
            " and (:phoneNumMin is null or t.phone_num >= cast(cast(:phoneNumMin as text) as INTEGER) )  \n" +
            " and (:phoneNumMax is null or t.phone_num <= cast(cast(:phoneNumMax as text) as INTEGER) )  \n" +
            " and (:remainingNumMin is null or (t.calling_phone_num + t.recalling_phone_num) >= cast(cast(:remainingNumMin as text) as INTEGER) )  \n" +
            " and (:remainingNumMax is null or (t.calling_phone_num + t.recalling_phone_num) <= cast(cast(:remainingNumMax as text) as INTEGER) )  \n" +
            " and (:callingPhoneNumMin is null or t.calling_phone_num >= cast(cast(:callingPhoneNumMin as text) as INTEGER) )  \n" +
            " and (:callingPhoneNumMax is null or t.calling_phone_num <= cast(cast(:callingPhoneNumMax as text) as INTEGER) )  \n" +
            " and (:recallingPhoneNumMin is null or t.recalling_phone_num >= cast(cast(:recallingPhoneNumMin as text) as INTEGER) )  \n" +
            " and (:recallingPhoneNumMax is null or t.recalling_phone_num <= cast(cast(:recallingPhoneNumMax as text) as INTEGER) )  \n" +
            " and (:groupId is null or t.group_id = cast(:groupId as text))",nativeQuery=true)
    List<AIOutboundTask> findTaskListByAITool(LocalDateTime startTime, LocalDateTime endTime,
                                              String expectedStartTime, String expectedFinishTime,
                                              String taskType, String ifTaskIds, List<Long> ids,
                                              Integer ifAutoRecall, Integer ifAutoStop,
                                              Integer smsTemplateAbnormal,
                                                Integer concurrentMin, Integer concurrentMax,
                                                Integer phoneNumMin, Integer phoneNumMax,
                                                Integer remainingNumMin, Integer remainingNumMax,
                                                Integer callingPhoneNumMin, Integer callingPhoneNumMax,
                                                Integer recallingPhoneNumMin, Integer recallingPhoneNumMax,
                                              String groupId);


    List<AIOutboundTask> findAllByIdIn(List<Long> taskIds);

    List<AIOutboundTask> findAllByIdInAndGroupId(List<Long> taskIds, String groupId);


    @Query(value="select * from t_ai_outbound_task t \n" +
            " where t.create_time >= :startTime\n" +
            " and t.create_time < :endTime",nativeQuery=true)
    List<AIOutboundTask> findTaskListByTime(@Param("startTime")LocalDateTime startTime,
                                            @Param("endTime")LocalDateTime endTime);

    @Query(value="select * from t_ai_outbound_task t \n" +
            " where t.create_time >= :startTime\n" +
            " and t.create_time < :endTime\n" +
            " and (:groupId is null or t.group_id = cast(:groupId as text))",nativeQuery=true)
    List<AIOutboundTask> findTaskListByTimeAndGroupId(@Param("startTime")LocalDateTime startTime,
                                                      @Param("endTime")LocalDateTime endTime, String groupId);

    @Query(value="select * from t_ai_outbound_task t \n" +
            " where t.create_time >= :startTime\n" +
            " and t.create_time < :endTime",nativeQuery=true)
    List<AIOutboundTask> findTaskListByTimeAndGroupId(@Param("startTime")LocalDateTime startTime,
                                                      @Param("endTime")LocalDateTime endTime);

    @Query(value="select * from t_ai_outbound_task t \n" +
            " where t.create_time >= :startTime\n" +
            " and t.create_time < :endTime\n" +
        " and t.group_id = :account",nativeQuery=true)
    List<AIOutboundTask> findTaskListByTimeWithAccount(@Param("startTime")LocalDateTime startTime,
                                                       @Param("endTime")LocalDateTime endTime,
                                                       @Param("account")String account);

    @Query(value="select t.id as id, t.create_time as createTime," +
            "t.task_name as taskName,\n" +
            "t.phone_num as phoneNum,\n" +
            "t.called_phone_num as calledPhoneNum,\n" +
            "t.ai_answer_num as aiAnswerNum,\n" +
            "t.call_status as callStatus,\n" +
            "t.ai_called_num as aiCalledNum,\n" +
            "t.ai_call_rates as aiCallRates,\n" +
            "t.ai_connected_num as aiConnectedNum,\n" +
            "t.ai_connected_rates as aiConnectedRates,\n" +
            "t.a_class_num as aClassNum,\n" +
            "t.a_class_rates as aClassRates,\n" +
            "t.speech_craft_id as speechCraftId,\n" +
            "t.script_string_id as scriptStringId,\n" +
            "t.version as version,\n" +
            "t.line_id as lineId,\n" +
            "t.call_ratio_type as callRatioType,\n" +
            "t.line_name as lineName,\n" +
            "t.line_code as lineCode,\n" +
            "t.all_restrict_province as allRestrictProvince,\n" +
            "t.all_restrict_city as allRestrictCity,\n" +
            "t.yd_restrict_province as ydRestrictProvince,\n" +
            "t.yd_restrict_city as ydRestrictCity,\n" +
            "t.lt_restrict_province as ltRestrictProvince,\n" +
            "t.lt_restrict_city as ltRestrictCity,\n" +
            "t.dx_restrict_city as dxRestrictCity,\n" +
            "t.dx_restrict_province as dxRestrictProvince,\n" +
            "t.start_work_times as startWorkTimes,\n" +
            "t.end_work_times as endWorkTimes,\n" +
            "t.auto_re_call as autoReCall from t_ai_outbound_task t \n" +
            " where t.update_time >= :startTime\n" +
            " and t.update_time < :endTime\n"+
            " and t.call_status = '进行中'\n",nativeQuery=true)
    List<Tuple> findAllByTime(@Param("startTime")LocalDateTime startTime,
                                  @Param("endTime")LocalDateTime endTime);


    @Query(value="select t.id as id, t.create_time as createTime," +
            "t.task_name as taskName,\n" +
            "t.phone_num as phoneNum,\n" +
            "t.called_phone_num as calledPhoneNum,\n" +
            "t.put_through_phone_num as putThroughPhoneNum,\n" +
            "t.ai_answer_num as aiAnswerNum,\n" +
            "t.call_status as callStatus,\n" +
            "t.ai_called_num as aiCalledNum,\n" +
            "t.ai_call_rates as aiCallRates,\n" +
            "t.ai_connected_num as aiConnectedNum,\n" +
            "t.ai_connected_rates as aiConnectedRates,\n" +
            "t.a_class_num as aClassNum,\n" +
            "t.a_class_rates as aClassRates,\n" +
            "t.speech_craft_id as speechCraftId,\n" +
            "t.script_string_id as scriptStringId,\n" +
            "t.version as version,\n" +
            "t.line_id as lineId,\n" +
            "t.call_ratio_type as callRatioType,\n" +
            "t.line_name as lineName,\n" +
            "t.line_code as lineCode,\n" +
            "t.all_restrict_province as allRestrictProvince,\n" +
            "t.all_restrict_city as allRestrictCity,\n" +
            "t.yd_restrict_province as ydRestrictProvince,\n" +
            "t.yd_restrict_city as ydRestrictCity,\n" +
            "t.lt_restrict_province as ltRestrictProvince,\n" +
            "t.lt_restrict_city as ltRestrictCity,\n" +
            "t.dx_restrict_city as dxRestrictCity,\n" +
            "t.dx_restrict_province as dxRestrictProvince,\n" +
            "t.start_work_times as startWorkTimes,\n" +
            "t.end_work_times as endWorkTimes,\n" +
            "t.auto_re_call as autoReCall from t_ai_outbound_task t \n" +
            " where t.create_time >= :startTime\n" +
            " and t.create_time < :endTime\n",nativeQuery=true)
    List<Tuple> findAllByTimeWithoutStatus(@Param("startTime")LocalDateTime startTime,
                                           @Param("endTime")LocalDateTime endTime);

    @Query(value="select t.id as id, t.group_id as groupId," +
            "t.phone_num as phoneNum,\n" +
            "t.called_phone_num as calledPhoneNum,\n" +
            "t.put_through_phone_num as putThroughPhoneNum,\n" +
            "t.recalling_phone_num as recallingPhoneNum,\n" +
            "t.ai_answer_num as aiAnswerNum,\n" +
            "t.call_status as callStatus \n" +
            "from t_ai_outbound_task t \n" +
            " where t.create_time >= :startTime\n" +
            " and t.create_time < :endTime\n",nativeQuery=true)
    List<Tuple> findAllByTimeOperationMonitoring(LocalDateTime startTime, LocalDateTime endTime);


    AIOutboundTask findFirstByTaskNameAndCreateTimeBetween(String name, LocalDateTime startTime, LocalDateTime endTime);

    List<AIOutboundTask> findAIOutboundTasksByCallStatusAndLineCode(String callStatus, String lineCode);

    @Query(value = "select * from t_ai_outbound_task where id = :id",nativeQuery = true)
    AIOutboundTask findOneById(Long id);

    @Query(value = "select * from t_ai_outbound_task t where t.id in :taskIds and t.call_status='进行中'", nativeQuery = true)
    List<AIOutboundTask> findAIOutboundTasksCanBeCheckIn(Set<Long> taskIds);

    AIOutboundTask findFirstById(Long taskId);

    @Query(value = "select t.id as id, \n" +
            "t.task_name as taskName,\n" +
            "t.task_type as taskType " +
            "from t_ai_outbound_task t \n" +
            " where t.create_time >= :startTime\n" +
            " and t.create_time < :endTime\n" +
            " and (:taskName is null or t.task_name like cast(:taskName as text)) \n" +
            " and (:speechCraftName is null or t.speech_craft_name like cast(:speechCraftName as text)) \n" +
            " and (:taskType is null or t.task_type = cast(:taskType as text)) \n" +
            " and (:groupId is null or t.group_id = cast(:groupId as text))\n" +
            " order by t.id\n" +
            " limit :pageNum offset :startPage", nativeQuery = true)
    List<Tuple> findTaskListByPageQueryDto(LocalDateTime startTime, LocalDateTime endTime, String taskName,
                                           String speechCraftName, String taskType, String groupId,
                                           Integer pageNum, Integer startPage);


    @Query(value = "select count(*) from t_ai_outbound_task t" +
            " where t.create_time >= :startTime\n" +
            " and t.create_time < :endTime\n" +
            " and (:taskName is null or t.task_name like cast(:taskName as text)) \n" +
            " and (:speechCraftName is null or t.speech_craft_name like cast(:speechCraftName as text)) \n" +
            " and (:taskType is null or t.task_type = cast(:taskType as text)) \n" +
            " and (:groupId is null or t.group_id = cast(:groupId as text))\n", nativeQuery = true)
    Integer findTaskListNumByPageQueryDto(LocalDateTime startTime, LocalDateTime endTime, String taskName, String speechCraftName,
                                          String taskType, String groupId);

    @Query(value = "select id from t_ai_outbound_task t " +
            " where t.create_time >= :startTime\n" +
            " and t.create_time < :endTime\n" +
            " and (:programId is null or t.program_id = cast(:programId as text)) \n" +
            " and (:productId is null or t.product_id = cast(:productId as text)) \n" +
            " and (:industrySecondFieldId is null or t.industry_second_field_id = cast(:industrySecondFieldId as text))" +
            " and t.task_type = :taskType \n", nativeQuery = true)
    List<String> findIdsByProductIdAndProgramIdAndIndustrySecondFieldIdAndTaskType(LocalDateTime startTime, LocalDateTime endTime, String programId,
                                                                        String productId, String industrySecondFieldId, String taskType);

    @Query(value = "select id as id,program_id as ProgramId,product_id as productId,industry_second_field_id as industrySecondFieldId from t_ai_outbound_task t " +
            " where id in :ids \n", nativeQuery = true)
    List<Tuple> findFilterTaskIdsByIds(List<Long> ids);

    @Query(value = "SELECT * " +
            "FROM t_ai_outbound_task " +
            "WHERE create_time > :today " +
            "AND group_id = :groupId " +
            "AND call_status='进行中'", nativeQuery = true)
    List<AIOutboundTask> findRunningTaskByGroupId(LocalDateTime today, String groupId);


    @Query(value="SELECT id \n" +
            "FROM t_ai_outbound_task\n" +
            "WHERE script_sms @> jsonb_build_array(json_build_object('smsTemplateId', :smsTemplateId)) \n" +
            "AND create_time >= :startTime \n" +
            "AND call_status != '进行中' ",nativeQuery=true)
    List<Long> findByScriptTriggerSms(Long smsTemplateId, LocalDateTime startTime);

    @Query(value="SELECT id \n" +
            "FROM t_ai_outbound_task\n" +
            "WHERE hang_up_sms @> jsonb_build_array(json_build_object('smsTemplateId', :smsTemplateId)) \n" +
            "AND create_time >= :startTime \n" +
            "AND call_status != '进行中' ",nativeQuery=true)
    List<Long> findByHangUpTriggerSms(Long smsTemplateId, LocalDateTime startTime);


    @Query(value="SELECT id \n" +
            "FROM t_ai_outbound_task\n" +
            "WHERE (script_sms @> jsonb_build_array(json_build_object('smsTemplateId', :smsTemplateId))  " +
            " OR hang_up_sms @> jsonb_build_array(json_build_object('smsTemplateId', :smsTemplateId)) ) " +
            "AND create_time >= :startTime \n" +
            "AND call_status = '进行中' ",nativeQuery=true)
    List<Long> findRunningTaskByTemplateId(Long smsTemplateId, LocalDateTime startTime);

    @Query(value = "SELECT * " +
            "FROM t_ai_outbound_task " +
            "WHERE create_time > :today " +
            "AND script_string_id = :scriptStringId " +
            "AND call_status='进行中'", nativeQuery = true)
    List<AIOutboundTask> findRunningTaskByScriptStringId(LocalDateTime today, String scriptStringId);

    @Query(value = "SELECT * " +
            "FROM t_ai_outbound_task " +
            "WHERE create_time > :today " +
            "AND CAST(script_sms AS TEXT) LIKE :smsTemplate OR CAST(hang_up_sms AS TEXT) LIKE :smsTemplate " +
            "AND call_status='进行中'", nativeQuery = true)
    List<AIOutboundTask> findRunningTaskBySmsTemplate(LocalDateTime today, String smsTemplate);

    @Query(value = "select * from t_ai_outbound_task t " +
            " where t.create_time >= :startTime\n" +
            " and t.create_time < :endTime\n" +
            " and (:taskName is null or t.task_name like '%' || cast(:taskName as text) || '%' )  \n" +
            " and t.group_id in :groupIds \n", nativeQuery = true)
    List<AIOutboundTask> findAllTaskByModelName(LocalDateTime startTime, LocalDateTime endTime, String taskName, List<String> groupIds);

    /**
     * 根据创建时间范围查询使用的script_string_id并去重
     * 用于脚本使用情况检测 - 只查询指定时间范围内创建的任务
     */
    @Query(value = "SELECT DISTINCT script_string_id " +
            "FROM t_ai_outbound_task " +
            "WHERE script_string_id IS NOT NULL " +
            "AND script_string_id != '' " +
            "AND create_time >= :startTime " +
            "AND create_time < :endTime", nativeQuery = true)
    List<String> findDistinctScriptStringIdsByCreateTime(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询使用了指定黑名单分组ID的任务
     *
     * @param blackListGroupId 黑名单分组ID
     * @param startTime        开始时间
     * @return 使用该黑名单分组的任务列表
     */
    @Query(value = "SELECT * FROM t_ai_outbound_task t " +
            "WHERE t.tenant_black_list IS NOT NULL " +
            "AND CAST(t.tenant_black_list AS TEXT) LIKE CONCAT('%', CAST(:blackListGroupId AS TEXT), '%') " +
            "AND t.create_time >= :startTime  ", nativeQuery = true)
    List<AIOutboundTask> findTasksUsingBlackListGroup(String blackListGroupId,
                                                      LocalDateTime startTime);






    @Query(value="select * from t_ai_outbound_task t \n" +
            " where t.create_time >= :startTime\n" +
            " and t.create_time < :endTime " +
            " and t.call_status = '进行中'",nativeQuery=true)
    List<AIOutboundTask> findProcessingTaskListByTime(LocalDateTime startTime, LocalDateTime endTime);
}
