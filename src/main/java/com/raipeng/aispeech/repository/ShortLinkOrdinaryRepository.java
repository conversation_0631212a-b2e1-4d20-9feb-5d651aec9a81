package com.raipeng.aispeech.repository;

import com.raipeng.aispeech.model.ShortLinkOrdinary;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface ShortLinkOrdinaryRepository extends JpaRepository<ShortLinkOrdinary, Long> {
    boolean existsByLinkNumber(String linkNumber);

    Optional<ShortLinkOrdinary> findByLinkName(String linkName);

    List<ShortLinkOrdinary> findAllByGroupId(String groupId);

    @Query(value = "SELECT * " +
            "FROM short_link_ordinary t " +
            "WHERE (:linkNumber IS NULL OR t.link_number = CAST(:linkNumber AS TEXT)) " +
            "AND (:linkName IS NULL OR t.link_name like CONCAT('%', :linkName ,'%') )  " +
            "AND (:shortUrl IS NULL OR t.short_url = CAST(:shortUrl AS TEXT)) " +
            "AND (:originalUrl IS NULL OR t.original_url = CAST(:originalUrl AS TEXT)) " +
            "AND (:domain IS NULL OR t.domain = CAST(:domain AS TEXT)) " +
            "AND (:account IS NULL OR t.account = CAST(:account AS TEXT)) " +
            "AND t.is_deleted = false", nativeQuery = true)
    List<ShortLinkOrdinary> findAllByConditions(String linkNumber, String linkName, String shortUrl,
                                                String originalUrl, String domain, String account);

    @Query(value = "SELECT count(1) " +
            "FROM short_link_ordinary t " +
            "WHERE t.link_name like CONCAT( '%', :linkName ,'%') " , nativeQuery = true)
    Integer countByLinkNameLike(String linkName);
}
