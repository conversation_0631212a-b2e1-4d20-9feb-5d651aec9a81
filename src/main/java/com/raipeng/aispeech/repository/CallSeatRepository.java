package com.raipeng.aispeech.repository;

import com.raipeng.aispeech.enums.CallSeatStatus;
import com.raipeng.aispeech.model.callteam.CallSeat;
import feign.Param;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Set;

public interface CallSeatRepository extends JpaRepository<CallSeat, Long> {

    CallSeat findCallSeatByGroupIdAndAccountId(String groupId, Long accountId);

    CallSeat findCallSeatByAccountId(Long accountId);

    List<CallSeat> findCallSeatsByGroupId(String groupId);

    @Query(value = "select * from call_seat t where t.call_seat_status<>'OFF_LINE'", nativeQuery = true)
    List<CallSeat> findCallSeatsByCallSeatStatusIsNot(CallSeatStatus callSeatStatus);

    List<CallSeat> findCallSeatsByGroupIdAndCallTeamIdIsNot(String groupId, Long callTeamId);

    @Query(value = "select * from call_seat t " +
            "where t.task_ids is not null " +
            "and t.task_ids @> to_jsonb(:taskId) " +
            "and t.call_seat_status='HUMAN_MACHINE_IDLE' " +
            "or t.call_seat_status='MANUAL_DIRECT_IDLE' " +
            "order by random() limit 1", nativeQuery = true)
    CallSeat findCallSeatsByTaskId(Set<Long> taskId);

    @Query(value = "SELECT * FROM call_seat c \n" +
            " WHERE c.call_team_id IN (:teams)\n" +
            " AND c.group_id = :groupId\n" +
            " AND c.call_seat_status IN ('HUMAN_MACHINE_IDLE', 'MANUAL_DIRECT_IDLE')\n" +
            " AND c.task_ids is not null", nativeQuery = true)
    List<CallSeat> findAllSignedSeats(@Param("groupId") String groupId,
                                      @Param("teams") List<Long> teams);

    @Query(value = "select * from call_seat t " +
            "where t.call_team_id in (:teams) " +
            "and t.task_ids is not null " +
            "and t.task_ids @> to_jsonb(:taskId) " +
            "and t.call_seat_status <> 'OFF_LINE' " +
            "and t.group_id = :groupId "
            , nativeQuery = true)
    List<CallSeat> findCallSeatsByStatus(@Param("taskId")Long taskId,
                                         @Param("groupId")String groupId,
                                         @Param("teams")List<Long> teams);

    @Query(value = "select * from call_seat t " +
            "where t.call_seat_status <> 'OFF_LINE' "
            , nativeQuery = true)
    List<CallSeat> findAllCallSeatsByStatus();


    @Query(value = "select count(*) from call_seat t " +
            "where t.group_id = :groupId " +
            "and t.call_team_id = :callTeamId ", nativeQuery = true)
    Integer findCallSeatsCountByGroupIdAndCallTeamId(String groupId, Long callTeamId);

    @Query(value = "select * from call_seat t " +
            "where t.id in :callSeatIdList ", nativeQuery = true)
    List<CallSeat> findByCallSeatIdList(List<Long> callSeatIdList);

    List<CallSeat> findAllByGroupId(String groupId);
}
