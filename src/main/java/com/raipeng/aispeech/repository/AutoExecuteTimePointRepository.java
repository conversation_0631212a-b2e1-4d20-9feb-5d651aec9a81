package com.raipeng.aispeech.repository;


import com.raipeng.aispeech.model.AutoExecuteTimePoint;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;


public interface AutoExecuteTimePointRepository extends JpaRepository<AutoExecuteTimePoint, Long> {
    @Transactional
    @Modifying
    @Query(value = "delete from t_auto_execute_time_point   \n" +
            "where rule_id = :ruleId and rule_type = :ruleType", nativeQuery = true)
    void deleteByRuleIdAndRuleType(String ruleId, String ruleType);

    @Query(value = "select * from t_auto_execute_time_point where execute_time <= :executeTime and rule_type = :ruleType \n", nativeQuery = true)
    List<AutoExecuteTimePoint> findExecuteTimePointBeforeTime(LocalDateTime executeTime, String ruleType);

    @Transactional
    @Modifying
    @Query(value = "delete from t_auto_execute_time_point \n" +
            "where execute_time <= :executeTime and rule_type = :ruleType", nativeQuery = true)
    void deleteByExecuteTimeAndType(LocalDateTime executeTime, String ruleType);

    @Transactional
    @Modifying
    @Query(value = "delete from t_auto_execute_time_point \n" +
            "where id = :id ", nativeQuery = true)
    void deleteExecuteTimePointById(Long id);
}
