package com.raipeng.aispeech.repository;

import com.raipeng.aispeech.model.ShortLinkThousand;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface ShortLinkThousandRepository extends JpaRepository<ShortLinkThousand, Long> {
    boolean existsByLinkNumber(String linkNumber);

    Optional<ShortLinkThousand> findByLinkName(String linKName);

    List<ShortLinkThousand> findAllByGroupId(String groupId);

    @Query(value = "SELECT * " +
            "FROM short_link_thousand t " +
            "WHERE (:linkNumber IS NULL OR t.link_number = CAST(:linkNumber AS TEXT)) " +
            "AND (:linkName IS NULL OR t.link_name like CONCAT('%', :linkName ,'%') )  " +
            "AND (:originalUrl IS NULL OR t.original_url = CAST(:originalUrl AS TEXT)) " +
            "AND (:domain IS NULL OR (SELECT COUNT(*) != 0 FROM jsonb_each_text(t.domain_map) j WHERE j.key = CAST(:domain AS TEXT))) " +
            "AND (:account IS NULL OR t.account = CAST(:account AS TEXT)) " +
            "AND t.is_deleted = false", nativeQuery = true)
    List<ShortLinkThousand> findAllByConditions(String linkNumber, String linkName, String originalUrl,
                                                String domain, String account);
}
