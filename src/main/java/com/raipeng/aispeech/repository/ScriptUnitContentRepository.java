package com.raipeng.aispeech.repository;

import com.raipeng.aispeech.model.script.ScriptUnitContent;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface ScriptUnitContentRepository extends JpaRepository<ScriptUnitContent, Long> {
    List<ScriptUnitContent> findAllByCorpusId(Long corpusId);

    List<ScriptUnitContent> findByCorpusId(Long id);

    void deleteByMultiContentId(Long id);

    @Query(value = "SELECT * FROM script_unit_content t " +
            "WHERE t.multi_content_id in :ids " +
            "AND (:name is null or t.content_name like '%' || cast(:name as text) || '%' )  \n" +
            "AND (:content is null or t.content like '%' || cast(:content as text) || '%' )  \n" +
            "AND t.is_deleted=:isDeleted", nativeQuery = true)
    List<ScriptUnitContent> findAllByContentNameLikeAndMultiContentIdInAndIsDeleted(String name, List<Long> ids, String content, Boolean isDeleted);

    List<ScriptUnitContent> findAllByMultiContentIdInAndIsDeleted(List<Long> ids, Boolean isDeleted);

    List<ScriptUnitContent> findByCorpusIdIn(List<Long> ids);

    List<ScriptUnitContent> findAllByMultiContentIdAndIsDeletedOrderByOrders(Long id, boolean b);

    @Modifying
    @Transactional
    @Query(value = "update script_unit_content set content=:content where id=:id", nativeQuery = true)
    void updateContent(Long id, String content);

    @Modifying
    @Transactional
    @Query(value = "update script_unit_content set is_played = true , audio_status = :audioStatus where id=:id", nativeQuery = true)
    void updateUnitContent(Long id, String audioStatus);

    @Query(value = "SELECT * FROM script_unit_content t WHERE t.corpus_id in :ids AND t.is_deleted=:isDeleted order by orders", nativeQuery = true)
    List<ScriptUnitContent> findAllByCorpusIdInAndIsDeletedOrderByOrders(List<Long> ids, boolean isDeleted);

    void deleteAllByCorpusIdIn(List<Long> corpusIds);

    @Query(value = "SELECT * FROM script_unit_content t WHERE t.is_deleted=FALSE AND t.script_id=:scriptId", nativeQuery = true)
    List<ScriptUnitContent> findActivesByScriptId(Long scriptId);

    ScriptUnitContent findFirstById(long id);

    boolean existsByScriptId(Long scriptId);

    @Query(value = "SELECT * FROM script_unit_content t WHERE t.script_id=:scriptId AND t.is_deleted=True", nativeQuery = true)
    List<ScriptUnitContent> findAllDeletedContentByScriptId(Long scriptId);

    List<ScriptUnitContent> findAllByScriptId(Long scriptId);

    @Modifying
    @Transactional
    @Query(value = "update script_unit_content set audio_tag = :audioTag, audio_status = :audioStatus  where id=:id", nativeQuery = true)
    void updateAudioTag(Long id, String audioStatus, String audioTag);
}
