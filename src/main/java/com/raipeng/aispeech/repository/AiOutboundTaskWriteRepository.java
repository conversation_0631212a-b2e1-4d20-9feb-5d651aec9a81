package com.raipeng.aispeech.repository;

import com.raipeng.aispeech.model.AIOutboundTask;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public interface AiOutboundTaskWriteRepository extends JpaRepository<AIOutboundTask, Long> {
    @Transactional
    @Modifying
    @Query(value = "update t_ai_outbound_task  set line_ratio = :lineRatio \n" +
            "where id = :taskId and group_id = :groupId ", nativeQuery = true)
    void updateLineRatio(Long taskId, String groupId, Double lineRatio);

    @Transactional
    @Modifying
    @Query(value = "update t_ai_outbound_task  set occupy_rate = :occupyRate \n" +
            "where id = :taskId and group_id = :groupId ", nativeQuery = true)
    void updateOccupyRate(Long taskId, String groupId, Integer occupyRate);

    @Transactional
    @Modifying
    @Query(value = "update t_ai_outbound_task  set virtual_seat_ratio = :virtualRate \n" +
            "where id = :taskId and group_id = :groupId ", nativeQuery = true)
    void updateVirtualSeatRatio(Long taskId, String groupId, Double virtualRate);

    @Transactional
    @Modifying
    @Query(value = "update t_ai_outbound_task  set ai_answer_num = :aiAnswerNum \n" +
            "where id = :taskId", nativeQuery = true)
    void updateAiAnswerNum(Integer aiAnswerNum, Long taskId);

    @Transactional
    @Modifying
    @Query(value="update t_ai_outbound_task  set phone_num = phone_num + :phoneNum, \n" +
            "called_phone_num = called_phone_num + :calledPhoneNum, \n" +
            "calling_phone_num = calling_phone_num + :callingPhoneNum, \n" +
            "recalling_phone_num = recalling_phone_num + :recallingPhoneNum, \n" +
            "finished_phone_num = finished_phone_num + :finishedPhoneNum, \n" +
            "fee_minute = fee_minute + :feeMinute, \n" +
            "put_through_phone_num = put_through_phone_num + :putThroughPhoneNum  \n" +
            "where id = :taskId",nativeQuery=true)
    void updatePhoneNum(Long taskId, Integer phoneNum,
                        Integer calledPhoneNum,
                        Integer callingPhoneNum,
                        Integer recallingPhoneNum,
                        Integer finishedPhoneNum,
                        Integer putThroughPhoneNum,
                        Integer feeMinute);

    @Transactional
    @Modifying
    @Query(value="update t_ai_outbound_task  set called_phone_rate = :calledPhoneRate, \n" +
            "finished_phone_rate = :finishedPhoneRate, \n" +
            "put_through_phone_rate = :putThroughPhoneRate\n" +
            "where id = :taskId",nativeQuery=true)
    void updatePhoneRate(Long taskId, BigDecimal calledPhoneRate,
                         BigDecimal finishedPhoneRate,
                         BigDecimal putThroughPhoneRate);

    @Transactional
    @Modifying
    @Query(value="update t_ai_outbound_task  set call_status = :callStatus, task_end_time = :taskEndTime\n" +
            "where id = :taskId",nativeQuery=true)
    void updateStatus(Long taskId, String callStatus, String taskEndTime);

    @Transactional
    @Modifying
    @Query(value="update t_ai_outbound_task " +
            "set call_status = :callStatus, " +
            "task_end_time = :taskEndTime, " +
            "sms_template_abnormal = 1 " +
            "where id = :taskId",nativeQuery=true)
    void updateStatusForChannelPending(Long taskId, String callStatus, String taskEndTime);

    @Transactional
    @Modifying
    @Query(value="update t_ai_outbound_task  set call_status = :callStatus, task_end_time = :taskEndTime,ai_answer_num = 0 \n" +
            "where id = :taskId",nativeQuery=true)
    void updateAiManualStatus(Long taskId, String callStatus, String taskEndTime);

    @Transactional
    @Modifying
    @Query(value="update t_ai_outbound_task " +
            "set call_status = :callStatus, " +
            "task_end_time = :taskEndTime, " +
            "ai_answer_num = 0, " +
            "sms_template_abnormal = 1 " +
            "where id = :taskId",nativeQuery=true)
    void updateAiManualStatusForChannelPending(Long taskId, String callStatus, String taskEndTime);

    @Transactional
    @Modifying
    @Query(value="update t_ai_outbound_task  set call_status = :callStatus, task_end_time = :taskEndTime,is_auto_stop = :isAutoStop\n" +
            "where id = :taskId",nativeQuery=true)
    void updateStatus(Long taskId, String callStatus, String taskEndTime,Integer isAutoStop);

    @Transactional
    @Modifying
    @Query(value="update t_ai_outbound_task  set call_status = :callStatus, sms_template_abnormal = 1  \n" +
            "where id = :taskId",nativeQuery=true)
    void stopTaskDueToNoChannel(Long taskId, String callStatus);

    @Modifying
    @Query(value="update t_ai_outbound_task set phone_intention_num = :phoneIntentionNum where id = :taskId",nativeQuery=true)
    void updateResetIntentionPhoneNum(Long taskId, Integer phoneIntentionNum);

    @Modifying
    @Transactional
    @Query(value = "UPDATE t_ai_outbound_task t SET if_lock = :lock WHERE t.id = :id", nativeQuery = true)
    void lockUnlockTaskById(Integer lock , Long id);

    @Modifying
    @Transactional
    @Query(value = "UPDATE t_ai_outbound_task t SET " +
            "all_restrict_city = :allRestrictCity, all_restrict_province = :allRestrictProvince, " +
            "virtual_restrict_city = :virtualRestrictCity, virtual_restrict_province = :virtualRestrictProvince, " +
            "unknown_restrict_city = :unknownRestrictCity, unknown_restrict_province = :unknownRestrictProvince, " +
            "dx_restrict_city = :dxRestrictCity, dx_restrict_province = :dxRestrictProvince, " +
            "lt_restrict_city = :ltRestrictCity, lt_restrict_province = :ltRestrictProvince, " +
            "yd_restrict_city = :ydRestrictCity, yd_restrict_province = :ydRestrictProvince, " +
            "batch_status = :batchStatus " +
            "WHERE t.id = :id", nativeQuery = true)
    void updateRestrictAreaTaskById(
            Long id, String allRestrictCity, String allRestrictProvince,
            String virtualRestrictCity, String virtualRestrictProvince,
            String unknownRestrictCity, String unknownRestrictProvince,
            String dxRestrictCity, String dxRestrictProvince,
            String ltRestrictCity, String ltRestrictProvince,
            String ydRestrictCity, String ydRestrictProvince,  String batchStatus);

    @Transactional
    @Modifying
    @Query(value = "INSERT INTO t_ai_outbound_task \n" +
            "SELECT * FROM t_ai_outbound_task_history t \n" +
            "where  t.create_time >= :startTime \n" +
            "and t.create_time < :endTime  " +
            "ON CONFLICT (id) DO NOTHING \n", nativeQuery = true)
    void migrationTaskByDate(LocalDateTime startTime, LocalDateTime endTime);


    @Transactional
    @Modifying
    @Query(value="update t_ai_outbound_task set call_status = '未完成' \n" +
            " where create_time < :endTime and call_status = '进行中'",nativeQuery=true)
    void updateHistoryTaskStatus(LocalDateTime endTime);
}
