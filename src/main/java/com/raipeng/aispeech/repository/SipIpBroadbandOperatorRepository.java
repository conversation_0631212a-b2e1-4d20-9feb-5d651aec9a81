package com.raipeng.aispeech.repository;

import com.raipeng.aispeech.model.SipIpBroadbandOperator;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.persistence.Tuple;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 线路运营商数据Repository
 */
@Repository
public interface SipIpBroadbandOperatorRepository extends JpaRepository<SipIpBroadbandOperator, Long> {
    
    /**
     * 根据线路号码查找
     */
    Optional<SipIpBroadbandOperator> findByLineNumber(String lineNumber);
    
    /**
     * 根据IP地址查找
     */
    List<SipIpBroadbandOperator> findByIpAddress(String ipAddress);
    
    /**
     * 查找所有未查询IP的记录
     */
    @Query("SELECT l FROM SipIpBroadbandOperator l WHERE l.ipQueried = false AND l.ipAddress IS NOT NULL")
    List<SipIpBroadbandOperator> findUnqueriedIpRecords();
    
    /**
     * 查找指定时间之后更新的记录
     */
    @Query("SELECT l FROM SipIpBroadbandOperator l WHERE l.updateTime >= :updateTime")
    List<SipIpBroadbandOperator> findByUpdateTimeAfter(@Param("updateTime") LocalDateTime updateTime);
    
    /**
     * 批量查找线路号码
     */
    List<SipIpBroadbandOperator> findByLineNumberIn(List<String> lineNumbers);
    
    /**
     * 查找所有有效记录（用于全量加载到Redis）
     */
    @Query("SELECT l FROM SipIpBroadbandOperator l ORDER BY l.updateTime DESC")
    List<SipIpBroadbandOperator> findAllOrderByUpdateTime();
    
    /**
     * 统计未查询IP的记录数量
     */
    @Query("SELECT COUNT(l) FROM SipIpBroadbandOperator l WHERE l.ipQueried = false AND l.ipAddress IS NOT NULL")
    long countUnqueriedIpRecords();

    /**
     * 查找需要重新查询运营商的记录（未查询的 + 超过指定天数的）
     */
    @Query("SELECT l FROM SipIpBroadbandOperator l WHERE l.ipAddress IS NOT NULL")
    List<SipIpBroadbandOperator> findRecordsNeedingOperatorQuery();

    @Query(value = "select f.line_number,f.register_ip,f.operator from supply_line f where f.update_time > :updateTime",nativeQuery = true)
    List<Tuple> findSupplyLineInfo(LocalDateTime updateTime);
}
