# 方法生成功能设计文档

## 1. 概述

方法生成功能是为AI外呼系统设计的代码自动生成工具，旨在提高开发效率，减少重复性编码工作。该功能将基于模板和配置，自动生成常用的业务方法、CRUD操作、API接口等代码结构。

### 核心价值
- 提高开发效率，减少样板代码编写
- 统一代码规范和结构
- 减少人为错误，提高代码质量
- 支持快速原型开发和迭代

## 2. 技术栈与依赖

基于现有项目架构：
- **Framework**: Spring Boot 2.2.11.RELEASE
- **Java Version**: 1.8
- **模板引擎**: FreeMarker (用于代码模板)
- **构建工具**: Maven
- **代码生成工具**: MyBatis Maven Plugin (现有)
- **数据库**: MySQL + MyBatis

## 3. 架构设计

### 3.1 整体架构

```mermaid
graph TB
    A[用户输入] --> B[方法生成服务]
    B --> C[模板引擎]
    B --> D[配置解析器]
    B --> E[代码生成器]
    
    C --> F[FreeMarker模板]
    D --> G[生成配置]
    E --> H[生成的代码文件]
    
    subgraph "模板类型"
        F1[Controller模板]
        F2[Service模板]
        F3[Repository模板]
        F4[Entity模板]
        F5[DTO模板]
    end
    
    F --> F1
    F --> F2
    F --> F3
    F --> F4
    F --> F5
    
    H --> I[目标项目文件]
```

### 3.2 核心组件设计

#### 3.2.1 方法生成服务 (MethodGenerationService)
- 负责协调整个生成流程
- 处理用户输入参数
- 调用各个子组件完成代码生成

#### 3.2.2 模板管理器 (TemplateManager)
- 管理各种代码模板
- 支持模板的动态加载和更新
- 提供模板验证功能

#### 3.2.3 代码生成器 (CodeGenerator)
- 基于模板和配置生成代码
- 支持多种代码结构生成
- 提供代码格式化功能

#### 3.2.4 配置解析器 (ConfigurationParser)
- 解析生成配置文件
- 验证配置参数的有效性
- 提供默认配置支持

## 4. 功能模块设计

### 4.1 支持的生成类型

| 生成类型 | 描述 | 输出文件 |
|---------|------|---------|
| Controller | REST API控制器 | XxxController.java |
| Service | 业务逻辑服务层 | XxxService.java, XxxServiceImpl.java |
| Repository | 数据访问层 | XxxRepository.java, XxxMapper.xml |
| Entity | 数据库实体类 | XxxEntity.java |
| DTO | 数据传输对象 | XxxDTO.java, XxxRequest.java, XxxResponse.java |
| Utils | 工具类方法 | XxxUtil.java |

### 4.2 方法生成配置

#### 4.2.1 基础配置结构
```yaml
generation:
  target_module: "ai-call"           # 目标模块
  package_base: "com.raipeng.aicall" # 基础包名
  entity_name: "User"                # 实体名称
  table_name: "t_user"               # 表名
  author: "系统自动生成"               # 作者
  generate_types:                    # 生成类型
    - controller
    - service
    - repository
    - entity
    - dto
```

#### 4.2.2 字段配置
```yaml
fields:
  - name: "id"
    type: "Long"
    comment: "主键ID"
    primary_key: true
  - name: "userName"
    type: "String"
    comment: "用户名"
    required: true
    max_length: 50
  - name: "createTime"
    type: "Date"
    comment: "创建时间"
    auto_fill: true
```

### 4.3 模板设计

#### 4.3.1 Controller模板特性
- 标准REST API结构 (GET, POST, PUT, DELETE)
- 统一响应格式
- 参数验证注解
- API文档注解 (Swagger)
- 异常处理

#### 4.3.2 Service模板特性
- 业务逻辑接口和实现分离
- 事务管理注解
- 日志记录
- 异常处理
- 参数校验

#### 4.3.3 Repository模板特性
- MyBatis Mapper接口
- 基础CRUD操作
- 分页查询支持
- 动态SQL构建

### 4.4 生成规则

#### 4.4.1 命名规范
- **类名**: 采用大驼峰命名 (PascalCase)
- **方法名**: 采用小驼峰命名 (camelCase)
- **变量名**: 采用小驼峰命名
- **常量名**: 采用大写下划线分割

#### 4.4.2 文件结构
```
src/main/java/com/raipeng/{module}/
├── controller/
│   └── {Entity}Controller.java
├── service/
│   ├── {Entity}Service.java
│   └── impl/
│       └── {Entity}ServiceImpl.java
├── repository/
│   └── {Entity}Repository.java
├── entity/
│   └── {Entity}.java
└── dto/
    ├── {Entity}DTO.java
    ├── {Entity}Request.java
    └── {Entity}Response.java
```

## 5. API接口设计

### 5.1 方法生成接口

#### 5.1.1 生成代码接口
```http
POST /method-generation/generate
Content-Type: application/json

{
  "target_module": "ai-call",
  "package_base": "com.raipeng.aicall",
  "entity_name": "User",
  "table_name": "t_user",
  "author": "开发者",
  "generate_types": ["controller", "service", "repository"],
  "fields": [
    {
      "name": "id",
      "type": "Long",
      "comment": "主键ID",
      "primary_key": true
    }
  ]
}
```

#### 5.1.2 模板管理接口
```http
GET /method-generation/templates
GET /method-generation/templates/{type}
POST /method-generation/templates
PUT /method-generation/templates/{id}
DELETE /method-generation/templates/{id}
```

#### 5.1.3 预览生成代码接口
```http
POST /method-generation/preview
```

### 5.2 响应格式

```json
{
  "code": 200,
  "message": "生成成功",
  "data": {
    "generated_files": [
      {
        "file_path": "src/main/java/com/raipeng/aicall/controller/UserController.java",
        "file_content": "...",
        "file_type": "controller"
      }
    ],
    "generation_time": "2024-01-15 10:30:00"
  }
}
```

## 6. 数据模型设计

### 6.1 生成配置表

```sql
CREATE TABLE method_generation_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    config_name VARCHAR(100) NOT NULL COMMENT '配置名称',
    target_module VARCHAR(50) NOT NULL COMMENT '目标模块',
    package_base VARCHAR(200) NOT NULL COMMENT '基础包名',
    config_content TEXT NOT NULL COMMENT '配置内容(JSON)',
    creator VARCHAR(50) COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 6.2 模板管理表

```sql
CREATE TABLE method_generation_template (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    template_name VARCHAR(100) NOT NULL COMMENT '模板名称',
    template_type VARCHAR(50) NOT NULL COMMENT '模板类型',
    template_content TEXT NOT NULL COMMENT '模板内容',
    version VARCHAR(20) DEFAULT '1.0' COMMENT '版本号',
    status TINYINT DEFAULT 1 COMMENT '状态 1-启用 0-禁用',
    creator VARCHAR(50) COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 6.3 生成记录表

```sql
CREATE TABLE method_generation_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    generation_id VARCHAR(50) NOT NULL COMMENT '生成任务ID',
    entity_name VARCHAR(100) NOT NULL COMMENT '实体名称',
    generate_types VARCHAR(200) NOT NULL COMMENT '生成类型',
    target_module VARCHAR(50) NOT NULL COMMENT '目标模块',
    file_count INT DEFAULT 0 COMMENT '生成文件数量',
    status TINYINT DEFAULT 1 COMMENT '状态 1-成功 0-失败',
    error_message TEXT COMMENT '错误信息',
    creator VARCHAR(50) COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 7. 业务逻辑设计

### 7.1 方法生成流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Controller as GenerationController
    participant Service as GenerationService
    participant Template as TemplateManager
    participant Generator as CodeGenerator
    participant FileSystem as 文件系统

    User->>Controller: 提交生成请求
    Controller->>Service: 调用生成服务
    Service->>Service: 验证参数
    Service->>Template: 获取模板
    Template-->>Service: 返回模板内容
    Service->>Generator: 执行代码生成
    Generator->>Generator: 渲染模板
    Generator->>FileSystem: 写入生成文件
    FileSystem-->>Generator: 确认写入
    Generator-->>Service: 返回生成结果
    Service-->>Controller: 返回操作结果
    Controller-->>User: 返回响应
```

### 7.2 核心业务方法

#### 7.2.1 GenerationService接口
```java
public interface GenerationService {
    /**
     * 生成代码文件
     */
    GenerationResult generateCode(GenerationRequest request);
    
    /**
     * 预览生成代码
     */
    PreviewResult previewCode(GenerationRequest request);
    
    /**
     * 验证生成配置
     */
    ValidationResult validateConfig(GenerationConfig config);
    
    /**
     * 获取支持的生成类型
     */
    List<GenerationType> getSupportedTypes();
}
```

#### 7.2.2 TemplateManager接口
```java
public interface TemplateManager {
    /**
     * 获取模板内容
     */
    String getTemplate(String templateType);
    
    /**
     * 注册新模板
     */
    void registerTemplate(String type, String content);
    
    /**
     * 更新模板
     */
    void updateTemplate(String type, String content);
    
    /**
     * 验证模板语法
     */
    ValidationResult validateTemplate(String content);
}
```

### 7.3 代码生成策略

#### 7.3.1 Controller生成策略
- 生成标准的CRUD操作方法
- 包含参数验证和异常处理
- 自动添加Swagger注解
- 支持分页查询

#### 7.3.2 Service生成策略
- 生成接口和实现类
- 包含事务管理注解
- 添加业务逻辑验证
- 支持缓存注解

#### 7.3.3 Repository生成策略
- 生成MyBatis Mapper接口
- 包含基础CRUD方法
- 支持动态查询条件
- 自动生成XML映射文件

## 8. 中间件与拦截器

### 8.1 生成前置处理器

#### 8.1.1 参数验证拦截器
- 验证必需参数完整性
- 检查包名和类名规范
- 验证字段类型有效性

#### 8.1.2 文件冲突检测器
- 检查目标文件是否已存在
- 提供覆盖确认机制
- 支持文件备份功能

### 8.2 生成后置处理器

#### 8.2.1 代码格式化器
- 统一代码缩进和空格
- 添加必要的导入语句
- 移除未使用的导入

#### 8.2.2 质量检查器
- 检查生成代码语法正确性
- 验证注解使用规范
- 检查命名规范符合性

## 9. 单元测试设计

### 9.1 测试覆盖范围

#### 9.1.1 服务层测试
- GenerationService核心业务逻辑测试
- TemplateManager模板管理测试
- CodeGenerator代码生成测试
- ConfigurationParser配置解析测试

#### 9.1.2 控制器层测试
- API接口功能测试
- 参数验证测试
- 异常处理测试
- 响应格式测试

#### 9.1.3 工具类测试
- 代码生成工具方法测试
- 文件操作工具测试
- 字符串处理工具测试

### 9.2 测试数据准备

#### 9.2.1 模板测试数据
```java
@TestComponent
public class TestDataProvider {
    public static GenerationRequest createTestRequest() {
        return GenerationRequest.builder()
            .targetModule("ai-call")
            .packageBase("com.raipeng.aicall")
            .entityName("TestUser")
            .tableName("t_test_user")
            .generateTypes(Arrays.asList("controller", "service"))
            .build();
    }
}
```

#### 9.2.2 Mock配置
```java
@ExtendWith(MockitoExtension.class)
class GenerationServiceTest {
    @Mock
    private TemplateManager templateManager;
    
    @Mock
    private CodeGenerator codeGenerator;
    
    @InjectMocks
    private GenerationServiceImpl generationService;
}
```

### 9.3 测试方法示例

```java
@Test
void testGenerateController() {
    // Given
    GenerationRequest request = createTestRequest();
    when(templateManager.getTemplate("controller"))
        .thenReturn("controller template content");
    
    // When
    GenerationResult result = generationService.generateCode(request);
    
    // Then
    assertThat(result.isSuccess()).isTrue();
    assertThat(result.getGeneratedFiles()).hasSize(1);
    verify(codeGenerator).generateFromTemplate(any(), any());
}
```